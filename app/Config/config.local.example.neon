parameters:
	mode: 'dev'
	rabbitMqAllowed: true
	trackingAllowed: true
	fio:
		cs:
			account: ********/2010
			token: kx11cwB3qPSR6wVkgxEuQpvOK1lIHebpbxQRTHNof1Nye8O70rqu8GVcaSakFEnX
		sk:
			account: ********/2010
			token: LivF8wdngnwYMvkqxqJcpvcK5OyH6EOoStggOoA2qCtzc2FNxYZ2BKbjhOsMo9kF
	intercom:
		appId: gw38ro14
		apiKey: 123
	amazon:
		smtp:
			host:
			port: 465
			username:
			password:
		sqs:
			region: eu-west-1
			version: latest
			credentials:
				key: 123
				secret: 123
		ses:
			region: eu-west-1
			version: latest
			credentials:
				key: 123
				secret: 123
	db:
		dsn: "mysql:host=127.0.0.1;dbname=tipli"
		host: 127.0.0.1
		dbname: tipli
		user: root
		password: root
		metadataCache: default
	elasticSearch:
		host: **************
		port: 9200
		username: elastic
		password: "--he<PERSON><PERSON> v 1pass--"

rabbitMq:
	connections:
		default:
			host: '************'
			port: 5673
			user: admin
			password: "-- he<PERSON><PERSON> v 1pass --"
			heartbeat: 200
services:
	amazonEmailSender: tipli\Model\Messages\AmazonFakeEmailSender
	mailgunEmailSender: tipli\Model\Messages\MailgunEmailSender(%mailgun.apiKey%, %mailgun.domains%)
	mandrillEmailSender: tipli\Model\Messages\MandrillEmailSender(%mandrill.apiKey%)
	smsSender: tipli\Model\Messages\SmsFakeClient
	freshdeskClient: tipli\Model\Freshdesk\Client(%freshdesk%)

extensions:
	logging: Contributte\Logging\DI\TracyLoggingExtension

logging:
	logDir: %appDir%/../log

