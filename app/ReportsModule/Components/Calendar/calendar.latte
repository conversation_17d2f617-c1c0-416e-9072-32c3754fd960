{snippet}
<div class="btn-group pull-right">
    <img src="{$basePath}/images/spinner.gif" style="padding-top:10px;padding-right:15px" class="hide" id="spinner" />
    <label class="btn btn-default" style="float:right">
        {if $shortcut}<span id="current-shortcut">{$shortcut}</span>:
        {else}Vlastní:{/if}
        <input name="daterange" type="text" value="{$from->format('d/m/Y')} - {$to->format('d/m/Y')}" style="background:none;border:0;padding:0;margin:0;" readonly>
    </label>
</div>

<script>
    var labels = {
        "Dnes": "today",
        "Včera": "yesterday",
        "Tento týden": "this week",
        "Minulý týden": "last week",
        "Posledních 7 dní": "last 7 days",
        "Posledních 31 dní": "last 31 days",
        "Posledních 90 dní": "last 90 days",
        "Posledních 365 dní": "last 365 days",
        "Tento měsíc": "this month",
        "Minulý měsíc": "last month",
        "Předminulý měsíc": "month before",
        "Tento rok": "this year",
        "Minulý rok": "last year",
        "Vše": "all",
        "Vlastní": 0
    };

    function getShortcutByLabel(label) {
        return labels[label];
    }
    function getLabelByShortcut(shortcut) {
        label = Object.keys(labels).filter(function(key) {
            return labels[key] == shortcut;
        });
        return label;
    }

    $('input[name="daterange"]').daterangepicker({
        ranges: {
            'Dnes': [moment(), moment()],
            'Včera': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Tento týden': [moment().startOf('week').add(1, 'days'), moment().endOf('week').add(1, 'days')],
            'Minulý týden': [moment().subtract(1, 'week').startOf('week').add(1, 'days'), moment().subtract(1, 'week').endOf('week').add(1, 'days')],
            'Posledních 7 dní': [moment().subtract(7, 'days'), moment()],
            'Posledních 31 dní': [moment().subtract(31, 'days'), moment()],
            'Posledních 90 dní': [moment().subtract(90, 'days'), moment()],
            'Posledních 365 dní': [moment().subtract(365, 'days'), moment()],
            'Tento měsíc': [moment().startOf('month'), moment().endOf('month')],
            'Minulý měsíc': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'Předminulý měsíc': [moment().subtract(2, 'months').startOf('month'), moment().subtract(2, 'months').endOf('month')],
            'Tento rok': [moment().startOf('year'), moment().endOf('year')],
            'Minulý rok': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
            'Vše': [moment().subtract(10, 'year'), moment()],
        },
        "opens": "left",
        "locale": {
            "format": "DD.MM.YYYY",
            "separator": " - ",
            "applyLabel": "Potvrdit",
            "cancelLabel": "Zrušit",
            "fromLabel": "Od",
            "toLabel": "Do",
            "customRangeLabel": "Vlastní",
            "weekLabel": "W",
            "daysOfWeek": [
                "Ne",
                "Po",
                "Út",
                "St",
                "Čt",
                "Pá",
                "So"
            ],
            "monthNames": [
                "Leden",
                "Únor",
                "Březen",
                "Duben",
                "Květen",
                "Červen",
                "Červenec",
                "Srpen",
                "Září",
                "Říjen",
                "Listopad",
                "Prosinec"
            ],
            "firstDay": 1
        },
        alwaysShowCalendars: true,
    }).on('apply.daterangepicker', function(ev, picker) {
        var label = picker.chosenLabel;
        var shortcut = getShortcutByLabel(label);

        $("#spinner").removeClass("hide");

        $.nette.ajax({
            url: {link changeDate!},
            data: {
                "calendar-dateFrom": picker.startDate.format('YYYY-MM-DD'),
                "calendar-dateTo": picker.endDate.format('YYYY-MM-DD'),
                "calendar-dateShortcut": shortcut
            },
            type: "GET",
            success: function () {
                $("#spinner").addClass("hide");
            }
        });
    });

    if ($("#current-shortcut").length) {
        $("#current-shortcut").text(getLabelByShortcut($("#current-shortcut").text()));
    }

    $(window).on("resize", function() {
        var ranges = $(".daterangepicker .ranges");
        if ($(window).width() <= 992) {
            ranges.prependTo(ranges.parent());
        } else {
            ranges.appendTo(ranges.parent());
        }
    }).trigger("resize");
</script>
{/snippet}
