<?php

namespace tipli\ReportsModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Nette\Utils\Html;
use Nette\Utils\Strings;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Seo\Entities\Keyword;
use tipli\Model\Seo\Entities\PageExtension;
use tipli\Model\Seo\Entities\PageExtensionKeyword;
use tipli\Model\Seo\SeoFacade;
use tipli\Model\Shops\Entities\ShopSteveData;
use tipli\ReportsModule\Components\ImportVisits\ImportVisitsControl;
use tipli\ReportsModule\Components\ImportVisits\ImportVisitsControlFactory;

class SeoExtensionKeywordsPresenter extends BasePresenter
{
	/** @var ImportVisitsControlFactory @inject */
	public ImportVisitsControlFactory $importVisitsControlFactory;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var StatisticDataProvider @inject */
	public StatisticDataProvider $statisticDataProvider;

	public bool $cashback = true;

	/** @var bool @persistent */
	public bool $topTipli = false;

	public function actionWithoutCashback()
	{
		$this->actionDefault(false);
		$this->setView('default');
	}

	public function actionDefault(bool $cashback = true)
	{
		$this->cashback = $cashback;
		$qb = $this->seoFacade->getPageExtensions(true)
			->leftJoin(ShopSteveData::class, 'std', 'WITH', 'std.shop = s')
			->andWhere('s.active = :active')
			->setParameter('active', true)
			->andWhere('pe.pageType = :type')
			->setParameter('type', PageExtension::PAGE_TYPE_SHOP)
			->andWhere('s.createdAt <= :createdAt')
			->setParameter('createdAt', (new \DateTime())->modify('-3 months'));

		if ($this->topTipli) {
			$qb->andWhere('std.id IS NOT NULL AND std.couponCategory LIKE :category');
		} else {
			$qb->andWhere('std.id IS NULL OR std.couponCategory NOT LIKE :category');
		}

		$qb->setParameter('category', '%tipli%');

		if ($this->cashback) {
			$qb->andWhere('s.cashbackAllowed = true');
		} else {
			$qb->andWhere('s.cashbackAllowed = false');
		}

		$qbTop = clone $qb;

		$qb->andWhere('DATE_ADD(pe.contentRevisedAt, pe.contentRevisionPivotShift, \'DAY\') <= :now OR pe.contentRevisedAt IS NULL')
			->setParameter('now', new \DateTime());

		$localizations = [];
		$pageExtensionsIds = [];
		foreach ($qb->getQuery()->getResult() as $pageExtension) {
			if (isset($localizations[$pageExtension->getLocalization()->getLocale()]) === false) {
				$localizations[$pageExtension->getLocalization()->getLocale()] = 1;
			} else {
				$localizations[$pageExtension->getLocalization()->getLocale()]++;
			}
		}

		$currentLocalizations = $this->localizationFacade->findLocalizations(false);

		foreach ($currentLocalizations as $currentLocalization) {
			if (isset($localizations[$currentLocalization->getLocale()]) === false) {
				$localizations[$currentLocalization->getLocale()] = 0;
			}
		}

		foreach ($qbTop->getQuery()->getResult() as $pageExtension) {
			$pageExtensionsIds[] = $pageExtension->getId();
		}

		$this->template->localizations = $localizations;

		$this->template->currentLocalizations = $this->localizationFacade->findLocalizations(false);

		$this->template->getLocalizationById = static function ($id) use ($currentLocalizations) {
			foreach ($currentLocalizations as $localization) {
				if ($localization->getId() === $id) {
					return $localization;
				}
			}
		};

		$this->template->getTopUsersByRevision = function (?Localization $localization = null, ?int $lastDays = null) use ($pageExtensionsIds) {
			return $this->seoFacade->getTopUsersByRevisions($localization, $lastDays, $pageExtensionsIds, 10);
		};

		$this->template->getTopLocalizationsByRevision = function (?int $lastDays = null) use ($pageExtensionsIds) {
			return $this->seoFacade->getTopLocalizationsByRevisions($lastDays, $pageExtensionsIds, 10);
		};

		$this->template->topTipli = $this->topTipli;
		$this->template->cashback = $this->cashback;
	}

	public function createComponentPageExtensionsGrid($name)
	{
		$shopHostTurnovers = [];
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$hostTurnovers = $this->statisticDataProvider->getShopsHostTurnovers($localization);

			foreach ($hostTurnovers as $hostTurnover) {
				$shopHostTurnovers[$hostTurnover['id']] = $hostTurnover['turnover'];
			}
		}

		$qb = $this->seoFacade->getPageExtensions(true)
			->leftJoin(ShopSteveData::class, 'std', 'WITH', 'std.shop = s')
			->andWhere('s.active = :active')
			->setParameter('active', true)
			->andWhere('s.visible = :visible')
			->setParameter('visible', true)
			->andWhere('pe.pageType = :type')
			->setParameter('type', PageExtension::PAGE_TYPE_SHOP)
			->andWhere('s.createdAt <= :createdAt')
			->setParameter('createdAt', (new \DateTime())->modify('-3 months'));

		if ($this->topTipli) {
			$qb->andWhere('std.id IS NOT NULL AND std.couponCategory LIKE :category');
		} else {
			$qb->andWhere('std.id IS NULL OR std.couponCategory NOT LIKE :category');
		}

		$qb->setParameter('category', '%tipli%');

		if ($this->cashback) {
			$qb->andWhere('s.cashbackAllowed = true');
		} else {
			$qb->andWhere('s.cashbackAllowed = false');
		}

		$grid = $this->dataGridFactory->create()->getGrid($this, $name, $qb);

		$grid->setRememberState(true);
		$grid->setRefreshUrl(true);
		$grid->setColumnsHideable();

		$grid->setTemplateFile(__DIR__ . '/templates/SeoExtensionKeywords/grid/default.latte');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getLocalization()->getName();
			})
		;

		$grid->addFilterSelect('localization', 'Localization', $this->localizationFacade->findPairs(false));

		$presenter = $this->getPresenter();

		$grid->addColumnText('page', 'Page')
			->setRenderer(static function (PageExtension $pageExtension) use ($presenter) {
				return Html::el('a', $pageExtension->getPage())
					->href($presenter->link(':Admin:Shops:Shop:open', ['id' => $pageExtension->getShop()->getId()]))
					->setAttribute('target', '_blank');
			})
			->setFitContent()
			->setSortable()
		;

		$grid->addFilterText('page', 'Page');

//		$grid->addColumnText('pageType', 'Typ')
//			->setRenderer(static function (PageExtension $pageExtension) {
//				return $pageExtension->getPageType() ? PageExtension::getPageTypes()[$pageExtension->getPageType()] : '?';
//			})
//			->setSortable()
//		;
//
//		$grid->addFilterMultiSelect('pageType', 'Typ', [
//			PageExtension::PAGE_TYPE_ARTICLE => 'Článek',
//			PageExtension::PAGE_TYPE_LEAFLETS => 'Letáky',
//			PageExtension::PAGE_TYPE_SHOP => 'Obchod',
//			PageExtension::PAGE_TYPE_PRODUCTS => 'Produkty',
//		]);

		$grid->addColumnText('currentMetaTitle', 'Meta title')
			->setSortable()
			->setSortableCallback(static function (QueryBuilder $qb, $sort) {
				if ($sort['currentMetaTitle'] === 'ASC') {
					$qb->addOrderBy('LENGTH(pe.currentMetaTitle)', 'ASC');
				} else {
					$qb->addOrderBy('LENGTH(pe.currentMetaTitle)', 'DESC');
				}
			})->setRenderer(function (PageExtension $pageExtension) {
				return $pageExtension->getPageType() === PageExtension::PAGE_TYPE_SHOP && $pageExtension->getShop() ? $pageExtension->getCurrentMetaTitle() .
					' <a target="_blank" class="text-primary" href="' . $this->link(':Admin:Shops:Shop:shop', $pageExtension->getShop()->getId()) . '"><i class="fa fa-pencil"></i></a>' .
					' <span style="color: ' . ($pageExtension->getCurrentMetaTitle() && Strings::length($pageExtension->getCurrentMetaTitle()) > 65 ? "red" : "#b3b3b3") . '">' . ($pageExtension->getCurrentMetaTitle() !== null ? Strings::length($pageExtension->getCurrentMetaTitle()) : '0') . '</p>'
					: '';
			})->setTemplateEscaping(false);

//		$grid->addColumnText('metaTitle', 'Meta popis')
//			->setSortable();

		$grid->addFilterRange('currentMetaTitle', 'Character counts', 'currentMetaTitle')
			->setCondition(static function (QueryBuilder $qb, $values) {
				if ($values->from) {
					$qb->andWhere('LENGTH(pe.currentMetaTitle) >= :from')
					   ->setParameter('from', (int) $values->from);
				}

				if ($values->to) {
					$qb->andWhere('LENGTH(pe.currentMetaTitle) <= :to')
						->setParameter('to', (int) $values->to);
				}
			});

//		$grid->addColumnText('priority', 'Priorita', 's.priority')
//			->setRenderer(static function (PageExtension $pageExtension) {
//				return $pageExtension->getShop() ? $pageExtension->getShop()->getPriority() : '-';
//			})->setSortable();

		$grid->addColumnText('turnoverLast30Days', 'Turnover 30days')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getShop() && $pageExtension->getShop()->getShopData() ? $pageExtension->getShop()->getShopData()->getTotalCommissionAmountInLast30Days() : '-';
			})->setSortable()
			->setSortableCallback(static function (QueryBuilder $qb, $sort) {
				if ($sort['turnoverLast30Days'] === 'ASC') {
					$qb->addOrderBy('sd.totalCommissionAmountInLast30Days', 'ASC');
				} else {
					$qb->addOrderBy('sd.totalCommissionAmountInLast30Days', 'DESC');
				}
			});

		$grid->addColumnText('hostTurnover', 'Host turnover')
			->setRenderer(static function (PageExtension $pageExtension) use ($shopHostTurnovers) {
				if (isset($shopHostTurnovers[$pageExtension->getShop()->getId()])) {
					return number_format($shopHostTurnovers[$pageExtension->getShop()->getId()], "2", ",", " ") . ' Kč';
				} else {
					return '-';
				}
			})->setSortableCallback(static function (QueryBuilder $qb, $sort) use ($shopHostTurnovers) {
				$uniqueId = key($sort);
				$sort = $sort[$uniqueId];

				asort($shopHostTurnovers);

				$qb->orderBy('FIELD(s.id, ' . implode(",", array_keys($shopHostTurnovers)) . ')', $sort);
			})->setSortable()
			->setFilterSelect(['' => 'All', 'with_turnover' => 'With turnover', 'without_turnover' => 'Without turnover'])
			->setCondition(static function (QueryBuilder $qb, $values) use ($shopHostTurnovers) {
				if ($values === 'without_turnover') {
					$qb->andWhere('s.id NOT IN (:ids)')
						->setParameter('ids', array_keys($shopHostTurnovers));
				} elseif ($values === 'with_turnover') {
					$qb->andWhere('s.id IN (:ids)')
						->setParameter('ids', array_keys($shopHostTurnovers));
				}
			});

//		$grid->addColumnText('organicLastMonth', 'Návštěvnost za poslední měsíc')
//			->setSortable()
//			->getElementPrototype('td')->setAttribute('style', 'width: 1%; white-space:nowrap');

		$grid->addColumnDateTime('createdAt', 'Created at')
			->setDefaultHide()
			->setSortable();

		$grid->addColumnDateTime('contentRevisedAt', 'Last revision at')
			->setSortable()
			->setFilterSelect(['' => 'All', 'already_checked' => 'Already revised', 'not_checked' => 'Not yet'])
			->setCondition(static function (QueryBuilder $qb, $values) {
				if ($values === 'already_checked') {
					$qb->andWhere('pe.contentRevisedAt IS NOT NULL');
				}

				if ($values === 'not_checked') {
					$qb->andWhere('pe.contentRevisedAt IS NULL');
				}
			});

		$grid->addColumnDateTime('contentRevisedBy', 'Revised by')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getContentRevisedBy() ? $pageExtension->getContentRevisedBy()->getEmail() : 'Revision has not been checked yet';
			})->setFilterText()
			->setCondition(static function (QueryBuilder $qb, $email) {
				if ($email !== '') {
					$qb->join('pe.contentRevisedBy', 'u')
						->andWhere('u.email = :email')
						->setParameter('email', $email);
				}
			});


		$grid->addColumnDateTime('nextContentRevisedAt', 'Next revision')
			->setSortableCallback(static function (QueryBuilder $qb, $sort) {
				if ($sort['nextContentRevisedAt'] === 'ASC') {
					$qb->addOrderBy('DATE_ADD(pe.contentRevisedAt, pe.contentRevisionPivotShift, \'DAY\')', 'ASC');
				} else {
					$qb->addOrderBy('DATE_ADD(pe.contentRevisedAt, pe.contentRevisionPivotShift, \'DAY\')', 'DESC');
				}
			})->setFilterSelect(['' => 'All', 'check' => 'Revision required'])
			->setCondition(static function (QueryBuilder $qb, $values) {
				if ($values === 'check') {
					$qb->andWhere('DATE_ADD(pe.contentRevisedAt, pe.contentRevisionPivotShift, \'DAY\') <= :now OR pe.contentRevisedAt IS NULL')
						->setParameter('now', new \DateTime());
				}
			});

		$grid->addColumnNumber('contentRevisionPivotShift', 'Days until the next revision')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getContentRevisionPivotShift() ? $pageExtension->getContentRevisionPivotShift() : '-';
			})
			->setSortable();
//			->setFilterSelect(['' => 'All', 'check' => 'Check', 'not_check' => 'Not check'])
//			->setCondition(static function (QueryBuilder $qb, $values) {
//				if ($values === 'check') {
//					$qb->andWhere('pe.contentRevisionPivotShift > 0');
//				}
//
//				if ($values === 'not_check') {
//					$qb->andWhere('pe.contentRevisionPivotShift = 0');
//				}
//			});

		$grid->addColumnText('googleVisits', 'Visits')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getGoogleVisits() ? $pageExtension->getGoogleVisits() : '-';
			})
			->setSortable();

		$grid->addColumnText('cashbackChange', 'Cashback history')
			->setRenderer(static function (PageExtension $pageExtension) {
				return $pageExtension->getShop()->getShopData()->getLastTransactionAt() === null ? 'No' : 'Yes';
			})
			->setFilterSelect(['' => 'All', 1 => 'Yes', 0 => 'No'])
			->setCondition(static function (QueryBuilder $qb, $values) {
				if ($values === 1) {
					$qb->andWhere('sd.lastTransactionAt IS NOT NULL');
				} elseif ($values === 0) {
					$qb->andWhere('sd.lastTransactionAt IS NULL');
				}
			});

		$grid->addInlineEdit()->onControlAdd[] = static function (Container $container): void {
			 $container->addText('keywords', '');
			 $container->addText('contentRevisionPivotShift');
		};

		$grid->getInlineEdit()->onSetDefaults[] = function (Container $container, PageExtension $pageExtension): void {
			$keywords = [];
			/** @var PageExtensionKeyword $pageExtensionKeyword */
			foreach ($this->sortKeywords($pageExtension->getKeywords()->toArray()) as $pageExtensionKeyword) {
				/** @var Keyword $keyword */
				$keyword = $pageExtensionKeyword->getKeyword();

				$keywords[] = $keyword->getName() . ($keyword->isImportant() ? '!' : '');
			}

			$container->setDefaults([
				'keywords' => implode(', ', $keywords),
				'contentRevisionPivotShift' => $pageExtension->getContentRevisionPivotShift(),
			]);
		};

		$grid->getInlineEdit()->onSubmit[] = function ($id, ArrayHash $values): void {
			/** @var PageExtension $pageExtension */
			$pageExtension = $this->seoFacade->findPageExtension($id);
			$this->seoFacade->updateKeywords($pageExtension, explode(',', $values->keywords));
			$pageExtension->setContentRevisionPivotShift($values->contentRevisionPivotShift);

			$this->seoFacade->savePageExtension($pageExtension);
		};

		$grid->addAction('visitPage', '', 'visitPage!')->setClass('btn btn-xs btn-primary')->setIcon('search')
			->addAttributes(['target' => '_blank']);

		$grid->addAction('contentRevised', '', 'setContentRevised!')->setClass('btn btn-xs ajax btn-success')->setIcon('check');

		$grid->addGroupAction('Set as revised')->onSelect[] = [$this, 'handleUpdateRevision'];
		$grid->addGroupTextAction('Set days until next revision')->onSelect[] = [$this, 'handleSubmitRevision'];

		$grid->setDefaultFilter(
			[
				'nextContentRevisedAt' => 'check',
				'localization' => 1,
			]
		);

		$grid->setDefaultSort(['nextContentRevisedAt' => 'ASC']);

		$grid->setDefaultPerPage(250);
		return $grid;
	}

	protected function createComponentImportVisits(): ImportVisitsControl
	{
		$control = $this->importVisitsControlFactory->create();

		$control->onSuccess[] = function () {
			$this->flashMessage('Visits has been updated', 'success');
			$this->redirect('this');
		};

		return $control;
	}

	public function handleSubmitRevision(array $ids, $days): void
	{
		$days = (int) Strings::trim($days);

		if ($this->isAjax()) {
			/** @var PageExtension $pageExtension */
			foreach ($this->seoFacade->findPageExtensionsIn($ids) as $pageExtension) {
				$pageExtension->setContentRevisionPivotShift($days);
				$this->seoFacade->savePageExtension($pageExtension);
			}

			$this['pageExtensionsGrid']->reload();
		} else {
			$this->redirect('this');
		}
	}

	public function handleUpdateRevision(array $ids): void
	{
		if ($this->isAjax()) {
			/** @var PageExtension $pageExtension */
			foreach ($this->seoFacade->findPageExtensionsIn($ids) as $pageExtension) {
				$pageExtension->setContentRevisedAt((new \DateTime()));
				$pageExtension->setContentRevisedBy($this->getUserIdentity());

				$this->seoFacade->savePageExtension($pageExtension);
			}

			$this['pageExtensionsGrid']->reload();
		} else {
			$this->redirect('this');
		}
	}

	public function handleVisitPage($id)
	{
		/** @var PageExtension $pageExtension */
		$pageExtension = $this->seoFacade->find($id);

		$this->redirectUrl($pageExtension->getLocalization()->getBaseUrl() . '/' . $pageExtension->getPage());
	}

	public function handleSetContentRevised($id)
	{
		/** @var PageExtension $pageExtension */
		$pageExtension = $this->seoFacade->find($id);

		$user = $this->userFacade->find($this->getUserIdentity()->getId());

		$pageExtension->setContentRevisedAt((new \DateTime()));
		$pageExtension->setContentRevisedBy($user);

		$this->seoFacade->savePageExtension($pageExtension);

		if ($this->isAjax()) {
			$this['pageExtensionsGrid']->reload();
		} else {
			$this->redirect('this');
		}
	}

	public function sortKeywords(array $pageExtensionKeywords)
	{
		$isImportant = [];
		$searches = [];
		foreach ($pageExtensionKeywords as $key => $row) {
			$isImportant[$key]  = $row->getKeyword()->isImportant();
			$searches[$key] = $row->getKeyword()->getSearchesGoogle();
		}

		array_multisort($isImportant, SORT_DESC, $searches, SORT_DESC, $pageExtensionKeywords);

		return $pageExtensionKeywords;
	}

	public function handleTopTipli(bool $topTipli)
	{
		$this->topTipli = $topTipli;
		$this->redirect('this');
	}
}
