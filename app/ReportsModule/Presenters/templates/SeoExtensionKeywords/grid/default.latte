{extends $originalTemplate}

{define col-contentRevisedAt}
	{if $item->getContentRevisedAt()}
		{$item->getContentRevisedAt()->format('j. n. Y')}
	{else}
		<span>Revision not yet</span>
	{/if}
{/define}

{define col-turnoverLast30Days}
	{if $item->getShop() && $item->getShop()->getShopData()}
		{$item->getShop()->getShopData()->getTotalCommissionAmountInLast30Days() |amount} {$item->getShop()->getLocalization()->getCurrency() |currency}
	{/if}
{/define}

{define col-nextContentRevisedAt}
	{if $item->getContentRevisedAt() && $item->getContentRevisionPivotShift() > 0}
		<span style="color: {$item->getContentRevisedAt()->modify('+ ' . $item->getContentRevisionPivotShift() . ' days')->setTime(0, 0, 0) < (new DateTime)->setTime(0, 0, 0) ? 'red'}">
				{$item->getContentRevisedAt()->format('j. n. Y')}
        </span>
	{elseif $item->getContentRevisionPivotShift() === 0}
		<span>Kontrolu neprovádíme</span>
	{else}
		<span>Kontrola nebyla zatím provedena</span>
	{/if}
{/define}

{define col-nextContentRevisedAt-header}
    <a n:class="$column->isSortedBy() ? 'sort' : '', 'ajax'" href="{link sort!, sort => $control->getSortNext($column)}" id="datagrid-sort-contentRevisedAt">
        {include #column-header, column => $column}

		{if $column->isSortedBy()}
            {if $column->isSortAsc()}
                <i n:block="icon-sort-up" class="{$iconPrefix}caret-up"></i>
            {else}
                <i n:block="icon-sort-down" class="{$iconPrefix}caret-down"></i>
            {/if}
        {else}
            <i n:block="icon-sort" class="{$iconPrefix}sort"></i>
        {/if}
    </a>

    <br /><small>(untill this date)</small>
{/define}
