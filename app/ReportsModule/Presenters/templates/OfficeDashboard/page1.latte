<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>Office dashboard</title>

		<meta name="robots" content="noindex, nofollow">

		<link rel="stylesheet" href="{$basePath}/plugins/bootstrap/css/bootstrap.min.css">

		<link rel="stylesheet" href="{$basePath}/css/animate.css">
		<link rel="stylesheet" href="{$basePath}/plugins/font-awesome/css/font-awesome.min.css">

		<link rel="apple-touch-icon" sizes="152x152" href="{$basePath}/images/favicon/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/favicon-16x16.png">
		<link rel="manifest" href="{$basePath}/images/favicon/manifest.json">
		<link rel="mask-icon" href="{$basePath}/images/favicon/safari-pinned-tab.svg" color="#ee7836">
		<meta name="theme-color" content="#ffffff">

		<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
		<!--[if lt IE 9]>
{*		<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script> *}
{*		<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>*}
		<![endif]-->

		<script src="https://browser.sentry-cdn.com/4.5.2/bundle.min.js" crossorigin="anonymous" defer></script>
		<script type="text/javascript" src="{$basePath}/js/sentry.js" defer></script>

{*		<script src="{$basePath}/js/jquery-1.12.0.min.js"></script>*}
		<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
		<script src="{$basePath}/js/nette.ajax.js"></script>

		<script>
			window.onerror = function(message, file, line) {
				alert(message);
				alert(file);
				alert(line);
			};
		</script>
		<script src="{$basePath}/js/wow.min.js"></script>

		<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">
	</head>
	<body>
		<img src="{$basePath}/images/tipli7.png" class="tipli-logo" alt="" />
		<div class="settings-box">
			<label><input type="radio" value="off" name="sounds" checked /> <i class="fa fa-volume-off"></i></label>
			<label><input type="radio" value="vip" name="sounds" /> <i class="fa fa-volume-down"></i> VIP odměny</label>
			<label><input type="radio" value="all" name="sounds" /> <i class="fa fa-volume-up"></i> všechny odměny</label>
		</div>
		<div class="spinner" data-type="spinner">
			čekám na data...
		</div>
		<div class="jackpot" data-type="jackpot">
			<div class="content">
				<div class="message" data-type="message"></div>
				<div class="value" data-type="value"></div>
			</div>
		</div>
		<div class="main-container">
			<div class="row row-1">
				<div class="col-md-6 no-padding">
					<div class="main-box box" data-type="main-box">
						<div class="content">
							<h2 data-type="name"></h2>
							<h1 data-type="value"></h1>
							<div data-type="image" class="image">
								<img src="" alt="" />
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-6 no-padding">
					<div class="messages-box box" data-type="messages-box">
						<div class="messages" data-type="messages">
						</div>
						<div class="white-overlay"></div>

						<div class="vip-transactions" data-type="vip-transactions">
						</div>
					</div>
				</div>
			</div>
			<div class="row row-2">
				<div class="col-md-6 no-padding">
					<div class="users-box box" data-type="users-box">
						<div class="content">
							<h2 data-type="count-of-users">0</h2>
							<h3>nových uživatelů dnes</h3>
						</div>
					</div>
				</div>
				<div class="col-md-6 no-padding">
					<div class="total-box box" data-type="total-box">
						<div class="content">
							<h2 data-type="count-of-transactions">0</h2>
							<h3>transakcí dnes</h3>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="countdown" data-type="countdown">30</div>
		<script>
			$(document).ready(function() {
				var countdown = 45;
				triggerCountdown();
				function triggerCountdown() {
					countdown = countdown - 1;
					$("[data-type=countdown]").text(countdown);
					if (countdown < 1) {
						location.reload();
						window.location = {$nextSlideLink};
						countdown = 0;
						return;
					}
					setTimeout(triggerCountdown, 1000);
				}
			});
		</script>
		<script>
			var messagesBufferMOCK = [
				{ isJackpot: true, countOfUsers: 1, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "151 kč", amount: 151, currency: "CZK", countOfTransactions: 1, isVip: 1},
				{ isJackpot: false, countOfUsers: 2, imageUrl: "https://static.tipli.cz/shops-shop-logo/fba3/170x/fit/5929.png", type: "transaction", name: "Petr J.", value: "152 kč", amount: 1200, currency: "CZK",countOfTransactions: 2, isVip: 1 },
				{ isJackpot: false, countOfUsers: 3, imageUrl: "https://static.tipli.cz/shops-shop-logo/32c4/170x/fit/5938.png", type: "transaction", name: "Petr J.", value: "153 kč", amount: 10910, currency: "CZK", countOfTransactions: 3, isVip: 1 },
				{ isJackpot: false, countOfUsers: 4, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "154 kč", amount: 10910, currency: "CZK",countOfTransactions: 4, isVip: 1 },
				{ isJackpot: false, countOfUsers: 5, imageUrl: "https://static.tipli.cz/shops-shop-logo/fba3/170x/fit/5929.png", type: "transaction", name: "Petr J.", value: "13255 kč", countOfTransactions: 5, isVip: 1},
				{ isJackpot: false, countOfUsers: 6, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "12356 kč", countOfTransactions: 6, isVip: 1 },
				{ isJackpot: false, countOfUsers: 7, imageUrl: "https://static.tipli.cz/shops-shop-logo/32c4/170x/fit/5938.png", type: "transaction", name: "Petr J.", value: "15657 kč", countOfTransactions: 7, isVip: 1 },
				{ isJackpot: false, countOfUsers: 8, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1548 kč", countOfTransactions: 8, isVip: 1 },
				{ isJackpot: false, countOfUsers: 9, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1351 kč", countOfTransactions: 9, isVip: 1 },
				{ isJackpot: false, countOfUsers: 10, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1522 kč", countOfTransactions: 10, isVip: 1 },
				{ isJackpot: false, countOfUsers: 11, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1513 kč", countOfTransactions: 11, isVip: 1 },
				{ isJackpot: false, countOfUsers: 12, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1544 kč", amount: 10910, currency: "CZK",countOfTransactions: 12 },
				{ isJackpot: false, countOfUsers: 13, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1355 kč", countOfTransactions: 13 },
				{ isJackpot: false, countOfUsers: 14, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1256 kč", countOfTransactions: 14 },
				{ isJackpot: false, countOfUsers: 15, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "11357 kč", amount: 10910, currency: "CZK",countOfTransactions: 15 },
				{ isJackpot: false, countOfUsers: 16, imageUrl: "https://static.tipli.cz/shops-shop-logo/fba3/170x/fit/5929.png", type: "transaction", name: "Petr J.", value: "15218 kč", countOfTransactions: 16 },
				{ isJackpot: false, countOfUsers: 17, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1651 kč", countOfTransactions: 17 },
				{ isJackpot: false, countOfUsers: 18, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1552 kč", countOfTransactions: 18 },
				{ isJackpot: false, countOfUsers: 19, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1453 kč", amount: 10910, currency: "CZK", countOfTransactions: 19 },
				{ isJackpot: false, countOfUsers: 20, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1354 kč", countOfTransactions: 20 },
				{ isJackpot: false, countOfUsers: 21, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1155 kč", countOfTransactions: 21 },
				{ isJackpot: false, countOfUsers: 22, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "13256 kč", countOfTransactions: 22 },
				{ isJackpot: false, countOfUsers: 23, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1357 kč", countOfTransactions: 23 },
				{ isJackpot: true, countOfUsers: 24, imageUrl: "https://www.tipli.cz/upload/thumbnails/0/1/i-170x0-fit.png", type: "transaction", name: "Petr J.", value: "1581 kč", amount: 10910, currency: "CZK", countOfTransactions: 24 },
			];

			var stateHash = null;
			// var messagesBuffer = messagesBufferMOCK;
			var messagesBuffer = [];
			var clearMessagesInterval = 10000;
			var lastReceivedCountOfMessages = 0;
			{*var baseUrl = {$baseUrl};*}
			var baseUrl = "";
			var stateFrom = {(new \DateTime)->modify('- 5 minutes')->format('Y-m-d H:i:s')};
			var isInitialized = false;

			$(document).ready(function() {
				wow = new WOW({ boxClass: 'animate'});
				wow.init();

				processState();
				processMessagesBuffer();

				setTimeout(clearMessages, clearMessagesInterval);
			});


			function processState()
			{
				$.ajax({
					url: {link getState!},
					type: "post",
					data: { "from": stateFrom, isFirstRequest: isInitialized },
					success: function(data) {
						isInitialized = true;
						messagesBuffer = messagesBuffer.concat(data.messages);

						lastReceivedCountOfMessages = data.messages.length;

						stateFrom = data.to;

						setTimeout(processState, getProcessStateInterval());

						if (stateHash === null) {
							stateHash = data.stateHash;
						} else if (stateHash !== data.stateHash) {
							window.location = window.location;
						}
					},
					error: function(xhr, status, error) {
						// alert(status);
						// alert(error);
					}
				});
			}

			function getProcessStateInterval()
			{
				return 20 * 1000;
				// if (lastReceivedCountOfMessages < 4) {
				// 	return 5000;
				// }

				// return getRandomInt(1000, 3000);
			}

			function processMessagesBuffer()
			{
				if (messagesBuffer[0] !== undefined) {
					$("[data-type=spinner]").addClass("hide");

					var message = messagesBuffer[0];

					refreshMainBox(message);
					addMessageToMessagesBox(message);
					refreshTotalBox(message.countOfTransactions);
					refreshUsersBox(message.countOfUsers);

					messagesBuffer.shift(); // removes first item from array
				}

				var interval = getProcessMessagesBufferInterval();
				setTimeout(processMessagesBuffer, interval);
			}

			function getProcessMessagesBufferInterval()
			{
				if (messagesBuffer.length < 100) {
					return getRandomInt(1000, 5000);
				}

				return getRandomInt(50, 400);
			}

			function refreshMainBox(message)
			{
				var mainBoxObj = $("[data-type=main-box]");

				if (message.type === "transaction") {
					var text = message.name + " získává";
					mainBoxObj.find("[data-type=name]").text(text);
					mainBoxObj.find("[data-type=value]").text(message.value);
					mainBoxObj.find("[data-type=image] img").attr("src", message.imageUrl);
				}
			}

			function addMessageToMessagesBox(message)
			{
				var messageObj = $($("[data-type=message-template]").html());
				if (message.type === "transaction") {
					var text = message.name + " získává " + message.value;
					messageObj.find("[data-type=text]").text(text);
					messageObj.find("[data-type=image] img").attr("src", message.imageUrl);

					if (message.hasOwnProperty("isJackpot") && message.hasOwnProperty("isVip")) {
						var isJackpotTransaction = message.isJackpot;
						var isVipTransaction = message.isVip;

						if (isJackpotTransaction) {
							fireJackpot(message);

							messageObj.addClass("vip");
						} else if (isVipTransaction) {
							addVipTransaction(message);

							messageObj.addClass("vip");
						}
					}

					// $("[data-type=audio-cash]").trigger("play");
					if ($("input[name=sounds]:checked").val() == "all") {
						playCash();
					}
					$("[data-type=messages]").prepend(messageObj);
				}
			}

			function addVipTransaction(message)
			{
				if ($("[data-type=vip-transactions] .transactions").length > 4) {
					$('[data-type=vip-transactions] .transactions').last().remove();
				}

				if ($("input[name=sounds]:checked").val() == "vip") {
					playCash();
				}

				var transaction = $("<div>")
						.addClass("transaction")
						.html(message.name + " získává <strong>" + message.value + "</strong>!")
						// .css("background-color", getRandomColor())
						.prepend("<div class=bar></div>")
				;

				$("[data-type=vip-transactions]").addClass("active").append(transaction);

				// var delay = 20 * 1000;
				// var delayLeft = delay;
				// var interval = 25;
				// var timer = setInterval(function() {
				// 	if (delayLeft <= 0) {
				// 		transaction.fadeOut().remove();
				// 		clearInterval(timer);
				// 	}
				// 	delayLeft -= interval;
				// }, interval);
			}

			var jackpotActive = false;
			function fireJackpot(message) {
				if (jackpotActive) {
					// return;
				}

				jackpotActive = true;

				$("[data-type=jackpot]").addClass("active");
				$("[data-type=jackpot] [data-type=message]").text(message.name + " získává");
				$("[data-type=jackpot] [data-type=value]").text(message.value);

				if ($("input[name=sounds]:checked").val() != "off") {
					playJackpot();
				}

				var backgroundInterval = setInterval(function() {
					$("[data-type=jackpot]").toggleClass("white");
				}, 300);

				setTimeout(function() {
					$("[data-type=jackpot]").removeClass("active");
					jackpotActive = false;
					clearInterval(backgroundInterval);
				}, 10 * 1000);
			}

			function getRandomInt(min, max) {
				min = Math.ceil(min);
				max = Math.floor(max);
				return Math.floor(Math.random() * (max - min + 1)) + min;
			}

			function clearMessages()
			{
				var limit = 100;

				var countOfMessages = $("[data-type=messages] .message-row").length;
				if (countOfMessages > limit) {
					$('[data-type=messages] .message-row').slice((countOfMessages - limit) * -1).remove();
				}

				var countOfVipMessages = $("[data-type=vip-transactions] .message-row").length;
				if (countOfMessages > limit) {
					$('[data-type=vip-transactions] .message-row').slice((countOfVipMessages - limit) * -1).remove();
				}

				setTimeout(clearMessages, clearMessagesInterval);
			}

			function refreshTotalBox(countOfTransactions)
			{
				$("[data-type=total-box] [data-type=count-of-transactions]").text(countOfTransactions);
			}

			function refreshUsersBox(countOfUsers)
			{
				$("[data-type=users-box] [data-type=count-of-users]").text(countOfUsers);
			}

			function getRandomColor()
			{
				var colors = ["0074D9", "7FDBFF", "39CCCC", "3D9970", "2ECC40", "01FF70", "FFDC00", "FF851B", "FF4136", "F012BE"];
				return "#" + colors[Math.floor(Math.random() * colors.length)];
			}

			function playCash()
			{
				if (jackpotActive) {
					return;
				}

				var obj = $("[data-type=audio-cash]").clone();

				obj.removeAttr("data-type");

				$("body").append(obj);

				obj[0].play();

				setTimeout(function () {
					obj.remove();
				}, 10 * 1000);
			}

			function playJackpot()
			{
				var obj = $("[data-type=audio-jackpot]");
				obj[0].play();
			}

		</script>
		<style>
			.tipli-logo {
				position: absolute;
				width:100px;
				left:45px;
				top:45px;
			}
			.countdown {
				position: absolute;
				left: 5px;
				bottom: 2px;
				font-size:20px;
				font-weight:bold;
			}
			.settings-box {
				position:absolute;
				right:15px;
			}
			.settings-box label {
				display:inline-block;
				margin-left: 10px;
			}
			.spinner {
				position: fixed;
				top:0;
				left:0;
				width:100%;
				height:100%;
				background:#eee;
				color:#000;
				padding-top:20vh;
				font-size:64px;
				text-align:center;
				z-index:10000;
			}
			.jackpot {
				position: fixed;
				top:0;
				left:0;
				width:100%;
				height:100%;
				background: #eedb39;
				color:#000;
				padding-top:20vh;
				font-size:92px;
				text-align:center;
				z-index:10000;
				display:none;
			}
			.jackpot.active {
				display:block;
			}
			.jackpot.white {
				background:#fff !important;;
			}
			.jackpot .value {
				font-weight:bold;
				font-size:120px;
			}
			.main-container {
				border:25px solid #eee;
				height: 100vh;
				width: 100vw;
			}
			.main-container .row-1 {
				border-bottom:25px solid #eee;
			}
			.main-container .main-box {
				text-align: center;
				/*padding-top:25px;*/
				/*padding-bottom:25px;*/
				border-right:25px solid #eee;
				height:calc(60vh - 75px);
				display:flex;
				/*border-bottom:25px solid #eee;*/
			}
			.main-container .main-box .content {
				margin:auto;
			}
			.main-container .main-box h2 {
				font-weight:normal;
			}
			.main-container .main-box h1 {
				font-size: 100px;
			}
			.main-container .main-box .image {
				margin-top:30px;
			}
			.main-container .main-box .image img {
				max-width: 200px;
				max-height: 120px;
			}
			.main-container .messages-box .image {
				text-align: center;
				line-height:30px;
				height: 30px;
			}
			.main-container .messages-box .image img {
				max-width: 125px;
				max-height:30px;
			}
			.main-container .messages-box {
				height:calc(60vh - 75px);
			}
			.main-container .messages-box .messages {
				overflow-y: auto;
				max-height: 100%;
			}
			.main-container .messages-box .white-overlay {
				background: -moz-linear-gradient(top,  rgba(255,255,255,0) 0%, rgba(255,255,255,0.01) 1%, rgba(255,255,255,1) 99%, rgba(255,255,255,1) 100%); /* FF3.6-15 */
				background: -webkit-linear-gradient(top,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.01) 1%,rgba(255,255,255,1) 99%,rgba(255,255,255,1) 100%); /* Chrome10-25,Safari5.1-6 */
				background: linear-gradient(to bottom,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.01) 1%,rgba(255,255,255,1) 99%,rgba(255,255,255,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
				filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
				position: absolute;
				bottom: 0;
				left: 0;
				height:100px;
				width:calc(100% - 20px);
				z-index: 100;
			}
			.main-container .messages-box .messages .message-row {
				border-bottom:2px solid #eee;
				padding:3px 0;
				clear:both;
				position: relative;;
			}
			.main-container .messages-box .messages .message-row.vip {
				font-weight: bold;
			}
			.main-container .messages-box .messages .message-row .text {
				line-height:30px;
			}
			.main-container .messages-box .messages .message-row .animated-bg-layer {
				animation-name: yellowFade;
				animation-duration: 1.8s;
				position: absolute;
				left:0;
				top:0;
				width:100%;
				height:100%;
				z-index:100;
			}

			.main-container .messages-box .vip-transactions {
				display:none;
				position: absolute;
				bottom:0;
				left:0px;
				height:50px;
				background:#fff;
				z-index:200;
				width:calc(100% - 20px);
				padding-top:5px;
				padding-left:10px;
				white-space:nowrap;
				overflow:auto;
			}

			.main-container .messages-box .vip-transactions.active {
				display:block;
			}

			.main-container .messages-box .vip-transactions .transaction {
				display:inline-block;
				margin:0 5px;
				border-radius: 20px;
				background: #ffe941;
				padding:8px 10px;
				position: relative;
				white-space: normal;
			}
			/*.main-container .messages-box .vip-transactions .transaction .bar {*/
			/*	position: absolute;*/
			/*	width:0%;*/
			/*	height:100%;*/
			/*	left:0;*/
			/*	top:0;*/
			/*	z-index:1;*/
			/*	border-radius:20px;*/
			/*	background: rgba(255, 255, 255, 0.3);*/
			/*}*/

			.main-container .users-box {
				/*text-align: center;*/
				padding-top:25px;
				padding-bottom:25px;
				border-right:25px solid #eee;
				height:calc(40vh);
				text-align: center;
				display: flex;
			}
			.main-container .users-box .content {
				margin:auto;
			}
			.main-container .users-box h2 {
				font-size: 100px;
			}
			.main-container .users-box h3 {
				font-weight:normal;
				font-size 30px;
			}

			.main-container .total-box {
				/*text-align: center;*/
				padding-top:25px;
				padding-bottom:25px;
				height:calc(40vh);
				text-align: center;
				display: flex;
			}
			.main-container .total-box .content {
				margin:auto;
			}
			.main-container .total-box h2 {
				font-size: 100px;
			}
			.main-container .total-box h3 {
				font-weight:normal;
				font-size 30px;
			}

			.main-container .main-box h2 {
				font-weight:normal;
			}

			.no-padding {
				padding:0 !important;
			}

			@keyframes yellowFade {
				0% { background: #ffff00; }
				50% { background: rgba(249, 249, 0, 0.51); }
				80% { background: rgba(255, 255, 0, 0.26); }
				100% { background: none; }
			}
		</style>

		<div class="hide" data-type="message-template">
			<div class="row message-row animate slideInRight">
				<div class="animated-bg-layer"></div>
				<div class="col-md-3 no-padding">
					<div class="image" data-type="image">
						<img alt="" />
					</div>
				</div>
				<div class="col-md-9 no-padding">
					<div class="text" data-type="text">
					</div>
				</div>
			</div>
		</div>

		<audio preload="auto" data-type="audio-cash">
			<source src="{$basePath}/files/audio-cash.mp3" />
		</audio>

		<audio preload="auto" data-type="audio-jackpot">
			<source src="{$basePath}/files/audio-jackpot.wav" />
		</audio>

	</body>
</html>
