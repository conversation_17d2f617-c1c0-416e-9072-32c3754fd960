{block content}

<div class="row">
    <div class="col-md-12">
        <h1>{block title}Nespárované nabídky konkurence{/block}<a n:href="back" class="btn btn-default" style="float:right">< Zpět na report</a></h1>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="btn-group">
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    {$currentLocalization->getLocale() |flag|noescape} {$currentLocalization->getName()} <span class="caret"></span>
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a n:foreach="$localizations as $localization" n:href="this, localizationId => $localization->getId()" class="dropdown-item">{$localization->getName()}</a></li>
                </div>
                <a n:href="offerToShopMapping!" class="btn btn-info">Automatické párování</a>
            </div>
        </div>
    </div>
</div>

<hr />

<div class="row">
    <div class="col-lg-12">
        <table class="table table-responsive table-striped table-hover">
            <thead>
            <tr>
                <th>
                    Obchod
                </th>
                <th>
                    Konkurent
                </th>
                <th n:foreach="$metrics as $metric">
                    {$metric}
                </th>
                <th>
                    Akce
                </th>
            </tr>
            </thead>
            <tbody>
            {foreach $offers as $offer}
                <tr {if $offer->isIgnored()}style="background-color: #ececec; color: #b9b9b9;" data-toggle="tooltip" data-placement="bottom" title="{$offer->getNote()}"{/if}>
                    <td>
                        {$offer->getShopName()}
                    </td>
                    <td>
                        {$offer->getCompany()->getName()}
                    </td>
                    <td n:foreach="$metrics as $metricIndex => $metric">
                        {$getCompetitorValueByOfferAndMetric($offer, $metricIndex)}

                        {var $sourceUrl = $getSourceUrlByOfferAndMetric($offer, $metricIndex)}
                        {if !empty($sourceUrl)}
                            <a href="{$sourceUrl}" target="_blank" style="color: rgba(0,0,0,0.3);">
                                <span class="fa fa-link"></span>
                            </a>
                        {/if}
                    </td>
                    <td>
                        <a n:href="offer, offerId => $offer->getId(), backLink => $presenter->link('//this')">Přiřadit obchod / Upravit</a> |
                        <a n:href="ignoreOffer!, $offer->getId()">Ignorovat</a>
                    </td>
                </tr>
            {/foreach}
            </tbody>
        </table>
    </div>
</div>
