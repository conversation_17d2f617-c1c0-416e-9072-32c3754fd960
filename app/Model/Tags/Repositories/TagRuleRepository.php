<?php

namespace tipli\Model\Tags\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Tags\Entities\Tag;

class TagRuleRepository extends BaseRepository
{
	public function findTagRules(Localization $localization, ?array $exceptTags = null)
	{
		$qb = $this->createQueryBuilder('tr')
			->innerJoin('tr.tag', 't')
			->andWhere('t.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('t.type = :type')
			->setParameter('type', Tag::TYPE_PRODUCT)
			->addOrderBy('t.parentTag');

		if ($exceptTags) {
			$qb->andWhere('t NOT IN (:tags)')
				->setParameter('tags', $exceptTags);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findTagRulesByTag(Tag $tag)
	{
		return $this->createQueryBuilder('tr')
			->innerJoin('tr.tag', 't')
			->where('tr.tag = :tag')
			->setParameter('tag', $tag)
			->getQuery()
			->getResult();
	}

	public function findTopLevelTagRules()
	{
		$qb = $this->createQueryBuilder('tr')
			->innerJoin('tr.tag', 't')
			->andWhere('t.type = :type')
			->setParameter('type', Tag::TYPE_PRODUCT)
			->addOrderBy('t.parentTag')
			->andWhere('tr.parentRule IS NULL');

		return $qb->getQuery()
			->getResult();
	}
}
