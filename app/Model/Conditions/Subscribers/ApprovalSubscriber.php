<?php

namespace tipli\Model\Conditions\Subscribers;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Events\UserApprovedEdenredApprovalsEvent;
use tipli\Model\Account\Events\UserApprovedZasilkovnaApprovalsEvent;
use tipli\Model\Account\Events\UserCreatedEvent;
use tipli\Model\Conditions\ConditionsFacade;
use tipli\Model\Conditions\Entities\Document;
use tipli\Model\Layers\ClientLayer;

final class ApprovalSubscriber implements EventSubscriberInterface
{
	/** @var ClientLayer */
	private $clientLayer;

	/** @var ConditionsFacade */
	private $conditionsFacade;

	public function __construct(ConditionsFacade $conditionsFacade, ClientLayer $clientLayer)
	{
		$this->clientLayer = $clientLayer;
		$this->conditionsFacade = $conditionsFacade;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			UserCreatedEvent::class => [
				['trackConditionsApproval'],
				['trackPrivacyApproval'],
			],
			UserApprovedZasilkovnaApprovalsEvent::class => [
				['trackZasilkovnaApproval'],
			],
			UserApprovedEdenredApprovalsEvent::class => [
				['trackEdenredApproval'],
			],
		];
	}

	public function trackConditionsApproval(UserCreatedEvent $userCreatedEvent)
	{
		$user = $userCreatedEvent->getUser();

		$conditionsDocument = $this->conditionsFacade->findLatestPublishedDocument($user->getLocalization(), Document::DOCUMENT_CONDITIONS);

		$this->conditionsFacade->createApproval($user, $conditionsDocument, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent());
	}

	public function trackPrivacyApproval(UserCreatedEvent $userCreatedEvent)
	{
		$user = $userCreatedEvent->getUser();

		$privacyDocument = $this->conditionsFacade->findLatestPublishedDocument($user->getLocalization(), Document::DOCUMENT_PRIVACY);

		$this->conditionsFacade->createApproval($user, $privacyDocument, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent());
	}

	public function trackZasilkovnaApproval(UserApprovedZasilkovnaApprovalsEvent $userApprovedZasilkovnaApprovalsEvent)
	{
		$user = $userApprovedZasilkovnaApprovalsEvent->getUser();

		$privacyDocument = $this->conditionsFacade->findLatestPublishedDocument($user->getLocalization(), Document::DOCUMENT_ZASILKOVNA);

		$this->conditionsFacade->createApproval($user, $privacyDocument, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent());
	}

	public function trackEdenredApproval(UserApprovedEdenredApprovalsEvent $userApprovedEdenredApprovalsEvent)
	{
		$user = $userApprovedEdenredApprovalsEvent->getUser();

		$document = $this->conditionsFacade->findLatestPublishedDocument($user->getLocalization(), Document::DOCUMENT_EDENRED);

		$this->conditionsFacade->createApproval($user, $document, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent());
	}
}
