<?php

namespace tipli\Model\Account\Subscribers;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Events\NotificationSavedEvent;
use tipli\Model\Account\Events\ShareRewardSavedEvent;
use tipli\Model\Account\Events\UserOpenedNotificationEvent;
use tipli\Model\PartnerOrganizations\Events\PartnerOrganizationAssignedToUserEvent;
use tipli\Model\Transactions\Events\TransactionSavedEvent;

class UserCacheSubscriber implements EventSubscriberInterface
{
	/** @var Cache */
	private $cache;

	public function __construct(Storage $storage)
	{
		$this->cache = new Cache($storage);
	}

	public static function getSubscribedEvents(): array
	{
		return [
			TransactionSavedEvent::class => 'transactionSave',
			ShareRewardSavedEvent::class => 'shareRewardSave',
			NotificationSavedEvent::class => 'notificationSave',
			UserOpenedNotificationEvent::class => 'userOpenNotification',
			PartnerOrganizationAssignedToUserEvent::class => 'partnerOrganizationAssignedToUser',
		];
	}

	public function transactionSave(TransactionSavedEvent $transactionSavedEvent)
	{
		$transaction = $transactionSavedEvent->getTransaction();

		if ($transaction->getUser()) {
			$this->invalidateCache($transaction->getUser());
		}
	}

	public function shareRewardSave(ShareRewardSavedEvent $shareRewardSavedEvent)
	{
		$shareReward = $shareRewardSavedEvent->getShareReward();

		if ($shareReward->getUser()) {
			$this->invalidateCache($shareReward->getUser());
		}
	}

	public function notificationSave(NotificationSavedEvent $notificationSavedEvent)
	{
		$notification = $notificationSavedEvent->getNotification();

		if ($notification->getUser()) {
			$this->invalidateCache($notification->getUser());
		}
	}

	public function userOpenNotification(UserOpenedNotificationEvent $userOpenedNotificationEvent)
	{
		$user = $userOpenedNotificationEvent->getUser();

		$this->cache->clean([
			Cache::TAGS => ['user/' . $user->getId()],
		]);
	}

	public function partnerOrganizationAssignedToUser(PartnerOrganizationAssignedToUserEvent $partnerOrganizationAssignedToUserEvent)
	{
		$user = $partnerOrganizationAssignedToUserEvent->getUser();

		$this->cache->clean([
			Cache::TAGS => ['user/' . $user->getId()],
		]);
	}

	public function invalidateCache(User $user)
	{
		$this->cache->clean([
			Cache::TAGS => ['user/' . $user->getId()],
		]);
	}
}
