<?php

namespace tipli\Model\Account;

use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserVisit;
use tipli\Model\Account\Managers\UserVisitManager;
use tipli\Model\Account\Producers\UserVisitProducer;
use tipli\Model\Account\Repositories\UserVisitRepository;
use DateTime;
use tipli\Model\Shops\Entities\Shop;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Tracy\Debugger;

class UserVisitFacade
{
	private Cache $cache;

	public function __construct(
		private UserVisitManager $userVisitManager,
		private UserVisitRepository $userVisitRepository,
		private UserVisitProducer $userVisitProducer,
		private Storage $storage,
	) {
		$this->cache = new Cache($storage, self::class);
	}

	public function trackUserVisit(User $user, string $platform, ?Shop $shop, DateTime $dateTime): void
	{
		$uniqueId = UserVisit::buildUniqueId($user, $platform, $shop, $dateTime);

		$userVisit = $this->userVisitRepository->findByUniqueId($uniqueId);

		if ($userVisit === null) {
			$this->userVisitManager->trackUserVisit($user, $platform, $shop, $dateTime);
		}
	}

	public function scheduleTrackUserVisit(User $user, string $platform, ?Shop $shop, DateTime $createdAt): void
	{
		if (in_array($platform, UserVisit::getPlatforms()) === false) {
			Debugger::log('Invalid platform: ' . $platform, 'user-visit');
			return;
		}

		$cacheKey = 'userVisit-' . UserVisit::buildUniqueId($user, $platform, $shop, $createdAt);

		if ($this->cache->load($cacheKey) === null) {
			$this->userVisitProducer->scheduleUserVisit($user, $platform, $shop, $createdAt);

			$this->cache->save($cacheKey, true, ['expire' => '1 hour']);
		}
	}
}
