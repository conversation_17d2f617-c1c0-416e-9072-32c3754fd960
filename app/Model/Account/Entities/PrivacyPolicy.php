<?php

namespace tipli\Model\Account\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="tipli_account_privacy_policy")
 */
class PrivacyPolicy
{
	public const PRIVACY_TYPE_PRIVACY1 = 'privacy1';
	public const PRIVACY_TYPE_PRIVACY2 = 'privacy2';
	public const PRIVACY_TYPE_PRIVACY3 = 'privacy3';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User", inversedBy="privacyPolicy")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $user;

	/**
	 * @ORM\Column(type="string")
	 */
	private $privacyType;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $createdAt;

	public function __construct(User $user, $privacyType = null)
	{
		$this->user = $user;
		$this->privacyType = $privacyType;
		$this->createdAt = new \DateTime();
	}

	public static function getPrivacyTypes()
	{
		return [
			self::PRIVACY_TYPE_PRIVACY1,
			self::PRIVACY_TYPE_PRIVACY2,
			self::PRIVACY_TYPE_PRIVACY3,
		];
	}

	/**
	 * @return mixed
	 */
	public function getUser()
	{
		return $this->user;
	}

	/**
	 * @return mixed
	 */
	public function getPrivacyType()
	{
		return $this->privacyType;
	}

	/**
	 * @param mixed $privacyType
	 */
	public function setPrivacyType($privacyType)
	{
		$this->privacyType = $privacyType;
	}

	/**
	 * @return mixed
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}
}
