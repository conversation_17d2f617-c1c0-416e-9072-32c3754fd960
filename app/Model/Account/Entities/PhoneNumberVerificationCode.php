<?php

namespace tipli\Model\Account\Entities;

use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Random;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Account\Repositories\PhoneNumberVerificationCodeRepository")
 * @ORM\Table(name="tipli_account_phone_number_verification_code")
 */
class PhoneNumberVerificationCode
{
	public const EXPIRATION_TIME = '60 minutes';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $user;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $phoneNumber;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="string")
	 */
	private $verificationCode;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $ip;

	/**
	 * @ORM\Column(type="string", length=64, nullable=true)
	 */
	private ?string $platform;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $usedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(User $user, ?string $ip = null, ?string $platform = null)
	{
		$this->user = $user;
		$this->ip = $ip;
		$this->platform = $platform;
		$this->phoneNumber = $user->getPhoneNumber();
		$this->validTill = (new \DateTime())->modify('+ ' . self::EXPIRATION_TIME);
		$this->verificationCode = $this->generateVerificationCode();
		$this->createdAt = new \DateTime();
	}

	private function generateVerificationCode()
	{
		return Random::generate(4, '0-9');
	}

	public function useVerificationCode()
	{
		$this->usedAt = new \DateTime();
	}

	public function setVerificationCode($code)
	{
		$this->verificationCode = $code;
	}

	public function getVerificationCode()
	{
		return $this->verificationCode;
	}

	public function getUser()
	{
		return $this->user;
	}

	public function getValidTill()
	{
		return $this->validTill;
	}

	public function getUsedAt()
	{
		return $this->usedAt;
	}

	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	public function getPhoneNumber()
	{
		return $this->phoneNumber;
	}

	/**
	 * @param mixed $phoneNumber
	 */
	public function setPhoneNumber($phoneNumber)
	{
		$this->phoneNumber = $phoneNumber;
	}
}
