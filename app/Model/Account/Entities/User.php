<?php

namespace tipli\Model\Account\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Nette\Security\IIdentity;
use Nette\Utils\Strings;
use tipli\Model\Funnels\Entities\Funnel;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerOrganizations\Entities\PartnerOrganization;
use tipli\Model\Reviews\Entities\Review;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Utm\Entities\Utm;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Account\Repositories\UserRepository")
 * @ORM\Table(name="tipli_account_user", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="email_unique", columns={"localization_id", "email"})
 * }, indexes={
 *      @ORM\Index(name="segment_idx", columns={"segment"}),
 *      @ORM\Index(name="created_at_idx", columns={"created_at"}),
 *      @ORM\Index(name="email_idx", columns={"email"})
 * })
 */
class User implements IIdentity
{
	public const HOST_IDS = [4035981, 128745, 4035980, 128749, 1396120, 4035979, 128747];
	public const HASH_METHOD_MD5 = 'md5';
	public const HASH_METHOD_BCRYPT = 'bcrypt';

	public const ROLE_MEMBER = 'member';
	public const ROLE_ADMIN = 'admin';

	public const SEGMENT_NEW = 'new';
	public const SEGMENT_ACTIVE = 'active';
	public const SEGMENT_INACTIVE = 'inactive';

	public const ENTRANCE_TYPE_DEFAULT = 'default';
	public const ENTRANCE_TYPE_RECOMMENDATION = 'rich';
	public const ENTRANCE_TYPE_RECOMMENDATION_WITHOUT_FRIEND_BONUS = 'pure';

	private const SALT = 'tipli15916';

	public const MINIMAL_PASSWORD_LENGTH = 6;

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\Synchronization", cascade={"persist"})
	 * @ORM\JoinColumn(name="synchronization_id", referencedColumnName="id")
	 */
	private $synchronization;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Funnels\Entities\Funnel", mappedBy="user")
	 */
	private $funnels;

	/**
	 * @ORM\Column(type="string")
	 */
	private $email;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $password;

	/**
	 * @ORM\Column(type="string")
	 */
	private $hashMethod = self::HASH_METHOD_BCRYPT;

	/**
	 * @ORM\Column(type="string")
	 */
	private $role = self::ROLE_MEMBER;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization", cascade={"persist"})
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User", inversedBy="referencedUsers")
	 * @ORM\JoinColumn(name="parent_id", referencedColumnName="id", nullable=true)
	 */
	private $parentUser;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\User", mappedBy="parentUser")
	 */
	private $referencedUsers;

	/**
	* @ORM\OneToMany(targetEntity="\tipli\Model\Rewards\Entities\MoneyReward", mappedBy="user")
	*/
	private $moneyRewards;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\PartnerOrganizations\Entities\PartnerOrganization", inversedBy="users")
	 * @ORM\JoinColumn(name="partner_organization_id", referencedColumnName="id", nullable=true)
	 */
	private $partnerOrganization;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\UserData", mappedBy="user", cascade={"persist"})
	 */
	private $userData;

	/**
	 * @ORM\OneToMany(targetEntity="UserSettings", mappedBy="user", cascade={"persist"})
	 * @var ArrayCollection
	 */
	private $userSettings;

	/**
	 * @ORM\OneToMany(targetEntity="UserLuckyShopData", mappedBy="user", cascade={"persist"})
	 * @var ArrayCollection
	 */
	private $userLuckyShopData;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\UserSecurity", mappedBy="user", cascade={"persist"})
	 */
	private $userSecurity;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Utm\Entities\Utm")
	 * @ORM\JoinColumn(name="utm_id", referencedColumnName="id")
	 */
	private $utm;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Articles\Entities\Article", mappedBy="author")
	 */
	private $articles;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Shops\Entities\Shop", inversedBy="favouriteByUsers")
	 * @ORM\JoinTable(name="tipli_account_user_favourite_shops")
	 */
	private $favouriteShops;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $segment = self::SEGMENT_NEW;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\SegmentData", mappedBy="user", cascade={"persist"})
	 */
	private $segmentData;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\Mailchimp", mappedBy="user", cascade={"persist"})
	 */
	private $mailchimp;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Account\Entities\Mailkit", mappedBy="user", cascade={"persist"})
	 */
	private $mailkit;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\SendingPolicy", mappedBy="user", cascade={"persist", "remove"}, orphanRemoval=true)
	 */
	private $sendingPolicy;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\PrivacyPolicy", mappedBy="user", cascade={"persist", "remove"}, orphanRemoval=true)
	 */
	private $privacyPolicy;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\UserDuplicity", mappedBy="user", cascade={"persist", "remove"}, orphanRemoval=true)
	 */
	private $userDuplicity;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $emailsUnsubscribedAt;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $emailVerifiedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $phoneNumberVerifiedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $accountNumberVerifiedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Reviews\Entities\Review", mappedBy="user")
	 */
	private $reviews;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Transactions\Entities\Transaction", mappedBy="user")
	 */
	private $transactions;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Zasilkovna\Entities\Packet", mappedBy="user")
	 */
	private $zasilkovnaPackets;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Shops\Entities\Redirection", mappedBy="user")
	 */
	private $redirections;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Marketing\Entities\BannerClick", mappedBy="user")
	 */
	private $bannersClicks;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\MobileDevice", mappedBy="user")
	 */
	private $mobileDevices;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\Event", mappedBy="user")
	 */
	private $events;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Conditions\Entities\Approval", mappedBy="user")
	 */
	private $approvals;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Account\Entities\Change", mappedBy="user")
	 * @var ArrayCollection
	 * @ORM\OrderBy({"createdAt" = "DESC"})
	 */
	private $changes;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\Account\Entities\UserPermission", mappedBy="user")
	 * @var ArrayCollection
	 */
	private $permissions;

	/**
	 * @ORM\OneToMany(targetEntity="tipli\Model\LuckyShop\Entities\UserLuckyShop", mappedBy="user")
	 */
	private Collection $userLuckyShops;

	/** @var mixed */
	private $permissionsModules = null;

	public function __construct($email, $password, $localization)
	{
		$this->email = $email;
		$this->password = $password;
		$this->localization = $localization;

		$this->userData = new UserData($this);
		$this->userSecurity = new UserSecurity($this);
		$this->segmentData = new SegmentData($this);
		$this->mailchimp = new Mailchimp($this);
		$this->mailkit = new Mailkit($this);
		$this->synchronization = new Synchronization($this);
		$this->sendingPolicy = new ArrayCollection();
		$this->privacyPolicy = new ArrayCollection();
		$this->moneyRewards = new ArrayCollection();
		$this->favouriteShops = new ArrayCollection();
		$this->reviews = new ArrayCollection();
		$this->transactions = new ArrayCollection();
		$this->zasilkovnaPackets = new ArrayCollection();
		$this->bannersClicks = new ArrayCollection();
		$this->changes = new ArrayCollection();
		$this->mobileDevices = new ArrayCollection();
		$this->events = new ArrayCollection();
		$this->userDuplicity = new ArrayCollection();
		$this->permissions = new ArrayCollection();
		$this->funnels = new ArrayCollection();
		$this->userSettings = new ArrayCollection();
		$this->userLuckyShops = new ArrayCollection();
		$this->userLuckyShopData = new ArrayCollection();

		$this->createdAt = new DateTime();
	}

	/**
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return mixed
	 */
	public function getUtm()
	{
		return $this->utm;
	}

	/**
	 * @param Utm $utm
	 */
	public function setUtm(Utm $utm)
	{
		$this->utm = $utm;
	}

	/**
	 * @return array
	 */
	public function getRoles(): array
	{
		return [$this->role];
	}

	/**
	 * @param string $role
	 */
	public function setRole($role)
	{
		if (!in_array($role, array_keys(self::getUserRoles()))) {
			throw new \InvalidArgumentException('Invalid role');
		}

		$this->role = $role;
	}

	/**
	 * @return array
	 */
	public static function getUserRoles()
	{
		return [
			self::ROLE_MEMBER => 'User',
			self::ROLE_ADMIN => 'Admin',
		];
	}

	/**
	 * @return string
	 */
	public function getRoleName()
	{
		return $this->getUserRoles()[$this->role];
	}

	/**
	 * @return mixed
	 */
	public function getEmail()
	{
		return $this->email;
	}

	/**
	 * @return bool
	 */
	public function isActiveUser()
	{
		return $this->segment === self::SEGMENT_ACTIVE;
	}

	public function setSegment(string $segment)
	{
		$this->segment = $segment;
	}

	/**
	 * @return bool
	 */
	public function isInactiveUser()
	{
		return !$this->isActive();
	}

	/**
	 * @return mixed
	 */
	public function getFirstName()
	{
		return $this->userData->getFirstName();
	}

	public function getInitials(): ?string
	{
		if ($this->getFullName() === null) {
			return null;
		}

		$initials = '';
		$names = explode(' ', $this->getFullName());
		foreach ($names as $name) {
			$initials .= Strings::upper(Strings::substring($name, 0, 1));
		}

		return $initials;
	}

	/**
	 * @return mixed
	 */
	public function getLastName()
	{
		return $this->userData->getLastName();
	}

	/**
	 * @return string|null
	 */
	public function getFullName()
	{
		return $this->getFirstName() && $this->getLastName() ? $this->getFirstName() . ' ' . $this->getLastName() : null;
	}

	public function getAnonymousFullName()
	{
		return $this->getFullName() ? ($this->getFirstName() . ' ' . Strings::upper(Strings::substring($this->getLastName(), 0, 1)) . '.') : null;
	}

	/**
	 * @return string|null
	 */
	public function getFullNameSurnameFirst()
	{
		return $this->getFirstName() && $this->getLastName() ? $this->getLastName() . ' ' . $this->getFirstName() : null;
	}

	/**
	 * @return string|null
	 */
	public function getUserName()
	{
		return $this->getFullName() ? $this->getFullName() : $this->getEmail();
	}

	/**
	 * @return string|null
	 */
	public function getUserNameSurnameFirst()
	{
		return $this->getFullNameSurnameFirst() ? $this->getFullNameSurnameFirst() : $this->getEmail();
	}

	/**
	 * @return mixed
	 */
	public function getGender()
	{
		return $this->userData->getGender();
	}

	/**
	 * @return mixed
	 */
	public function getBirthdate()
	{
		return $this->userData->getBirthdate();
	}

	/**
	 * @return mixed
	 */
	public function getPhoneNumber()
	{
		return $this->userData->getPhoneNumber();
	}

	/**
	 * @return mixed
	 */
	public function getAccountNumber()
	{
		return $this->userData->getAccountNumber();
	}

	/**
	 * @return mixed
	 */
	public function getIp()
	{
		return $this->userData->getIp();
	}

	/**
	 * @return mixed
	 */
	public function getUserAgent()
	{
		return $this->userData->getUserAgent();
	}

	/**
	 * @return mixed
	 */
	public function getReferer()
	{
		return $this->userData->getReferer();
	}

	/**
	 * @return User|null
	 */
	public function getParentUser()
	{
		return $this->parentUser;
	}

	/**
	 * @param mixed $parentUser
	 */
	public function setParentUser($parentUser)
	{
		$this->parentUser = $parentUser;
	}

	/**
	 * @return mixed
	 */
	public function getHashMethod()
	{
		return $this->hashMethod;
	}

	/**
	 * @return SegmentData
	 */
	public function getSegmentData()
	{
		return $this->segmentData;
	}

	/**
	 * @return mixed
	 */
	public function getReferencedUsers()
	{
		return $this->referencedUsers;
	}

	/**
	 * @return mixed
	 */
	public function getSegment()
	{
		return $this->segment;
	}

	/**
	 * @return PartnerOrganization|mixed
	 */
	public function getPartnerOrganization()
	{
		return $this->partnerOrganization;
	}

	/**
	 * @param mixed $partnerOrganization
	 */
	public function setPartnerOrganization($partnerOrganization)
	{
		$this->partnerOrganization = $partnerOrganization;
	}

	/**
	 * @return mixed
	 */
	public function getEmailVerifiedAt()
	{
		return $this->emailVerifiedAt;
	}

	/**
	 * @return DateTime
	 */
	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	/**
	 * @param DateTime $createdAt
	 */
	public function setCreatedAt(\DateTime $createdAt)
	{
		$this->createdAt = $createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getReviews()
	{
		return $this->reviews;
	}

	/**
	 * @return mixed
	 */
	public function getEmailsUnsubscribedAt()
	{
		return $this->emailsUnsubscribedAt;
	}

	/**
	 * @return mixed
	 */
	public function getPhoneNumberVerifiedAt()
	{
		return $this->phoneNumberVerifiedAt;
	}

	/**
	 * @return mixed
	 */
	public function getRole()
	{
		return $this->role;
	}

	/**
	 * @return mixed
	 */
	public function getArticles()
	{
		return $this->articles;
	}

	/**
	 * @return mixed
	 */
	public function getRedirections()
	{
		return $this->redirections;
	}

	/**
	 * @return mixed
	 */
	public function getPassword()
	{
		return $this->password;
	}

	/**
	 * @param mixed $password
	 */
	public function setPassword($password)
	{
		$this->password = $password;
	}

	/**
	 * @param mixed $hashMethod
	 */
	public function setHashMethod($hashMethod)
	{
		$this->hashMethod = $hashMethod;
	}

	/**
	 * @param Shop[] $favouriteShops
	 */
	public function setFavouriteShops(array $favouriteShops)
	{
		foreach ($this->favouriteShops as $shop) {
			$this->removeFavouriteShop($shop);
		}

		foreach ($favouriteShops as $shop) {
			$this->addFavouriteShop($shop);
		}
	}

	/**
	 * @return array
	 */
	public function getIdsOfFavouriteShops()
	{
		$ids = [];
		foreach ($this->favouriteShops as $shop) {
			$ids[] = $shop->getId();
		}

		return $ids;
	}

	/**
	 * @param Shop $shop
	 * @return bool
	 */
	public function hasFavouriteShop(Shop $shop): bool
	{
		return in_array($shop->getId(), $this->getIdsOfFavouriteShops());
	}

	/**
	 * @param Shop $shop
	 */
	public function addFavouriteShop(Shop $shop)
	{
		if (!$this->favouriteShops->contains($shop)) {
			$this->favouriteShops->add($shop);
		}
	}

	/**
	 * @param Shop $shop
	 */
	public function removeFavouriteShop(Shop $shop)
	{
		if ($this->favouriteShops->contains($shop)) {
			$this->favouriteShops->removeElement($shop);
		}
	}

	/**
	 * @return bool
	 */
	public function isMember()
	{
		return $this->role === self::ROLE_MEMBER;
	}

	/**
	 * @return bool
	 */
	public function isAdmin()
	{
		return $this->role === self::ROLE_ADMIN;
	}

	/**
	 * @return bool
	 */
	public function isSuperadmin(): bool
	{
		return in_array($this->id, [1, 118774, 64017, 796091, 602842, 621425, 757085, 3539358, 3519768, 2666380, 4175772, 4178717, 4343639, 4361423, 4408937, 3747695, 5224947, 5225943], true);
	}

	/**
	 * @return bool
	 */
	public function isFbConnected()
	{
		return $this->getFacebookId() ? true : false;
	}

	public function isAppleConnected()
	{
		return $this->getAppleId() ? true : false;
	}

	public function isGoogleConnected()
	{
		return $this->getGoogleId() ? true : false;
	}

	public function setIp($ip)
	{
		$this->userData->setIp($ip);
	}

	public function setUserAgent($userAgent)
	{
		$this->userData->setUserAgent($userAgent);
	}

	public function setReferer($referer)
	{
		$this->userData->setReferer($referer);
	}


//    public function setSendingPolicyValues($values)
//    {
//        $emailCheckBoxChecked = false;
//        foreach ($this->sendingPolicy as $sendingPolicy) {
//            $key = $sendingPolicy->getMessageType() . ucfirst($sendingPolicy->getContentType());
//            if (isset($values[$key]) && $values[$key]) {
//                $this->sendingPolicy->removeElement($sendingPolicy);
//            }
//        }
//
//        foreach (SendingPolicy::getEmailContentTypes() as $emailContentType => $contentType) {
//            if (isset($values[$emailContentType])) {
//                if ($values[$emailContentType]) {
//                    $emailCheckBoxChecked = true;
//                } else if (!$this->hasUnsubscribedEmails() && !$this->hasUnsubscribedType(SendingPolicy::MESSAGE_TYPE_EMAIL, $contentType)) {
//                    $this->unSubscribeType(SendingPolicy::MESSAGE_TYPE_EMAIL, $contentType);
//                }
//            }
//        }
//        foreach (SendingPolicy::getSMSContentTypes() as $smsContentType => $contentType) {
//            if (isset($values[$smsContentType]) && !$values[$smsContentType] && !$this->hasUnsubscribedType(SendingPolicy::MESSAGE_TYPE_SMS, $contentType)) {
//                $this->unSubscribeType(SendingPolicy::MESSAGE_TYPE_SMS, $contentType);
//            }
//        }
//
//        if ($this->emailsUnsubscribedAt != null && $emailCheckBoxChecked) {
//            $this->subscribeEmails();
//        }
//    }

	public function setPrivacyPolicyValues($values)
	{
		foreach ($this->privacyPolicy as $privacyPolicy) {
			if (isset($values[$privacyPolicy->getPrivacyType()]) && $values[$privacyPolicy->getPrivacyType()]) {
				// odstranit stare
				$this->privacyPolicy->removeElement($privacyPolicy);
			}
		}

		foreach (PrivacyPolicy::getPrivacyTypes() as $privacyType) {
			if (
				isset($values[$privacyType])
				&& !$values[$privacyType]
				&& $this->hasAllowedPrivacyPolicy($privacyType)
			) {
				$this->privacyPolicy->add(new PrivacyPolicy($this, $privacyType));
			}
		}
	}

	public function getMobileDevices()
	{
		return $this->mobileDevices;
	}

	public function getPermissions()
	{
		return $this->permissions;
	}

	public function hasPermission(string $module): bool
	{
		if ($this->permissionsModules === null) {
			$this->permissionsModules = [];

			/** @var UserPermission $userPermission */
			foreach ($this->getPermissions() as $userPermission) {
				$this->permissionsModules[] = $userPermission->getModule();
			}
		}

		return in_array($module, $this->permissionsModules, true);
	}

	public function setBalance(?float $balance): void
	{
		$this->userData->setBalance($balance);
	}

	public function getBalance(): ?float
	{
		return $this->userData->getBalance();
	}

	public function getMailchimp(): Mailchimp
	{
		return $this->mailchimp;
	}

	public function scheduleMailchimpSynchronization(bool $prioritized)
	{
		$this->mailchimp->setSynchronizeAt($prioritized ? (new \DateTime())->modify('- 5 years') : new \DateTime());
	}

	public function getMailkit(): Mailkit
	{
		return $this->mailkit;
	}

	public function scheduleMailkitSynchronization(bool $prioritized)
	{
		$this->mailkit->setSynchronizeAt($prioritized ? (new \DateTime())->modify('- 5 years') : new \DateTime());
	}

	public function getLastShopRedirectionAt(): ?\DateTime
	{
		return $this->getSegmentData()->getLastShopRedirectionAt();
	}

	private function hasUnsubscribedType($messageType, $contentType): bool
	{
		foreach ($this->sendingPolicy as $sendingPolicy) {
			if ($sendingPolicy->getMessageType() === $messageType && $sendingPolicy->getContentType() === $contentType) {
				return true;
			}
		}

		return false;
	}

	public function getSendingPolicyValues()
	{
		$disallowed = [];
		if (!empty($this->sendingPolicy)) {
			foreach ($this->sendingPolicy as $sendingPolicy) {
				$disallowed[] = $sendingPolicy->getMessageType() . ucfirst($sendingPolicy->getContentType());
			}
		}

		$values = [];
		foreach (SendingPolicy::getEmailContentTypes() as $emailContentType => $contentType) {
			$values[$emailContentType] = !in_array($emailContentType, $disallowed);
		}
		foreach (SendingPolicy::getSMSContentTypes() as $smsContentType => $contentType) {
			$values[$smsContentType] = !in_array($smsContentType, $disallowed);
		}

		return $values;
	}

	public function getPrivacyPolicyValues()
	{
		$disallowed = [];
		if (!empty($this->privacyPolicy)) {
			/** @var PrivacyPolicy $privacyPolicy */
			foreach ($this->privacyPolicy as $privacyPolicy) {
				$disallowed[] = $privacyPolicy->getPrivacyType();
			}
		}

		$values = [];
		foreach (PrivacyPolicy::getPrivacyTypes() as $privacyType) {
			$values[$privacyType] = !in_array($privacyType, $disallowed);
		}

		return $values;
	}

	public function subscribeEmails()
	{
		$this->setEmailsUnsubscribedAt(null);
	}

	public function hasUnsubscribedEmails()
	{
		return $this->getEmailsUnsubscribedAt() !== null;
	}

	/**
	 * Global unsubscribed from e-mails
	 * Has more priority than sendingPolicy
	 * @param null $reason
	 */
	public function unsubscribeEmails($reason = null)
	{
		if ($this->hasUnsubscribedEmails()) {
			return;
		}

		$this->userData->setEmailUnsubscribeReason($reason);
		$this->setEmailsUnsubscribedAt(new \DateTime());
	}

	/**
	 * @param string $messageType
	 * @param string $contentType
	 */
	public function unSubscribeType($messageType, $contentType)
	{
		$sendingPolicy = new SendingPolicy($this);
		$sendingPolicy->setMessageType($messageType);
		$sendingPolicy->setContentType($contentType);
		$this->sendingPolicy->add($sendingPolicy);
	}

	public function __toString()
	{
		return 'user-' . $this->id;
	}

	/**
	 * @return string
	 */
	public function getLocale()
	{
		return $this->getLocalization()->getLocale();
	}

	/**
	 * @return string
	 */
	public function getCurrency()
	{
		return $this->getLocalization()->getCurrency();
	}

	/**
	 * @return bool
	 */
	public function hasFilledProfile()
	{
		return $this->userData->hasFilledProfile();
	}

	/**
	 * @return bool
	 */
	public function hasFilledPersonalData()
	{
		return $this->userData->hasFilledPersonalData();
	}

	/**
	 * @return bool
	 */
	public function hasFilledBankDetails()
	{
		return $this->userData->hasFilledBankDetails();
	}

	/**
	 * @return bool
	 */
	public function hasFilledPassword()
	{
		return $this->getPassword() !== null;
	}

	public function hasAllowedSendingEmails($messageType, $contentType): bool
	{
		if (!$this->isActive()) {
			return false;
		}

		if ($this->hasUnsubscribedEmails()) {
			return false;
		}

		foreach ($this->sendingPolicy as $sendingPolicy) {
			if ($sendingPolicy->getMessageType() === $messageType && $sendingPolicy->getContentType() === $contentType) {
				return false;
			}
		}

		return true;
	}

	public function hasAllowedPushNotifications(string $messageType, string $contentType): bool
	{
		foreach ($this->sendingPolicy as $sendingPolicy) {
			if ($sendingPolicy->getMessageType() === $messageType && $sendingPolicy->getContentType() === $contentType) {
				return false;
			}
		}

		return true;
	}

	public function hasAllowedPrivacyPolicy($privacyType): bool
	{
		/** @var PrivacyPolicy $privacyPolicy */
		foreach ($this->privacyPolicy as $privacyPolicy) {
			if ($privacyPolicy->getPrivacyType() === $privacyType) {
				return false;
			}
		}

		return true;
	}

	public function hasAllowedSendingSms(): bool
	{
		if (!$this->isActive()) {
			return false;
		}

		foreach ($this->sendingPolicy as $sendingPolicy) {
			if ($sendingPolicy->getMessageType() === SendingPolicy::MESSAGE_TYPE_SMS) {
				return false;
			}
		}

		return true;
	}

	/**
	 * @return bool
	 */
	public function isActive()
	{
		return $this->active && !$this->isRemoved();
	}

	/**
	 * @param Shop $shop
	 * @return bool
	 */
	public function isFavouriteShop(Shop $shop)
	{
		return $this->favouriteShops->contains($shop);
	}

	/**
	 * @return bool
	 */
	public function hasInstalledAddon()
	{
		return $this->segmentData->getAddonInstalled();
	}

	/**
	 * @return bool
	 */
	public function hasInstalledMobileApplication()
	{
		return !$this->mobileDevices->isEmpty();
	}

	/**
	 * @return bool
	 */
	public function hasEvents()
	{
		return !$this->events->isEmpty();
	}

	/**
	 * @return bool
	 */
	public function hasEvent($action)
	{
		return $this->events->matching(Criteria::create()->where(Criteria::expr()->eq('action', $action)))->isEmpty() ? false : true;
	}

	/**
	 * @param string $action
	 * @return ArrayCollection
	 */
	public function getEvents($action = null)
	{
		return $action ? $this->events->matching(Criteria::create()->where(Criteria::expr()->eq('action', $action))) : $this->events;
	}

	/**
	 * @return DateTime
	 */
	public function getLastLoggedInAt()
	{
		return $this->segmentData->getLastLoggedInAt();
	}

	/**
	 * @return DateTime
	 */
	public function getOpenedAt()
	{
		return $this->segmentData->getOpenedAt();
	}

	public function setOpenedAt(\DateTime $dateTime)
	{
		$this->getSegmentData()->setOpenedAt($dateTime);
	}

	/**
	 * @param DateTime $emailsUnsubscribedAt
	 */
	public function setEmailsUnsubscribedAt($emailsUnsubscribedAt)
	{
		$this->emailsUnsubscribedAt = $emailsUnsubscribedAt;
	}

	/**
	 * @param DateTime $phoneNumberVerifiedAt
	 */
	public function setPhoneNumberVerifiedAt($phoneNumberVerifiedAt)
	{
		$this->phoneNumberVerifiedAt = $phoneNumberVerifiedAt;
	}

	/**
	 * @param DateTime $emailVerifiedAt
	 */
	public function setEmailVerifiedAt($emailVerifiedAt)
	{
		$this->emailVerifiedAt = $emailVerifiedAt;
	}

	/**
	 * @param mixed $active
	 */
	public function setActive($active)
	{
		$this->active = $active;
	}

	/**
	 * @param mixed $email
	 */
	public function setEmail($email)
	{
		$this->email = $email;
	}

	/**
	 * @param string $note
	 */
	public function setNote($note)
	{
		$this->userData->setNote($note);
	}

	/**
	 * @return DateTime
	 */
	public function getClickedAt()
	{
		return $this->segmentData->getClickedAt();
	}

	public function setClickedAt(\DateTime $dateTime)
	{
		$this->getSegmentData()->setClickedAt($dateTime);
	}

	public function setLastLoggedInAt(\DateTime $dateTime)
	{
		$this->getSegmentData()->setLastLoggedInAt($dateTime);
	}

	/**
	 * @return DateTime
	 */
	public function getAddonFeedDownloadedAt()
	{
		return $this->segmentData->getAddonFeedDownloadedAt();
	}

	public function setAddonFeedDownloadedAt(\DateTime $dateTime)
	{
		$this->getSegmentData()->setAddonFeedDownloadedAt($dateTime);
	}

	public function isManagerOfPartnerOrganization()
	{
		return $this->getPartnerOrganization() && $this->getPartnerOrganization()->getManager() === $this;
	}

	public function getNote()
	{
		return $this->userData->getNote();
	}

	public function hasTipliReview(): bool
	{
		foreach ($this->getReviews() as $review) {
			if (!$review->getShop()) {
				return true;
			}
		}

		return false;
	}

	public function hasReview()
	{
		return !$this->getReviews()->isEmpty();
	}

	public function getShopReview(Shop $shop)
	{
		/** @var Review $review */
		foreach ($this->getReviews() as $review) {
			if ($review->getShop() === $shop) {
				return $review;
			}
		}
	}

	public function setPhoneNumber($countryCode, $phoneNumber)
	{
		$this->userData->setPhoneNumber($countryCode, $phoneNumber);
	}

	public function getPhoneNumberWithoutCountryCode()
	{
		return $this->userData->getPhoneNumberWithoutCountryCode();
	}

	public function getPhoneNumberCountryCode()
	{
		return $this->userData->getPhoneNumberCountryCode();
	}

	public function clearPhoneNumber()
	{
		$this->userData->clearPhoneNumber();
	}

	public function getFavouriteShops()
	{
		$criteria = Criteria::create()
			->andWhere(Criteria::expr()->eq('active', true))
			->andWhere(Criteria::expr()->eq('visible', true));

		return $this->favouriteShops->matching($criteria);
	}

	/**
	 * @param string $firstName
	 */
	public function setFirstName($firstName)
	{
		$this->userData->setFirstName($firstName);
		$this->userData->setVocalFirstName(null);
	}

	/**
	 * @param string $gender
	 */
	public function setGender($gender)
	{
		$this->userData->setGender($gender);
	}

	/**
	 * @param DateTime|null $birthDate
	 */
	public function setBirthDate($birthDate)
	{
		$this->userData->setBirthDate($birthDate);
	}

	/**
	 * @param string $accountNumber
	 */
	public function setAccountNumber($accountNumber)
	{
		$this->userData->setAccountNumber($accountNumber);
	}

	/**
	 * @param string $lastName
	 */
	public function setLastName($lastName)
	{
		$this->userData->setLastName($lastName);
	}

	public function getVocalFirstName()
	{
		return $this->userData->getVocalFirstName();
	}

	public function verifyPhoneNumber()
	{
		$this->setPhoneNumberVerifiedAt(new \DateTime());
	}

	public function verifyEmail()
	{
		$this->setEmailVerifiedAt(new \DateTime());
	}

	public function hasVerifiedPhoneNumber()
	{
		return $this->getPhoneNumberVerifiedAt() !== null;
	}

	public function hasVerifiedEmail()
	{
		return $this->getEmailVerifiedAt() !== null;
	}

	/**
	 * @return Localization
	 */
	public function getLocalization()
	{
		return $this->localization;
	}

	/**
	 * @param mixed $localization
	 */
	public function setLocalization($localization)
	{
		$this->localization = $localization;
	}

	public function generateNewPasswordRequestHash()
	{
		$this->userSecurity->generateNewPasswordRequestHash();
	}

	public function generateAccessToken()
	{
		$this->userSecurity->generateAccessToken();
	}

	public function getAccessToken()
	{
		return $this->userSecurity->getAccessToken();
	}

	public function isAccessTokenValid()
	{
		return $this->userSecurity->isAccessTokenValid();
	}

	public function getNewPasswordRequestHash()
	{
		return $this->userSecurity->getNewPasswordRequestHash();
	}

	public function setFacebookId($facebookId)
	{
		$this->userSecurity->setFacebookId($facebookId);
	}

	public function getFacebookId()
	{
		return $this->userSecurity->getFacebookId();
	}

	public function getAppleId()
	{
		return $this->userSecurity->getAppleId();
	}

	public function setAppleId($appleId)
	{
		$this->userSecurity->setAppleId($appleId);
	}

	public function setGoogleId($googleId)
	{
		$this->userSecurity->setGoogleId($googleId);
	}

	private function getGoogleId()
	{
		$this->userSecurity->getGoogleId();
	}

	public function getLastTransactionAt()
	{
		return $this->segmentData->getLastTransactionAt();
	}

	public function getFirstTransactionAt()
	{
		return $this->segmentData->getFirstTransactionAt();
	}

	public function getCountOfRecommendedUsers()
	{
		return $this->segmentData->getCountOfRecommendedUsers();
	}

	public function getLastRecommendedUserAt()
	{
		return $this->segmentData->getLastRecommendedUserAt();
	}

	public function cancelPhoneNumberVerification()
	{
		$this->phoneNumberVerifiedAt = null;
	}

	public function isCzech()
	{
		return $this->getLocalization()->isCzech();
	}

	public function isSlovak()
	{
		return $this->getLocalization()->isSlovak();
	}

	public function isPolish()
	{
		return $this->getLocalization()->isPolish();
	}

	public function isBulgarian(): bool
	{
		return $this->getLocalization()->isBulgarian();
	}

	public function isHungarian(): bool
	{
		return $this->getLocalization()->isHungarian();
	}

	public function isRomanian(): bool
	{
		return $this->getLocalization()->isRomanian();
	}

	public function isSlovenian(): bool
	{
		return $this->getLocalization()->isSlovenian();
	}

	public function isCroatian(): bool
	{
		return $this->getLocalization()->isCroatian();
	}

	public function getActivationUtm()
	{
		foreach ($this->transactions as $transaction) {
			if ($transaction->getType() === Transaction::TYPE_COMMISSION) {
				return $transaction->getUtm();
			}
		}

		return null;
	}

	public function setRegistrationPage($registrationPage)
	{
		$this->userData->setRegistrationPage($registrationPage);
	}

	public function getMissingDetails()
	{
		return $this->userData->getMissingDetails();
	}

	/**
	 * @return Synchronization
	 */
	public function getSynchronization()
	{
		return $this->synchronization;
	}

	public function getFirstVisitPage()
	{
		return $this->userData->getFirstVisitPage();
	}

	public function setFirstVisitPage($firstVisitPage)
	{
		$this->userData->setFirstVisitPage($firstVisitPage);
	}

	public function getLocaleRole()
	{
		return $this->userData->getLocaleRole();
	}

	public function setLocaleRole(string $localeRole)
	{
		$this->userData->setLocaleRole($localeRole);
	}

	public function isWhiteLabelUser()
	{
		return $this->getPartnerOrganization() && $this->getPartnerOrganization()->isWhiteLabel();
	}

	public function isUsingCashback()
	{
		return $this->segmentData->isUsingCashback();
	}

	public function isUsingLeaflets()
	{
		return $this->segmentData->isUsingLeaflets();
	}

	public function isUsingDeals()
	{
		return $this->segmentData->isUsingDeals();
	}

	public function getCashbackUsedAt()
	{
		return $this->segmentData->getCashbackUsedAt();
	}

	public function getDealsUsedAt()
	{
		return $this->segmentData->getDealsUsedAt();
	}

	public function getLeafletsUsedAt()
	{
		return $this->segmentData->getLeafletsUsedAt();
	}

	public function setVocalFirstName($vocalFirstName = null)
	{
		$this->userData->setVocalFirstName($vocalFirstName);
	}

	public function isRemoved()
	{
		return $this->userData->isRemoved();
	}

	public function remove(User $removedBy)
	{
		$this->userData->remove();
		$this->userData->setRemovedBy($removedBy);
	}

	/**
	 * @return bool
	 */
	public function isBanned()
	{
		return $this->userData->isBanned();
	}

	/**
	 * @param User|null $bannedBy
	 */
	public function ban(?User $bannedBy = null)
	{
		$this->userData->ban($bannedBy);
	}

	public function unban()
	{
		$this->userData->unban();
	}

	/**
	 * @return null|User
	 */
	public function getBannedBy()
	{
		return $this->userData->getBannedBy();
	}

	public function hasZasilkovnaPacket()
	{
		return $this->zasilkovnaPackets !== null && !$this->zasilkovnaPackets->isEmpty();
	}

	public function getLastInteractionAt()
	{
		return $this->segmentData->getLastInteractionAt();
	}

	/**
	 * @return mixed
	 */
	public function getChanges()
	{
		return $this->changes;
	}

	public function getSendingPolicy()
	{
		return $this->sendingPolicy;
	}

	public function getAccountNumberHash()
	{
		return sha1($this->getAccountNumber() . ' - ' . self::SALT);
	}

	public function isAccountNumberHashValid($hash)
	{
		return $this->getAccountNumberHash() === $hash;
	}

	/**
	 * @return mixed
	 */
	public function getAccountNumberVerifiedAt()
	{
		return $this->accountNumberVerifiedAt;
	}

	public function requestAccountNumberVerification()
	{
		$this->accountNumberVerifiedAt = null;
	}

	public function verifyAccountNumber()
	{
		$this->accountNumberVerifiedAt = new DateTime();
	}

	public function hasVerifiedAccountNumber()
	{
		return $this->accountNumberVerifiedAt !== null;
	}

	public function isSuspected()
	{
		return $this->getSegmentData()->isSuspected();
	}

	public function getSuspectedReason(): ?string
	{
		return $this->getSegmentData()->getSuspectedReason();
	}

	public function isVip()
	{
		return $this->getSegmentData()->isVip();
	}

	public function isAffiliate()
	{
		return $this->getSegmentData()->isAffiliate();
	}

	/**
	 * @return string
	 */
	public function getPlatform()
	{
		return $this->userData->getPlatform();
	}

	/**
	 * @param string $platform
	 */
	public function setPlatform($platform)
	{
		$this->userData->setPlatform($platform);
	}

	/**
	 * @return string
	 */
	public function getCountry()
	{
		return $this->userData->getCountry();
	}

	/**
	 * @param string $country
	 */
	public function setCountry($country)
	{
		$this->userData->setCountry($country);
	}

	/**
	 * @return bool
	 */
	public function isAliexpressAddonEnabled(): bool
	{
		return $this->segmentData->isAliexpressAddonEnabled();
	}

	/**
	 * @return mixed
	 */
	public function getUserDuplicity()
	{
		return $this->userDuplicity;
	}

	public function getCountOfCanceledCommissionTransactions()
	{
		return $this->segmentData->getCountOfCanceledCommissionTransactions();
	}

	public function getCountOfCommissionTransactions()
	{
		return $this->segmentData->getCountOfCommissionTransactions();
	}

	public function getCanceledCommissionTransactionsRatio()
	{
		if ($countOfCanceledTransactions = $this->getCountOfCanceledCommissionTransactions()) {
			return $countOfCanceledTransactions / $this->getCountOfCommissionTransactions();
		}

		return 0;
	}

	public function setRegistrationPlatform($platform): void
	{
		$this->segmentData->setRegistrationPlatform($platform);
	}

	public function getShareCoefficient(): float
	{
		return $this->segmentData->getShareCoefficient();
	}

	/**
	 * @return bool
	 */
	public function hasSpecialShareCoefficient(): bool
	{
		return $this->segmentData->hasSpecialShareCoefficient();
	}

	public function getCountOfRedirections()
	{
		return $this->segmentData->getCountOfRedirections();
	}

	public function getUserSettings(): UserSettings
	{
		if ($this->userSettings->isEmpty()) {
			$this->userSettings->add(new UserSettings($this));
		}

		return $this->userSettings->first();
	}

	public function hasDoubleAddonReward(): bool
	{
		$userCreatedAt = clone $this->getCreatedAt();
		return $userCreatedAt->modify('+70 minutes') > new \DateTime();
	}

	public function isOlderThanThreeDays(): bool
	{
		$userCreatedAt = clone $this->getCreatedAt();
		return $userCreatedAt->modify('+3 days') < new \DateTime();
	}

	public function isOlderThanTwoWeeks(): bool
	{
		$userCreatedAt = clone $this->getCreatedAt();
		return $userCreatedAt->modify('+ 14 days') < new \DateTime();
	}

	public function isRegisteredToday(): bool
	{
		$userCreatedAt = clone $this->getCreatedAt();
		return $userCreatedAt->format('Y-m-d') === (new \DateTime())->format('Y-m-d');
	}

	public function getAgentResponderEmail(): string
	{
		$localization = $this->getLocalization();

		if ($localization->isRomanian()) {
			return '<EMAIL>';
		} elseif ($localization->isHungarian()) {
			return '<EMAIL>';
		} else {
			return '<EMAIL>';
		}
	}

	public function hasDisabledNewslettersDueToInactivity(): bool
	{
		return $this->segmentData->hasDisabledNewslettersDueToInactivity();
	}

	public function getActiveFunnels()
	{
		$funnels = [];

		/** @var Funnel $funnel */
		foreach ($this->funnels as $funnel) {
			if ($funnel->isActive() === true) {
				$funnels[] = $funnel;
			}
		}

		return $funnels;
	}

	public function getValidMoneyRewardCampaigns(): ?array
	{
		$moneyRewardCampaigns = [];

		/** @var MoneyReward $moneyReward */
		foreach ($this->moneyRewards as $moneyReward) {
			if ($moneyReward->isValid() && $moneyReward->getMoneyRewardCampaign() !== null) {
				$moneyRewardCampaigns[] = $moneyReward->getMoneyRewardCampaign();
			}
		}

		return $moneyRewardCampaigns;
	}

	public function setSupportLocalizations(array $localizations): void
	{
		$userSettings = $this->getUserSettings();

		$localizationIds = array_map(static function (Localization $localization) {
			return $localization->getId();
		}, $localizations);


		$userSettings->setSupportLocalizationIds(implode(',', array_unique($localizationIds)));
	}

	public function getSupportLocalizationIds(): array
	{
		return $this->getUserSettings()->getSupportLocalizationIds();
	}

	public function hasUnsubscribedNewsletters(): bool
	{
		return $this->segmentData->hasUnsubscribedNewsletters();
	}

	public function reportFirstPurchase(): void
	{
		$this->userData->reportFirstPurchase();
	}

	public function hasFirstPurchaseReported(): bool
	{
		return $this->userData->hasFirstPurchaseReported();
	}

	public function hasAddonPromo(): ?bool
	{
		return $this->userData->hasAddonPromo();
	}

	public function setHasAddonPromo(): void
	{
		$this->userData->setHasAddonPromo();
	}

	public function setLuckyShopCheckStreak(int $streak): void
	{
		$this->getUserLuckyShopData()->setCheckStreak($streak);
	}

	public function hasUserLuckyShopData(): bool
	{
		return $this->userLuckyShopData->isEmpty() === false;
	}

	public function getLuckyShopCheckStreak(): ?int
	{
		return $this->getUserLuckyShopData()->getCheckStreak();
	}

	public function getLuckyShopLastCheckAt(): ?DateTime
	{
		return $this->getUserLuckyShopData()->getLastLuckyShopCheckAt();
	}

	public function isHost(): bool
	{
		return in_array($this->id, self::HOST_IDS);
	}

	public function createUserLuckyShopData(): void
	{
		$this->userLuckyShopData->add(new UserLuckyShopData($this));
	}

	public function getUserLuckyShopData(): UserLuckyShopData
	{
		if ($this->userLuckyShopData->isEmpty()) {
			$this->userLuckyShopData->add(new UserLuckyShopData($this));
		}

		return $this->userLuckyShopData->first();
	}
}
