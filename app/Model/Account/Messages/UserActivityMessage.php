<?php

namespace tipli\Model\Account\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;
use DateTime;

final class UserActivityMessage implements IMessage
{
	public function __construct(
		public int $userId,
		public string $eventName,
		public ?string $campaign,
		public string $source,
		public DateTime $dateTime,
		public ?string $data,
		public ?int $notificationCampaignId,
		public ?int $bannerId
	) {
	}

	public function __toString(): string
	{
		return Json::encode([
			'userId' => $this->userId,
			'eventName' => $this->eventName,
			'campaign' => $this->campaign,
			'source' => $this->source,
			'dateTime' => $this->dateTime->format('Y-m-d H:i:s'),
			'data' => $this->data,
			'notificationCampaignId' => $this->notificationCampaignId,
			'bannerId' => $this->bannerId,
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);

		return new self(
			$data->userId,
			$data->eventName,
			$data->campaign,
			$data->source,
			new DateTime($data->dateTime),
			$data->data,
			$data->notificationCampaignId,
			$data->bannerId
		);
	}
}
