<?php

namespace tipli\Model\Account\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;

class ChangeRepository extends BaseRepository
{
	public function getChange()
	{
		return $this->createQueryBuilder('c');
	}

	public function findChangeToVerify(User $user, $token)
	{
		$qb = $this->createQueryBuilder('c')
			->select('c')
			->andWhere('c.token = :token')
			->andWhere('c.user = :user')
			->andWhere('c.verificationRequired = 1')
			->andWhere('c.verifiedAt IS NULL')
			->andWhere('c.verificationValidTill >= :now')
			->setParameter('token', $token)
			->setParameter('user', $user)
			->setParameter('now', new \DateTime())
			->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findAccountNumberChangesWaitingForVerify(User $user)
	{
		$qb = $this->createQueryBuilder('c')
			->select('c')
			->andWhere('c.user = :user')
			->andWhere('c.accountNumber IS NOT NULL')
			->andWhere('c.verificationRequired = 1')
			->andWhere('c.verifiedAt IS NULL')
			->andWhere('c.verificationValidTill >= :now')
			->setParameter('user', $user)
			->setParameter('now', new \DateTime());

		return $qb->getQuery()->getResult();
	}

	/**
	 * @param User $user
	 * @param int $lastMonths
	 * @return int
	 */
	public function findCountOfRecentAccountNumberChanges(User $user, int $lastMonths = 6)
	{
		$changes = $this->createQueryBuilder('c')
			->select('c.accountNumber')
			->andWhere('c.user = :user')
			->andWhere('c.createdAt >= :time')
			->setParameter('user', $user)
			->setParameter('time', (new \DateTime())->modify('-' . $lastMonths . ' months'))
			->getQuery()
			->getResult();

		$lastAccountNumber = false;
		$countOfAccountNumberChanges = 0;

		foreach ($changes as $change) {
			if ($lastAccountNumber === false || $change['accountNumber'] !== $lastAccountNumber) {
				$countOfAccountNumberChanges++;
			}

			$lastAccountNumber = $change['accountNumber'];
		}

		return $countOfAccountNumberChanges;
	}

	public function findChangesByUser(User $user)
	{
		$qb = $this->createQueryBuilder('c')
			->andWhere('c.user = :user')
			->setParameter('user', $user);

		return $qb->getQuery()->getResult();
	}

	/**
	 * @param string $phoneNumber
	 * @param User|null $exceptUser
	 * @return int|mixed|string
	 */
	public function findChangesByPhoneNumber(string $phoneNumber, ?User $exceptUser = null)
	{
		$qb = $this->createQueryBuilder('c')
			->andWhere('c.phoneNumber = :phoneNumber')
			->setParameter('phoneNumber', $phoneNumber)
			->orderBy('c.id', 'DESC')
			->groupBy('c.user');

		if ($exceptUser) {
			$qb->andWhere('c.user != :user')
				->setParameter('user', $exceptUser);
		}

		return $qb->getQuery()->getResult();
	}

	/**
	 * @param string $accountNumber
	 * @param User|null $exceptUser
	 * @return int|mixed|string
	 */
	public function findChangesByAccountNumber(string $accountNumber, ?User $exceptUser = null)
	{
		$qb = $this->createQueryBuilder('c')
			->andWhere('c.accountNumber = :accountNumber')
			->setParameter('accountNumber', $accountNumber)
			->orderBy('c.id', 'DESC')
			->groupBy('c.user');

		if ($exceptUser) {
			$qb->andWhere('c.user != :user')
				->setParameter('user', $exceptUser);
		}

		return $qb->getQuery()->getResult();
	}
}
