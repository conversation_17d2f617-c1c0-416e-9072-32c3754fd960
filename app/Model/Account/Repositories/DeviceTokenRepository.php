<?php

namespace tipli\Model\Account\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;

class DeviceTokenRepository extends BaseRepository
{
	public function findValidDeviceTokens($token)
	{
		$qb = $this->createQueryBuilder('ut')
			->andWhere('ut.validTill >= :now')
			->setParameter('now', new \DateTime())
			->andWhere('ut.token = :token')
			->setParameter('token', $token);

		return $qb->getQuery()->getResult();
	}

	public function findValidUserDeviceTokens(User $user)
	{
		$qb = $this->createQueryBuilder('ut')
			->andWhere('ut.validTill >= :now')
			->setParameter('now', new \DateTime())
			->andWhere('ut.user = :user')
			->setParameter('user', $user);

		return $qb->getQuery()->getResult();
	}

	public function findUserDeviceToken(User $user, $token)
	{
		$qb = $this->createQueryBuilder('ut')
			->andWhere('ut.user = :user')
			->setParameter('user', $user)
			->andWhere('ut.token = :token')
			->setParameter('token', $token)
			->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findDeviceToken(string $token, bool $valid = true)
	{
		$qb = $this->createQueryBuilder('ut')
			->andWhere('ut.token = :token')
			->setParameter('token', $token)
			->setMaxResults(1);

		if ($valid) {
			$qb->andWhere('ut.validTill >= :now')
				->setParameter('now', new \DateTime());
		}

		return $qb->getQuery()->getOneOrNullResult();
	}
}
