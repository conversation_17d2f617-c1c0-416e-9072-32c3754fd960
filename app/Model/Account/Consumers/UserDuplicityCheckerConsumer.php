<?php

namespace tipli\Model\Account\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Account\Messages\UserDuplicityCheckerMessage;
use tipli\Model\Account\UserDuplicityCheckerFacade;
use tipli\Model\Account\UserFacade;
use tipli\Model\RabbitMq\BaseConsumer;

class UserDuplicityCheckerConsumer extends BaseConsumer implements IConsumer
{
	/** @var UserDuplicityCheckerFacade */
	private $userDuplicityCheckerFacade;

	/** @var UserFacade */
	private $userFacade;

	public function __construct(UserDuplicityCheckerFacade $userDuplicityCheckerFacade, UserFacade $userFacade)
	{
		$this->userDuplicityCheckerFacade = $userDuplicityCheckerFacade;
		$this->userFacade = $userFacade;
	}

	public function consume(Message $data): int
	{
		$message = UserDuplicityCheckerMessage::fromJson($data->content);

		$this->userDuplicityCheckerFacade->processUserDuplicityCheck(
			$this->userFacade->find($message->getUserId()),
			$message->getType(),
			$message->getValue()
		);

		return IConsumer::MESSAGE_ACK;
	}
}
