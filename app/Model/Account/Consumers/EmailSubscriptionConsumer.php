<?php

namespace tipli\Model\Account\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Account\EmailSubscriptionLogManager;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Messages\EmailSubscriptionMessage;
use tipli\Model\Account\UserFacade;
use tipli\Model\RabbitMq\BaseConsumer;

class EmailSubscriptionConsumer extends BaseConsumer implements IConsumer
{
	/** @var UserFacade */
	private $userFacade;

	/** @var EmailSubscriptionLogManager */
	private $emailSubscriptionLogManager;

	public function __construct(UserFacade $userFacade, EmailSubscriptionLogManager $emailSubscriptionLogManager)
	{
		$this->userFacade = $userFacade;
		$this->emailSubscriptionLogManager = $emailSubscriptionLogManager;
	}

	public function consume(Message $data): int
	{
		$message = EmailSubscriptionMessage::fromJson($data->content);

		if ($message->getAction() === EmailSubscriptionMessage::ACTION_LOG) {
			/** @var User $user */
			$user = $this->userFacade->find($message->getUserId());
			$action = $message->getActionType();
			$site = $message->getSite();
			$source = $message->getSource();
			$reason = $message->getReason();
			$createdAt = (new \DateTime())->setTimestamp($message->getTime());

			$this->emailSubscriptionLogManager->createEmailSubscriptionLog($user, $action, $site, $source, $reason, $createdAt);
		}

		return IConsumer::MESSAGE_ACK;
	}
}
