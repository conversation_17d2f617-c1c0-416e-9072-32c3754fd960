<?php

namespace tipli\Model\Account\Samples;

use Doctrine\ORM\EntityManagerInterface;
use tipli\Model\Account\Entities\UserPermission;
use tipli\Model\Account\UserFacade;
use tipli\Model\Layers\ISamplePart;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;

class UserPermissionSample implements ISamplePart
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var UserFacade */
	private $userFacade;

	public function __construct(LocalizationFacade $localizationFacade, UserFacade $userFacade)
	{
		$this->localizationFacade = $localizationFacade;
		$this->userFacade = $userFacade;
	}

	public function fillDatabase(EntityManagerInterface $em): void
	{
		$localizationCzech = $this->localizationFacade->findOneByLocale(Localization::LOCALE_CZECH);
		$superadmin = $this->userFacade->findOneByEmail('<EMAIL>', $localizationCzech);
		$items = [
			'<EMAIL>' => UserPermission::getModules(),
			'<EMAIL>' => UserPermission::getContentModules(),
			'<EMAIL>' => UserPermission::getSupportModules(),
			'<EMAIL>' => [UserPermission::MODULE_SHOPS],
		];

		foreach ($items as $email => $modules) {
			$user = $this->userFacade->findOneByEmail($email, $localizationCzech);

			if ($user) {
				foreach ($modules as $module) {
					$userPermission = new UserPermission($user, $superadmin, $module);

					$em->persist($userPermission);
				}
			}
		}

		$em->flush();
	}
}
