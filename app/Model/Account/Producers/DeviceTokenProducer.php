<?php

namespace tipli\Model\Account\Producers;

use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Messages\DeviceTokenMessage;
use tipli\Model\RabbitMq\BaseProducer;

final class DeviceTokenProducer extends BaseProducer
{
	public function scheduleCreateDeviceToken(User $user, string $token, string $platform, ?string $version)
	{
		$this->producer->publish(
			new DeviceTokenMessage($user->getId(), $token, $platform, $version)
		);
	}
}
