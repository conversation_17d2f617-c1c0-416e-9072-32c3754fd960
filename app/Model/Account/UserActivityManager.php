<?php

namespace tipli\Model\Account;

use Nette\SmartObject;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserActivity;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Inbox\Entities\NotificationCampaign;
use tipli\Model\Marketing\Entities\Banner;

class UserActivityManager
{
	use SmartObject;

	public function __construct(private EntityManager $em)
	{
	}

	public function createUserActivity(
		User $user,
		string $eventName,
		?string $campaign,
		string $source,
		\DateTime $dateTime,
		?string $data,
		?NotificationCampaign $notificationCampaign = null,
		?Banner $banner = null
	): UserActivity {
		return $this->saveUserActivity(new UserActivity($user, $eventName, $campaign, $source, $dateTime, $data, $notificationCampaign, $banner));
	}

	public function saveUserActivity(UserActivity $userActivity)
	{
		$this->em->persist($userActivity);
		$this->em->flush($userActivity);

		return $userActivity;
	}
}
