<?php

namespace tipli\Model\Algolia;

use tipli\Model\Articles\Entities\Article;
use tipli\Model\Configuration;

class ArticleObject implements IObject
{
	/** @var Article */
	private $article;

	/** @var Configuration */
	private $configuration;

	/** @var int */
	private $objectId;

	/** @var  string */
	private $logoPath;

	/** @var string */
	private $destinationUrl;

	/**
	 * @param Article $article
	 * @param Configuration $configuration
	 */
	public function __construct(Article $article, Configuration $configuration)
	{
		$this->article = $article;
		$this->objectId = $article->getId();
		$this->configuration = $configuration;
	}

	public function getObjectId()
	{
		return $this->objectId;
	}

	/**
	 * @return bool
	 */
	public function isUpdated()
	{
		return !$this->isDeleted();
	}

	/**
	 * @return bool
	 */
	public function isDeleted()
	{
		return !$this->article->isPublished();
	}

	/**
	 * @param bool $simplified
	 * @return array
	 */
	public function toArray($simplified = false)
	{
		$article = $this->article;

		return [
			'objectID' => $this->objectId,
			'name' => $article->getName(),
			'preview_image' => $this->logoPath,
			'slug' => $article->getSlug(),
			'destination_url' => $this->destinationUrl,
			'description' => mb_substr($article->getDescription(), 0, 400),
			'tags' => $this->getTags($article),
			'shops' => $this->getShops($article),
//			'meta_title' => $article->getMetaTitle(),
//			'meta_keywords' => $article->getMetaKeywords(),
//			'meta_description' => $article->getMetaDescription(),
			'published_at' => $article->getPublishedAt()->format('Y-m-d H:i:s'),
		];
	}

	/**
	 * @param Article $article
	 * @return array
	 */
	private function getTags(Article $article)
	{
		$tags = $article->getTags();

		$output = [];
		foreach ($tags as $tag) {
			$output[] = [
				'name' => $tag->getName(),
			];
		}

		return $output;
	}

	/**
	 * @return Article
	 */
	public function getArticle()
	{
		return $this->article;
	}

	/**
	 * @param string $logoPath
	 */
	public function setLogoPath($logoPath)
	{
		$this->logoPath = $logoPath;
	}

	/**
	 * @param Article $article
	 * @return array
	 */
	private function getShops(Article $article)
	{
		$shops = $article->getShops();

		$output = [];
		foreach ($shops as $shop) {
			$output[] = [
				'name' => $shop->getName(),
				'description' => '',
			];
		}

		return $output;
	}

	/**
	 * @param string $destinationUrl
	 */
	public function setDestinationUrl($destinationUrl)
	{
		$this->destinationUrl = $destinationUrl;
	}

	/**
	 * @return Article
	 */
	public function getEntity()
	{
		return $this->article;
	}

	/**
	 * @return array
	 */
	public function getIndexesNames()
	{
		return [$this->configuration->getArticlesIndexName($this->getEntity()->getLocalization())];
	}
}
