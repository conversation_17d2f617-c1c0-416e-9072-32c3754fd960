<?php

namespace tipli\Model\Algolia\Consumers;

use AlgoliaSearch\AlgoliaException;
use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Algolia\Client;
use tipli\Model\Algolia\Messages\AlgoliaMessage;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\RabbitMq\BaseConsumer;
use Tracy\Debugger;

class AlgoliaConsumer extends BaseConsumer implements IConsumer
{
	/** @var Client */
	private $client;

	/** @var DatadogProducer  */
	private $datadogProducer;

	public function __construct(Client $client, DatadogProducer $datadogProducer)
	{
		$this->client = $client;
		$this->datadogProducer = $datadogProducer;
	}

	public function consume(Message $data): int
	{
		$message = AlgoliaMessage::fromJson($data->content);

		$this->datadogProducer->scheduleSendEvent('algolia.sync.' . $message->getIndexName());
		Debugger::log($message->getIndexName() . ' - ' . $message->getObjectId(), 'algolia-sync');

		try {
			if ($message->getAction() === AlgoliaMessage::ACTION_SAVE_OBJECT) {
				$this->client->saveObject($message->getObjectData(), $message->getIndexName());
			} elseif ($message->getAction() ===  AlgoliaMessage::ACTION_DELETE_OBJECT) {
				Debugger::log('Deleting object ' . $message->getObjectId() . ' from index ' . $message->getIndexName(), 'algolia-delete');
				$this->client->deleteObject($message->getObjectId(), $message->getIndexName());
			}
		} catch (AlgoliaException $e) {
			Debugger::log($message->getObjectData(), 'algolia-error');
		}

		return IConsumer::MESSAGE_ACK;
	}
}
