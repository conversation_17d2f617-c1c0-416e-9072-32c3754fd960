<?php

namespace tipli\Model\Risk\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Risk\Repositories\RiskPolicyRepository")
 * @ORM\Table(name="tipli_risk_risk_policy")
 */
class RiskPolicy
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Shops\Entities\Shop", inversedBy="riskPolicy")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private ?Shop $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\PartnerSystems\Entities\PartnerSystem")
	 * @ORM\JoinColumn(name="partner_system_id", referencedColumnName="id")
	 */
	private ?PartnerSystem $partnerSystem;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $riskTransactionCommissionAmountThreshold = null;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $minimalPercentThreshold = null;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $isHighRisk = false;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function getRiskTransactionCommissionAmountThreshold(): ?int
	{
		return $this->riskTransactionCommissionAmountThreshold;
	}

	public function isHighRisk(): bool
	{
		return $this->isHighRisk;
	}

	public function __construct(?Shop $shop, ?PartnerSystem $partnerSystem)
	{
		$this->shop = $shop;
		$this->partnerSystem = $partnerSystem;

		$this->createdAt = new \DateTime();
	}
}
