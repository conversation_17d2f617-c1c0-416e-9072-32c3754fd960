<?php

namespace tipli\Model\Mailchimp\Producers;

use tipli\Model\Mailchimp\MailchimpMember;
use tipli\Model\Mailchimp\Messages\MailchimpPrepareUserForSynchronization;
use tipli\Model\Mailchimp\Messages\MailchimpProcessCampaignMessage;
use tipli\Model\Mailchimp\Messages\MailchimpProcessEventMessage;
use tipli\Model\Mailchimp\Messages\MailchimpSynchronizeMemberMessage;
use tipli\Model\RabbitMq\BaseProducer;

class MailchimpProducer extends BaseProducer
{
	public const ROUTING_KEY_PREPARE_FOR_SYNCHRONIZATION = 'prepare_for_synchronization';
	public const ROUTING_KEY_SYNCHRONIZE_MEMBER = 'synchronize_members';
	public const ROUTING_KEY_PROCESS_CAMPAIGN_EVENTS = 'process_campaign_events';
	public const ROUTING_KEY_PROCESS_EMAIL_EVENTS = 'process_email_events';

	public function scheduleMembershipSynchronizeMember(MailchimpMember $mailchimpMember)
	{
		$this->producer->publish(
			new MailchimpSynchronizeMemberMessage($mailchimpMember),
			[],
			self::ROUTING_KEY_SYNCHRONIZE_MEMBER
		);
	}

	public function scheduleMailchimpUserPrepareForSynchronization(int $userId): void
	{
		$this->producer->publish(
			new MailchimpPrepareUserForSynchronization($userId),
			[],
			self::ROUTING_KEY_PREPARE_FOR_SYNCHRONIZATION
		);
	}

	public function scheduleCampaignProcess(string $campaignId): void
	{
		$this->producer->publish(
			new MailchimpProcessCampaignMessage(
				$campaignId
			),
			[],
			self::ROUTING_KEY_PROCESS_CAMPAIGN_EVENTS
		);
	}

	public function scheduleEventProcess(int $userId, string $datetimeString, string $type): void
	{
		$this->producer->publish(
			new MailchimpProcessEventMessage(
				$userId,
				$datetimeString,
				$type
			),
			[],
			self::ROUTING_KEY_PROCESS_EMAIL_EVENTS
		);
	}
}
