<?php

namespace tipli\Model\Vouchers\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use tipli\Model\Transactions\Entities\Transaction;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Vouchers\Repositories\UserVoucherTransactionRepository")
 * @ORM\Table(name="tipli_vouchers_user_voucher_transaction")
 */
class UserVoucherTransaction
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private User $user;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Vouchers\Entities\UserVoucher")
	 * @ORM\JoinColumn(name="user_voucher_id", referencedColumnName="id")
	 */
	private UserVoucher $userVoucher;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="bonus_transaction_id", referencedColumnName="id")
	 */
	private Transaction $bonusTransaction;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="transaction_id", referencedColumnName="id")
	 */
	private ?Transaction $transaction = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(UserVoucher $userVoucher, Transaction $bonusTransaction, ?Transaction $transaction = null)
	{
		$this->user = $userVoucher->getUser();
		$this->userVoucher = $userVoucher;
		$this->bonusTransaction = $bonusTransaction;
		$this->transaction = $transaction;
		$this->createdAt = new \DateTime();
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUser(): User
	{
		return $this->user;
	}

	public function getUserVoucher(): UserVoucher
	{
		return $this->userVoucher;
	}

	public function getBonusTransaction(): Transaction
	{
		return $this->bonusTransaction;
	}

	public function getTransaction(): ?Transaction
	{
		return $this->transaction;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}
}
