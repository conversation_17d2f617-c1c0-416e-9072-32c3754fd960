<?php

namespace tipli\Model\Vouchers;

use tipli\Model\Account\Entities\User;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Repositories\TransactionRepository;
use tipli\Model\Vouchers\Entities\UserVoucher;
use tipli\Model\Vouchers\Entities\VoucherCampaign;
use tipli\Model\Vouchers\Repositories\UserVoucherRepository;
use tipli\Model\Vouchers\Repositories\VoucherCampaignRepository;

class VoucherProvider
{
	public function __construct(
		private VoucherCampaignRepository $voucherCampaignRepository,
		private UserVoucherRepository $userVoucherRepository,
		private TransactionRepository $transactionRepository
	) {
	}

	public function isUserAbleToUseVoucher(User $user, VoucherCampaign $voucherCampaign): bool
	{
		// kampan byla smazana
		if ($voucherCampaign->isRemoved() || ($voucherCampaign->isValid() === false && $voucherCampaign->isPublic() === false)) {
			bdump("ia1");
			return false;
		}

		$voucherCampaignCondition = $voucherCampaign->getVoucherCampaignCondition();

		// neplatny segment
		if ($voucherCampaignCondition->getSegment() !== null && $voucherCampaignCondition->getSegment() !== $user->getSegment()) {
			bdump("ia2");
			return false;
		}

		// datum posledni transakce je vetsi nez vyzaduje kampan
		if (
			$voucherCampaignCondition->getLastTransactionBefore() !== null &&
			($user->getLastTransactionAt() === null || $user->getLastTransactionAt() > $voucherCampaignCondition->getLastTransactionBefore())
		) {
			bdump("ia3");
			return false;
		}

		// uzivatel uz nekoho doporucil
		if ($voucherCampaignCondition->getWithoutRecommendedUser() === true && $user->getCountOfRecommendedUsers() > 0) {
			bdump("ia4");
			return false;
		}

		// kampan je pro uzivatele mladsi 24 hod
		if ($voucherCampaignCondition->isOnlyForNewUsers() && $user->getCreatedAt() < (new \DateTime())->modify('-24 hours')) {
			bdump("ia5");
			return false;
		}

		// kampan je pro uzivatele starsi 7 dn
		if ($voucherCampaignCondition->isOnlyForUsersRegisteredAfter7Days() && $user->getCreatedAt() > (new \DateTime())->modify('-7 days')) {
			bdump("ia5a");
			return false;
		}

		// kampan je pouze pro uzivatele bez doplnku
		if ($voucherCampaignCondition->getWithoutAddonInstalled() === true && $user->hasInstalledAddon() === true) {
			bdump("ia6");
			return false;
		}

		$shopsIdsWhereUserHasTransactions = array_map(static function (Shop $shop) {
			return $shop->getId();
		}, $this->transactionRepository->findShopsWhereUserHasTransaction($user));

		bdump($shopsIdsWhereUserHasTransactions);

		// kampan vyzaduje striktne obchody,kde uzivatel musi mit alespon 1 nakup
		if (!empty($voucherCampaignCondition->getRequiredTransactionInShopIds())) {
			foreach ($voucherCampaignCondition->getRequiredTransactionInShopIds() as $requiredShopId) {
				if (in_array((int) $requiredShopId, $shopsIdsWhereUserHasTransactions, true) === false) {
					return false;
				}
			}
		}

		// kampan se vylucuje, pokud uzivatel nakoupil ve vybranych obchodech
		if (!empty($voucherCampaignCondition->getNoTransactionInShopIds())) {
			foreach ($voucherCampaignCondition->getNoTransactionInShopIds() as $noShopId) {
				if (in_array((int) $noShopId, $shopsIdsWhereUserHasTransactions, true) === true) {
					return false;
				}
			}
		}

		// kampan lze pouzit pouze 1x
		if ($voucherCampaign->isReusableForUser() === false && !empty($this->userVoucherRepository->findUserVouchers($user, $voucherCampaign))) {
			bdump("ia7");
			return false;
		}

		// uzivateli uz bezi tato kampan
		if ($this->userVoucherRepository->findUserVouchers($user, $voucherCampaign, true)) {
			bdump("ia8");
			return false;
		}

		return true;
	}

	public function provideVoucherForUserByCampaign(User $user, VoucherCampaign $voucherCampaign): ?VoucherObject
	{
		$userVouchers = $this->userVoucherRepository->findUserVouchers($user, $voucherCampaign, onlyValidOrPublic: true);
		$userVoucher = $userVouchers[0] ?? null;

		if ($userVoucher !== null) {
			return new VoucherObject($voucherCampaign, $userVoucher);
		}

		if ($this->isUserAbleToUseVoucher($user, $voucherCampaign) === false) {
			return null;
		}

		return new VoucherObject($voucherCampaign, null);
	}

	public function provideVouchersForUser(User $user, bool $onlyExpired = false, bool $onlyFinished = false): array
	{
		/** @var VoucherObject[] $voucherObjects */
		$voucherObjects = [];

		$voucherCampaigns = $this->voucherCampaignRepository->findPublicVoucherCampaigns($user->getLocalization());

		/** @var VoucherCampaign $voucherCampaign */
		foreach ($voucherCampaigns as $voucherCampaign) {
			$userVouchers = $this->userVoucherRepository->findUserVouchers($user, $voucherCampaign, onlyValidOrPublic: true);
			/** @var ?UserVoucher $userVoucher */
			$userVoucher = $userVouchers[0] ?? null;

			if ($userVoucher === null && $voucherCampaign->isHidden()) {
				continue;
			}

			if ($onlyExpired === true && $voucherCampaign->isExpired() === false) {
				continue;
			}

			if ($onlyFinished === true && ($userVoucher === null || $userVoucher->isFinished() === false)) {
				continue;
			}

			if ($user->hasInstalledAddon() && $voucherCampaign->isRewardTypeAddon() && ($userVoucher === null || ($userVoucher !== null && $userVoucher->isActive()))) {
				continue;
			}

			// kupon uz je prirazen, takze ho uzivatel vzdy uvidi
			if ($userVoucher !== null) {
				$voucherObjects[] = new VoucherObject($voucherCampaign, $userVoucher);
			}

			if ($this->isUserAbleToUseVoucher($user, $voucherCampaign) === true) {
				$voucherObjects[] = new VoucherObject($voucherCampaign, null);
			}
		}

		usort($voucherObjects, static function (VoucherObject $voucherObjectA, VoucherObject $voucherObjectB) {
			$statePriority = [
				VoucherObject::STATUS_ACTIVE => 1,
				VoucherObject::STATUS_ASSIGNED => 2,
				VoucherObject::STATUS_AVAILABLE => 3,
				VoucherObject::STATUS_AVAILABLE_SOON => 4,
				VoucherObject::STATUS_FINISHED => 5,
				VoucherObject::STATUS_EXPIRED => 6,
			];

			$stateComparison = $statePriority[$voucherObjectA->getStatus()] <=> $statePriority[$voucherObjectB->getStatus()];

			if ($stateComparison === 0) {
				return $voucherObjectA->getVoucherCampaign()->getPublicSince()->getTimestamp() <=> $voucherObjectB->getVoucherCampaign()->getPublicSince()->getTimestamp();
			}

			return $stateComparison;
		});

		return $voucherObjects;
	}
}
