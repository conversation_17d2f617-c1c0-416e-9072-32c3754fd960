<?php

namespace tipli\Model\Articles;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use tipli\Model\Articles\Entities\Article;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;

class ArticlesRecommender
{
	/**
	 * @var Cache
	 */
	private $cache;

	/**
	 * @var ArticleFacade
	 */
	private $articleFacade;

	/**
	 * @var ShopFacade
	 */
	private $shopFacade;

	public function __construct(Storage $storage, ArticleFacade $articleFacade, ShopFacade $shopFacade)
	{
		$this->cache = new Cache($storage, self::class);
		$this->articleFacade = $articleFacade;
		$this->shopFacade = $shopFacade;
	}

	/**
	 * @param null|Article|Tag|Shop $entity
	 *
	 * @return Article[]
	 */
	public function getRecommendedArticles(Localization $localization, $entity = null, $maxResults = 6)
	{
		if ($entity instanceof Article) {
			$cashKey = $localization->getLocale() . '-recommendedArticles-article-' . $entity->getId();
		} elseif ($entity instanceof Shop) {
			$cashKey = $localization->getLocale() . '-recommendedArticles-shop-' . $entity->getId() . '-';
		} elseif ($entity instanceof Tag) {
			$cashKey = $localization->getLocale() . '-recommendedArticles-tag-' . $entity->getId() . '-' . $maxResults;
		} else {
			$cashKey = $localization->getLocale() . '-recommendedArticles' . $maxResults;
		}

		$recommendedArticles = $this->cache->load($cashKey, function (&$dp) use ($localization, $entity, $maxResults) {
			$dp = [
				Cache::EXPIRATION => '8 hours',
			];

			if ($entity instanceof Article) {
				$allRecommendedArticles = $this->getRecommendedArticlesByArticle($entity);
			} elseif ($entity instanceof Shop) {
				$allRecommendedArticles = $this->getRecommendedArticlesByShop($entity);
			} elseif ($entity instanceof Tag) {
				if ($entity->isArticleTag()) {
					$allRecommendedArticles = $this->getRecommendedArticlesByTag($entity);
				} else {
					$shops = $this->shopFacade->findPublishedShopsByTag($entity);

					$allRecommendedArticles = $this->getRecommendedArticlesByShops($shops);
				}
			} else {
				$allRecommendedArticles = $this->getTopRecommendedArticles($localization);
			}

			if (!$allRecommendedArticles && !($entity instanceof Shop)) {
				$allRecommendedArticles = $this->getRandomArticles($localization, $maxResults);
			}

			return array_slice($allRecommendedArticles, 0, $maxResults);
		});

		$result = [];
		foreach ($recommendedArticles as $recommendedArticle) {
			$result[] = $this->articleFacade->find($recommendedArticle);
		}

		return $result;
	}

	private function getTopRecommendedArticles(Localization $localization, Article $disabledArticle = null)
	{
		$allRecommendedArticles = [];

		$query = $this->articleFacade->createPublishedArticlesQuery($localization);
		$query->sortTopped();

		foreach ($this->articleFacade->fetch($query) as $recommendedArticle) {
			if (!$disabledArticle || $recommendedArticle != $disabledArticle) {
				$allRecommendedArticles[$recommendedArticle->getId()] = $recommendedArticle->getId();
			}
		}

		return $allRecommendedArticles;
	}

	private function getRecommendedArticlesByArticle(Article $article)
	{
		return $this->getTopRecommendedArticles($article->getLocalization(), $article);
	}

	private function getRecommendedArticlesByShop(Shop $shop)
	{
		$allRecommendedArticles = [];

		$query = $this->articleFacade->createPublishedArticlesQuery();
		$query->withShop($shop)
			->sortNewest()
			->withDescription();

		foreach ($this->articleFacade->fetch($query) as $recommendedArticle) {
			$allRecommendedArticles[$recommendedArticle->getId()] = $recommendedArticle->getId();
		}

		return $allRecommendedArticles;
	}

	private function getRecommendedArticlesByShops(array $shops)
	{
		$allRecommendedArticles = [];

		$query = $this->articleFacade->createPublishedArticlesQuery();
		$query->withShops($shops);
		$query->sortNewest();

		foreach ($this->articleFacade->fetch($query) as $recommendedArticle) {
			$allRecommendedArticles[$recommendedArticle->getId()] = $recommendedArticle->getId();
		}

		return $allRecommendedArticles;
	}

	private function getRecommendedArticlesByTag(Tag $tag)
	{
		$allRecommendedArticles = [];

		$query = $this->articleFacade->createPublishedArticlesQuery();
		$query->withTags([$tag]);

		foreach ($this->articleFacade->fetch($query) as $recommendedArticle) {
			$allRecommendedArticles[$recommendedArticle->getId()] = $recommendedArticle->getId();
		}

		return $allRecommendedArticles;
	}

	private function getRandomArticles(Localization $localization, $maxResults)
	{
		$allRecommendedArticles = [];

		$query = $this->articleFacade->createPublishedArticlesQuery($localization);
		$query->sortRandom();

		foreach ($this->articleFacade->fetch($query)->applyPaging(0, $maxResults) as $recommendedArticle) {
			$allRecommendedArticles[$recommendedArticle->getId()] = $recommendedArticle->getId();
		}

		return $allRecommendedArticles;
	}
}
