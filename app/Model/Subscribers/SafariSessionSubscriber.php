<?php

namespace tipli\Model\Subscribers;

use Contributte\Events\Extra\Event\Application\RequestEvent;
use Nette\SmartObject;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Layers\SafariSessionTokenLayer;

final class SafariSessionSubscriber implements EventSubscriberInterface
{
	use SmartObject;

	public function __construct(private SafariSessionTokenLayer $safariSessionTokenLayer)
	{
	}

	public static function getSubscribedEvents(): array
	{
		return [
			RequestEvent::class => ['listenRequest'],
		];
	}

	public function listenRequest(RequestEvent $event)
	{
		$this->safariSessionTokenLayer->trackSafariSessionUser();
	}
}
