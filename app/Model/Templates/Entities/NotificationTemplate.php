<?php

namespace tipli\Model\Templates\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="\tipli\Model\Templates\Repositories\NotificationTemplateRepository")
 * @ORM\Table(name="tipli_notifications_notification_template", indexes={
 *    @ORM\Index(name="name_idx", columns={"name"}),
 * })
 */
class NotificationTemplate
{
	public const STATUS_DRAFT = 'draft';
	public const STATUS_PUBLISHED = 'published';
	public const STATUS_ARCHIVED = 'archived';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private Localization $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Images\Entities\Image")
	 * @ORM\JoinColumn(name="image_id", referencedColumnName="id", nullable=true)
	 */
	private ?Image $image = null;

	/**
	 * @ORM\OneToMany(targetEntity="NotificationTemplateParameter", mappedBy="notificationTemplate")
	 * @ORM\OrderBy({"createdAt" = "DESC"})
	 * @var NotificationTemplateParameter[]|ArrayCollection
	 */
	private $parameters;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $triggerKey = null;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $name;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $displayName;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $type;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $message;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $status;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $link;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $linkText;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $scenario;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $push = true;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private ?string $note;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $duration = null;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $locked = false;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?DateTime $publishedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?DateTime $archivedAt = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private DateTime $updatedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private DateTime $createdAt;

	public function __construct(Localization $localization, string $triggerKey, string $type, string $name, string $displayName, string $message, string $link)
	{
		$this->localization = $localization;
		$this->triggerKey = $triggerKey;
		$this->type = $type;
		$this->name = $name;
		$this->displayName = $displayName;
		$this->message = $message;
		$this->status = self::STATUS_DRAFT;
		$this->link = $link;
		$this->updatedAt = new DateTime();
		$this->createdAt = new DateTime();
	}

	public function publish()
	{
		$this->status = self::STATUS_PUBLISHED;
		$this->publishedAt = new DateTime();
	}

	public function unPublish()
	{
		$this->status = self::STATUS_DRAFT;
		$this->publishedAt = null;
		$this->archivedAt = null;
	}

	public function archive(): void
	{
		$this->status = self::STATUS_ARCHIVED;
		$this->archivedAt = new DateTime();
	}

	public function isDraft(): bool
	{
		return $this->status === self::STATUS_DRAFT;
	}

	public function isPublished(): bool
	{
		return $this->status === self::STATUS_PUBLISHED;
	}

	public function isArchived(): bool
	{
		return $this->status === self::STATUS_ARCHIVED;
	}

	public static function getStatuses(): array
	{
		return [
			self::STATUS_DRAFT => 'Draft',
			self::STATUS_PUBLISHED => 'Published',
			self::STATUS_ARCHIVED => 'Archived',
		];
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function setLocalization(Localization $localization): void
	{
		$this->localization = $localization;
	}

	public function getImage(): ?Image
	{
		return $this->image;
	}

	public function setImage(?Image $image): void
	{
		$this->image = $image;
	}

	public function getParameters()
	{
		return $this->parameters;
	}

	public function setParameters($parameters): void
	{
		$this->parameters = $parameters;
	}

	public function getTriggerKey(): ?string
	{
		return $this->triggerKey;
	}

	public function setTriggerKey(?string $triggerKey): void
	{
		$this->triggerKey = $triggerKey;
	}

	public function getType(): string
	{
		return $this->type;
	}

	public function setType(string $type): void
	{
		$this->type = $type;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getDisplayName(): string
	{
		return $this->displayName;
	}

	public function setDisplayName(string $displayName): void
	{
		$this->displayName = $displayName;
	}

	public function getMessage(): string
	{
		return $this->message;
	}

	public function setMessage(string $message): void
	{
		$this->message = $message;
	}

	public function getStatus(): string
	{
		return $this->status;
	}

	public function setStatus(string $status): void
	{
		$this->status = $status;
	}

	public function getLink(): string
	{
		return $this->link;
	}

	public function setLink(string $link): void
	{
		$this->link = $link;
	}

	public function getLinkText(): ?string
	{
		return $this->linkText;
	}

	public function setLinkText(?string $linkText): void
	{
		$this->linkText = $linkText;
	}

	public function getScenario(): ?string
	{
		return $this->scenario;
	}

	public function setScenario(?string $scenario): void
	{
		$this->scenario = $scenario;
	}

	public function isPush(): bool
	{
		return $this->push === true;
	}

	public function setPush(bool $push): void
	{
		$this->push = $push;
	}

	public function getNote(): ?string
	{
		return $this->note;
	}

	public function setNote(?string $note): void
	{
		$this->note = $note;
	}

	public function getDuration(): ?int
	{
		return $this->duration;
	}

	public function setDuration(?int $duration): void
	{
		$this->duration = $duration;
	}

	public function isLocked(): bool
	{
		return $this->locked === true;
	}

	public function setLocked(bool $locked): void
	{
		$this->locked = $locked;
	}

	public function getPublishedAt(): ?DateTime
	{
		return $this->publishedAt;
	}

	public function setPublishedAt(?DateTime $publishedAt): void
	{
		$this->publishedAt = $publishedAt;
	}

	public function getArchivedAt(): ?DateTime
	{
		return $this->archivedAt;
	}

	public function setArchivedAt(?DateTime $archivedAt): void
	{
		$this->archivedAt = $archivedAt;
	}

	public function getUpdatedAt(): DateTime
	{
		return $this->updatedAt;
	}

	public function setUpdatedAt(DateTime $updatedAt): void
	{
		$this->updatedAt = $updatedAt;
	}

	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}
}
