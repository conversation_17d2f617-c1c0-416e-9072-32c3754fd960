<?php

namespace tipli\Model\Elastic\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;

class ElasticSearchMessage implements IMessage
{
	public const ACTION_SAVE_ENTITY = 'save_entity';
	public const ACTION_SAVE_ENTITIES = 'save_entities';

	/** @var string */
	private $action;

	/** @var string */
	private $indexName;

	/** @var array */
	private $documentData;

	public function __construct(string $action, string $indexName, array $documentData)
	{
		$this->action = $action;
		$this->indexName = $indexName;
		$this->documentData = $documentData;
	}

	public function getAction(): string
	{
		return $this->action;
	}

	public function getIndexName(): string
	{
		return $this->indexName;
	}

	public function getDocumentData(): array
	{
		return $this->documentData;
	}

	public function __toString(): string
	{
		return Json::encode([
			'action' => $this->getAction(),
			'indexName' => $this->getIndexName(),
			'documentData' => $this->getDocumentData(),
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data, Json::FORCE_ARRAY);

		return new self(
			$data['action'],
			$data['indexName'],
			$data['documentData']
		);
	}
}
