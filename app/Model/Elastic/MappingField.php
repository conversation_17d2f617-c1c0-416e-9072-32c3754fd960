<?php

namespace tipli\Model\Elastic;

final class MappingField
{
	public const TYPE_INTEGER = 'integer';
	public const TYPE_LONG = 'long';
	public const TYPE_TEXT = 'text';
	public const TYPE_DATE = 'date';

	/** @var string */
	private $name;

	/** @var string */
	private $type;

	/**
	 * @param string $name
	 * @param string $type
	 */
	public function __construct(string $name, string $type)
	{
		$this->name = $name;
		$this->type = $type;
	}

	/**
	 * @return string
	 */
	public function getName(): string
	{
		return $this->name;
	}

	/**
	 * @return string
	 */
	public function getType(): string
	{
		return $this->type;
	}
}
