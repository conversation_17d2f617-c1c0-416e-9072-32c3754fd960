<?php

namespace tipli\Model\HtmlBuilders;

use tipli\Exceptions\LatteBuildException;
use Latte\CompileException;
use LogicException;
use Latte\Engine;
use Latte\Loaders\StringLoader;
use tipli\Model\Localization\LocalDateFilter;

class LatteBuilder
{
	public function __construct(
		private LocalDateFilter $localDateFilter
	) {
	}

	public function buildString(string $string, array $parameters = []): string
	{
		try {
			$latte = new Engine();
			$latte->setLoader(new StringLoader());
			$latte->compile($string);

			$undefinedParameters = [];

			preg_match_all('/{[^}]*}/', $string, $blockMatches);

			foreach ($blockMatches[0] as $block) {
				preg_match_all('/\$\w+/', $block, $matches);

				foreach ($matches as $match) {
					foreach ($match as $variable) {
						$variable = str_replace('$', '', $variable);
						if (!array_key_exists($variable, $parameters)) {
							$undefinedParameters[] = $variable;
						}
					}
				}
			}

			if (!empty($undefinedParameters)) {
				throw new CompileException("Parameter(s) '" . implode(', ', $undefinedParameters) . "' not defined");
			}

			// filters
			$latte->addFilter('localDate', $this->localDateFilter);
			$latte->addFilter('defaultNumber', static function (float $amount, int $decimal = 0, string $decimalSeparator = ',', $thousandSeparator = ' '): string {
				if ($decimal === 0 && ($amount - floor($amount)) > 0) {
					$decimal = 2;
				}
				return number_format($amount, $decimal, $decimalSeparator, $thousandSeparator);
			});

			return $latte->renderToString($string, $parameters);
		} catch (LogicException | CompileException $e) {
			throw new LatteBuildException($e->getMessage());
		}
	}
}
