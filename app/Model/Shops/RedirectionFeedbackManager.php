<?php

namespace tipli\Model\Shops;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\Entities\RedirectionFeedback;

class RedirectionFeedbackManager
{
	public function __construct(private EntityManager $em)
	{
	}

	public function createRedirectionFeedback(Redirection $redirection, string $type, ?string $ip, ?string $platform, ?string $country): RedirectionFeedback
	{
		if (array_key_exists($type, RedirectionFeedback::getFeedbackTypes()) === false) {
			throw new \InvalidArgumentException('Invalid feedback type');
		}

		$redirectionFeedback = new RedirectionFeedback(
			$redirection,
			$type,
			$ip,
			$platform,
			$country
		);

		return $this->saveRedirectionFeedback($redirectionFeedback);
	}

	public function saveRedirectionFeedback(RedirectionFeedback $redirectionFeedback): RedirectionFeedback
	{
		$this->em->persist($redirectionFeedback);
		$this->em->flush($redirectionFeedback);

		return $redirectionFeedback;
	}
}
