<?php

namespace tipli\Model\Shops;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Shops\Entities\Contact;
use tipli\Model\Shops\Repositories\ContactRepository;

class ContactFacade
{
	/** @var EntityManager */
	private $em;

	/** @var ContactRepository */
	private $contactRepository;

	public function __construct(EntityManager $em, ContactRepository $contactRepository)
	{
		$this->em = $em;
		$this->contactRepository = $contactRepository;
	}

	public function saveContact(Contact $contact)
	{
		$this->em->persist($contact);
		$this->em->flush($contact);

		return $contact;
	}

	public function createContact()
	{
		$contact = new Contact();

		return $this->saveContact($contact);
	}
}
