<?php

namespace tipli\Model\Shops\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Localization\Entities\Localization;
use tipli\Routers\RouterFactory;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Shops\Repositories\ForeignShopRepository")
 * @ORM\Table(name="tipli_shops_foreign_shop")
 */
class ForeignShop
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop", inversedBy="foreignShops")
	 * @ORM\JoinColumn(name="parent_shop_id", referencedColumnName="id")
	 * @var Shop
	 */
	private $foreignParentShop;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop", inversedBy="foreignParentShops")
	 * @ORM\JoinColumn(name="foreign_shop_id", referencedColumnName="id")
	 * @var Shop
	 */
	private $foreignShop;

	/**
	 * @ORM\Column(type="integer")
	 * @var int
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	public function __construct(Shop $parentShop, Shop $foreignShop, $priority)
	{
		$this->foreignParentShop = $parentShop;
		$this->foreignShop = $foreignShop;
		$this->priority = $priority;
		$this->createdAt = new DateTime();
	}

  /**
   * @return int
   */
	public function getId()
	{
		return $this->id;
	}

  /**
   * @return Shop
   */
	public function getShop(): Shop
	{
		return $this->foreignShop;
	}

	/**
	 * @return int
	 */
	public function getPriority()
	{
		return $this->priority;
	}

	/**
	 * @return Shop
	 */
	public function getParentShop()
	{
		return $this->foreignParentShop;
	}

	/**
	 * @param int $priority
	 */
	public function setPriority(int $priority)
	{
		$this->priority = $priority;
	}

	/**
	 * @return string
	 */
	public function getForeignShopUrl()
	{
		/** @var Localization $localization */
		$localization = $this->foreignShop->getLocalization();

		$baseUrl = $localization->getBaseUrl();
		$path = RouterFactory::getTranslation('shop', $localization);

		return $baseUrl . '/' . $path . '/' . $this->foreignShop->getSlug();
	}
}
