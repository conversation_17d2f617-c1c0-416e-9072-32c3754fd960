<?php

namespace tipli\Model\Shops;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Shops\Entities\ShopReport;

class ShopReportManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function saveShopReport(ShopReport $shopReport): ShopReport
	{
		$this->em->persist($shopReport);
		$this->em->flush($shopReport);

		return $shopReport;
	}
}
