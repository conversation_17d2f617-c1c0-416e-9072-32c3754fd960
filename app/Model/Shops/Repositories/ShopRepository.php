<?php

namespace tipli\Model\Shops\Repositories;

use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Doctrine\BaseRepository;
use Doctrine\ORM\QueryBuilder;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Shops\Entities\DescriptionBlock;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopExternalData;
use tipli\Model\Shops\Entities\ShopSettings;
use tipli\Model\Shops\Entities\ShopTag;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Shops\Queries\ShopsQuery;

class ShopRepository extends BaseRepository
{
	public function getShops(Localization $localization = null, $sortByLocale = true): QueryBuilder
	{
		$qb = $this->createQueryBuilder('s')
			->leftJoin('s.shopData', 'd')->addSelect('d')
			->leftJoin('s.pageExtension', 'pe');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('s.localization', 'l');

			if ($sortByLocale === true) {
				$qb->addOrderBy('l.locale', 'ASC');
			}
		}

		return $qb;
	}

	public function findByIds($ids): array
	{
		return $this->getShops(null, false)
			->andWhere('s.id IN (:ids)')
			->addOrderBy('FIELD(s.id, :ids)')
			->setParameter('ids', $ids)
			->getQuery()
			->getResult();
	}

	public function getPublishedShops(Localization $localization = null)
	{
		return $this->getShops($localization)
			->andWhere('s.active = 1')
			->andWhere('s.visible = 1')
			->andWhere('d.onlyLeafletShop = false')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()');
	}

	public function getActiveAndVisibleShops(?Localization $localization = null)
	{
		return $this->getShops($localization)
			->andWhere('s.active = 1')
			->andWhere('s.visible = 1');
	}

	public function getCountOfActiveAndVisibleShops(?Localization $localization = null): int
	{
		$qb = $this->getActiveAndVisibleShops($localization)
			->select('count(s.id)');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findActiveAndVisibleShops(?Localization $localization = null)
	{
		return $this->getActiveAndVisibleShops($localization)
			->getQuery()
			->getResult();
	}

	/**
	 * Get published shops with one of the selected tags
	 * @param array $tags Array of tags IDs
	 * @return QueryBuilder
	 */
	public function getPublishedShopsByTags(array $tags, $onlyWithCashbackAllowed = false)
	{
		$qb = $this->getShops();

		$qb = $qb->leftJoin('s.shopTags', 'st')
			->andWhere('s.active = 1')
			->andWhere('s.active = 1')
			->andWhere($qb->expr()->in('st.tag', $tags))
			->groupBy('s.id')
			->addOrderBy('IFNULL (st.priority, s.priority)', 'DESC');

		if ($onlyWithCashbackAllowed) {
			$qb->andWhere('s.cashbackAllowed = true');
		}

		return $qb;
	}

	public function findBySlug($slug, Localization $localization = null)
	{
		$qb = $this->getShops($localization)
			->andWhere('s.visible = 1')
			->andWhere('s.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

//	public function findVisibleShopBySlug($slug, Localization $localization = null)
//	{
//		$qb = $this->getShops($localization)
//
//            ->andWhere('s.slug = :slug')
//			->setParameter('slug', $slug);
//
//		return $qb->getQuery()->getOneOrNullResult();
//	}


	public function findShopsWhereUserHasRedirection(User $user, \DateTime $dateFrom = null, $sortByPriority = false)
	{
		$qb = $this->getShops()
			->leftJoin('s.redirections', 'r');

		$qb->andWhere('r.user = :user')
			->setParameter('user', $user);

		if ($dateFrom) {
			$qb->andWhere('r.createdAt >= :dateFrom')
				->setParameter('dateFrom', $dateFrom);
		}

		if ($sortByPriority) {
			$qb->addOrderBy('s.priority', 'DESC');
		}

		$qb->addGroupBy('s');

		return $qb->getQuery()->getResult();
	}

	public function findCountOfShops(Localization $localization = null, bool $onlyCashback = false)
	{
		$qb = $this->getPublishedShops($localization)
			->select('count(s.id)');

		if ($onlyCashback) {
			$qb->andWhere('s.cashbackAllowed = true');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findTotalCountOfShops(Localization $localization = null)
	{
		$qb = $this->getPublishedShops($localization)
			->select('count(s.id)');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findShopByDomain(Localization $localization = null, $shopDomain)
	{
		$qb = $this->createQueryBuilder('s')
			->select('s')
			->andWhere('s.domain LIKE :domain')
			->setParameter('domain', '%' . $shopDomain . '%');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		$qb->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findShopsByDomain($shopDomain)
	{
		$qb = $this->createQueryBuilder('s')
			->select('s')
			->andWhere('s.domain LIKE :domain')
			->setParameter('domain', '%' . $shopDomain . '%');

		return $qb->getQuery()->getResult();
	}

	public function findPublishedShopsByTag(Tag $tag, bool $onlyCashback, int $maxResults, ?Shop $exceptShop = null)
	{
		$qb = $this->getShops()
			->innerJoin(ShopTag::class, 'st', 'WITH', 'st.shop = s.id AND st.tag = :tag')
			->andWhere('st.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('s.active = 1')
			->andWhere('s.visible = 1')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
			->addOrderBy('IFNULL (st.priority, s.priority)', 'DESC')
			->andWhere('(d.pausedAt IS NULL OR d.pausedAt >= CURRENT_TIMESTAMP())');

		if ($onlyCashback) {
			$qb->andWhere('s.cashbackAllowed = true');
		}

		if ($exceptShop) {
			$qb->andWhere('s != :exceptShop')
				->setParameter('exceptShop', $exceptShop);
		}

		$qb->setMaxResults($maxResults);

		return $qb->getQuery()->getResult();
	}

	public function findShopsByTag(Tag $tag): array
	{
		$qb = $this->getShops()
			->innerJoin(ShopTag::class, 'st', 'WITH', 'st.shop = s.id AND st.tag = :tag')
			->andWhere('st.tag = :tag')
			->setParameter('tag', $tag);

		return $qb->getQuery()->getResult();
	}

	public function findCountOfPublishedShopsByTag(Tag $tag, bool $onlyCashback)
	{
		$qb = $this->getShops()
			->select('count(s.id)')
			->innerJoin(ShopTag::class, 'st', 'WITH', 'st.shop = s.id AND st.tag = :tag')
			->andWhere('st.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('s.active = 1')
			->andWhere('s.visible = 1')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
			->addOrderBy('IFNULL (st.priority, s.priority)', 'DESC')
			->andWhere('(d.pausedAt IS NULL OR d.pausedAt >= CURRENT_TIMESTAMP())');

		if ($onlyCashback) {
			$qb->andWhere('s.cashbackAllowed = true');
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function searchShops(ShopsQuery $shopsQuery, $term)
	{
		return $shopsQuery->search($term)
			->onlyPublished()
			->onlyVisible()
			->sortTop();
	}

	public function findShopsWithActiveBanner(ShopsQuery $shopsQuery)
	{
		return $shopsQuery->onlyVisible()
			->onlyActive()
			->onlyPublished()
			->onlyWithSomeActiveBanners();
	}

	public function findShopsToProcessProducts()
	{
		return $this->getShops()
			->leftJoin('s.shopProductData', 'spd')
			->orderBy('spd.processedAt', 'ASC')
			->andWhere('spd IS NOT NULL')
			->andWhere('spd.processedAt < :now')
			->andWhere('spd.processedAt IS NOT NULL')
			->setParameter('now', new \DateTime())
			->setMaxResults(1)
			->getQuery()->getOneOrNullResult();
	}

	public function findShopsFromUserLastRedirections(User $user, array $exceptShops = null, int $maxResults = 10)
	{
		$qb = $this->getPublishedShops()
			->leftJoin('s.redirections', 'r')
			->andWhere('d.pausedAt IS NULL')
			->andWhere('s.cashbackAllowed = true')
			->andWhere('s.active = true')
			->andWhere('r.shop IS NOT NULL')
			->andWhere('r.user = :user')
			->setParameter('user', $user);

		if ($exceptShops) {
			$qb->andWhere('s NOT IN (:shops)')
				->setParameter('shops', $exceptShops);
		}

		$qb->groupBy('s.id')
			->orderBy('MAX(r.createdAt)', 'DESC')
			->setMaxResults($maxResults);

		return $qb->getQuery()->getResult();
	}

	public function findShopsFromUserLastTransactions(User $user, int $maxResults = 10)
	{
		return $this->getPublishedShops()
			->select('s as shop, MAX(t.registeredAt) as transactionDate, CONCAT(MAX(t.registeredAt), \' \', s.id) as key')
			->leftJoin('s.transactions', 't')
			->andWhere('d.pausedAt IS NULL')
			->andWhere('s.cashbackAllowed = true')
			->andWhere('s.active = true')
			->andWhere('t.billable = 1')
			->andWhere('t.currency = :currency')
			->setParameter('currency', $user->getLocalization()->getCurrency())
			->andWhere('t.confirmedAt IS NOT NULL')
			->andWhere('t.type IN (:types)')
			->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND])
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->groupBy('s.id')
			->orderBy('transactionDate', 'DESC')
			->setMaxResults($maxResults)
			->getQuery()
			->getResult();
	}

	public function getShopsWithProductFeed(Localization $localization)
	{
		return $this->getShops($localization)
			->addSelect('spf, ps')
			->leftJoin('s.shopProductFeed', 'spf')
			->andWhere('spf IS NOT NULL')
			->innerJoin('s.partnerSystem', 'ps')
		;
	}

	public function findShopsWithProductFeed(Localization $localization)
	{
		return $this->getShopsWithProductFeed($localization)
			->getQuery()
			->getResult()
		;
	}

	public function getShopsForAddon(Localization $localization, ?User $user = null)
	{
		$qb = $this->getShops($localization)
			->leftJoin('s.shopSettings', 'ss')->addSelect('ss')
			->andWhere('(d.pausedAt > CURRENT_TIMESTAMP() or d.pausedAt IS NULL)')
			->andWhere('s.active = 1')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
			->andWhere("s.domain != ''")
			->andWhere('ss.addonFeedDisabledAt IS NULL');

		if ($user !== null && $user->isAdmin()) {
			$qb->andWhere('ss.addonVisibility != :hidden OR ss.addonPublicForAdmins = true')
				->setParameter('hidden', ShopSettings::ADDON_VISIBILITY_HIDDEN)
			;
		} else {
			$qb->andWhere('ss.addonVisibility != :hidden')
				->setParameter('hidden', ShopSettings::ADDON_VISIBILITY_HIDDEN)
			;
		}

		return $qb;
	}

	public function findTopShopsWithProducts(Localization $localization, ?int $limit = null)
	{
		$qb = $this->getShops($localization)
			->andWhere('d.countOfValidProducts > 0')
			->addOrderBy('s.priority', 'DESC')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function getShopsWithProducts()
	{
		return $this->getShops()
			->andWhere('d.countOfValidProducts > 0')
		;
	}

	public function findShopsWithProducts(Localization $localization, ?int $limit = null)
	{
		$qb = $this->getShopsWithProducts();

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization)
			;
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findShopsInProductTag(Localization $localization, Tag $tag)
	{
		$qb = $this->getShops($localization)
		   ->innerJoin('s.products', 'p')
		   ->addGroupBy('s.id')
		   ->addOrderBy('s.priority', 'DESC');

		if ($tag->getLevel() === 0) {
			$qb->andWhere('p.primaryTag = :tag');
		} else {
			$qb->innerJoin('p.productTags', 'pt')
			->andWhere('pt.tag = :tag');
		}

		$qb->setParameter('tag', $tag);

		return $qb->getQuery()
		   ->getResult();
	}

	public function findShopsToCalculateReports(?Localization $localization = null)
	{
		$qb = $this->getShops()
			->addSelect('src')
			->innerJoin('s.reportCheck', 'src')
			->andWhere('d.lastTransactionAt >= :lastTransactionAt')
			->setParameter('lastTransactionAt', (new \DateTime('- 6 months'))->setTime(0, 0))
			->addOrderBy('s.priority', 'DESC');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		}

		return $qb->getQuery()
		->getResult();
	}

	public function findShopsToReports(?Localization $localization = null)
	{
		$dateFrom = (new \DateTime('- 13 days'))->setTime(0, 0);

		return $this->getShops($localization)
			->addSelect('r')
			->leftJoin('s.reports', 'r')
			->andWhere('d.lastTransactionAt >= :lastTransactionAt')
			->setParameter('lastTransactionAt', $dateFrom)
			->andWhere('r.startedAt >= :dateFrom')
			->setParameter('dateFrom', $dateFrom)
			->addOrderBy('s.countOfTransactions', 'DESC')
			->addOrderBy('r.startedAt', 'ASC')
			->andWhere('s.createdAt < :createdAt')
			->setParameter('createdAt', $dateFrom)
			->andWhere('s.cashbackAllowed = true')
			->andWhere('s.active = true')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
			->getQuery()
			->getResult()
		;
	}

	public function getShopsWithMessage(Localization $localization = null)
	{
		$qb = $this->getShops($localization);

		return $qb->where(
			'(
				d.warningMessage IS NOT NULL OR
				d.mobileMessageBeforeRedirect IS NOT NULL OR
		 	    d.mobileWarningMessage IS NOT NULL
		 	)'
		);
	}

	public function findShopsForGeneratedDescription(Tag $tag)
	{
		return $this->getShops($tag->getLocalization())
			->leftJoin('s.products', 'p')
			->innerJoin('s.shopTags', 'st')
			->andWhere('st.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('p.countOfImageCheckErrors = 0')
			->setMaxResults(3)
			->getQuery()
			->getResult();
	}

	public function findByPartnerSystem(PartnerSystem $partnerSystem)
	{
		return $this->getShops()
			->andWhere('s.partnerSystem = :partnerSystem')
			->setParameter('partnerSystem', $partnerSystem)
			->getQuery()
			->getResult();
	}

	public function getShopsWithoutDescriptionBlock(string $type): QueryBuilder
	{
		return $this->getShops()
			->leftJoin('s.descriptionBlocks', 'db', 'WITH', 'db.type = :type')
			->andWhere('db.description IS NULL OR TRIM(db.description) = :description')
			->setParameters([
				'type' => $type,
				'description' => '',
			]);
	}

	public function getCountOfShopsWithoutDeals(Localization $localization)
	{
		$qb = $this->getShops($localization)
				->select('s.id', 'COUNT(deal.id) AS dealsCount')
				->leftJoin('s.deals', 'deal', 'WITH', 'deal.shop = s.id AND deal.validTill >= :validTill AND deal.type != :type')
				->setParameter('validTill', new \DateTime())
				->setParameter('type', Deal::TYPE_COUPON)
				->having('dealsCount = 0')
				->groupBy('s.id');

		return $qb->getQuery()->getResult();
	}

	public function getCountOfShopsWithoutCoupons(Localization $localization)
	{
		$qb = $this->getShops($localization)
				->select('s.id', 'COUNT(deal.id) AS dealsCount')
				->leftJoin('s.deals', 'deal', 'WITH', 'deal.shop = s.id AND deal.validTill >= :validTill AND deal.type = :type')
				->setParameter('validTill', new \DateTime())
				->setParameter('type', Deal::TYPE_COUPON)
				->having('dealsCount = 0')
				->groupBy('s.id');

		return $qb->getQuery()->getResult();
	}

	public function findShopsReadyToProcessExternalData(int $limit = 5): array
	{
		return $this->getShopsWithoutDescriptionBlock(DescriptionBlock::TYPE_SHORT_DESCRIPTION)
			->leftJoin(ShopExternalData::class, 'ed', 'WITH', 'ed.shop = s')
			->andWhere('ed.shop IS NULL')
			->andWhere('s.cashbackAllowed = :active')
			->andWhere('s.active = :active')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
//			->andWhere('l.locale IN (:locales)')
//			->setParameter('locales', ['pl', 'cs'])
			->setParameter('active', true)
			->orderBy('s.localization', 'ASC')
			->setMaxResults($limit)
			->getQuery()
			->getResult();
	}

	public function findShopsReadyToGenerateDescription(string $type): array
	{
		return $this->getShopsWithoutDescriptionBlock($type)
			->select('s')
			->andWhere('s.cashbackAllowed = :active')
			->andWhere('s.active = :active')
			->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
//			->andWhere('l.locale IN (:locales)')
//			->setParameter('locales', ['pl', 'cs'])
			->setParameter('active', true)
			->orderBy('s.localization', 'ASC')
			->getQuery()
			->getResult();
	}

	public function findSortedShops(Localization $localization, string $sort, ?int $offset = null, ?int $limit = null): array
	{
		$queryBuilder = $this->getShops($localization);

		if ($sort === 'priority') {
			$queryBuilder->addOrderBy('s.priority', 'DESC');
		} elseif ($sort === 'created') {
			$queryBuilder
				->andWhere('s.active = true')
				->addOrderBy('s.publishedAt', 'DESC');
		}

		if ($offset !== null) {
			$queryBuilder->setFirstResult($offset);
		}

		if ($limit !== null) {
			$queryBuilder->setMaxResults($limit);
		}
		return $queryBuilder->getQuery()->getResult();
	}

	public function findRandomShopForLuckyShopPicker(Localization $localization)
	{
		return $this->getShops($localization)
			->andWhere('s.cashbackAllowed = true')
			->andWhere('s.active = true')
			->setMaxResults(64)
			->addOrderBy('s.priority', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findShopByPartnerSystemUrl(string $url): ?Shop
	{
		return $this->createQueryBuilder('s')
			->andWhere('s.partnerSystemRedirectUrl = :url')
			->setParameter('url', $url)
			->setMaxResults(1)
			->getQuery()->getOneOrNullResult();
	}
}
