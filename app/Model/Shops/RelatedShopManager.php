<?php

namespace tipli\Model\Shops;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Shops\Entities\RelatedShop;
use tipli\Model\Shops\Entities\Shop;

class RelatedShopManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function addRelatedShop(Shop $parentShop, Shop $relatedShop, $priority)
	{
		if ($parentShop->hasRelatedShop($relatedShop)) {
			return;
		}

		$relatedShop = new RelatedShop($parentShop, $relatedShop, $priority);

		$this->em->persist($relatedShop);
		$this->em->flush($relatedShop);
		$this->em->refresh($parentShop);
	}

	public function saveRelatedShop(RelatedShop $relatedShop)
	{
		$this->em->persist($relatedShop);
		$this->em->flush($relatedShop);
	}

	public function removeRelatedShop(RelatedShop $relatedShop)
	{
		$this->em->remove($relatedShop);
		$this->em->flush($relatedShop);
	}
}
