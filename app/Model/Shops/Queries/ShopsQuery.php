<?php

namespace tipli\Shops\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\Entities\ShopIndex;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Shops\Entities\ShopLeafletTag;
use tipli\Model\Shops\Entities\ShopSettings;
use tipli\Model\Shops\Entities\ShopTag;
use tipli\Model\Tags\Entities\Tag;

class ShopsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	/** @var bool */
	private $optimized = false;

	/**
	 * @param Localization $localization
	 * @return ShopsQuery
	 */
	public function withLocalization(Localization $localization)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($localization) {
			$queryBuilder->andWhere('s.localization = :localization')
				->setParameter('localization', $localization);
		};

		return $this;
	}

	/**
	 * @param int[] $ids
	 * @return ShopsQuery
	 */
	public function in(array $ids, $sortByField = false)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($ids, $sortByField) {
			$queryBuilder
				->andWhere('s.id IN (:ids)')
				->setParameter('ids', $ids);

			if ($sortByField) {
				$queryBuilder
					->addOrderBy('FIELD(s.id, :values)')
					->setParameter('values', $ids);
			}
		};

		return $this;
	}

	/**
	 * @param int[] $ids
	 * @return ShopsQuery
	 */
	public function notIn(array $ids)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($ids) {
			$queryBuilder
				->andWhere('s.id NOT IN (:ids)')
				->setParameter('ids', $ids);
		};

		return $this;
	}

	/**
	 * @param Tag[] $tags
	 * @return ShopsQuery
	 */
	public function withTags(array $tags)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($tags) {
			$queryBuilder
				->leftJoin('s.shopTags', 'st')
				->andWhere('st.tag IN (:tags)')
				->setParameter('tags', $tags)
				->groupBy('s.id')
				->addOrderBy('IFNULL (st.priority, s.priority)', 'DESC');
		};

		return $this;
	}

	public function withTag($tag)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($tag) {
			$queryBuilder
				->innerJoin(ShopTag::class, 'st', 'WITH', 'st.shop = s.id AND st.tag = :tag')
				->andWhere('st.tag = :tag')
				->setParameter('tag', $tag)
				->groupBy('s.id');
		};

		return $this;
	}

	public function withName(string $name)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($name) {
			$queryBuilder
				->andWhere('s.name = :name')
				->setParameter('name', $name);
		};

		return $this;
	}

	public function sortByTagPriority()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('IFNULL (st.priority, s.priority)', 'DESC');
		};

		return $this;
	}

	public function onlyCjShops()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->innerJoin(PartnerSystem::class, 'ps', 'WITH', 'ps.id = s.partnerSystem')
				->andWhere('ps.type = :type')
				->setParameter('type', 'cj');
		};

		return $this;
	}

	public function onlyWithLeafletTag()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->innerJoin(ShopLeafletTag::class, 'lt', 'WITH', 'lt.shop = s.id');
		};

		return $this;
	}

	public function sortAlphabetically()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.name', 'ASC');
		};

		return $this;
	}

	public function sortByUpdatedAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.updatedAt', 'DESC');
		};

		return $this;
	}

	public function sortBySitemapUpdatedAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.sitemapUpdatedAt', 'DESC');
		};

		return $this;
	}

	public function sortByFirstDescriptionAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('sd.firstDescriptionAt', 'DESC');
		};

		return $this;
	}

	public function sortByCreatedAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.createdAt', 'DESC');
		};

		return $this;
	}

	public function sortByShopCheckerProcessedAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->orderBy('sd.shopCheckerScheduledAt', 'ASC')
				->andWhere('sd.shopCheckerScheduledAt <= :now')->setParameter('now', new \DateTime());
		};

		return $this;
	}

	public function sortByTransactionCheckerProcessAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->orderBy('sd.transactionCheckerScheduledAt', 'ASC')
				->andWhere('sd.transactionCheckerScheduledAt <= :now')
				->setParameter('now', new \DateTime());
		};

		return $this;
	}

	public function withFilledAverageTransactionRegistrationPeriod()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.averageTransactionRegistrationPeriod IS NOT NULL');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyPublished(bool $includeActive = true)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($includeActive) {
			$queryBuilder
				->andWhere('s.visible = 1')
				->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()');

			if ($includeActive) {
				$queryBuilder->andWhere('s.active = 1');
			}
		};

		return $this;
	}

	public function onlyWithShowedDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('sd.showDeals = :showDeals')
				->setParameter('showDeals', true);
		};

		return $this;
	}

	public function onlyWithShowedLeaflets()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('sd.showLeaflets = :showLeaflets')
				->setParameter('showLeaflets', true);
		};

		return $this;
	}

	public function onlyWithSomeDealsOrCoupons()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfCouponDeals > 0 OR sd.countOfDeals > 0');
		};

		return $this;
	}

	public function onlyWithRequiredDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfRequiredDeals > 0');
		};

		return $this;
	}

	public function onlyWithDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfDeals > 0');
		};

		return $this;
	}

	public function onlyWithoutDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfDeals = 0');
		};

		return $this;
	}

	public function onlyWithCoupons()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfCouponDeals > 0');
		};

		return $this;
	}

	public function onlyWithoutCoupons()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfCouponDeals = 0');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyWithSomeCoupons()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.countOfCouponDeals > 0');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function sortBoosted(): ShopsQuery
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.boostCoupons', 'DESC');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyActive()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.active = 1');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyVisible()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.visible = 1');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyPaused()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('sd.pausedAt < CURRENT_TIMESTAMP()');
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function exceptPaused()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('(sd.pausedAt > CURRENT_TIMESTAMP() or sd.pausedAt IS NULL)');
		};

		return $this;
	}

	public function forShopChecker(): static
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->leftJoin('s.partnerSystem', 'ps')
				->andWhere('(sd.pausedAt IS NULL OR sd.pausedByShopChecker = true)')
				->andWhere('ps.shopCheckerAllowed = true')
				->andWhere('sd.shopCheckerScheduledAt <= :now')
				->setParameter('now', new \DateTime())
			;
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyForAddon()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->leftJoin('s.shopSettings', 'ss')
				->andWhere('ss.addonFeedDisabledAt IS NULL')
				->andWhere('ss.addonVisibility = :public')
				->setParameter('public', ShopSettings::ADDON_VISIBILITY_PUBLIC)
				->andWhere('s.active = 1')
				->andWhere('s.publishedAt <= CURRENT_TIMESTAMP()')
				->andWhere("s.domain != ''")
			;
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function onlyForNewAddon()
	{
		$this->onlyActive();
		$this->onlyPublished();
		$this->exceptPaused();

//		$this->filters[] = function (QueryBuilder $queryBuilder) {
//			$queryBuilder->andWhere('sd.countOfCouponDeals > 0 OR s.cashbackAllowed = true');
//		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function sortTop()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.priority', 'DESC');
		};

		return $this;
	}

	public function sortByOrganicTrafficInLast14Days()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->innerJoin('s.pageExtension', 'pe');
			$queryBuilder->addOrderBy('pe.organicLast14Days', 'DESC');
		};

		return $this;
	}

	public function sortByCommissionAmountByLast30days()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('sd.totalCommissionAmountInLast30Days', 'DESC');
		};

		return $this;
	}

	public function sortByCountOfRequiredDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('sd.countOfRequiredDeals', 'DESC');
		};

		return $this;
	}

	public function sortByGoogleSearches()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->innerJoin('s.pageExtension', 'pe');
			$queryBuilder->addOrderBy('pe.googleSearchesAvg', 'DESC');
		};

		return $this;
	}

	public function sortByLeafletsPriority()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->orderBy('s.priorityLeaflets', 'DESC');
		};

		return $this;
	}

	public function onlyShops(array $shops)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shops) {
			if (!empty($shops)) {
				$queryBuilder->andWhere('s IN (:shops)')
					->setParameter('shops', $shops);
			}
		};

		return $this;
	}

	public function exceptShops(array $shops)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shops) {
			if (!empty($shops)) {
				$queryBuilder->andWhere('s NOT IN (:shops)')
					->setParameter('shops', $shops);
			}
		};

		return $this;
	}

	public function exceptGamble(): static
	{
		$tagId = Tag::GAMBLE_TAG_ID;
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($tagId) {
			$queryBuilder->leftJoin(ShopTag::class, 'st', 'WITH', 'st.shop = s.id AND st.tag = :tag')
				->andWhere('st.tag IS NULL OR st.tag != :tag')
				->setParameter('tag', $tagId);
		};

		return $this;
	}

	public function exceptShop($shop)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shop) {
			$queryBuilder->andWhere('s != :shop')
				->setParameter('shop', $shop);
		};

		return $this;
	}

	/**
	 * @return ShopsQuery
	 */
	public function sortNewest()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.publishedAt', 'DESC');
		};

		return $this;
	}

	public function optimized()
	{
		$this->optimized = true;

		return $this;
	}

	public function onlyWithCashbackAllowed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.cashbackAllowed = true');
		};

		return $this;
	}

	public function onlyWithCashbackDisabled()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.cashbackAllowed = false');
		};

		return $this;
	}

	public function sortWithCashbackFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.cashbackAllowed', 'DESC');
		};

		return $this;
	}

	public function sortByIds(array $ids = [])
	{
		if (!empty($ids)) {
			$this->filters[] = static function (QueryBuilder $queryBuilder) use ($ids) {
				$queryBuilder->addOrderBy('FIELD(s.id,' . implode(",", $ids) . ')', 'DESC');
			};
		}
		return $this;
	}

	public function onlyWithSomeLeaflets()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
//			$queryBuilder->innerJoin(Leaflet::class, 'l', 'WITH', 'l.shop = s.id');
			$queryBuilder->andWhere('sd.countOfLeaflets > 0');
		};

		return $this;
	}

	public function onlyWithSomeActiveBanners()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->innerJoin(Banner::class, 'b');
			$queryBuilder->innerJoin('b.shops', 'shops', 'WITH', 'shops = s.id');
			$queryBuilder->andWhere('b.validSince <= CURRENT_TIMESTAMP()');
			$queryBuilder->andWhere('b.validTill >= CURRENT_TIMESTAMP()');
		};

		return $this;
	}

	public function search($term)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($term) {
			$queryBuilder->andWhere('s.name LIKE :term')->setParameter('term', '%' . $term . '%');
		};

		return $this;
	}

	public function onlyWithSomeActiveLeaflets()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->innerJoin(Leaflet::class, 'l', 'WITH', 'l.shop = s.id');
			$queryBuilder->andWhere('l.validSince <= :datetime')
				->andWhere('l.validTill >= :datetime')->setParameter('datetime', new \DateTime())
				->andWhere('l.confirmedAt IS NOT NULL')
				->andWhere('l.type IS NULL OR l.type != :newsletter')->setParameter('newsletter', Leaflet::TYPE_NEWSLETTER)
				->andWhere('l.type IS NULL OR l.type != :screenshot')->setParameter('screenshot', Leaflet::TYPE_SCREENSHOT)
				->andWhere('l.validTill > :date')->setParameter('date', (new \DateTime())->modify('- 9 days'))
				->andWhere('l.primary = true')
				->andWhere('l.visible = true');
		};

		return $this;
	}

	public function onlyIndexable()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->leftJoin(ShopIndex::class, 'si', 'WITH', 'si.shop = s')
				->andWhere('si.id IS NOT NULL');
		};

		return $this;
	}

	public function notOnlyLeaflet()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('sd.onlyLeafletShop = 0');
		};

		return $this;
	}

	public function withProducts($minCountOfProducts = 1): static
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($minCountOfProducts) {
			$queryBuilder->andWhere('sd.countOfValidProducts >= :minCountOfProducts')
				->setParameter('minCountOfProducts', $minCountOfProducts);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('s', 's.id')
			->leftJoin('s.shopData', 'sd')->addSelect('sd')
		;

		if ($this->optimized) {
			$queryBuilder->select('PARTIAL s.{id, slug, name, logo, svgLogo, cashbackAllowed, partnerSystemRedirectUrl}, sd');
		} else {
			$queryBuilder->select('s, sd');
		}

		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		$queryBuilder->addOrderBy('s.id');

		return $queryBuilder;
	}

	public function postFetch(Queryable $repository, \Iterator $iterator)
	{
		$ids = array_keys(iterator_to_array($iterator, true));

		$repository->createQueryBuilder('s')
			->select('PARTIAL s.{id}, o, t, sd')
			->leftJoin('s.offers', 'o')
			->leftJoin('s.shopTags', 't')
			->leftJoin('s.shopData', 'sd')
			->andWhere('s.id IN (:ids)')->setParameter('ids', $ids)
			->getQuery()->getResult();
	}

	public function sortByCountOfDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('sd.countOfDeals', 'DESC');
		};

		return $this;
	}

	public function sortByCountOfCouponDeals()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('sd.countOfCouponDeals', 'DESC');
		};

		return $this;
	}

	public function withTwistoAllowed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->leftJoin('s.shopSettings', 'ss')
				->andWhere('ss.twistoDisabledAt IS NULL');
		};

		return $this;
	}

	public function withRondoAllowed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->leftJoin('s.shopSettings', 'ss')
				->andWhere('ss.rondoDisabledAt IS NULL');
		};

		return $this;
	}

	public function withHomeCreditAllowed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->leftJoin('s.shopSettings', 'ss')
				->andWhere('ss.homecreditDisabledAt IS NULL');
		};

		return $this;
	}

	public function withTypes(array $types = [])
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($types) {

			if (empty($types) === false) {
				$queryBuilder->andWhere('s.type IN (:types)')
					->setParameter('types', $types);
			}
		};

		return $this;
	}
}
