<?php

namespace tipli\Model\Shops;

use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\EntityManager;
use Nette\SmartObject;
use tipli\Model\Doctrine\QueryObject\ResultSet;
use tipli\Model\Shops\Repositories\RedirectionRepository;
use tipli\Shops\Queries\RedirectionsQuery;

class RedirectionFacade
{
	use SmartObject;

	public function __construct(
		private readonly EntityManager $em,
		private readonly RedirectionRepository $shopRedirectionRepository
	) {
	}

	public function find($id)
	{
		return $this->shopRedirectionRepository->find($id);
	}

	public function fetch(RedirectionsQuery $redirectionsQuery): array|ResultSet
	{
		return $this->shopRedirectionRepository->fetch($redirectionsQuery);
	}

	public function createRedirectionsQuery(User $user = null): RedirectionsQuery
	{
		$redirectionsQuery = new RedirectionsQuery();

		if ($user) {
			$redirectionsQuery->withUser($user);
		}

		return $redirectionsQuery;
	}
}
