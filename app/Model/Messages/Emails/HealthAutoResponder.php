<?php

namespace tipli\Model\Messages\Emails;

use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\RawEmail;
use DateTime;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\TagFacade;

interface IHealthAutoResponderFactory
{
	/**
	 * @return HealthAutoResponder
	 */
	public function create();
}

class HealthAutoResponder
{
	private const HEALTH_TAG_IDS = [
		1 => 730,
		2 => 753,
		3 => 162,
		4 => 1094,
		6 => 1121,
	];

	/** @var ShopFacade */
	private $shopFacade;

	/** @var TagFacade */
	private $tagFacade;

	public function __construct(ShopFacade $shopFacade, TagFacade $tagFacade)
	{
		$this->shopFacade = $shopFacade;
		$this->tagFacade = $tagFacade;
	}

	public function createRawEmail(User $user)
	{
		$localization = $user->getLocalization();

		$tag = $this->tagFacade->find(self::HEALTH_TAG_IDS[$localization->getId()]);

		if (!$tag) {
			throw new InvalidArgumentException('Unknown tag');
		}

		$shopsQuery = $this->shopFacade->createShopsQuery($user->getLocalization())
			->onlyPublished()
			->exceptPaused()
			->onlyWithCashbackAllowed()
			->withTag($tag)
		;

		$countOfCashbackShopsQuery = $this->shopFacade->createShopsQuery($user->getLocalization())
			->onlyPublished()
			->exceptPaused()
			->onlyWithCashbackAllowed();

		$countOfCashbackShops = $this->shopFacade->fetch($countOfCashbackShopsQuery)->getTotalCount();

		$params = [
			'contentType' => SendingPolicy::CONTENT_TYPE_AUTORESPONDER,
			'user' => $user,
			'shops' => $this->shopFacade->fetch($shopsQuery)->applyPaging(0, 4)->toArray(),
			'countOfShops' => $countOfCashbackShops,
			'tag' => $tag,
		];

		return new RawEmail(
			$localization,
			__CLASS__ . '.' . $user->getId() . ($user->isAdmin() ? rand(0, 9999) : ''),
			$user,
			['email.health.title'],
			'2021/health.mjml',
			$params,
			RawEmail::UTM_SOURCE_AUTORESPONDER,
			RawEmail::UTM_MEDIUM,
			'health',
			$user,
			SendingPolicy::CONTENT_TYPE_AUTORESPONDER,
			(new DateTime())->setTime(rand(7, 17), rand(0, 59), rand(0, 59)),
			['email.general.fromEmail'],
			['email.general.fromName'],
			Email::PRIORITY_LOW
		);
	}
}
