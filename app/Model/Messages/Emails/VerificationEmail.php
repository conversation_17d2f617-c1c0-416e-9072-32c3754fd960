<?php

namespace tipli\Model\Messages\Emails;

use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\RawEmail;

interface IVerificationEmailFactory
{
	/** @return VerificationEmail */
	public function create();
}

class VerificationEmail
{
	public function createRawEmail(User $user)
	{
		$isAfterRegistration = ((new \DateTime())->getTimestamp() - $user->getCreatedAt()->getTimestamp()) < 60;

		$params = [
			'contentType' => SendingPolicy::CONTENT_TYPE_NOTIFICATION,
			'user' => $user,
			'isAfterRegistration' => $isAfterRegistration,
		];

		return new RawEmail(
			$user->getLocalization(),
			$isAfterRegistration ? (__CLASS__ . '.' . $user->getId()) : null,
			$user,
			['email.verificationEmail.subject'],
			'verificationEmail.mjml',
			$params,
			null,
			null,
			'verification',
			$user,
			SendingPolicy::CONTENT_TYPE_NOTIFICATION,
			null,
			['email.verificationEmail.fromEmail'],
			['email.general.fromName'],
			Email::PRIORITY_SEND_ALWAYS
		);
	}
}
