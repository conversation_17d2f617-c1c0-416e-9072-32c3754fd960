<?php

namespace tipli\Model\Messages\Emails;

use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Campaign\CampaignAvailableBonusResolver;
use tipli\Model\Campaign\Entities\CampaignSubscription;
use tipli\Model\Currencies\CurrencyFilter;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\RawEmail;
use tipli\Model\Messages\Entities\UserAutoResponder;
use tipli\Model\Transactions\AmountFilter;
use tipli\Model\Transactions\Entities\Transaction;

class CampaignAutoResponderEmail
{
	/** @var CampaignAvailableBonusResolver */
	private $campaignAvailableBonusResolver;

	/** @var AmountFilter */
	private $amountFilter;

	/** @var CurrencyFilter */
	private $currencyFilter;

	private const CAMPAIGN_SUBSCRIPTION_TEMPLATE_TYPES = [
		UserAutoResponder::TYPE_CAMPAIGN_FINISHED => 'campaignFinished',
		UserAutoResponder::TYPE_CAMPAIGN_BONUS_CREATED_NEW_USER => 'campaignBonusCreatedNewUser',
		UserAutoResponder::TYPE_CAMPAIGN_BONUS_REMAINING => 'campaignBonusRemaining',
	];

	public function __construct(CampaignAvailableBonusResolver $campaignAvailableBonusResolver, AmountFilter $amountFilter, CurrencyFilter $currencyFilter)
	{
		$this->campaignAvailableBonusResolver = $campaignAvailableBonusResolver;
		$this->amountFilter = $amountFilter;
		$this->currencyFilter = $currencyFilter;
	}

	public function createRawEmail(CampaignSubscription $campaignSubscription, $type, ?Transaction $transaction = null, ?Transaction $bonusTransaction = null): RawEmail
	{
		$user = $campaignSubscription->getUser();

		$params = [
			'user' => $user,
			'transaction' => $transaction,
			'bonusTransaction' => $bonusTransaction,
		];

		$templateType = self::CAMPAIGN_SUBSCRIPTION_TEMPLATE_TYPES[$type];

		$subjectParams = [];
		$subject = null;

		if ($type === UserAutoResponder::TYPE_CAMPAIGN_BONUS_CREATED_NEW_USER) {
			$availableBonus = $this->campaignAvailableBonusResolver->getAvailableBonus($user);
			$params['availableBonus'] = $availableBonus;
			$params['shop'] = $transaction->getShop();

			$amount = $this->amountFilter->__invoke($transaction->getUserCommissionAmount());
			$bonusAmount = $this->amountFilter->__invoke($bonusTransaction->getBonusAmount());
			$currency = $this->currencyFilter->__invoke($bonusTransaction->getCurrency());

			if ($availableBonus) {
				$subjectParams = ['amount' => $amount, 'bonusAmount' => $bonusAmount, 'currency' => $currency];
			} else {
				$subject = ['email.campaign.campaignBonusCreatedNewUser.finishedTitle'];
			}
		} elseif ($type === UserAutoResponder::TYPE_CAMPAIGN_BONUS_REMAINING) {
			$availableBonus = $this->campaignAvailableBonusResolver->getAvailableBonus($user);

			$params['availableBonus'] = $availableBonus;
			$params['usedBonus'] = $this->campaignAvailableBonusResolver->getUsedBonus($user);

			$subjectParams = ['amount' => $this->amountFilter->__invoke($availableBonus) . ' ' . $this->currencyFilter->__invoke($user->getCurrency())];
		} elseif ($type === UserAutoResponder::TYPE_CAMPAIGN_FINISHED) {
			$params['usedBonus'] = $this->campaignAvailableBonusResolver->getUsedBonus($user);

			$subjectParams = ['bonusAmount' => $params['usedBonus']];
		}

		$params['version'] = '2';

		return new RawEmail(
			$user->getLocalization(),
			__CLASS__ . $type . '.' . $user->getId() . '.' . ($transaction ? $transaction->getId() : 0),
			$user,
			$subject ?: ['email.campaign.' . $templateType . '.subject', $subjectParams],
			'campaign/' . $templateType . '.mjml',
			$params,
			RawEmail::UTM_SOURCE_NOTIFICATION,
			RawEmail::UTM_MEDIUM,
			$user->getLocale() . '-' . $type,
			$user,
			SendingPolicy::CONTENT_TYPE_NOTIFICATION,
			null,
			['email.general.fromEmail'],
			['email.general.fromName'],
			Email::PRIORITY_LOW
		);
	}
}

interface ICampaignAutoResponderEmailFactory
{
	/** @return CampaignAutoResponderEmail */
	public function create(): CampaignAutoResponderEmail;
}
