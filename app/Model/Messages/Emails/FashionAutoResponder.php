<?php

namespace tipli\Model\Messages\Emails;

use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Entities\RawEmail;
use DateTime;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\TagFacade;

interface IFashionAutoResponderFactory
{
	/**
	 * @return FashionAutoResponder
	 */
	public function create();
}

class FashionAutoResponder
{
	private const FASHION_TAG_IDS = [
		1 => 736,
		2 => 750,
		3 => 127,
		4 => 1092,
		6 => 1119,
	];

	/** @var ShopFacade */
	private $shopFacade;

	/** @var TagFacade */
	private $tagFacade;

	public function __construct(ShopFacade $shopFacade, TagFacade $tagFacade)
	{
		$this->shopFacade = $shopFacade;
		$this->tagFacade = $tagFacade;
	}

	public function createRawEmail(User $user)
	{
		$localization = $user->getLocalization();

		$tag = $this->tagFacade->find(self::FASHION_TAG_IDS[$localization->getId()]);

		if (!$tag) {
			throw new InvalidArgumentException('Unknown tag');
		}

		$shopsQuery = $this->shopFacade->createShopsQuery($user->getLocalization())
			->onlyPublished()
			->exceptPaused()
			->onlyWithCashbackAllowed()
			->withTag($tag)
		;

		$countOfCashbackShopsQuery = $this->shopFacade->createShopsQuery($user->getLocalization())
			->onlyPublished()
			->exceptPaused()
			->onlyWithCashbackAllowed();

		$countOfCashbackShops = $this->shopFacade->fetch($countOfCashbackShopsQuery)->getTotalCount();

		$params = [
			'contentType' => SendingPolicy::CONTENT_TYPE_AUTORESPONDER,
			'user' => $user,
			'shops' => $this->shopFacade->fetch($shopsQuery)->applyPaging(0, 4)->toArray(),
			'countOfShops' => $countOfCashbackShops,
			'tag' => $tag,
		];

		return new RawEmail(
			$localization,
			__CLASS__ . '.' . $user->getId() . ($user->isAdmin() ? rand(0, 9999) : ''),
			$user,
			['email.fashion.title'],
			'2021/fashion.mjml',
			$params,
			RawEmail::UTM_SOURCE_AUTORESPONDER,
			RawEmail::UTM_MEDIUM,
			'fashion',
			$user,
			SendingPolicy::CONTENT_TYPE_AUTORESPONDER,
			(new DateTime())->setTime(rand(7, 17), rand(0, 59), rand(0, 59)),
			['email.general.fromEmail'],
			['email.general.fromName'],
			Email::PRIORITY_LOW
		);
	}
}
