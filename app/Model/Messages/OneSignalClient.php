<?php

namespace tipli\Model\Messages;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use tipli\Model\Localization\Entities\Localization;

class OneSignalClient
{
	private const API_URL = 'https://onesignal.com/api/v1/';

	/** @var array */
	private $credentials;

	public function __construct(array $credentials)
	{
		$this->credentials = $credentials;
	}

	public function getNotifications(Localization $localization, int $offset = 0, int $limit = 50)
	{
		$notifications = [];

		$response = Json::decode($this->request($localization, 'notifications', [
			'offset' => $offset,
			'limit' => $limit,
		])->getBody()->getContents());

		foreach ($response->notifications as $notification) {
			$notifications[] = new OneSignalNotificationObject(
				$notification->id,
				$notification->headings->en,
				$notification->url,
				$notification->contents->en,
				$notification->converted,
				$notification->successful,
				$notification->failed,
				$notification->remaining,
				(new \DateTime())->setTimestamp($notification->queued_at),
				$notification->completed_at ? (new \DateTime())->setTimestamp($notification->completed_at) : null
			);
		}

		return $notifications;
	}

	private function request(Localization $localization, string $url, $params = [])
	{
		$credentials = $this->credentials[$localization->getLocale()];

		$url = new Url(self::API_URL . $url);
		$url->setQueryParameter('app_id', $credentials['appId']);

		if ($params) {
			foreach ($params as $key => $value) {
				$url->setQueryParameter($key, $value);
			}
		}

		$client = new Client();

		return $client->request('GET', $url->getAbsoluteUrl(), [
				'headers' => [
					'Authorization' => 'Basic ' . $credentials['apiKey'],
				]]);
	}
}
