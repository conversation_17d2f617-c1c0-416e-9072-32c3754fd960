<?php

namespace tipli\Model\Messages;

use tipli\Model\Doctrine\EntityManager;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\SmartObject;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidEmailSyntaxException;
use tipli\MjmlConvertErrorException;
use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\Messages\EmailMessage;
use tipli\Model\Messages\Producers\SendEmailProducer;
use tipli\Model\Messages\Queries\EmailsQuery;
use tipli\Model\Messages\Repositories\EmailRepository;
use tipli\Model\Queues\QueueFacade;
use Tracy\Debugger;

class EmailManager
{
	use SmartObject;

	private Cache $cache;

	public function __construct(
		private EmailRouter $emailRouter,
		private EntityManager $em,
		private EmailRepository $emailRepository,
		private SendingPolicyManager $sendingPolicyManager,
		private MjmlConverter $mjmlConverter,
		private Storage $storage,
		private BlockedEmailFacade $blockedEmailFacade,
		private EmailSubscriptionManager $emailSubscriptionManager,
		private DatadogProducer $datadogProducer,
		private SendEmailProducer $sendEmailProducer,
		private QueueFacade $queueFacade
	) {
		$this->cache = new Cache($storage, self::class);
	}

	public function sendEmail(EmailMessage $emailMessage): void
	{
		$this->emailRouter->sendEmail($emailMessage);

		$sentAt = (new \DateTime())->format('Y-m-d H:i:s');

		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_messages_email SET sent_at = ? WHERE sent_at IS NULL AND id = ?', [$sentAt, $emailMessage->getEmailId()]);

		$this->datadogProducer->scheduleSendEvent('sendEmail');
	}

	private function convertEmailHtmlBody(Email $email): void
	{
		if ($email->isMjml()) {
			$email->setBody($this->mjmlConverter->mjmlToHtml($email->getBody()));
			$this->saveEmail($email);
		} else {
			$email->setBody($email->getBody());
		}
	}

	public function scheduleSendEmail(Email $email): void
	{
		if ($this->sendingPolicyManager->isAllowedToSendEmail($email) === false) {
			return;
		}

		if ($email->isEnqueued()) {
			return;
		}

		$this->saveEmail($email);

		if ($email->isScheduled() && $email->getScheduledAt() >= new \DateTime()) {
			return;
		}

		if (
			$this->sendingPolicyManager->isBanned($email)
			|| $this->blockedEmailFacade->isEmailBlocked($email->getToEmail())
			|| Strings::contains($email->getToEmail(), '@mobiletest.com')
		) {
			$this->abortEmail($email);

			return;
		}

		if ($email->isOlderThan3Days()) {
			$this->abortEmail($email);

			Debugger::log($email->getId(), 'old-email-send-error');

			return;
		}

		try {
			// kontrola na duplicitu a prevod z MJML
			$this->checkEmailDuplicity($email);
			$this->convertEmailHtmlBody($email);

			if ($this->cache->load($email->getId())) {
				return;
			}

			$this->cache->save($email->getId(), true);

			$emailMessage = new EmailMessage(
				$email->getId(),
				$email->getFromEmail(),
				$email->getToEmail(),
				$email->getFromName(),
				$email->getUser()?->getFullName(),
				$email->getSubject(),
				$email->getBody(),
				$email->getType(),
				$email->getPriority(),
				$email->getCreatedAt(),
				$email->getCampaign()
			);

			if ($this->sendingPolicyManager->isAllowedToSendEmailImmediately($email)) {
				$this->sendEmail($emailMessage);

				$email->send();
			} else {
				$this->sendEmailProducer->scheduleSendEmail($emailMessage);

				$email->enqueue();
			}

			$this->saveEmail($email);
		} catch (InvalidEmailSyntaxException $e) {
			Debugger::log($e->getMessage(), 'email-syntax-error');
			Debugger::log($email->getId() . '####' . $email->getType() . '####' . $email->getBody() . ' ### ' . $e->getMessage(), 'email-syntax-error');

			$this->abortEmail($email);

			#if ($user = $email->getUser()) {
			#	$this->emailSubscriptionManager->unsubscribe($user, null, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_APPLICATION, 'EmailManager: invalid email syntax');
			#}
		} catch (MjmlConvertErrorException $e) {
			Debugger::log('Email ' . $email->getId() . ' ### ' . $e->getMessage(), 'mjml-server-errors');
			return;
		} catch (InvalidArgumentException $e) {
			Debugger::log($e->getMessage(), 'email-errors');
			Debugger::log($e->getMessage(), 'emails');

			if (Strings::contains($e->getMessage(), 'duplicity error')) {
				$this->abortEmail($email);
			}

			return;
		}
	}

	public function sendScheduledEmails(): void
	{
		$emailsQuery = $this->createEmailsQuery()
			->onlyScheduled()
			->withoutSent()
			->withoutEnqueued()
			->topPriorityFirst()
		;

		$emails = $this->fetch($emailsQuery)
			->applyPaging(0, 100);

		foreach ($emails as $email) {
			if ($this->sendingPolicyManager->isAllowedToSendEmail($email) === false) {
				$this->abortEmail($email);

				continue;
			}

			$this->scheduleSendEmail($email);
		}
	}

	private function saveEmail(Email $email)
	{
		$this->em->persist($email);
		$this->em->flush($email);
	}

	private function checkEmailDuplicity(Email $email)
	{
		if ($email->getProtectionDuplicityHash() && count($this->emailRepository->findBy(['protectionDuplicityHash' => $email->getProtectionDuplicityHash()])) > 1) {
			throw new InvalidArgumentException('Email id ' . $email->getId() . ' - duplicity error.');
		}
	}

	public function getSentEmails()
	{
		return $this->emailRepository->getSentEmails();
	}

	public function find($id)
	{
		return $this->emailRepository->find($id);
	}

	public function createEmailsQuery()
	{
		return new EmailsQuery();
	}

	public function fetch(EmailsQuery $query)
	{
		return $this->emailRepository->fetch($query);
	}

	public function abortEmail(Email $email)
	{
		$email->abort();
		$this->saveEmail($email);
	}

	public function clearEmailUserData(Email $email)
	{
		$email->clearUserData();

		if ($email->isScheduled()) {
			$email->abort();
		}

		$this->saveEmail($email);
	}
}
