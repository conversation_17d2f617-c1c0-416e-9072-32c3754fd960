{layout '@layout202010.mjml'}

{var $headerImageFilename = 'purchase.jpg'}

{block title}{_'email.transactionRecommendationBonusRegistrationEmail.title' |noescape}{/block}

{block content}

{capture $amount}{$transaction->getAmount() |amount |noescape}&nbsp;{$transaction->getUser()->getLocalization()->getCurrency() |currency |noescape}{/capture}

<mj-section padding="0 10px">
    <mj-column>
        <mj-text padding="35px 0 0px 0" font-size="35px" font-weight="700" line-height="45px" color="#000">
          {_'email.transactionRecommendationBonusRegistrationEmail.title' |noescape}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section padding="0 10px">
    <mj-column>
        <mj-text padding="20px 0 0 0" font-size="16px" font-weight="400" line-height="22px" color="#656565">
            <p>
                {if $user->getVocalFirstName()}
                    {_'email.general.welcomeWithName', [name => $user->getVocalFirstName()] |noescape}
                {else}
                    {_'email.general.welcome' |noescape}
                {/if}
            </p>
            <p>
                {_'email.transactionRecommendationBonusRegistrationEmail.welcomeText', ['recommendedUserName' => $transaction->getRelatedRecommendedUser()->getUserName(), 'amount' => $amount] |noescape}
            </p>
            <p>
                {_'email.transactionRecommendationBonusRegistrationEmail.bonusTresholdDescription1', ['recommenderTreshold' => ($transaction->getRecommendationBonusTreshold() |amount)] |noescape}
            </p>
            <p>
                {_'email.transactionRecommendationBonusRegistrationEmail.bonusTresholdDescription2', ['confirmationTreshold' => ($transaction->getConfirmationTreshold() |amount)] |noescape}
            </p>
        </mj-text>
    </mj-column>
</mj-section>

<mj-section padding="0 10px">
    <mj-column width="100%">
        <mj-button align="center" background-color="#EF7F1A" color="#fff" font-size="16px" font-weight="700" href="{_'front.links.account.tellFriend' |noescape}" padding="0" inner-padding="20px 40px">
            {_'email.transactionRecommendationBonusRegistrationEmail.cta'}
        </mj-button>
    </mj-column>
</mj-section>
