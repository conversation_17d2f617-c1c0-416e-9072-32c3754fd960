{var $collection = $block->getOption('dealCollection') ? $block->getOption('dealCollection') : null}
{if $collection === null || isset($collections[$collection]) === false}
{var $deals = $block->getData()}
{else}
{var $deals = $collections[$collection]}
{/if}

{foreach $block->getData() as $deal}
<mj-section background-color="#fff" padding="8px" border="0px solid">
	<mj-column width="18%" vertical-align="middle">
		<mj-image align="left" alt="$deal->name" padding="8px" width="90px" src="{$deal->imageUrl}" target="_blank"
			href="{$deal->detailUrl}"></mj-image>
	</mj-column>
	<mj-column width="49%" vertical-align="middle" padding="8px 16px">
		<mj-text font-size="16px" line-height="1.3" color="#080b10" padding="0px"><a href="{$deal->detailUrl}"
				style="color: #080b10; text-decoration: none">
				{if isset($deal->highlightedName)}
				{$deal->highlightedName|noescape} » {else}
				{$deal->name|dealName|noescape} » {/if}
			</a></mj-text>

		<mj-text css-class="deal" font-size="14px" font-weight="600" line-height="1.3" color="#66b940"
			padding="5px 0 0 0">
			{if isset($deal->reward)}
				{$deal->reward|noescape}
			{else}
				{$deal->description |striptags|truncate:65|noescape}
			{/if}
		</mj-text>
	</mj-column>
	{if $deal->type === "coupon"}
	<mj-column width="33%" vertical-align="middle">
		<mj-text padding="0">
			<a href="{$deal->detailUrl}" style="
					display:inline-block;
					width:160px;
					height:51px;
					line-height:51px;
					text-align:left;
					background:url('{$baseUrl|noescape}/images/coupon-bg.png')
					no-repeat center;
					background-size: 100% 100%;
					background-repeat: no-repeat;
					color:white;
					text-decoration:none;
					font-weight:bold;
					background-color: #f69028;
					border-radius: 12px;
					font-size: 16px;
					padding-left:20px">
				{_'newFront.deals.deal.getCode'}
			</a>
		</mj-text>
	</mj-column>
	{else}
	<mj-column width="33%" vertical-align="middle">
		<mj-text padding="0">
			<a href="{$deal->detailUrl}" class="gradient-button" style="
					display:inline-block;
					width:180px;
					height:51px;
					line-height:51px;
					color:white;
					text-decoration:none;
					font-weight:bold;
					background-image: linear-gradient(#f69028, #f69028);
					border-radius: 12px;
					font-size: 16px;
					text-align:center">
				{_'newFront.deals.deal.getSale'}
			</a>
		</mj-text>
	</mj-column>
	{/if}
</mj-section>

{*
<mj-section background-color="#fff" padding="0">
	<mj-column width="20%" vertical-align="middle">
		<mj-image align="left" alt="{$deal->name}" padding="0" width="90px" src="{$deal->imageUrl}" target="_blank"
			href="{$deal->detailUrl}"></mj-image>
	</mj-column>

	<mj-column width="80%" vertical-align="middle">
		<mj-text font-size="18px" line-height="1.3" color="#080b10" padding="0">
			<a href="{$deal->detailUrl}" style="color: #080b10; text-decoration: none">
				{if isset($deal->highlightedName)}
				{$deal->highlightedName|noescape} » {else}
				{$deal->name|dealName|noescape} » {/if}
			</a>
		</mj-text>

		<mj-text n:ifset="$deal->code" font-size="18px" line-height="1.3" color="#080b10" padding="5px 0 0 0">
			<strong>{_'front.deals.deal.coupon'}:</strong>
			<a href="{$deal->detailUrl}" style="text-decoration: none; color: #ef7f1a">
				<strong style="
						color: #ef7f1a;
						background-color: #fef3e9;
						padding: 5px 10px;
						border-radius: 12px;
					">
					{$deal->code}
				</strong>
			</a>
		</mj-text>

		<mj-text css-class="deal" font-size="18px" line-height="1.3" color="#66b940" padding="5px 0 0 0">
			{if isset($deal->reward)}
				{$deal->reward|noescape}
			{else}
				{$deal->description |striptags|truncate:65|noescape}
			{/if}
		</mj-text>
	</mj-column>
</mj-section>
*}

<mj-section padding="10px 0 10px 0" n:if="!$iterator->last">
	<mj-column padding="0">
		<mj-divider padding="0" border-color="#f0f0f0" border-width="1px"></mj-divider>
	</mj-column>
</mj-section>
{/foreach}

<mj-section padding="20px 0 0 0" n:if="$block->getCtaLink()" background-color="#fff">
	<mj-column width="100%">
		<mj-button css-class="gradient-button" align="center" background-color="#EF7F1A" border="none" color="#FFFFFF"
			font-size="18px" font-weight="700" href="{$block->getCtaLink()}" inner-padding="15px 45px"
			border-radius="12px" padding="0">
			{$block->getCtaText()}
		</mj-button>
	</mj-column>
</mj-section>
