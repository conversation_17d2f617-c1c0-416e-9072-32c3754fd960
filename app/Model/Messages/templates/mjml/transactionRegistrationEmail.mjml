{layout '@layout202010.mjml'}

{var $headerImageFilename = 'piggybank.jpg'}

{block title}{_'email.transactionRegistrationEmail.title' |noescape}{/block}

{block content}

{capture $amount}{$transaction->getAmount() |amount:2, ',', true, true}&nbsp;{$transaction->getCurrency() |currency}{/capture}
{capture $shopLogo}{($transaction->getShop()->getLogo() |image: 150, 80, 'fitMiddle')}{/capture}

<mj-section padding="0 10px">
    <mj-column>
        <mj-text padding="35px 0 0" font-size="35px" font-weight="700" line-height="45px" color="#000">
            {_'email.transactionRegistrationEmail.title'}
            <strong style="color:#ef7f1a" n:if="$addonBonus">
                <br n:if="!$localization->isCzech()">
                {_'email.transactionRegistrationEmail.addonBonus.titleAffix', ['addonBonusAmount' => $addonBonusAmount]}
            </strong>
        </mj-text>
    </mj-column>
</mj-section>

<mj-section padding="0 10px">
    <mj-column>
        <mj-text padding="20px 0 0 0" font-size="16px" font-weight="400" line-height="22px" color="#656565">
            <p>
                {if $user->getVocalFirstName()}
                    {_'email.general.welcomeWithName', [name => $user->getVocalFirstName()] |noescape}
                {else}
                    {_'email.general.welcome' |noescape}
                {/if}
            </p>
            <p>{_'email.transactionRegistrationEmail.baseInfo', ['shopName' => $transaction->getShop()->getName(), 'amount' => $amount] |noescape}</p>
            <p>{_'email.transactionRegistrationEmail.waiting' |noescape}</p>
        </mj-text>
    </mj-column>
</mj-section>

{if $addonBonus}
    <mj-section padding="0 10px">
        <mj-column>
            <mj-text padding="0" font-size="25px" font-weight="700" line-height="28px" color="#000">
                {_'email.transactionRegistrationEmail.addonBonus.promo.title', ['addonBonusAmount' => $addonBonusAmount] |noescape}
            </mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding="0 10px">
        <mj-column>
            <mj-text padding="20px 0 0 0" font-size="16px" font-weight="400" line-height="22px" color="#656565">
                {_'email.transactionRegistrationEmail.addonBonus.promo.text', ['addonBonusAmount' => $addonBonusAmount] |noescape}
            </mj-text>
        </mj-column>
    </mj-section>

<!--    <mj-section padding="20px 10px 15px">-->
<!--        <mj-column width="100%">-->
<!--            <mj-text align="center" padding="0">-->
<!--                <a id="desktop" href="{_'front.links.static.addon'}"><img src="{_'email.transactionRegistrationEmail.addonBonus.promo.ctaUrl'}" alt="bonus" style="max-width: 100%"></a>-->
<!--                <a id="mobile" href="{_'front.links.static.addon'}"><img src="{_'email.transactionRegistrationEmail.addonBonus.promo.ctaUrlMobile'}" alt="bonus" style="max-width: 100%"></a>-->
<!--            </mj-text>-->
<!--        </mj-column>-->
<!--    </mj-section>-->
{/if}

<mj-section padding="15px 10px 0">
    <mj-column>
        <mj-divider padding="0" border-color="#C4C4C4" border-width="1px"></mj-divider>
    </mj-column>
</mj-section>

<mj-section padding="0 10px">
    <mj-column>
        <mj-image alt="" border="none" width="150px" height="80px" padding="0" align="center" href="{_'front.links.shops.shop', [slug => $transaction->getShop()->getSlug()]}" src="{$shopLogo}" target="_blank" title=""></mj-image>
    </mj-column>
    <mj-column vertical-align="bottom">
        <mj-text padding="0 0 27px" font-size="32px" font-weight="700" align="center" color="#ef7f1a">
            <strong>{$amount |noescape}</strong>
        </mj-text>
    </mj-column>
</mj-section>

<mj-section padding="10px 0">
    <mj-column width="100%">
        <mj-button align="center" background-color="#EF7F1A" color="#fff" font-size="16px" font-weight="500" border-radius="4px" href="{_'front.links.account.transaction.default'}" inner-padding="20px 40px">
            {_'email.transactionRegistrationEmail.cta'}
        </mj-button>
    </mj-column>
</mj-section>

{if empty($ratingLinks) === false}
<mj-section padding="15px 10px 0 10px">
	<mj-column>
		<mj-divider padding="0" border-color="#C4C4C4" border-width="1px"></mj-divider>
	</mj-column>
</mj-section>

<mj-section padding="0 10px 20px 10px">
	<mj-column>
		<mj-text padding="35px 0 0" align="center" font-size="25px" font-weight="700" line-height="45px" color="#000">
			{_'email.transactionRegistrationEmail.rateTitle'}
		</mj-text>
	</mj-column>
</mj-section>


<mj-section padding="0 10px" padding-bottom="15px">
	<mj-group>
		<mj-column width="50%" padding="0 6px" padding-bottom="10px">
			<mj-button
				align="center"
				background-color="#D74540"
				color="#fff"
				font-size="16px"
				font-weight="500"
				border-radius="4px"
				href="{$ratingLinks[1]}"
				padding="4px 0"
				inner-padding="8px"
				width="100%"
			>
				<img
					src="https://www.tipli.cz/email/smile-4.png"
					style="
						width: 40px;
						height: 40px;
						vertical-align: middle;
						padding-top: 8px;
						padding-bottom: 10px;
					"
				/>
				<span style="display: block; min-height: 40px"
				>{_'email.reviewRequestEmail.rate.veryBad' |noescape}</span
				>
			</mj-button>
		</mj-column>

		<mj-column width="50%" padding="0 6px" padding-bottom="10px">
			<mj-button
				align="center"
				background-color="#EF7F1A"
				color="#fff"
				font-size="16px"
				font-weight="500"
				border-radius="4px"
				href="{$ratingLinks[2]}"
				padding="4px 0"
				inner-padding="8px"
				width="100%"
			>
				<img
					src="https://www.tipli.cz/email/smile-3.png"
					style="
						width: 40px;
						height: 40px;
						vertical-align: middle;
						padding-top: 8px;
						padding-bottom: 10px;
					"
				/>
				<span style="display: block; min-height: 40px"
				>{_'email.reviewRequestEmail.rate.bad' |noescape}</span
				>
			</mj-button>
		</mj-column>
	</mj-group>
	<mj-group>
		<mj-column width="50%" padding="0 6px" padding-bottom="10px">
			<mj-button
				align="center"
				background-color="#A0C048"
				color="#fff"
				font-size="16px"
				font-weight="500"
				border-radius="4px"
				href="{$ratingLinks[4]}"
				padding="4px 0"
				inner-padding="8px"
				width="100%"
			>
				<img
					src="https://www.tipli.cz/email/smile-2.png"
					style="
						width: 40px;
						height: 40px;
						vertical-align: middle;
						padding-top: 8px;
						padding-bottom: 10px;
					"
				/>
				<span style="display: block; min-height: 40px"
				>{_'email.reviewRequestEmail.rate.good' |noescape}</span
				>
			</mj-button>
		</mj-column>

		<mj-column width="50%" padding="0 6px" padding-bottom="10px">
			<mj-button
				align="center"
				background-color="#69AC52"
				color="#fff"
				font-size="16px"
				font-weight="500"
				border-radius="4px"
				href="{$ratingLinks[5]}"
				padding="4px 0"
				inner-padding="8px"
				width="100%"
			>
				<img
					src="https://www.tipli.cz/email/smile-1.png"
					style="
						width: 40px;
						height: 40px;
						vertical-align: middle;
						padding-top: 8px;
						padding-bottom: 10px;
					"
				/>
				<span style="display: block; min-height: 40px"
				>{_'email.reviewRequestEmail.rate.perfect' |noescape}</span
				>
			</mj-button>
		</mj-column>
	</mj-group>
</mj-section>
{/if}
