{layout '@newLayout.mjml'}
{block title}{_'email.tellFriendEmail.title' |noescape}{/block}
{block content}
<mj-section background-color="#ffffff" background-repeat="repeat" name="New section" padding-bottom="10px" padding="20px 0" text-align="center" vertical-align="top">
    <mj-column width="60%">
        <mj-text align="center" color="#000000" font-family="SourceSansPro, sans-serif" padding-bottom="0px" padding-top="0px" padding="10px 25px">
            <p style="margin: 20px 0; text-transform: uppercase; font-size: 27px; font-weight: 300;">
                {_'email.tellFriendEmail.title' |noescape}
            </p>
            <p style="font-size: 18px; color: #000000; font-family: SourceSansPro, sans-serif;">
                {_'email.tellFriendEmail.welcomeText', ['recommender' => $recommender->getUserName()] |noescape}</p>
        </mj-text>
    </mj-column>
</mj-section>

<mj-section background-color="#ef7f1a" background-repeat="repeat" name="New section" padding-bottom="10px" padding="0" text-align="center" vertical-align="top">
    <mj-column>
        <mj-text align="center" color="#ffffff" font-family="SourceSansPro, sans-serif" font-size="24px" line-height="1.5" padding-bottom="0px" padding-top="0px" padding="10px 25px">
            <p style="font-weight: 300; margin: 30px 0;">
                {_'email.tellFriendEmail.aboutTipli' |noescape}
            </p>
        </mj-text>
    </mj-column>
</mj-section>

<mj-section background-color="#ffffff" background-repeat="repeat" name="New section" padding-bottom="20px" padding-top="20px" padding="20px 0" text-align="center" vertical-align="top">
    <mj-column>
        <mj-text align="left" color="#55575d" font-family="Arial, sans-serif" font-size="13px" line-height="22px" padding-bottom="0px" padding-top="0px" padding="10px 25px">
            <p style="margin: 10px 0; text-align: center;"><span style="font-size:16px"><b>{_'email.tellFriendEmail.topShops' |noescape}</b></span></p>
        </mj-text>
    </mj-column>
</mj-section>
<mj-section background-color="#ffffff" background-repeat="repeat" name="New section" padding-bottom="20px" padding-top="20px" padding="20px 0" text-align="center" vertical-align="top">
            {foreach $shops as $shop}
                <mj-column>
                    <mj-image align="center" alt="{_'front.links.shops.shop', [slug => $shop->getSlug()] |noescape}" border="none" height="auto" href="{_'front.links.shops.shop', [slug => $shop->getSlug()] |noescape}" padding="10px 25px" src="{($shop->getLogo() |image: 200, 100, 'fitMiddle') |noescape}" target="_blank" title="" width="200px" ></mj-image>
                </mj-column>
            {/foreach}
</mj-section>
<mj-section background-color="#ffffff" background-repeat="repeat" name="New section" padding-bottom="20px" padding-top="20px" padding="20px 0" text-align="center" vertical-align="top">
    <mj-column>
        <mj-text align="center" color="#000000" container-background-color="#ffffff" font-family="SourceSansPro, sans-serif" font-size="13px" line-height="22px" padding-bottom="0px" padding-left="25px" padding-right="25px" padding-top="10px" padding="10px 25px">
            <p style="margin: 10px 0; font-size: 18px; font-weight:300; color: #b5b5b5;">
                {_'email.tellFriendEmail.registrationTitle' |noescape}<br />
                {_'email.tellFriendEmail.registrationText' |noescape}<br />
                {_'email.tellFriendEmail.registrationStart' |noescape}
            </p>
        </mj-text>
        <mj-button align="center" background-color="#ef7f1a" border-radius="3px" border="none" color="#ffffff" container-background-color="#ffffff" font-family="SourceSansPro, sans-serif" font-size="18px" font-weight="400" href="{_'front.links.homepage' |noescape}" inner-padding="25px 45px" padding-bottom="20px" padding="10px 25px" text-decoration="none" text-transform="none" vertical-align="middle">
            {_'email.tellFriendEmail.registrationButton' |noescape}
        </mj-button>
    </mj-column>
</mj-section>
