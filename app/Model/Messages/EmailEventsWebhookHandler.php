<?php

namespace tipli\Model\Messages;

use Nette\Utils\Json;
use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\UserActivity;
use tipli\Model\Mailchimp\MailchimpClient;
use tipli\Model\Webhooks\WebhookRequest;
use tipli\UserNotFoundException;
use Tracy\Debugger;

class EmailEventsWebhookHandler
{
	public const ACTION_EVENT_MANDRILL = 'event_mandrill';
	public const ACTION_EVENT_MAILGUN = 'event_mailgun';
	public const ACTION_EVENT_MAILCHIMP = 'event_mailchimp';

	/** @var EmailSubscriptionManager */
	private $emailSubscriptionManager;

	/** @var EmailsInteractionManager */
	private $emailsInteractionManager;

	public function __construct(EmailSubscriptionManager $emailSubscriptionManager, EmailsInteractionManager $emailsInteractionManager)
	{
		$this->emailSubscriptionManager = $emailSubscriptionManager;
		$this->emailsInteractionManager = $emailsInteractionManager;
	}

	public function handleWebhook(WebhookRequest $webhookRequest, $action)
	{
		if ($action === self::ACTION_EVENT_MANDRILL) {
			$this->processMandrill($webhookRequest);
		} elseif ($action === self::ACTION_EVENT_MAILGUN) {
			$this->processMailgun($webhookRequest);
		} elseif ($action === self::ACTION_EVENT_MAILCHIMP) {
			$this->processMailchimp($webhookRequest);
		}
	}

	private function processMandrill(WebhookRequest $webhookRequest)
	{
		$rawData = $webhookRequest->getRawBody();
		$rawData = urldecode($rawData);
		$rawData = substr($rawData, 16);

		if (empty(trim($rawData))) {
			Debugger::log('empty raw data', 'mandrill-bad-webhooks');
			return;
		}

		$events = Json::decode($rawData);

		/** @var \stdClass $event */
		foreach ($events as $event) {
			if (isset($event->event)) {
				$type = $event->event;
			} elseif (isset($event->type)) {
				$type = $event->type;
			} else {
				Debugger::log(Json::encode($event), 'mandrill-bad-webhooks');

				continue;
			}

			if (isset($event->msg->email)) {
				$email = $event->msg->email;
			} elseif (isset($event->reject->email)) {
				$email = $event->reject->email;
			} else {
				Debugger::log(Json::encode($event), 'mandrill-**********************');
				return;
//                throw new InvalidArgumentException('Email not found in Mandrill webhook.');
			}

			$dateTime = (new \DateTime())->setTimestamp($event->ts);

			if (in_array($type, ['hard_bounce', 'soft_bounce', 'unsub', 'blacklist', 'reject'])) {
				try {
					$this->emailSubscriptionManager->unsubscribe(null, $email, EmailSubscriptionManager::SITE_MANDRILL, EmailSubscriptionManager::SOURCE_WEBHOOK, $type);
				} catch (UserNotFoundException $e) {
					Debugger::log('User with email ' . $email . ' not found', 'mandrill-webhook-errors');
				}
			}

			if ($type === 'spam') {
				try {
					$this->emailSubscriptionManager->unsubscribeContentType(null, $email, SendingPolicy::CONTENT_TYPE_NEWSLETTER, EmailSubscriptionManager::SITE_MANDRILL, EmailSubscriptionManager::SOURCE_WEBHOOK, $type);

					Debugger::log('User with email ' . $email . ' not found', 'mandrill-webhook-unsub');
				} catch (UserNotFoundException $e) {
					Debugger::log('User with email ' . $email . ' not found', 'mandrill-webhook-errors');
				}
			}

			if ($type === 'click') {
//				Debugger::log('find users by email: ' . $email, 'mandrill--debug');

				$this->emailsInteractionManager->click($email, $dateTime, true, UserActivity::SOURCE_MANDRILL);
			}

			if ($type === 'open') {
//				Debugger::log('find users by email: ' . $email, 'mandrill--debug');

				$this->emailsInteractionManager->open($email, $dateTime, true, UserActivity::SOURCE_MANDRILL);
			}
		}
	}

	private function processMailgun(WebhookRequest $webhookRequest)
	{
		$data = (object) $webhookRequest->getRawBody();
		$data = Json::decode($data->scalar);

		if (!isset($data->{'event-data'})) {
			Debugger::log('event data not found: ' . Json::encode($data), 'mailgun-bad-webhooks');
			return;
		} else {
			Debugger::log('event data found: ' . Json::encode($data), 'mailgun-good-webhooks');
		}

		$data = $data->{'event-data'};

		if (!isset($data->event) || !isset($data->recipient) || !isset($data->timestamp)) {
			Debugger::log(Json::encode($data), 'mailgun-bad-webhooks');
			return;
		}

		$event = $data->event;
		$email = $data->recipient;
		$dateTime = (new \DateTime())->setTimestamp($data->timestamp);

		Debugger::log($event . '-' . $email .  '-' . $dateTime->format('d.m.Y H:i:s'), 'mailgun-good-webhooks');

		if (in_array($event, [MailgunClient::EVENT_TYPE_COMPLAINED, MailgunClient::EVENT_TYPE_UNSUBSCRIBED, MailgunClient::EVENT_TYPE_FAILED])) {
			try {
				$this->emailSubscriptionManager->unsubscribeContentType(null, $email, SendingPolicy::CONTENT_TYPE_NEWSLETTER, EmailSubscriptionManager::SITE_MAILGUN, EmailSubscriptionManager::SOURCE_WEBHOOK, $event);
			} catch (UserNotFoundException $e) {
				Debugger::log('User with email ' . $email . ' not found', 'mailgun-webhooks');
			}
		}

		if ($event === MailgunClient::EVENT_TYPE_CLICKED) {
			$this->emailsInteractionManager->click($email, $dateTime, true, UserActivity::SOURCE_MAILGUN);
		}

		if ($event === MailgunClient::EVENT_TYPE_OPENED) {
			$this->emailsInteractionManager->open($email, $dateTime, true, UserActivity::SOURCE_MAILGUN);
		}
	}

	private function processMailchimp(WebhookRequest $webhookRequest)
	{
		$event = $webhookRequest->getPost('type');
		$firedAt = $webhookRequest->getPost('fired_at');

		try {
			$data = (object) $webhookRequest->getPost('data');
		} catch (\Exception $e) {
			$data = null;
		}

		Debugger::log(Json::encode($webhookRequest->getPost()), 'mailchimp-all-webhooks');

		if (!isset($event) || !isset($firedAt) || !isset($data)) {
			Debugger::log(Json::encode($webhookRequest->getPost()), 'mailchimp-bad-webhooks');
			return;
		}

		$email = $data->email;
		$dateTime = new \DateTime($firedAt);

		if (in_array($event, [MailchimpClient::EVENT_CLEARED]) === true) {
			try {
				Debugger::log('#unsubscribe', 'mailchimp-webhooks');
				$this->emailSubscriptionManager->unsubscribeContentType(null, $email, SendingPolicy::CONTENT_TYPE_NEWSLETTER, EmailSubscriptionManager::SITE_MAILCHIMP, EmailSubscriptionManager::SOURCE_WEBHOOK, $event);
			} catch (UserNotFoundException $e) {
				Debugger::log('User with email ' . $email . ' not found', 'mailchimp-webhooks');
			}
		}

		if ($event === MailchimpClient::EVENT_CLICKED) {
			Debugger::log('#click', 'mailchimp-webhooks');
			$this->emailsInteractionManager->click($email, $dateTime, true, UserActivity::SOURCE_MAILCHIMP);
		}

		if ($event === MailchimpClient::EVENT_OPENED) {
			Debugger::log('#open', 'mailchimp-webhooks');
			$this->emailsInteractionManager->open($email, $dateTime, true, UserActivity::SOURCE_MAILCHIMP);
		}
	}
}
