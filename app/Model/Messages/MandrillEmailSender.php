<?php

namespace tipli\Model\Messages;

use MailchimpTransactional\ApiClient;
use tipli\Model\Messages\Messages\EmailMessage;
use Tracy\Debugger;

class MandrillEmailSender implements IMandrillEmailSender
{
	public function __construct(private $apiKey)
	{
	}

	public function sendEmail(EmailMessage $emailMessage): void
	{
		try {
			$tags = array_filter(
				array_merge([$emailMessage->getType()], [$emailMessage->getCampaign()])
			);

			$mailchimp = new ApiClient();
			$mailchimp->setApiKey($this->apiKey);

			$message = [];

			if (!$emailMessage->isJson()) {
				$message['html'] = $emailMessage->getBody();
				$message['subject'] = $emailMessage->getSubject();
				$message['from_email'] = $emailMessage->getFromEmail();
				$message['from_name'] = $emailMessage->getFromName();
				$message['headers'] = ['Reply-To' => $emailMessage->getFromEmail()];
			}

			$message['tags'] = $tags;

			$message['to'][] = [
				'email' => $emailMessage->getToEmail(),
				'name' => $emailMessage->getToName(),
			];

			if ($emailMessage->isJson()) {
				$message['merge_vars'][0]['rcpt'] = $emailMessage->getToEmail();

				foreach ($emailMessage->getDecodedBody() as $var => $content) {
					$message['merge_vars'][0]['vars'][] = ['name' => $var, 'content' => $content];
				}
			}

			if ($emailMessage->isJson()) {
				$response = $mailchimp->messages->sendTemplate([
					'key' => '',
					'message' => $message,
					'async' => false,
					'ip_pool' => '',
					'send_at' => '',
					'template_name' => $emailMessage->getCampaign(),
					'template_content' => [
						[
							'name' => 'main',
							'content' => '',
						],
					],
				]);
			} else {
				$response = $mailchimp->messages->send([
					'key' => '',
					'message' => $message,
					'async' => false,
					'ip_pool' => '',
					'send_at' => '',
				]);
			}

			if (is_array($response) && $response[0]->status != 'sent') {
				Debugger::log($emailMessage->getToEmail() . ', reason: ' . ($response[0]->reject_reason ?: 'undefined'), 'mandrill-sender-errors');
			}
		} catch (\Exception $e) {
			Debugger::log('Exception pri odesilani emailu: ' . $e->getMessage() . ' Kod: ' . $e->getCode(), 'error');
		}
	}
}
