<?php

namespace tipli\Model\Messages\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;

class SmsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	public function onlyScheduled()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.scheduledAt <= CURRENT_TIMESTAMP()');
		};

		$this->withoutSent();
		$this->withoutAborted();

		return $this;
	}

	public function withoutAborted()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.abortedAt IS NULL');
		};

		return $this;
	}

	public function withoutSent()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('s.sentAt IS NULL');
		};

		return $this;
	}

	public function topPriorityFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.priority', 'desc');
		};

		return $this;
	}

	public function oldestScheduledFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('s.scheduledAt', 'asc');
		};

		return $this;
	}

	public function setMaxResults($maxResults)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($maxResults) {
			$queryBuilder->setMaxResults($maxResults);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('s', 's.id')
			->select('s');


		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		$queryBuilder->addOrderBy('s.id');

		return $queryBuilder;
	}

	public function postFetch(Queryable $repository, \Iterator $iterator)
	{
	}
}
