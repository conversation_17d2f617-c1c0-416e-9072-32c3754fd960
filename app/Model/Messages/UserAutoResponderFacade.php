<?php

namespace tipli\Model\Messages;

use Nette\Utils\DateTime;
use tipli\InvalidStateException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Campaign\Entities\CampaignSubscription;
use tipli\Model\Messages\Repositories\UserAutoResponderRepository;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Transactions\Entities\Transaction;

class UserAutoResponderFacade
{
	/** @var UserAutoResponderManager */
	private $userAutoResponderManager;

	/** @var UserAutoResponderRepository */
	private $userAutoResponderRepository;

	/** @var UserAutoResponderSender */
	private $userAutoResponderSender;

	public function __construct(UserAutoResponderManager $userAutoResponderManager, UserAutoResponderRepository $userAutoResponderRepository, UserAutoResponderSender $userAutoResponderSender)
	{
		$this->userAutoResponderManager = $userAutoResponderManager;
		$this->userAutoResponderRepository = $userAutoResponderRepository;
		$this->userAutoResponderSender = $userAutoResponderSender;
	}

	/**
	 * @param User $user
	 * @throws InvalidStateException
	 */
	public function sendWelcomeAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendWelcomeAutoresponder($user);
	}

	/**
	 * @param User $user
	 * @throws InvalidStateException
	 */
	public function sendIsEverythingOkAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendIsEverythingOkAutoresponder($user);
	}

	/**
	 * @param User $user
	 * @throws InvalidStateException
	 */
	public function sendUserAfterFirstRedirectionAutoResponder(User $user): void
	{
		$this->userAutoResponderSender->sendUserAfterFirstRedirectionAutoResponder($user);
	}

	/**
	 * @param User $user
	 * @param array $recommendedShops
	 */
	public function sendAfterRegistration3DaysAutoResponder(User $user, array $recommendedShops, int $countOfShops): void
	{
		$this->userAutoResponderSender->sendAfterRegistration3DaysAutoResponder($user, $recommendedShops, $countOfShops);
	}

	public function findUserAutoResponderSentBetween(User $user, $type, DateTime $sentFrom, DateTime $sentTo)
	{
		return $this->userAutoResponderRepository->findUserAutoResponderSentBetween($user, $type, $sentFrom, $sentTo);
	}

	public function sendAutoresponderCampaignAutoresponder(CampaignSubscription $campaignSubscription, string $type, ?Transaction $transaction = null, ?Transaction $bonusTransaction = null): void
	{
		$this->userAutoResponderSender->sendAutoresponderCampaignAutoresponder($campaignSubscription, $type, $transaction, $bonusTransaction);
	}

	public function sendHowToVideoAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendHowToVideoAutoresponder($user);
	}

	public function sendHowToGetRewardsAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendHowToGetRewardsAutoresponder($user);
	}

	public function sendMobileAppAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendMobileAppAutoresponder($user);
	}

	public function sendAddonAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendAddonAutoresponder($user);
	}

	public function sendShopsAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendShopsAutoresponder($user);
	}

	public function sendElectronicsAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendElectronicsAutoresponder($user);
	}

	public function sendTravelAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendTravelAutoresponder($user);
	}

	public function sendAliexpressAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendAliexpressAutoresponder($user);
	}

	public function sendFashionAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendFashionAutoresponder($user);
	}

	public function sendHealthAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendHealthAutoresponder($user);
	}

	public function sendFeedbackAfterRedirectionAutoresponder(Redirection $redirection): void
	{
		$this->userAutoResponderSender->sendFeedbackAfterRedirectionAutoresponder($redirection);
	}

	public function sendBonusAfterRegistrationAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendBonusAfterRegistrationAutoresponder($user);
	}

	public function sendBonusAfterRegistrationLastDayAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendBonusAfterRegistrationLastDayAutoresponder($user);
	}

	public function sendDealsAutoresponder(User $user, array $deals): void
	{
		$this->userAutoResponderSender->sendDealsAutoresponder($user, $deals);
	}

	public function sendLuckyShopAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendLuckyShopAutoresponder($user);
	}

	public function sendDoubleExtraRewardAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendDoubleExtraRewardAutoresponder($user);
	}

	public function sendReactivationCampaignAutoresponder(User $user): void
	{
		$this->userAutoResponderSender->sendReactivationCampaignAutoresponder($user);
	}
}
