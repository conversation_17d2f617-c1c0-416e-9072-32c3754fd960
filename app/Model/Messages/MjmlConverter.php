<?php

namespace tipli\Model\Messages;

use tipli\MjmlConvertErrorException;
use <PERSON>\Debugger;

class MjmlConverter
{
	public const SERVER_AMAZON = 'amazon';
	public const SERVER_MJML = 'mjml';

	/** @var string */
	private $server = self::SERVER_MJML;

	public function __construct(private $appId, private $secretKey)
	{
	}

	public function mjmlToHtml($mjml)
	{
		if ($this->server === self::SERVER_MJML) {
//			try {
				$html = $this->mjmlServerConvert($mjml);

				return $html;
//			} catch (MjmlConvertErrorException $e) {
//				$this->server = self::SERVER_AMAZON;
//			}
		}

		if ($this->server === self::SERVER_AMAZON) {
			return $this->amazonServerConvert($mjml);
		}
	}

	private function mjmlServerConvert($mjml)
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, 'https://api.mjml.io/v1/render');
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['mjml' => $mjml]));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_USERPWD, $this->appId . ':' . $this->secretKey);
		curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		$output = curl_exec($ch);
		curl_close($ch);

		$data = json_decode($output);

		if (isset($data->mjml) && isset($data->html)) {
			return $data->html;
		} else {
			Debugger::log($output, 'mjml-converter-errors');
			throw new MjmlConvertErrorException($data->message ?? 'Mjml to HTML convert error (Mjml.io)');
		}
	}

	private function amazonServerConvert($mjml)
	{
		$ch = curl_init();

		curl_setopt($ch, CURLOPT_URL, 'https://ys6jh3qw18.execute-api.eu-west-1.amazonaws.com/Production/mjml');
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['mjml' => $mjml]));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POST, 1);
		$output = curl_exec($ch);
		curl_close($ch);

		$data = json_decode($output);

		if (isset($data->html)) {
			return $data->html;
		} else {
			Debugger::log($output, 'mjml-converter-errors-amazon');
			throw new MjmlConvertErrorException($data->message ?? 'Mjml to HTML convert error. (Amazon)');
		}
	}
}
