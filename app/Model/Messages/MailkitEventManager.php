<?php

namespace tipli\Model\Messages;

use tipli\Model\Account\Entities\UserActivity;
use tipli\Model\Account\UserFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Mailkit\MailkitClient;
use tipli\Model\Mailkit\MailkitEvent;
use tipli\Model\Mailkit\Producers\MailkitProducer;
use tipli\Model\Redis\RedisStorageManager;
use Tracy\Debugger;

class MailkitEventManager
{
	private const CACHE_KEY_EVENT_PREFIX = 'mailkit_event_';
	private const CACHE_KEY_LAST_LOG_ID = 'mailkit_event_last_log_id';

	public function __construct(
		private readonly MailkitClient $mailkitClient,
		private readonly UserFacade $userFacade,
		private readonly EmailsInteractionManager $emailsInteractionManager,
		private readonly MailkitProducer $mailkitProducer,
		private readonly RedisStorageManager $redisStorageManager,
		private readonly LocalizationFacade $localizationFacade
	) {
	}

	public function downloadEvents(): void
	{
		$lastIdLog = $this->loadLastLogId();
		$events = $this->mailkitClient->getResponses($lastIdLog);

		if (count($events) <= 1) {
			$this->saveLastLogId(null);
			return;
		}

		foreach ($events as $event) {
			// @todo jirka: do budoucna by se mohly stahovat i eventy subscribe, nevim ale kdy tento event nastane, jelikoz je unsubscribe vedeny v nasi rezii
			if (in_array($event->getType(), [MailkitEvent::TYPE_READ, MailkitEvent::TYPE_CLICK], true)) {
				$cacheKey = self::CACHE_KEY_EVENT_PREFIX . $event->getIdLog();
				if ($this->redisStorageManager->read($cacheKey) === null) {
					$this->mailkitProducer->scheduleMailkitEvent($event);
					$this->redisStorageManager->write($cacheKey, $event->getIdLog(), new \DateTime('+1 day'));
				}
			}

			$this->saveLastLogId($event->getIdLog());
		}
	}

	public function processEvent(MailkitEvent $mailkitEvent): void
	{
		$localization = $this->localizationFacade->findOneByLocale($mailkitEvent->getLocale());
		$user = $this->userFacade->findOneByEmail($mailkitEvent->getEmail(), $localization);

		if ($user === null) {
			Debugger::log("User with email {$mailkitEvent->getEmail()} not found", 'MailkitEventManager');
			return;
		}

		if ($mailkitEvent->getType() === MailkitEvent::TYPE_CLICK) {
			$this->emailsInteractionManager->click($user, $mailkitEvent->getEventDate(), false, UserActivity::SOURCE_MAILKIT, $mailkitEvent->getCampaignName());
		} elseif ($mailkitEvent->getType() === MailkitEvent::TYPE_READ) {
			$this->emailsInteractionManager->open($user, $mailkitEvent->getEventDate(), false, UserActivity::SOURCE_MAILKIT, $mailkitEvent->getCampaignName());
		}
	}

	private function saveLastLogId(?string $lastLogId): void
	{
		if ($lastLogId !== null) {
			$this->redisStorageManager->write(self::CACHE_KEY_LAST_LOG_ID, $lastLogId, new \DateTime('+1 day'));
		} else {
			$this->redisStorageManager->remove(self::CACHE_KEY_LAST_LOG_ID);
		}
	}

	private function loadLastLogId(): ?string
	{
		$lastLogId = $this->redisStorageManager->read(self::CACHE_KEY_LAST_LOG_ID);

		if ($lastLogId) {
			return $lastLogId;
		}

		return null;
	}
}
