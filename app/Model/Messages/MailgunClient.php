<?php

namespace tipli\Model\Messages;

use Mailgun\Mailgun;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Localization\LocalizationFacade;
use Tracy\Debugger;

class MailgunClient
{
	public const EVENT_TYPE_ACCEPTED = 'accepted';
	public const EVENT_TYPE_REJECTED = 'rejected';
	public const EVENT_TYPE_DELIVERED = 'delivered';
	public const EVENT_TYPE_FAILED = 'failed';
	public const EVENT_TYPE_OPENED = 'opened';
	public const EVENT_TYPE_CLICKED = 'clicked';
	public const EVENT_TYPE_UNSUBSCRIBED = 'unsubscribed';
	public const EVENT_TYPE_STORED = 'stored';
	public const EVENT_TYPE_COMPLAINED = 'complained';

	/** @var LocalizationFacade */
	private $localizationFacade;

	public function __construct(private $apiKey, LocalizationFacade $localizationFacade)
	{
		$this->localizationFacade = $localizationFacade;
	}

	public function getLastEvents(\DateTime $fromDate)
	{
		$events = [];

		$domains = $this->downloadDomains();

		foreach ($domains as $domain) {
			$locale = explode('.', $domain);
			$locale = end($locale);
			$locale = $locale === 'cz' ? 'cs' : $locale;

			$localization = $this->localizationFacade->findOneByLocale($locale);

			/** @var \Mailgun\Model\Event\Event $event */
			foreach ($this->downloadEvents($domain, $fromDate) as $event) {
				$type = $this->getEvent($event->getEvent());

				if (Strings::lower($type) === MailgunClient::EVENT_TYPE_FAILED && Strings::lower($event->getSeverity()) == 'temporary') {
					Debugger::log('Temporary failed event ' . $event->getId(), 'mailgun-client-logs');
					continue;
				}

				$events[] = (object) [
					'id' => $event->getId(),
					'email' => $event->getRecipient(),
					'localization' => $localization,
					'dateTime' => (new \DateTime())->setTimestamp($event->getTimestamp()),
					'type' => $type,
					'message' => isset($event->getDeliveryStatus()['message']) ? ('Mailgun: ' . $type . ' (' . $event->getReason() . ') - ' . $event->getDeliveryStatus()['message']) : null,
				];
			}
		}

		return $events;
	}

	public function downloadDomains()
	{
		$mgClient = new Mailgun($this->apiKey);

		$result = $mgClient->get('domains');

		$domains = [];

		$responseCode = (int) $result->http_response_code;

		if ($responseCode === 200 && isset($result->http_response_body->items)) {
			foreach ($result->http_response_body->items as $item) {
				$domains[] = $item->name;
			}
		}

		return $domains;
	}

	public function createWebhook(string $domain, string $url, string $event)
	{
		$mgClient = new Mailgun($this->apiKey);

		$epUrl = 'domains/' . $domain . '/webhooks';
		$mgClient->post($epUrl, ['url' => $url, 'id' => $event]);
	}

	private function downloadEvents($domain, \DateTime $fromDate)
	{
		$mgClient = Mailgun::create($this->apiKey);

		$response = $mgClient->events()->get($domain, [
			'begin' => $fromDate->format('D, d M Y H:i:s O'),
			'ascending' => 'no',
			'limit' => 50,
		]);

		$items = $response->getItems();
//        $countOfPages = 0;
//        while (count($response->getItems()) > 0) {
//            $response = $mgClient->events()->nextPage($response);
//
//            $items = array_merge($items, $response->getItems());
//
//            if ($countOfPages++ == 1) {
//                break;
//            }
//        }

		return $items;
	}

	private function getEventTypes()
	{
		return [
			self::EVENT_TYPE_ACCEPTED,
			self::EVENT_TYPE_REJECTED,
			self::EVENT_TYPE_DELIVERED,
			self::EVENT_TYPE_FAILED,
			self::EVENT_TYPE_OPENED,
			self::EVENT_TYPE_CLICKED,
			self::EVENT_TYPE_UNSUBSCRIBED,
			self::EVENT_TYPE_STORED,
			self::EVENT_TYPE_COMPLAINED,
		];
	}

	private function getEvent($event)
	{
		$events = $this->getEventTypes();

		if (($index = array_search($event, $events)) !== false) {
			return $events[$index];
		}

		throw new InvalidArgumentException('Unknown event ' . $event);
	}
}
