<?php

namespace tipli\Model\Twisto\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Messages\TwistoClient;
use tipli\Model\Messages\TwistoMessage;
use tipli\Model\RabbitMq\BaseConsumer;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;

class TwistoConsumer extends BaseConsumer implements IConsumer
{
	/** @var TransactionFacade */
	private $transactionFacade;

	/** @var TwistoClient */
	private $twistoClient;

	public function __construct(TransactionFacade $transactionFacade, TwistoClient $twistoClient)
	{
		$this->transactionFacade = $transactionFacade;
		$this->twistoClient = $twistoClient;
	}

	public function consume(Message $data): int
	{
		$message = TwistoMessage::fromJson($data->content);

		$transactionId = $message->getTransactionId();

		/** @var Transaction $transaction */
		$transaction = $transactionId ? $this->transactionFacade->find($transactionId) : null;

		if (!$transaction || !$transaction->getShop() || !$transaction->hasUser() || !$transaction->getUser()->isWhiteLabelUser()) {
			return IConsumer::MESSAGE_ACK;
		}

		$this->twistoClient->trackTransactionUpdate($transaction);

		sleep(20);

		return IConsumer::MESSAGE_ACK;
	}
}
