<?php

namespace tipli\Model\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;

class TwistoMessage implements IMessage
{
	/** @var int */
	private $transactionId;

	public function __construct(int $transactionId)
	{
		$this->transactionId = $transactionId;
	}

	public function getTransactionId(): int
	{
		return $this->transactionId;
	}

	public function __toString(): string
	{
		return Json::encode([
			'transactionId' => $this->getTransactionId(),
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);

		return new self(
			$data->transactionId
		);
	}
}
