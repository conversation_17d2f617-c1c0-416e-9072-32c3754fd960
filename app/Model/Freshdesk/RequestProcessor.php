<?php

namespace tipli\Model\Freshdesk;

use Nette\Utils\Json;
use tipli\Freshdesk\Events\MessageCreatedEvent;
use tipli\Freshdesk\Events\NoteAddedEvent;
use tipli\Freshdesk\Events\OutboundTicketCreatedEvent;
use tipli\Freshdesk\Events\TicketCreatedEvent;
use tipli\Model\Account\UserFacade;
use tipli\Model\Freshdesk\Entities\Request;

class RequestProcessor
{
	public function __construct(private FreshdeskFacade $freshdeskFacade, private UserFacade $userFacade, private FreshdeskClient $freshdeskClient)
	{
	}

	public function processRequest(Request $request): void
	{
		match ($request->getAction()) {
			Request::ACTION_CREATE_TICKET => $this->createTicketOnFreshdesk($request),
			Request::ACTION_CREATE_OUTBOUND_TICKET => $this->createOutboundTicket($request),
			Request::ACTION_ADD_NOTE => $this->addNote($request),
			Request::ACTION_SEND_MESSAGE_TO_TICKET => $this->sendMessageToTicket($request),
			default => throw new \Exception('Unknown action: ' . $request->getAction())
		};
	}

	private function createTicketOnFreshdesk(Request $request): void
	{
		$ticketCreatedEvent = TicketCreatedEvent::fromArray(Json::decode($request->getBody(), Json::FORCE_ARRAY));

		$freshdeskTicketId = $this->freshdeskClient->createTicketOnFreshdesk(
			$request->getTicket()->getLocalization(),
			$ticketCreatedEvent->getFrom(),
			$ticketCreatedEvent->getSubject(),
			$ticketCreatedEvent->getMessage(),
			$ticketCreatedEvent->getLocale(),
			$ticketCreatedEvent->getUserId()
		);

		$ticket = $request->getTicket();

		$ticket->setTicketId($freshdeskTicketId);
		$ticket->getMessages()->first()->setMessageid($freshdeskTicketId);

		$this->freshdeskFacade->saveTicket($request->getTicket());
	}

	private function createOutboundTicket(Request $request): void
	{
		$outboundTicketCreatedEvent = OutboundTicketCreatedEvent::fromArray(Json::decode($request->getBody(), Json::FORCE_ARRAY));

		$importedTicket = $this->freshdeskClient->createOutboundTicket(
			$this->userFacade->find($outboundTicketCreatedEvent->getUserId()),
			$outboundTicketCreatedEvent->getSubject(),
			$outboundTicketCreatedEvent->getDescription()
		);

		$ticket = $request->getTicket();

		$ticket->setTicketId($importedTicket->getTicketId());
		$ticket->getMessages()->first()->setMessageid($importedTicket->getTicketId());

		$this->freshdeskFacade->saveTicket($request->getTicket());
	}

	private function sendMessageToTicket(Request $request): void
	{
		$messageCreatedEvent = MessageCreatedEvent::fromArray(Json::decode($request->getBody(), Json::FORCE_ARRAY));

		$messageId = $this->freshdeskClient->sendMessageToTicket(
			$request->getTicket(),
			$messageCreatedEvent->getMessage(),
			$messageCreatedEvent->getUserId() ? $this->userFacade->find($messageCreatedEvent->getUserId()) : null
		);

		$message = $this->freshdeskFacade->findMessage($messageCreatedEvent->getMessageId());

		$message->setMessageId($messageId);

		$this->freshdeskFacade->saveMessage($message);
	}

	private function addNote(Request $request): void
	{
		$noteAddedEvent = NoteAddedEvent::fromArray(Json::decode($request->getBody(), Json::FORCE_ARRAY));

		$this->freshdeskClient->addNote(
			$request->getTicket(),
			$noteAddedEvent->getNote()
		);
	}
}
