<?php

namespace tipli\Model\Freshdesk\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;

class TicketRepository extends BaseRepository
{
	public function getTickets()
	{
		return $this->createQueryBuilder('t');
	}

	public function findTicketsToSynchronize()
	{
		$maxResults = date('H') >= 8 && date('H') <= 16 ? 5 : 10;

		return $this->getTickets()
			->andWhere('t.synchronizeAt IS NOT NULL')
			->andWhere('t.synchronizeAt <= :now')
			->setParameter('now', new \DateTime())
			->innerJoin('t.localization', 'l')
			->andWhere('(l.locale IN (:locales) OR t.openedAt > :dateBorder)')
			->setParameter('dateBorder', new \DateTime('2021-03-11 19:40:00'))
			->setParameter('locales', [Localization::LOCALE_CZECH, Localization::LOCALE_SLOVAK])
			->addOrderBy('t.synchronizeAt', 'DESC')
			->andWhere('t.ticketId IS NOT NULL')
			->setMaxResults($maxResults)
			->getQuery()
			->getResult();
	}

	public function fetchTicketsToSynchronizeMessages()
	{
		return $this->getTickets()
			->andWhere('t.messagesSynchronizedAt IS NULL')
			->setMaxResults(8)
			->getQuery()
			->getResult();
	}
}
