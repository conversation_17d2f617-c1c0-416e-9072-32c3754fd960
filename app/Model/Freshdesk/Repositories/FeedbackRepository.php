<?php

namespace tipli\Model\Freshdesk\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;

class FeedbackRepository extends BaseRepository
{
	public function getFeedbacks()
	{
		return $this
			->createQueryBuilder('f');
	}

	public function findCountOfFeedbacks(Localization $localization, $rating = null)
	{
		$qb = $this->getFeedbacks()
			->select('count(f.id)')
			->innerJoin('f.ticket', 't')
			->andWhere('t.localization = :localization')
			->setParameter('localization', $localization);

		if ($rating !== null) {
			$qb->andWhere('f.rating = :rating')
				->setParameter('rating', (string) $rating);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findCountOfFeedbacksByOperator(User $operator, $rating = null)
	{
		$qb = $this->getFeedbacks()
			->select('count(f.id)')
			->innerJoin('f.ticket', 't')
			->andWhere('t.resolvedBy = :operator')
			->setParameter('operator', $operator);

		if ($rating !== null) {
			$qb->andWhere('f.rating = :rating')
				->setParameter('rating', (string) $rating);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}
}
