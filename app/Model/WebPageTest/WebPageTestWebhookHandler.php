<?php

namespace tipli\Model\WebPageTest;

use Nette\Database\Context;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use tipli\Model\Webhooks\IWebhookHandler;
use tipli\Model\Webhooks\WebhookRequest;
use Tracy\Debugger;

final class WebPageTestWebhookHandler implements IWebhookHandler
{
	public const ACTION_WEBPAGETEST_RESULT = 'webpagetest_result';

	/** @var Context */
	private $context;

	public function __construct(Context $context)
	{
		$this->context = $context;
	}

	public function handleWebhook(WebhookRequest $webhookRequest, string $action = null)
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 300);

		try {
			$testId = $webhookRequest->getQuery('id');
		} catch (JsonException $e) {
			Debugger::log($webhookRequest->getUrl()->getAbsoluteUrl(), 'webpagetest-webhook-syntax-error');
			return;
		}

		$data = Json::decode(
			file_get_contents('https://www.webpagetest.org/jsonResult.php?test=' . $testId)
		);

		$url = new Url(rawurldecode($data->data->url));
		$firstViewData = $data->data->runs->{1}->firstView;

		if (!isset($firstViewData->TTFB) || !isset($firstViewData->loadTime)) {
			return;
		}

		$data = [
			'domain' => $url->getAuthority(),
			'url' => $url->getAbsoluteUrl(),
			'created_at' => new \DateTime(),
			'first_byte_ms' => $firstViewData->TTFB,
			'load_time_ms' => $firstViewData->loadTime,
			'fully_loaded_ms' => $firstViewData->fullyLoaded,
			'speed_index' => $firstViewData->SpeedIndex,
			'first_contentful_paint' => $firstViewData->{'chromeUserTiming.firstContentfulPaint'},
			'largest_contentful_paint' => $firstViewData->{'chromeUserTiming.LargestContentfulPaint'},
			'cumulative_layout_shift' => $firstViewData->{'chromeUserTiming.CumulativeLayoutShift'},
			'visual_complete' => $firstViewData->visualComplete,
			'render' => $firstViewData->render,
			'status_code' => $data->statusCode,
			'tested_at' => (new \DateTime())->setTimestamp($data->data->completed),
			'total_blocking_time' => $firstViewData->TotalBlockingTime,
			'time_to_interactive' => $firstViewData->TTIMeasurementEnd,
		];

		$this->context->query('INSERT INTO webpagetest_tests', $data);
	}
}
