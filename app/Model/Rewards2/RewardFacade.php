<?php

namespace tipli\Model\Rewards2;

use Nette\SmartObject;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Rewards2\Entities\RewardCampaign;
use tipli\Model\Rewards2\Entities\UserReward;
use tipli\Model\Rewards2\Queries\UserRewardsQuery;
use tipli\Model\Rewards2\Repositories\RewardCampaignRepository;
use tipli\Model\Rewards2\Repositories\UserRewardRepository;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Utm\Entities\Utm;

class RewardFacade
{
	use SmartObject;

	/**
	 * @var RewardManager
	 */
	private $rewardManager;

	/**
	 * @var RewardCampaignRepository
	 */
	private $rewardCampaignRepository;

	/**
	 * @var UserRewardRepository
	 */
	private $userRewardRepository;

	public function __construct(RewardManager $rewardManager, RewardCampaignRepository $rewardCampaignRepository, UserRewardRepository $userRewardRepository)
	{
		$this->rewardManager = $rewardManager;
		$this->rewardCampaignRepository = $rewardCampaignRepository;
		$this->userRewardRepository = $userRewardRepository;
	}

	public function getRewardCampaigns()
	{
		return $this->rewardCampaignRepository->getRewardCampaigns();
	}

	public function getUserRewards()
	{
		return $this->userRewardRepository->getUserRewards();
	}

	public function getUserRewardsByRewardCampaign(RewardCampaign $rewardCampaign)
	{
		return $this->userRewardRepository->getUserRewardByRewardCampaign($rewardCampaign);
	}

	public function createRewardCampaign(
		Localization $localization,
		$name,
		$description,
		$image,
		$segment,
		$code,
		array $utms,
		\DateTime $validSince,
		\DateTime $validTill,
		$maximumCountOfActivations,
		?\DateTime $ableToUseTill,
		$ableToUseInDays,
		$activateAfterAssign,
		$rewardBonusAmount,
		$rewardCoefficient,
		$conditionType,
		$conditionMinimalConfirmedBalance = null,
		$conditionShops = [],
		$conditionExcludedShops = [],
		$conditionShopTags = []
	) {
		return $this->rewardManager->createRewardCampaign(
			$localization,
			$name,
			$description,
			$image,
			$segment,
			$code,
			$utms,
			$validSince,
			$validTill,
			$maximumCountOfActivations,
			$ableToUseTill,
			$ableToUseInDays,
			$activateAfterAssign,
			$rewardBonusAmount,
			$rewardCoefficient,
			$conditionType,
			$conditionMinimalConfirmedBalance,
			$conditionShops,
			$conditionExcludedShops,
			$conditionShopTags
		);
	}

	public function findRewardCampaign($id)
	{
		return $this->rewardCampaignRepository->find($id);
	}

	public function saveRewardCampaign(RewardCampaign $rewardCampaign)
	{
		$this->rewardManager->saveRewardCampaign($rewardCampaign);
	}

	public function findAvailableRewardCampaignsForUser(User $user, Utm $utm = null, $code = null, $exceptAssigned = true)
	{
		return $this->rewardCampaignRepository->findAvailableRewardCampaignsForUser($user, $utm, $code, $exceptAssigned);
	}

	public function findUserRewards(User $user, RewardCampaign $rewardCampaign = null, $valid = null, $active = null, $used = null)
	{
		return $this->userRewardRepository->findUserRewards($user, $rewardCampaign, $valid, $active, $used);
	}

	public function assignRewardCampaign(User $user, RewardCampaign $rewardCampaign)
	{
		return $this->rewardManager->assignRewardCampaign($user, $rewardCampaign);
	}

	public function isRewardCampaignAbleToAssign(User $user, RewardCampaign $rewardCampaign): bool
	{
		return count($this->userRewardRepository->findUserRewards($user, $rewardCampaign, true, null, false)) === 0 && $rewardCampaign->isAbleToAssign();
	}

	public function findUserRewardByCampaign(User $user, RewardCampaign $rewardCampaign)
	{
		return $this->userRewardRepository->findUserRewardByCampaign($user, $rewardCampaign);
	}

	public function findUserReward($id)
	{
		return $this->userRewardRepository->find($id);
	}

	public function expireUserReward(UserReward $userReward)
	{
		$this->rewardManager->expireUserReward($userReward);
	}

	public function activateUserReward(UserReward $userReward)
	{
		$this->rewardManager->activateUserReward($userReward);
	}

	public function meetsTransactionRewardCampaignConditions(RewardCampaign $rewardCampaign, Transaction $transaction)
	{
		return $this->rewardManager->meetsTransactionRewardCampaignConditions($rewardCampaign, $transaction);
	}

	public function applyUserRewardBonus(UserReward $userReward, Transaction $transaction = null)
	{
		$this->rewardManager->applyUserRewardBonus($userReward, $transaction);
	}

	public function findCountOfActiveUserRewardByCampaign(RewardCampaign $rewardCampaign)
	{
		return $this->userRewardRepository->findCountOfActiveUserRewardByCampaign($rewardCampaign);
	}

	public function findRewardCampaignByCode($code)
	{
		return $this->rewardCampaignRepository->findOneBy(['code' => $code]);
	}

	public function saveUserReward(UserReward $userReward)
	{
		$this->rewardManager->saveUserReward($userReward);
	}

	public function createUserRewardsQuery()
	{
		$userRewardsQuery = new UserRewardsQuery();

		return $userRewardsQuery;
	}

	public function fetchUserRewards(UserRewardsQuery $dealsQuery)
	{
		return $this->userRewardRepository->fetch($dealsQuery);
	}

	public function markUserRewardAsExpirationSendet(UserReward $userReward)
	{
		$userReward->sendExpirationNotification();
		$this->saveUserReward($userReward);
	}

	public function userRewardStatisticsByUserReward(RewardCampaign $rewardCampaign, ?\DateTime $assignFrom, ?\DateTime $assignTo)
	{
		$userRewardsByRewardCampaign = $this->getUserRewardsByRewardCampaign($rewardCampaign);

		if ($assignFrom) {
			$userRewardsByRewardCampaign->andWhere('ur.createdAt >= :assignFrom')->setParameter('assignFrom', $assignFrom);
		}

		if ($assignTo) {
			$userRewardsByRewardCampaign->andWhere('ur.createdAt <= :assignTo')->setParameter('assignTo', $assignTo);
		}

		$userRewards = $userRewardsByRewardCampaign->getQuery()->getResult();

		$data = [
			'countOfAssigns' => count($userRewards),
			'countOfActivations' => 0,
			'countOfUsed' => 0,
			'countOfExpired' => 0,
		];

		/** @var UserReward $userReward */
		foreach ($userRewards as $userReward) {
			if ($userReward->isActive()) {
				$data['countOfActivations']++;
			}

			if ($userReward->isUsed()) {
				$data['countOfUsed']++;
			}

			if ($userReward->isExpired()) {
				$data['countOfExpired']++;
			}
		}

		return $data;
	}
}
