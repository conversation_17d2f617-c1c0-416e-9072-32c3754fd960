<?php

namespace tipli\Model\Rewards2\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Rewards2\Entities\RewardCampaign;

class UserRewardRepository extends BaseRepository
{
	public function getUserRewards()
	{
		return $this->createQueryBuilder('ur');
	}

	public function findUserRewards(User $user, RewardCampaign $rewardCampaign = null, $valid = null, $active = null, $used = null)
	{
		$qb = $this->createQueryBuilder('ur')
			->innerJoin('ur.rewardCampaign', 'rc');

		$qb->andWhere('ur.user = :user')
			->setParameter('user', $user);

		if ($rewardCampaign) {
			$qb->andWhere('ur.rewardCampaign = :rewardCampaign')
				->setParameter('rewardCampaign', $rewardCampaign);
		}

		if ($active !== null) {
			if ($active) {
				$qb->andWhere('ur.activatedAt IS NOT NULL');
			} else {
				$qb->andWhere('ur.activatedAt IS NULL');
			}
		}

		if ($valid !== null) {
			if ($valid) {
				$qb->andWhere('ur.validTill >= :now')
					->andWhere('(rc.maximumCountOfActivations = 0 OR rc.maximumCountOfActivations > rc.countOfActivations)');
			} else {
				$qb->andWhere('(ur.validTill < :now OR (rc.maximumCountOfActivations > 0 AND rc.maximumCountOfActivations <= rc.countOfActivations))');
			}

			$qb->setParameter('now', new \DateTime());
		}

		if ($used !== null) {
			if ($used) {
				$qb->andWhere('ur.usedAt IS NOT NULL');
			} else {
				$qb->andWhere('ur.usedAt IS NULL');
			}
		}

		return $qb->getQuery()->getResult();
	}

	public function findCountOfActiveUserRewardByCampaign(RewardCampaign $rewardCampaign)
	{
		$qb = $this->createQueryBuilder('ur')
			->innerJoin('ur.rewardCampaign', 'c')
			->select('count(ur.id)')
			->andWhere('ur.activatedAt IS NOT NULL')
			->andWhere('ur.rewardCampaign = :rewardCampaign')
			->setParameter('rewardCampaign', $rewardCampaign);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findUserRewardByCampaign(User $user, RewardCampaign $rewardCampaign)
	{
		return $this->findOneBy(['user' => $user, 'rewardCampaign' => $rewardCampaign]);
	}

	public function getUserRewardByRewardCampaign(RewardCampaign $rewardCampaign)
	{
		$qb = $this->createQueryBuilder('ur')
			->leftJoin('ur.rewardCampaign', 'rc')
			->andWhere('ur.rewardCampaign = :rewardCampaign')
			->setParameter('rewardCampaign', $rewardCampaign);

		return $qb;
	}
}
