<?php

namespace tipli\Model\Rewards2\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;

class UserRewardsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	public function onlyExpiringTommorow()
	{
		$this->validTillBetween(
			(new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0),
			(new \DateTime())->modify('+ 1 day')->setTime(23, 59, 59)
		);

		$this->onlyUnused();
		$this->onlyActivated();

		return $this;
	}

	public function onlyActivated()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('ur.activatedAt IS NOT NULL');
		};

		return $this;
	}

	public function withType($type)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($type) {
			$queryBuilder
				->leftJoin('rc.condition', 'c')
				->andWhere('c.type = :conditionType')->setParameter('conditionType', $type);
		};

		return $this;
	}

	public function validTillBetween(\DateTime $validFrom, \DateTime $validTill)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($validFrom, $validTill) {
			$queryBuilder->andWhere('ur.validTill >= :validFrom')->setParameter('validFrom', $validFrom);
			$queryBuilder->andWhere('ur.validTill <= :validTill')->setParameter('validTill', $validTill);
		};

		return $this;
	}

	public function onlyUnused()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('ur.usedAt IS NULL');
		};

		return $this;
	}

	public function withUnsentExpirationNotification()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('ur.expirationNotificationSentAt IS NULL');
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('ur', 'ur.id')
			->select('ur')
			->leftJoin('ur.rewardCampaign', 'rc');

		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		return $queryBuilder;
	}
}
