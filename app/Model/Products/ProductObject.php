<?php

namespace tipli\Model\Products;

use tipli\Model\Shops\Entities\Shop;

class ProductObject
{
	/** @var int */
	private $id;

	/** @var Shop */
	private $shop;

	/** @var string */
	private $name;

	/** @var float */
	private $currentMinimalPrice;

	/** @var string */
	private $currency;

	/** @var float|null */
	private $statedOldPMaximalPrice;

	/** @var string|null */
	private $description;

	/** @var string|null */
	private $pictureUrl;

	/** @var string */
	private $deepUrl;

	/** @var bool|null */
	private $isTrend;

	/** @var bool|null */
	private $isFreeShipping;

	/** @var bool|null */
	private $isAvailable;

	/** @var int|null */
	private $percentageDiscount;

	public function __construct(Shop $shop, int $id, string $deepUrl, string $name, float $currentMinimalPrice, string $currency, ?float $statedOldPMaximalPrice, ?string $description, ?string $pictureUrl, ?bool $isTrend = false, ?bool $isFreeShipping = false, ?bool $isAvailable = false, ?int $percentageDiscount = null)
	{
		$this->shop = $shop;
		$this->id = $id;
		$this->name = $name;
		$this->currentMinimalPrice = $currentMinimalPrice;
		$this->currency = $currency;
		$this->description = $description;
		$this->pictureUrl = $pictureUrl;
		$this->statedOldPMaximalPrice = $statedOldPMaximalPrice;
		$this->deepUrl = $deepUrl;
		$this->isTrend = $isTrend;
		$this->isFreeShipping = $isFreeShipping;
		$this->isAvailable = $isAvailable;
		$this->percentageDiscount = $percentageDiscount;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getShop(): Shop
	{
		return $this->shop;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function getCurrentMinimalPrice(): float
	{
		return $this->currentMinimalPrice;
	}

	public function getStatedOldPMaximalPrice(): ?float
	{
		return $this->statedOldPMaximalPrice;
	}

	public function getCurrency(): string
	{
		return $this->currency;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function getPictureUrl(): ?string
	{
		return $this->pictureUrl;
	}

	public function getDeepUrl(): string
	{
		return $this->deepUrl;
	}

	public function isTrend(): ?bool
	{
		return $this->isTrend;
	}

	public function isFreeShipping(): ?bool
	{
		return $this->isFreeShipping;
	}

	public function isAvailable(): ?bool
	{
		return $this->isAvailable;
	}

	public function getPercentageDiscount()
	{
		return $this->percentageDiscount;
	}
}
