<?php

namespace tipli\Model\Products\Importers;

use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\Model\Products\Entities\Product;
use tipli\Model\Products\Entities\ShopProductData;
use tipli\Model\Products\ImportedProduct;
use tipli\Model\Products\ProductFacade;
use tipli\Model\Shops\Entities\ShopProductFeed;

class GoogleShoppingImporter implements IImporter
{
	private const HTTP_BASE_GOOGLE_COM_NS_1_0 = "http://base.google.com/ns/1.0";

	/** @var ProductFacade */
	private $productFacade;

	public function __construct(ProductFacade $productFacade)
	{
		$this->productFacade = $productFacade;
	}

	public function processProductFeed(ShopProductFeed $shopProductFeed): ShopProductFeed
	{
		$localization = $shopProductFeed->getShop()->getLocalization();

		$reader = new \XMLReader();
		$reader->open($shopProductFeed->getFeedUrl());

		$doc = new \DOMDocument();

		$u = 0;
		while ($reader->read() && $reader->name !== 'item' && $u++ < 1000);

		$i = 0;
		while ($reader->name === 'item') {
			$product = simplexml_import_dom($doc->importNode($reader->expand(), true));

			$googleProduct = $product->children(self::HTTP_BASE_GOOGLE_COM_NS_1_0);

			$freeShipping = isset($googleProduct->shipping) && (float) $googleProduct->shipping->price <= 0;

			$this->productFacade->scheduleImportedProduct(
				new ImportedProduct(
					ShopProductData::SOURCE_IMPORTER,
					$shopProductFeed->getId(),
					$shopProductFeed->getShop()->getId(),
					Strings::replace($googleProduct->title, '/[\s]+/', ' '),
					$googleProduct->description,
					$googleProduct->link,
					$googleProduct->image_link,
					(float) $googleProduct->price,
					(float) $googleProduct->sale_price > 0 ? (float) $googleProduct->sale_price : null,
					new \DateTime(),
					$product->brand,
					$this->resolveCategoryPath($googleProduct->product_type),
					$googleProduct->gtin,
					$this->resolveParameters($googleProduct),
					null,
					false,
					$this->resolveGender($googleProduct->gender),
					$localization->getCurrency(),
					$freeShipping,
					$googleProduct->availability == 'in stock'
				)
			);

			$i++;

			if ($i >= 100) {
				break;
			}

			$reader->next('item');
		}

		$reader->close();

		$shopProductFeed->setCountOfProcessedProducts($i);
		return $shopProductFeed;
	}

	private function resolveCategoryPath(string $categories)
	{
		return str_replace(['&gt;', ' > '], ' | ', $categories);
	}

	private function resolveParameters(\SimpleXMLElement $product)
	{
		$params = [];

		if ((string)$product->color) {
			$params[] = ['param' => 'color', 'value' => (string)$product->color];
		}
		if ((string)$product->material) {
			$params[] = ['param' => 'material', 'value' => (string)$product->material];
		}
		if ((string)$product->pattern) {
			$params[] = ['param' => 'pattern', 'value' => (string)$product->pattern];
		}
		if ((string)$product->size) {
			$params[] = ['param' => 'size', 'value' => (string)$product->size];
		}
		if ((string)$product->age_group) {
			$params[] = ['param' => 'age_group', 'value' => (string)$product->age_group];
		}

		return $params ? Json::encode($params) : null;
	}

	private function resolveGender(?string $gender): ?string
	{
		$gender = Strings::lower($gender);

		return in_array($gender, Product::GENDERS) ? $gender : null;
	}
}
