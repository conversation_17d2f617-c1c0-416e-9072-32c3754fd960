<?php

namespace tipli\Model\Products;

use Doctrine\ORM\QueryBuilder;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Queues\QueueFacade;
use tipli\Model\Files\FileStorage;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Products\Entities\Brand;
use tipli\Model\Products\Entities\Category;
use tipli\Model\Products\Entities\Product;
use tipli\Model\Products\Entities\ProductParameter;
use tipli\Model\Products\Entities\ShopProduct;
use tipli\Model\Products\Entities\ShopProductData;
use tipli\Model\Products\Producers\ProductImporterProducer;
use tipli\Model\Products\Repositories\BrandRepository;
use tipli\Model\Products\Repositories\ProductRepository;
use tipli\Model\Products\Repositories\ShopProductRepository;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Repositories\ShopProductFeedRepository;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Products\Queries\ProductsQuery;

class ProductFacade
{
	/** @var ProductManager */
	private $productManager;

	/** @var ShopProductRepository */
	private $shopProductRepository;

	/** @var ShopProductManager */
	private $shopProductManager;

	/** @var ProductRepository */
	private $productRepository;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var DealFacade */
	private $dealFacade;

	/** @var ProductDetailCrawler */
	private $productDetailCrawler;

	/** @var ProductImporterProducer */
	private $productImporterProducer;

	/** @var CategoryFacade */
	private $categoryFacade;

	/** @var FileStorage */
	private $fileStorage;

	/** @var ShopProductFeedRepository */
	private $shopProductFeedRepository;

	/** @var ProductPriorityResolver */
	private $productPriorityResolver;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var BrandManager */
	private $brandManager;

	/** @var BrandRepository */
	private $brandRepository;

	public function __construct(
		ShopProductRepository $shopProductRepository,
		ProductRepository $productRepository,
		ProductManager $productManager,
		ShopProductManager $shopProductManager,
		ShopFacade $shopFacade,
		DealFacade $dealFacade,
		ProductDetailCrawler $productDetailCrawler,
		ProductImporterProducer $productImporterProducer,
		CategoryFacade $categoryFacade,
		FileStorage $fileStorage,
		ShopProductFeedRepository $shopProductFeedRepository,
		ProductPriorityResolver $productPriorityResolver,
		ImageFilter $imageFilter,
		BrandManager $brandManager,
		BrandRepository $brandRepository,
		private QueueFacade $queueFacade
	) {
		$this->shopProductRepository = $shopProductRepository;
		$this->productManager = $productManager;
		$this->shopProductManager = $shopProductManager;
		$this->productRepository = $productRepository;
		$this->shopFacade = $shopFacade;
		$this->dealFacade = $dealFacade;
		$this->productDetailCrawler = $productDetailCrawler;
		$this->productImporterProducer = $productImporterProducer;
		$this->categoryFacade = $categoryFacade;
		$this->fileStorage = $fileStorage;
		$this->shopProductFeedRepository = $shopProductFeedRepository;
		$this->productPriorityResolver = $productPriorityResolver;
		$this->imageFilter = $imageFilter;
		$this->brandManager = $brandManager;
		$this->brandRepository = $brandRepository;
	}

	/**
	 * @return object|null|Product
	 */
	public function find($id)
	{
		return $this->productRepository->find($id);
	}

	public function findShopProduct($id)
	{
		return $this->shopProductRepository->find($id);
	}

	public function saveShopProduct(ShopProduct $shopProduct): ShopProduct
	{
		return $this->shopProductManager->saveShopProduct($shopProduct);
	}

	public function saveProduct(Product $product): Product
	{
		return $this->productManager->saveProduct($product);
	}

	public function saveProducts(array $products): void
	{
		$this->productManager->saveProducts($products);
	}

	public function scheduleImportedProduct(ImportedProduct $importedProduct)
	{
		$this->productImporterProducer->scheduleImportedProduct($importedProduct);
	}

	public function createProduct(Localization $localization, Shop $shop, string $deepUrl, string $name, float $currentMinimalPrice, ?string $ean, ?string $pictureUrl, ?string $description, ?string $brand, ?float $statedOldMaximalPrice, ?string $gender = null, ?string $currency = null, ?bool $freeShipping = null, ?bool $available = null)
	{
		return $this->productManager->createProduct($localization, $shop, $deepUrl, $name, $currentMinimalPrice, $ean, $pictureUrl, $description, $brand, $statedOldMaximalPrice, $gender, $currency, $freeShipping, $available);
	}

	public function createOrUpdateProduct(ImportedProduct $importedProduct): ShopProduct
	{
		$shop = $this->shopFacade->find($importedProduct->getShopId());

		/** @var ShopProduct|null $shopProduct */
		$shopProduct = $this->shopProductRepository->findByUrl($importedProduct->getUrl(), $shop);

		if ($shopProduct) {
			$today = new \DateTime();
			$today->setTime(0, 0, 0);

			$productUpdatedAt = clone $shopProduct->getUpdatedAt();
			$productUpdatedAt->setTime(0, 0, 0);

			if ($shopProduct->getCurrentPrice() !== $importedProduct->getPrice() || $productUpdatedAt < $today) {
				$this->shopProductManager->createShopProductData(
					$shopProduct,
					$importedProduct->getSource(),
					$importedProduct->getPrice(),
					$importedProduct->getReceivedAt(),
					$shopProduct->getCurrentPrice(),
					$shopProduct->getCurrentPrice() - $importedProduct->getPrice()
				);
			}

			if ($shopProduct->getCurrentPrice() !== $importedProduct->getPrice()) {
				$shopProduct->priceUpdated();
			}

			if ($pictureUrl = $importedProduct->getPictureUrl()) {
				$shopProduct->setPictureUrl($pictureUrl);
			}

			if ($brand = $importedProduct->getBrand()) {
				$shopProduct->setBrand($brand);
			}

			if ($labels = $importedProduct->getLabels()) {
				$shopProduct->setLabels($labels);
			}

			if ($description = $importedProduct->getDescription()) {
				$shopProduct->setDescription($description);
			}

			if ($name = $importedProduct->getName()) {
				$shopProduct->setName($name);
			}

			if ($ean = $importedProduct->getEan()) {
				$shopProduct->setEan($ean);
			}

			if ($currency = $importedProduct->getCurrency()) {
				$shopProduct->setCurrency($currency);
			}

			$shopProduct->setCurrentPrice($importedProduct->getPrice());
			$shopProduct->setStatedOldPrice($importedProduct->getOldPrice());
			$shopProduct->setCpc($importedProduct->getCpc());
			$shopProduct->setParams($importedProduct->getParams());
			$shopProduct->update();
		} else {
			$shopProduct = $this->shopProductManager->createShopProduct(
				$shop,
				$this->shopProductFeedRepository->find($importedProduct->getShopProductFeedId()),
				$importedProduct->getName(),
				$importedProduct->getPictureUrl(),
				$importedProduct->getDescription(),
				$importedProduct->getUrl(),
				$importedProduct->getPrice(),
				$importedProduct->getOldPrice(),
				$importedProduct->getLabels(),
				$importedProduct->getCurrency()
			);

			$this->shopProductManager->createShopProductData($shopProduct, $importedProduct->getSource(), $importedProduct->getPrice(), $importedProduct->getReceivedAt());
		}

		if ($product = $shopProduct->getProduct()) {
			$updated = false;
			$shop = $shopProduct->getShop();
			$localization = $shop->getLocalization();
			$productMinimalPrice = $product->getCurrentMinimalPrice();

			if ($productMinimalPrice != $shopProduct->getCurrentPrice()) {
				$updated = true;
				$product->setCurrentMinimalPrice($shopProduct->getCurrentPrice());
			}

			if ($product->getStatedOldPMaximalPrice() && $product->getStatedOldPMaximalPrice() < $shopProduct->getStatedOldPrice()) {
				$updated = true;
				$product->setStatedOldPMaximalPrice($shopProduct->getStatedOldPrice());
			}

			if ($brand = $importedProduct->getBrand()) {
				$updated = true;
				$product->setBrand($this->createOrReturnBrand($localization, $brand));
			}

			if ($gender = $importedProduct->getGender()) {
				$updated = true;
				$product->setGender($gender);
			}

			if ($currency = $importedProduct->getCurrency()) {
				$updated = true;
				$product->setCurrency($currency);
			}

			if ($updated) {
				$product->update();
			}
		} else {
			/** @var Product $product */
			$product = $this->productRepository->searchProduct($shopProduct->getLocalization(), $shopProduct->getName(), $shopProduct->getEan());

			if (!$product) {
				$shop = $shopProduct->getShop();
				$localization = $shop->getLocalization();
				$pictureUrl = $shopProduct->getPictureUrl();

				if (!$pictureUrl || !$shopProduct->getName()) {
					return $shopProduct;
				}

				$product = $this->createProduct(
					$localization,
					$shopProduct->getShop(),
					$shopProduct->getUrl(),
					$shopProduct->getName(),
					$shopProduct->getCurrentPrice(),
					$shopProduct->getEan(),
					$pictureUrl,
					strip_tags($shopProduct->getDescription()),
					$shopProduct->getBrand(),
					$shopProduct->getStatedOldPrice(),
					$importedProduct->getGender(),
					$importedProduct->getCurrency(),
					$importedProduct->isFreeShipping(),
					$importedProduct->isAvailable()
				);
			}

			$shopProduct->setProduct($product);

			if ($params = $importedProduct->getParams()) {
				$parameters = Json::decode($params);

				foreach ($parameters as $parameter) {
					$values = explode(',', $parameter->value);

					if (!in_array($parameter->param, ['barva', 'velikost', 'color', 'size'])) {
						continue;
					}

					foreach ($values as $value) {
						$name = str_replace(['barva', 'velikost'], ['color', 'size'], Strings::lower($parameter->param));

						$product->addProductParameter(new ProductParameter(
							$product,
							$name,
							$value
						));
					}
				}
			}
		}

		$product->setPriority($this->productPriorityResolver->resolveProductPriority($importedProduct));
		$product->setFreeShipping($importedProduct->isFreeShipping());
		$product->setAvailable($importedProduct->isAvailable());

		$this->saveShopProduct($shopProduct);
		$this->saveProduct($product);

		return $shopProduct;
	}

	public function updateProduct(ImportedProduct $importedProduct): ?Product
	{
		/** @var Shop $shop */
		$shop = $this->shopFacade->find($importedProduct->getShopId());

		$product = $this->productRepository->searchByDeepUrl($shop->getLocalization(), $importedProduct->getUrl());

		if (!$product) {
			return null;
		}

		if ($importedProduct->getTrendAt() !== $product->getTrendAt()) {
			$product->setTrendAt($importedProduct->getTrendAt());
			$product->update();
		}

		if ($importedProduct->getTrendOrder() !== $product->getTrendOrder() && $importedProduct->getTrendOrder() >= 1) {
			$product->setTrendOrder($importedProduct->getTrendOrder());
			$product->update();
		}

		if ($importedProduct->getPrice() != $product->getCurrentMinimalPrice()) {
			$product->setCurrentMinimalPrice($importedProduct->getPrice());
		}

		if ($importedProduct->getOldPrice() && $importedProduct->getOldPrice() > $importedProduct->getPrice()) {
			$product->setStatedOldPMaximalPrice($importedProduct->getOldPrice());
		}

		return $this->saveProduct($product);
	}

	public function findProductsToProcess(Shop $shop)
	{
		return $this->shopProductRepository->findProductsToProcess($shop);
	}

	public function findProductsWithActiveDeal()
	{
		return $this->shopProductRepository->findProductsWithActiveDeal();
	}

	public function updateProductDetail(ShopProduct $product, float $price, ?float $statedOldPrice = null, ?array $labels = null, ?string $description = null, ?string $brand = null): void
	{
		$deal = $product->getDeal();

		if ($product->getCurrentPrice() != $price) {
			$deal->setValidTill(new \DateTime());
			$deal->setPrice($price);
			$product->priceUpdated();

			$this->shopProductManager->createShopProductData($product, ShopProductData::SOURCE_CRAWLER, $price, new \DateTime(), $product->getCurrentPrice(), $product->getCurrentPrice() - $price);
		}

		if ($description) {
			$product->setDescription($description);
		}

		if ($brand) {
			$product->setBrand($brand);
		}

		$product->setCurrentPrice($price);
		$product->setStatedOldPrice($statedOldPrice);
		$product->setLabels(implode(',', $labels));

		$deal->updateWithProduct($product);

		$this->dealFacade->saveDeal($deal);
		$this->shopProductManager->saveShopProduct($product);
	}

	public function findShopProductsToProcessCategories()
	{
		return $this->shopProductRepository->findShopProductsToProcessCategories();
	}

	public function findShopProductsToAssignTag($limit = null, $offset = null)
	{
		return $this->shopProductRepository->findShopProductsToAssignTag($limit, $offset);
	}

	public function findCountOfProductsToAssignTag(Localization $localization)
	{
		return $this->productRepository->findCountOfProductsToAssignTag($localization);
	}

	public function findCountOfProductsWithTag(Localization $localization)
	{
		return $this->productRepository->findCountOfProductsWithTag($localization);
	}

	public function findCountOfProductsToProcessCategory()
	{
		return $this->shopProductRepository->findCountOfProductsToProcessCategory();
	}

	public function createProductsQuery(Localization $localization = null)
	{
		$productsQuery = new ProductsQuery();

		if ($localization) {
			$productsQuery->withLocalization($localization);
		}

		return $productsQuery;
	}

	public function fetch(ProductsQuery $productsQuery)
	{
		return $this->productRepository->fetch($productsQuery);
	}

	public function findTopProducts($limit = 8, ?Tag $tag = null)
	{
		return $this->shopProductRepository->findTopProducts($limit, $tag);
	}

	public function findProductInTag(Tag $tag)
	{
		return $this->productRepository->findProductInTag($tag);
	}

	public function searchProduct(Localization $localization, string $name, ?string $ean): ?Product
	{
		return $this->productRepository->searchProduct($localization, $name, $ean);
	}

	public function findShopProductsWithoutProduct()
	{
		return $this->shopProductRepository->findShopProductsWithoutProduct();
	}

	public function findUnassignedProductsInTag(Tag $tag, array $categories): array
	{
		return $this->productRepository->findUnassignedProductsInTag($tag, $categories);
	}

	public function findCountOfProductsWithoutTag()
	{
		return $this->shopProductRepository->findCountOfProductsWithoutTag();
	}

	public function findCountOfShopProducts()
	{
		return $this->shopProductRepository->findCountOfShopProducts();
	}

	public function findShopProductsToAssignShopToCategory()
	{
		return $this->shopProductRepository->findShopProductsToAssignShopToCategory();
	}

	public function findShopProductsInCategory(Category $category, $limit = 24, $groupByShop = false)
	{
		return $this->shopProductRepository->findShopProductsInCategory($category, $limit, $groupByShop);
	}

	public function getProducts(): QueryBuilder
	{
		return $this->productRepository->getProducts();
	}

	public function findCountOfProducts(Localization $localization)
	{
		return $this->productRepository->findCountOfProducts($localization);
	}

	public function findCountOfShopProductsWithoutCategoryByShops()
	{
		return $this->shopProductRepository->findCountOfShopProductsWithoutCategoryByShops();
	}

	public function findCountOfShopProductsByShops()
	{
		return $this->shopProductRepository->findCountOfShopProductsByShops();
	}

	public function findCountOfShopProductsWithoutCategory(Localization $localization)
	{
		return $this->shopProductRepository->findCountOfShopProductsWithoutCategory($localization);
	}

	public function findByCondition(Localization $localization, string $expression, string $column, string $condition, string $rule, ?Tag $tag = null)
	{
		return $this->productRepository->findByCondition($localization, $expression, $column, $condition, $rule, $tag);
	}

	public function findForProductChecker()
	{
		return $this->productRepository->findForProductChecker();
	}

	public function scheduleProcessShopProductsInCategory(Category $category)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_products_shop_product
            SET categories_processed_at = \'1000-01-01\'
            WHERE category_id = ' . $category->getId() . '
        ');
	}

	public function removeAssignedProductsFromCategory(array $tagsPath, Category $category)
	{
		$assignedShopProducts = $this->shopProductRepository->findShopProductsInCategory($category);

		$productsToSave = [];

		/** @var ShopProduct $shopProduct */
		foreach ($assignedShopProducts as $shopProduct) {
			$product = $shopProduct->getProduct();

			if (!$product) {
				continue;
			}

			foreach ($tagsPath as $tag) {
				$productTag = $product->getProductTag($tag);

				if (!$productTag) {
					continue;
				}

				$product->removeProductTag($productTag);
			}

			$productsToSave[] = $product;
		}

		$this->saveProducts($productsToSave);
	}

	public function findIn(array $ids)
	{
		return $this->productRepository->findIn($ids);
	}

	public function findBrandsForGeneratedDescription(Tag $tag, ?Brand $exceptBrand = null)
	{
		return $this->brandRepository->findBrandsForGeneratedDescription($tag, $exceptBrand);
	}

	public function findProductsForGeneratedDescription(Tag $tag, ?Brand $brand, bool $sortCheapest = false, ?Brand $exceptBrand = null): array
	{
		return $this->productRepository->findProductsForGeneratedDescription($tag, $brand, $sortCheapest, $exceptBrand);
	}

	public function createOrReturnBrand(Localization $localization, string $brandName): Brand
	{
		$slug = Strings::webalize($brandName);

		if (!$brand = $this->brandRepository->findBySlug($localization, $slug)) {
			$brand = $this->brandManager->createBrand($localization, $brandName);
		}

		return $brand;
	}

	public function findBrandBySlug(Localization $localization, string $slug)
	{
		return $this->brandRepository->findBySlug($localization, $slug);
	}

	public function findBrandsInTag(Tag $tag)
	{
		return $this->brandRepository->findBrandsInTag($tag);
	}

	public function findMinAndMaxPrice(Tag $tag, Brand $defaultBrand): ?array
	{
		return $this->productRepository->findMinAndMaxPrice($tag, $defaultBrand);
	}

	/** @return array|null|Product */
	public function findCheapestProducts(Tag $tag, ?Brand $defaultBrand = null, $limit = 1)
	{
		return $this->productRepository->findCheapestProducts($tag, $defaultBrand, $limit);
	}

	public function findInWithLocalImageStored(array $ids)
	{
		return $this->productRepository->findInWithLocalImageStored($ids);
	}

	public function findByPicturePath(string $picturePath): ?Product
	{
		return $this->productRepository->findByPicturePath($picturePath);
	}
}
