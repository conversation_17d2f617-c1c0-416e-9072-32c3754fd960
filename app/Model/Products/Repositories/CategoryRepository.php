<?php

namespace tipli\Model\Products\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use Nette\Utils\Strings;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Products\Entities\Category;
use tipli\Model\Products\Entities\CategoryTag;
use tipli\Model\Tags\Entities\Tag;

class CategoryRepository extends BaseRepository
{
	public function getCategories()
	{
		return $this->createQueryBuilder('c');
	}

	public function findByParentId(int $parentId)
	{
		return $this->getCategories()
			->andWhere('c.parentCategory = :parentCategoryId')
			->setParameter('parentCategoryId', $parentId)
			->getQuery()
			->getResult();
	}

	public function findIn(array $categoryIds)
	{
		return $this->getCategories()
			->andWhere('c.id IN (:ids)')
			->setParameter('ids', $categoryIds)
			->getQuery()
			->getResult();
	}

	public function findCategoriesToExport()
	{
		return $this->getCategories()
			->andWhere('c.ignored = false')
			->getQuery()
			->getResult();
	}

	public function findCategoriesToAssignRootcategory()
	{
		return $this->getCategories()
			->andWhere('c.rootCategory IS NULL')
			->andWhere('c.parentCategory IS NOT NULL')
			->setMaxResults(100)
			->getQuery()
			->getResult();
	}

	public function findCountOfCategoriesToAssignTagsByShops()
	{
		return $this->getCategories()
			->select('COUNT(c.id) as countOfCategories, s.id as shopId')
			->leftJoin('c.shops', 's')
			->leftJoin('c.categoryTags', 'ct')
			->where('ct IS NULL')
			->andWhere('s IS NOT NULL')
			->groupBy('s')
			->getQuery()
			->getResult();
	}

	public function findCountOfCategoriesWithAssignTagsByShops()
	{
		return $this->getCategories()
			->select('COUNT(c.id) as countOfCategories, s.id as shopId')
			->leftJoin('c.shops', 's')
			->leftJoin('c.categoryTags', 'ct')
			->where('ct IS NOT NULL')
			->andWhere('s IS NOT NULL')
			->groupBy('s')
			->getQuery()
			->getResult();
	}

	public function findCountOfIgnoredCategoriesByShops()
	{
		return $this->getCategories()
			->select('COUNT(c.id) as countOfCategories, s.id as shopId')
			->leftJoin('c.shops', 's')
			->andWhere('s IS NOT NULL')
			->andWhere('c.ignored = true')
			->groupBy('s')
			->getQuery()
			->getResult();
	}

	public function findCategoriesToAssignShops()
	{
		return $this->getCategories()
			->orderBy('c.assignShopsProcessedAt', 'ASC')
			->setMaxResults(20)
			->getQuery()
			->getResult();
	}

	public function findByTag(Tag $tag)
	{
		return $this->getCategories()
			->leftJoin('c.categoryTags', 'ct')
			->leftJoin('ct.tag', 't')
			->andWhere('t = :tag')
			->setParameter('tag', $tag)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findCategoriesToProcessTags()
	{
		return $this->getCategories()
			->leftJoin('c.categoryTags', 'ct', 'WITH', 'ct.category = c AND ct.state = :approved')
			->andWhere('c.processTagsAt IS NOT NULL')
			->andWhere('c.processTagsAt <= :now')
			->andWhere('ct IS NULL')
			->setParameter('now', new \DateTime())
			->setParameter('approved', CategoryTag::STATE_APPROVED)
			->addOrderBy('c.processTagsAt', 'ASC')
			->andWhere('c.countOfProducts > 10')
			->setMaxResults(10)
			->getQuery()
			->getResult();
	}

	public function findSimilarCategoriesWithTags(Localization $localization, Category $category, bool $extractOnlyFirstWord = false)
	{
		$name = $category->getName();

		if ($extractOnlyFirstWord && Strings::contains($name, ' ')) {
			$words = explode(' ', $category->getName());
			$name = $words[0];
		}

		return $this->getCategories()
			->innerJoin('c.categoryTags', 'ct')
			->andWhere('c.name LIKE :name OR c.originalPath LIKE :name')
			->setParameter('name', '%' . $name . '%')
			->andWhere('c != :category')
			->setParameter('category', $category)
			->andWhere('ct.state = :approved')
			->setParameter('approved', CategoryTag::STATE_APPROVED)
			->andWhere('c.localization = :localization')
			->setParameter('localization', $localization)
			->setMaxResults(50)
			->getQuery()
			->getResult();
	}

	public function findCountOfCategoriesByProductRange(Localization $localization, int $min, ?int $max = null)
	{
		$qb = $this->getCategories()
			->select('COUNT(DISTINCT c.id) as cnt')
			->leftJoin('c.categoryTags', 'ct', 'WITH', 'ct.category = c AND ct.state = :approved')
			->setParameter('approved', CategoryTag::STATE_APPROVED)
			->where('c.countOfProducts >= :min')
			->andWhere('ct IS NULL')
			->andWhere('c.ignored = false')
			->setParameter('min', $min)
			->andWhere('c.localization = :localization')
			->setParameter('localization', $localization);

		if ($max) {
			$qb->andWhere('c.countOfProducts <= :max')
				->setParameter('max', $max);
		}

		return $qb->getQuery()
			->getSingleScalarResult();
	}
}
