<?php

namespace tipli\Model\Cache;

use Nette\Caching\Cache;
use Nette\Caching\Storage;

class CacheManager
{
	public const TEMPLATE_NAMESPACE = 'Nette.Templating.Cache';

	/** @var Storage */
	private $storage;

	public function __construct(Storage $storage)
	{
		$this->storage = $storage;
	}

	public function cleanByTags(?array $tags = [], $namespace = null): void
	{
		$namespace = $namespace ? : self::TEMPLATE_NAMESPACE;

		$cache = new Cache($this->storage, $namespace);
		$cache->clean([Cache::TAGS => $tags]);
	}
}
