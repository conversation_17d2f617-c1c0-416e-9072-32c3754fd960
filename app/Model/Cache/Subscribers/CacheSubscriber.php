<?php

namespace tipli\Model\Cache\Subscribers;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Events\ConstantRewardUpdatedEvent;
use tipli\Model\Account\Events\ShareRewardSavedEvent;
use tipli\Model\Cache\CacheFacade;
use tipli\Model\Deals\Events\DealSavedEvent;
use tipli\Model\Deals\Events\LeafletSavedEvent;
use tipli\Model\Rewards\Entities\ConstantReward;
use tipli\Model\Shops\Events\FavoriteShopUpdatedEvent;
use tipli\Model\Shops\Events\ShopsUpdatedEvent;
use tipli\Model\Shops\Events\ShopUpdatedEvent;
use tipli\Model\Shops\Events\DescriptionBlockSavedEvent;
use tipli\Model\Shops\Events\ForeignShopUpdatedEvent;
use tipli\Model\Shops\Events\OfferRemovedEvent;
use tipli\Model\Shops\Events\OfferSavedEvent;
use tipli\Model\Shops\OfferResolver;
use tipli\Model\Transactions\Events\TransactionSavedEvent;

class CacheSubscriber implements EventSubscriberInterface
{
	/** @var CacheFacade */
	private $cacheFacade;

	public function __construct(CacheFacade $cacheFacade)
	{
		$this->cacheFacade = $cacheFacade;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			TransactionSavedEvent::class => 'transactionChanged',
			ShareRewardSavedEvent::class => 'shareRewardChanged',
			OfferSavedEvent::class => 'offerChanged',
			OfferRemovedEvent::class => 'offerRemoved',
			DealSavedEvent::class => 'dealChanged',
			DescriptionBlockSavedEvent::class => 'descriptionBlockSaved',
			LeafletSavedEvent::class => 'leafletSaved',
			ForeignShopUpdatedEvent::class => 'foreignShopUpdated',
			ConstantRewardUpdatedEvent::class => 'constantRewardUpdated',
			ShopUpdatedEvent::class => 'shopUpdated',
			ShopsUpdatedEvent::class => 'shopsUpdated',
			FavoriteShopUpdatedEvent::class => 'favoriteShopUpdated',
		];
	}

	public function offerChanged(OfferSavedEvent $offerSavedEvent)
	{
		$offer = $offerSavedEvent->getOffer();

		$shop = $offer->getShop();

		$this->cacheFacade->cleanByTags('offers', OfferResolver::class);
		$this->cacheFacade->cleanByTags(CacheFacade::getShopTag($shop));
	}

	public function offerRemoved(OfferRemovedEvent $offerRemovedEvent)
	{
		$shop = $offerRemovedEvent->getShop();

		$this->cacheFacade->cleanByTags('offers', OfferResolver::class);
		$this->cacheFacade->cleanByTags(CacheFacade::getShopTag($shop));
	}

	public function transactionChanged(TransactionSavedEvent $transactionSavedEvent)
	{
		$transaction = $transactionSavedEvent->getTransaction();

		if (!$transaction->hasUser()) {
			return;
		}

		bdump('clear cache by tag:' . CacheFacade::getUserTransactionsTag($transaction->getUser()));

		$this->cacheFacade->cleanByTags(
			CacheFacade::getUserTransactionsTag($transaction->getUser())
		);
	}

	public function shareRewardChanged(ShareRewardSavedEvent $shareRewardSavedEvent)
	{
		$shareReward = $shareRewardSavedEvent->getShareReward();

		if (!$shareReward->getUser()) {
			return;
		}

		bdump('clear cache by tag:' . CacheFacade::getUserTransactionsTag($shareReward->getUser()));

		$this->cacheFacade->cleanByTags(
			CacheFacade::getUserRewardsTag($shareReward->getUser())
		);
	}

	public function dealChanged(DealSavedEvent $dealSavedEvent)
	{
		$shop = $dealSavedEvent->getDeal()->getShop();

		if ($dealSavedEvent->getDeal()->isCouponType()) {
			$this->cacheFacade->cleanByTags([CacheFacade::getShopTag($shop), 'coupons']);
		} else {
			$this->cacheFacade->cleanByTags([CacheFacade::getShopTag($shop), 'deals']);
		}

		$this->cacheFacade->cleanByTags(CacheFacade::getShopDealsTag($shop));
	}

	public function descriptionBlockSaved(DescriptionBlockSavedEvent $descriptionBlockSavedEvent)
	{
		$shop = $descriptionBlockSavedEvent->getDescriptionBlock()->getShop();

		$this->cacheFacade->cleanByTags(CacheFacade::getShopTag($shop));
	}

	public function leafletSaved(LeafletSavedEvent $leafletSavedEvent)
	{
		$locale = $leafletSavedEvent->getLeaflet()->getLocalization()->getLocale();

		$this->cacheFacade->cleanByTags('leaflets/' . $locale);
	}

	public function foreignShopUpdated(ForeignShopUpdatedEvent $foreignShopUpdatedEvent)
	{
		$shop = $foreignShopUpdatedEvent->getForeignShop()->getParentShop();

		$this->cacheFacade->cleanByTags('foreignShop/' . $shop->getId());
	}

	public function constantRewardUpdated(ConstantRewardUpdatedEvent $constantRewardUpdatedEvent)
	{
		if ($constantRewardUpdatedEvent->getConstantReward()->getType() !== ConstantReward::TYPE_BONUS_ADDON) {
			return;
		}

		$this->cacheFacade->cleanByTags('addonNavbar');
	}

	public function shopUpdated(ShopUpdatedEvent $shopUpdatedEvent)
	{
		$shop = $shopUpdatedEvent->getShop();

		$this->cacheFacade->cleanByTags(CacheFacade::getShopTag($shop));

		$this->cacheFacade->cleanByTags(CacheFacade::getDealsTag());

		if ($shop->getPageExtension()) {
			$this->cacheFacade->cleanByTags('pageExtension/' . $shop->getPageExtension()->getId());
		}
	}

	public function shopsUpdated(): void
	{
		$this->cacheFacade->cleanByTags(
			CacheFacade::getShopsTag()
		);
	}

	public function favoriteShopUpdated(FavoriteShopUpdatedEvent $favoriteShopUpdatedEvent): void
	{
		$favoriteShop = $favoriteShopUpdatedEvent->getFavoriteShop();

		$this->cacheFacade->cleanByTags(
			CacheFacade::getUserFavoriteShopsTag($favoriteShop->getUser())
		);
	}
}
