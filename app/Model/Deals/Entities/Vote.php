<?php

namespace tipli\Model\Deals\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Deals\Repositories\VoteRepository")
 * @ORM\Table(name="tipli_deals_vote", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="slug_unique", columns={"user_id", "deal_id"})
 * }
 * )
 */
class Vote
{
	public const POSITIVE_VALUE = 1;
	public const NEGATIVE_VALUE = -1;

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Deals\Entities\Deal")
	 * @ORM\JoinColumn(name="deal_id", referencedColumnName="id")
	 */
	private $deal;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
	 */
	private $user;

	/**
	 * @ORM\Column(type="smallint")
	 */
	private $value;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(Deal $deal, User $user, $value)
	{
		$this->deal = $deal;
		$this->user = $user;
		$this->value = $value;
		$this->createdAt = new \DateTime();
	}

	/**
	 * @return mixed
	 */
	public function getValue()
	{
		return $this->value;
	}

	public function isPositiveVote()
	{
		return $this->getValue() === self::POSITIVE_VALUE;
	}

	public function isNegativeVote()
	{
		return $this->getValue() === self::NEGATIVE_VALUE;
	}
}
