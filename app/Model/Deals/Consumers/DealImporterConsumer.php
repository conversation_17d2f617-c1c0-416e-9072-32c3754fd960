<?php

namespace tipli\Model\Deals\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Deals\DealImporter;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Messages\DealImporterMessage;
use tipli\Model\RabbitMq\BaseConsumer;

class DealImporterConsumer extends BaseConsumer implements IConsumer
{
	/** @var DealImporter $dealImporter */
	private $dealImporter;

	public function __construct(DealImporter $dealImporter)
	{
		$this->dealImporter = $dealImporter;
	}

	public function consume(Message $data): int
	{
		$message = DealImporterMessage::fromJson($data->content);

		$importedDeal = ImportedDeal::createFromArray($message->getImportedDealData());

		$this->dealImporter->processImportedDeal($importedDeal);

		return IConsumer::MESSAGE_ACK;
	}
}
