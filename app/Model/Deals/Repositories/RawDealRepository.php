<?php

namespace tipli\Model\Deals\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Deals\Entities\RawDeal;

class RawDealRepository extends BaseRepository
{
	public function getRawDeals()
	{
		return $this->createQueryBuilder('r');
	}

	public function getRawDealsWithoutCompetitive()
	{
		return $this->getRawDeals()
			->andWhere('r.source != :competitor')->setParameter('competitor', RawDeal::SOURCE_COMPETITIVE);
	}

	public function findCountOfWaitingRawDealsWithoutCompetitive()
	{
		$qb = $this->getRawDeals()
			->select('count(r.id)')
			->andWhere('r.removedAt IS NULL')
			->andWhere('r.deal IS NULL')
			->andWhere('r.source != :competitor')->setParameter('competitor', RawDeal::SOURCE_COMPETITIVE);

		return $qb->getQuery()->getSingleScalarResult();
	}
}
