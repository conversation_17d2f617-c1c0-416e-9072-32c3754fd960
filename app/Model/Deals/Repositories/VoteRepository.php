<?php

namespace tipli\Model\Deals\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Deals\Entities\Deal;

class VoteRepository extends BaseRepository
{
	public function findVote(Deal $deal, User $user)
	{
		return $this->findOneBy(['deal' => $deal, 'user' => $user]);
	}

	public function findVoteTotalScore(Deal $deal)
	{
		$qb = $this->createQueryBuilder('v')
			->select('IFNULL(sum(v.value), 0)')
			->andWhere('v.deal = :deal')
			->setParameter('deal', $deal);

		return $qb->getQuery()->getSingleScalarResult();
	}
}
