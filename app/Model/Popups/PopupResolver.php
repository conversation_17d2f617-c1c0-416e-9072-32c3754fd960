<?php

namespace tipli\Model\Popups;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\UrlScript;
use Nette\SmartObject;
use Nette\Utils\Strings;
use tipli\Account\Queries\PopupCampaignsQuery;
use tipli\Model\Account\Entities\User;
use tipli\Model\Groups\GroupFacade;
use tipli\Model\Layers\BrowserTokenLayer;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Popups\Entities\PopupCampaign;
use tipli\Model\Popups\Entities\PopupCampaignCondition;
use tipli\Model\Popups\Entities\PopupInteraction;
use tipli\Model\Popups\Repositories\PopupCampaignRepository;
use tipli\Model\Popups\Repositories\PopupInteractionRepository;

class PopupResolver
{
	use SmartObject;

	private const PAID_UTM_SOURCES = ['facebook', 'google'];

	private Cache $cache;

	/** @var \Nette\Security\IIdentity|User|null */
	private User|\Nette\Security\IIdentity|null $loggedUserIdentity;

	public function __construct(
		private PopupCampaignRepository $popupCampaignRepository,
		private \Nette\Security\User $user,
		private BrowserTokenLayer $browserTokenLayer,
		private PopupInteractionRepository $popupInteractionRepository,
		private LocalizationFacade $localizationFacade,
		private Storage $storage,
		private UtmLayer $utmLayer,
		private GroupFacade $groupFacade
	) {
		$this->loggedUserIdentity = $this->user->isLoggedIn() ? $this->user->getIdentity() : null;
		$this->cache = new Cache($storage, self::class);
	}

	public function resolvePopup(UrlScript $url, $presenterName)
	{
		$noPopupCacheKey = 'noPopup-' . $this->getUserClientUniqueIdentifier();

		if (!$this->arePopupsEnabledForUser()) {
			return false;
		}

		if ($this->cache->load($noPopupCacheKey)) {
			return null;
		}

		$popupCampaigns = $this->getAvailablePopupCampaigns($presenterName);

		/** @var PopupCampaign $popupCampaign */
		foreach ($popupCampaigns as $popupCampaign) {
			$matchesConditions = true;

			if (in_array($popupCampaign->getId(), $this->getIdsOfSeenPopupCampaigns())) {
				continue;
			}

			if ($popupCampaign->getPopupCampaignCondition()->getUrl() && $this->resolvePopupUrlCondition($popupCampaign, $url)) {
				$matchesConditions = true;
			}

			if ($popupCampaign->getPopupCampaignCondition()->getGroup() && $this->resolvePopupGroupCondition($popupCampaign) === false) {
				$matchesConditions = false;
			}

			if ($matchesConditions) {
				return $popupCampaign;
			}
		}

		$this->cache->save($noPopupCacheKey, true, [Cache::EXPIRATION => '15 minutes']);

		return null;
	}

	private function getAvailablePopupCampaigns($presenterName)
	{
		$popupCampaignsQuery = (new PopupCampaignsQuery())
			->withLocalization($this->localizationFacade->getCurrentLocalization())
			->onlyValid();

		$conditionUser = $this->loggedUserIdentity ? PopupCampaignCondition::CONDITION_USER_LOGGED_IN : PopupCampaignCondition::CONDITION_USER_NOT_LOGGED_IN;
		$conditionUserActive = $this->loggedUserIdentity && $this->loggedUserIdentity->isActiveUser() ? PopupCampaignCondition::CONDITION_USER_ACTIVE : PopupCampaignCondition::CONDITION_USER_NOT_ACTIVE;
		$conditionAddon = $this->loggedUserIdentity && $this->loggedUserIdentity->hasInstalledAddon() ? PopupCampaignCondition::CONDITION_ADDON_INSTALLED : PopupCampaignCondition::CONDITION_ADDON_NOT_INSTALLED;
		$conditionPaidSource = in_array($this->utmLayer->getUtm()->getUtmSource(), self::PAID_UTM_SOURCES) ? PopupCampaignCondition::CONDITION_ONLY_PAID_SOURCE : PopupCampaignCondition::CONDITION_NOT_PAID_SOURCE;
		$conditionLeafletSection = Strings::startsWith($presenterName, 'Front:Leaflets') ? PopupCampaignCondition::CONDITION_ONLY_LEAFLET_SECTION : PopupCampaignCondition::CONDITION_NOT_LEAFLET_SECTION;

		$popupCampaignsQuery->withConditionUser($conditionUser);
		$popupCampaignsQuery->withConditionUserActive($conditionUserActive);
		$popupCampaignsQuery->withConditionAddon($conditionAddon);
		$popupCampaignsQuery->withConditionPaidSource($conditionPaidSource);
		$popupCampaignsQuery->withConditionLeafletSection($conditionLeafletSection);

		return $this->popupCampaignRepository->fetch($popupCampaignsQuery);
	}

	private function resolvePopupUrlCondition(PopupCampaign $popupCampaign, UrlScript $url)
	{
		$conditionUrl = $popupCampaign->getPopupCampaignCondition()->getUrl();

		if ($conditionUrl !== null && !Strings::startsWith($conditionUrl, '/')) {
			$conditionUrl = '/' . $conditionUrl;
		}

		/** @var string $url */
		$url = $url->getPath();

		if ($conditionUrl !== null && Strings::contains($conditionUrl, '*')) {
			$pattern = str_replace(['*', '/'], ['(.+)?', '\/'], $conditionUrl);

			preg_match('/' . $pattern . '/', $url, $matches, PREG_OFFSET_CAPTURE);

			if (count($matches) > 0) {
				return true;
			}

			if (Strings::endsWith($conditionUrl, '/*')) {
				return str_replace('/*', '', $conditionUrl) === $url;
			}

			return false;
		}

		return $conditionUrl === $url;
	}

	private function getIdsOfSeenPopupCampaigns()
	{
		$browserToken = $this->loggedUserIdentity ? null : $this->browserTokenLayer->getBrowserToken();

		$cacheKey = 'idsOfSeenPopupCampaigns-' . ($this->loggedUserIdentity !== null ? 'u' . $this->loggedUserIdentity->getId() : 'b' . $browserToken->getId());

		if ($idsOfSeenPopupCampaigns = $this->cache->load($cacheKey)) {
			return $idsOfSeenPopupCampaigns;
		}

		$idsOfSeenPopupCampaigns = [];
		$onlyOpenedPopupInteractions = $this->popupInteractionRepository->findPopupInteractions(null, $this->loggedUserIdentity, $browserToken);

		/** @var PopupInteraction $popupInteraction */
		foreach ($onlyOpenedPopupInteractions as $popupInteraction) {
			$popupCampaign = $popupInteraction->getPopupCampaign();
			if (
				(!$popupInteraction->isClosed() && !$popupInteraction->isClicked() && $popupInteraction->getOpenedAt() > (new \DateTime())->modify('- 7 days'))
				|| $popupInteraction->isClosed()
				|| $popupInteraction->isClicked()
			) {
				$idsOfSeenPopupCampaigns[] = $popupCampaign->getId();
			}
		}

		if (count($idsOfSeenPopupCampaigns) > 0) {
			$this->cache->save('idsOfSeenPopupCampaigns', $idsOfSeenPopupCampaigns, [Cache::EXPIRE => '1 hour']);
		}

		return $idsOfSeenPopupCampaigns;
	}

	private function getUserClientUniqueIdentifier()
	{
		if ($this->loggedUserIdentity) {
			return 'user-' . $this->loggedUserIdentity->getId();
		}

		return 'host-' . $this->browserTokenLayer->getBrowserTokenString();
	}

	private function arePopupsEnabledForUser(): bool
	{
		$user = $this->loggedUserIdentity;

		if ($user && $user->getCreatedAt() >= (new \DateTime())->modify('- 24 hours')) { // nezobrazovat popupy uzivatelum mladsim 24 hod
			return false;
		}

		if ($user && $user->getLastLoggedInAt() >= (new \DateTime())->modify('- 1 minute')) { // nezobrazovat popupy uzivatelum co se pred chvili prihlasili
			return false;
		}

		return true;
	}

	private function resolvePopupGroupCondition(PopupCampaign $popupCampaign): bool
	{
		$group = $popupCampaign->getPopupCampaignCondition()->getGroup();

		if ($group === null) {
			return true;
		}

		if ($this->loggedUserIdentity !== null && $this->groupFacade->isUserInGroup($group, $this->loggedUserIdentity) === true) {
			return true;
		}

		return false;
	}
}
