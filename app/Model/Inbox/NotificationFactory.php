<?php

namespace tipli\Model\Inbox;

use Nette\Localization\Translator;
use tipli\InvalidArgumentException;
use tipli\Model\Account\DeviceTokenFacade;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\Entities\NotificationBody;
use tipli\Model\Messages\SendingPolicyManager;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Templates\DataObjects\NotificationTemplateDataObject;
use tipli\Model\Transactions\AmountFilter;
use tipli\Model\Transactions\Entities\Transaction;

class NotificationFactory
{
	/** @var Translator */
	private $translator;

	/** @var AmountFilter */
	private $amountFilter;

	/** @var UserFacade */
	private $userFacade;

	public function __construct(
		Translator $translator,
		AmountFilter $amountFilter,
		UserFacade $userFacade,
		private DeviceTokenFacade $deviceTokenFacade,
		private SendingPolicyManager $sendingPolicyManager
	) {
		$this->translator = $translator;
		$this->amountFilter = $amountFilter;
		$this->userFacade = $userFacade;
	}

	/**
	 * @param User $user
	 * @param Shop $shop
	 * @param array $transactions
	 * @return Notification
	 */
	public function createTransactionsRegistrationNotification(User $user, Shop $shop, array $transactions)
	{
		/** @var integer $totalAmount */
		$totalAmount = 0;

		/** @var Transaction $transaction */
		foreach ($transactions as $transaction) {
			if (!($transaction instanceof Transaction) || $transaction->getShop() !== $shop || $transaction->getUser() !== $user) {
				throw new InvalidArgumentException('Invalid  transaction.');
			}

			$totalAmount += $transaction->getAmount();
		}

		$countOfTransactions = count($transactions);
//		$rawTotalAmount = $totalAmount;
		$totalAmount = $this->amountFilter->__invoke($totalAmount);

		if ($countOfTransactions === 0) {
			throw new InvalidArgumentException('No transactions.');
		}

		$params = [
			'shop' => $shop->getName(),
			'amount' => $totalAmount,
			'count' => $countOfTransactions,
		];

		$title = $this->translator->translate('notification.transactionsRegistration.title', $countOfTransactions, $params, null, $user->getLocale());
		$content = $this->translator->translate('notification.transactionsRegistration.content', $countOfTransactions, $params, null, $user->getLocale());

		return $this->buildNotification($user, $title, $content, $shop->getLogo(), Notification::TYPE_TRANSACTION_REGISTRATION);
	}

	/**
	 * @param Payout $payout
	 * @return Notification
	 */
	public function createPayoutConfirmationNotification(Payout $payout)
	{
		$params = [
			'amount' => $this->amountFilter->__invoke($payout->getAmount()),
		];

		$title = $this->translator->translate('notification.payoutConfirmation.title', null, [], null, $payout->getUser()->getLocale());
		$content = $this->translator->translate('notification.payoutConfirmation.content', null, $params, null, $payout->getUser()->getLocale());

		return $this->buildNotification($payout->getUser(), $title, $content, null, Notification::TYPE_PAYOUT_CONFIRMATION);
	}

	/**
	 * @param User $user
	 * @param string $title
	 * @param string $content
	 * @param Image|null $image
	 * @param string $type
	 * @param \DateTime|null $scheduledAt
	 * @param bool $shouldBePushed
	 * @return Notification
	 */
	private function buildNotification(User $user, $title, $content, Image $image = null, $type = null, \DateTime $scheduledAt = null, $shouldBePushed = true)
	{
		$notification = new Notification($user, null, $scheduledAt ?: new \DateTime());

		$notification->setBody(new NotificationBody($title, $content, $image));
		$notification->setType($type);

		if ($shouldBePushed && count($this->deviceTokenFacade->findValidUserDeviceTokens($user)) > 0) {
			$notification->schedulePush();
		}

		return $notification;
	}

	public function createNotificationFromNotificationTemplateDataObject(User $user, NotificationTemplateDataObject $notificationTemplateDataObject, ?string $type = null, ?\DateTime $scheduledAt = null, ?Image $image = null, ?\DateTime $pushScheduledAt = null): Notification
	{
		$notification = new Notification($user, null, $scheduledAt ?: new \DateTime());

		$notification->setBody(
			new NotificationBody(
				$notificationTemplateDataObject->getDisplayName(),
				$notificationTemplateDataObject->getMessage(),
				$image !== null ? $image : $notificationTemplateDataObject->getImage(),
				$notificationTemplateDataObject->getLink(),
				$notificationTemplateDataObject->getLinkText()
			)
		);

		if ($notificationTemplateDataObject->getType()) {
			$notification->setType($notificationTemplateDataObject->getType());
		} elseif ($type) {
			$notification->setType($type);
		}

		// zatim nastaveno na 3 dny pokud neexistuje doba trvani
		if ($notificationTemplateDataObject->getDuration() !== null) {
			$notification->setValidTill((new \DateTime())->modify('+' . $notificationTemplateDataObject->getDuration() . ' hours'));
		} else {
			$notification->setValidTill((new \DateTime())->modify('+3 days'));
		}

		if ($notificationTemplateDataObject->isPush() && $this->sendingPolicyManager->isAllowedToPushNotification($notification)) {
			$notification->schedulePush($pushScheduledAt);
		}

		if ($notification->getType() === Notification::TYPE_LUCKY_SHOP) {
			$notification->setPriority(100);
		}

		return $notification;
	}
}
