<?php

namespace tipli\Model\Addon\FeedBuilders;

use Nette\Localization\Translator;
use Nette\Application\LinkGenerator;
use tipli\Model\Account\Entities\User;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopSettings;
use tipli\Model\Shops\RewardFilter;
use tipli\Model\Transactions\TransactionFacade;
use Tracy\Debugger;

class PageResponseBuilder
{
	public function __construct(
		private RewardFilter $rewardFilter,
		private Translator $translator,
		private LinkGenerator $linkGenerator,
		private ImageFilter $imageFilter,
		private DealFacade $dealFacade,
		private TransactionFacade $transactionFacade
	) {
	}

	public function buildResponse(Shop $shop = null, User $user = null): array
	{
		$deals = $this->getDeals($shop, $user);

		$countOfShopsCoupons = 0;
		$sales = [];

		if ($deals) {
			/** @var Deal $deal */
			foreach ($deals as $deal) {
				$reward = null;
				if ($deal->isFreeShippingType()) {
					$reward = $this->translator->translate('front.deals.deal.freeShipping');
				} elseif ($deal->getValue() && $deal->getUnit()) {
					$reward = $deal->getValue() . (!($deal->getUnit() === 'percentage' && !$deal->getLocalization()->isCzech()) ? ' ' : '') . $deal->getUnitSymbol();
				}

				$cashbackReward = null;

				if ($deal->getShop() && $deal->getShop()->isCashbackActive() && $deal->getShop()->isActive()) {
					$cashbackReward = '+ ' . $this->rewardFilter->__invoke($deal->getShop(), false, 'extended');
				}

				if ($deal->getShop() !== $shop) {
					Debugger::log('deal id ' . $deal->getId() . ' neni v obchode: ' . $shop->getId(), '___pageResponseBuilder');
					continue;
				}

				/** @var bool $isSimilar */
				$isSimilar = $deal->getShop() !== $shop;

				$redirectUrl = $this->linkGenerator->link('NewFront:Shops:Redirection:deal', ['deal' => $deal, 'direct' => false, 'source' => 'addon', 'utm_source' => 'addon', 'utm_medium' => ($isSimilar ? 'similar-coupon' : 'coupon'), 'utm_campaign' => $deal->getShop()->getSlug()]);

				$sales[] = [
					'id' => $deal->getId(),
					'type' => $deal->getType(),
					'name' => $deal->getName(),
					'redirectUrl' => $redirectUrl,
					'reward' => $reward,
					'code' => $deal->getCode(),
					'isSimilar' => $isSimilar,
					'cashbackReward' => $cashbackReward,
					'shopName' => $deal->getShop() ? $deal->getShop()->getName() : null,
					'shopLogoUrl' => $deal->getShop() ? $this->imageFilter->__invoke($deal->getShop()->getLogo(), 300) : null,
					'shopConditionsUrl' => $deal->getShop() ? $this->linkGenerator->link('NewFront:Shops:Shop:default#deal-' . $deal->getId(), ['shop' => $deal->getShop(), 'openDeal' => $deal->getFullSlug()]) : null,
					'shopConditionsText' => $deal->getDescription(),
				];

				$countOfShopsCoupons++;
			}
		}

		$reward = null;
		$rewardType = null;
		$rewardSuffix = null;
		$notice = null;

		if ($shop && $shop->isCashbackActive()) {
			$reward = html_entity_decode($this->rewardFilter->__invoke($shop, false, 'pure', $user));

			if (preg_match('~[0-9]~', $reward)) {
				$reward = str_replace($reward, '**' . $reward . '**', $reward);
			}

			$rewardSuffix = $this->translator->translate('model.shops.rewardFilter.suffixExtended');
			$rewardType = 'cashback';
		} elseif ($countOfShopsCoupons > 0) {
			$reward = '**' . $this->translator->translate('model.shops.rewardFilter.countOfCoupons', ['count' => $countOfShopsCoupons]) . '**';
			$rewardSuffix = $this->translator->translate('api.addon.page.defaultRewardSuffix');
			$rewardType = 'coupons';
		}

		if ($shop->isCashbackActive() && $shop->getRewardLabel()) {
			$rewardSuffix = $shop->getRewardLabel();
		}

		if (!$rewardType) {
			$rewardType = 'notice';
			$notice = "**Pro obchod " . $shop->getName() . " právě teď slevové kupóny nemáme.**";
		}

		$buttonUrl = $this->linkGenerator->link('NewFront:Shops:Shop:default', ['shop' => $shop, 'shortcut' => 1, 'fromAddon' => true, 'utm_source' => 'addon', 'utm_medium' => 'cashback', 'utm_campaign' => $shop->getSlug()]);
		$conditionsUrl = $this->linkGenerator->link('NewFront:Shops:Shop:default#shop-conditions', ['shop' => $shop, 'utm_source' => null, 'utm_medium' => null, 'utm_campaign' => null]);
		$shopDetailUrl = $this->linkGenerator->link('NewFront:Shops:Shop:default', ['shop' => $shop, 'utm_source' => 'addon', 'utm_medium' => 'cashback', 'utm_campaign' => $shop->getSlug()]);

		$rewardValue =  html_entity_decode($this->rewardFilter->__invoke($shop, false, 'numeric', $user));

		$rewardData = [];
		if ($shop->isCashbackActive()) {
			$rewardData = $this->rewardFilter->__invoke($shop, false, 'array', $user);
		}

		$shopData = [
			'message' => $shop->getAddonMessage(),
			'hidePromoAfterFirstTransaction' => $shop->isHidePromoAfterFirstTransaction(),
			'buttonLabel' => $this->translator->translate('api.addon.page.getReward'),
			'buttonLabelReactivation' => $this->translator->translate('api.addon.page.getRewardReactivation'),
			'buttonUrl' => $buttonUrl,
			'conditionsUrl' => $conditionsUrl,
			'shopDetailUrl' => $shopDetailUrl,
			'reward' => $reward,
			'rewardSuffix' => $rewardSuffix,
			'rewardType' => $rewardType,
			'rewardValue' => str_replace('.', ',', $rewardValue),
			'rewardUnit' => $shop->isCashbackActive() ? html_entity_decode($this->rewardFilter->__invoke($shop, false, 'symbol', $user)) : null,
			'rewardData' => $rewardData,
			'notice' => $notice,
			'shopLogoUrl' => $this->imageFilter->__invoke($shop->getLogo(), 300),
			'cashback' => $shop->isCashbackActive(),
			'coupons' => $shop->getCountOfCouponDeals() > 0,
		];

		if (!$shop->isVisible() || (!$shop->isCashbackActive() && $countOfShopsCoupons === 0)) {
			$shopData = null;
		}

		/** @var ShopSettings $shopSettings */
		$shopSettings = $shop->getShopSettings();

		$contentBoxAllowed = $shopSettings->isAddonContentBoxAllowed();

		// pro aliexpress obchod povolime content box pouze uzivatelum co maji min. 1 transakci v aliexpress obchode
		if ($user && $shop->isAliexpress()) {
			$transactionsQuery = $this->transactionFacade->createTransactionsQuery()
				->withShop($shop)
				->withUser($user);

			if ($this->transactionFacade->fetch($transactionsQuery)->getTotalCount() > 0) {
				$contentBoxAllowed = true;
			}
		}

		$isAddonPublic = $shopSettings->isAddonVisibilityPublic();

		if ($user !== null && $user->isAdmin() && $shopSettings->isAddonPublicForAdmins()) {
			$isAddonPublic = true;
		}

		$data = [
			'popup' => [
				'shop' => $isAddonPublic && $shopSettings->isAddonPopupBoxAllowed() ? $shopData : null,
				'sales' => $sales,
			],
			'content' => [
				'shop' => $isAddonPublic && $contentBoxAllowed ? $shopData : null,
				'sales' => $sales,
			],
			'priceComparison' => null,
		];

		return $data;
	}

	private function getDeals(?Shop $shop, ?User $user): array
	{
		if (!$shop) {
			return [];
		}

		$dealsQuery = $this->dealFacade->createDealsQuery($shop->getLocalization())
			->withShop($shop)
			->withType(Deal::TYPE_COUPON)
			->withCode()
			->onlyValid()
			->visibleOnAddon()
			->sortByDealTypePriority($user)
			->sortHighest()
			->sortTop();

		return $this->dealFacade->fetch($dealsQuery)
			->applyPaging(0, 5)
			->toArray()
		;
	}
}
