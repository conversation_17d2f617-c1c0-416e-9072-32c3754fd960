<?php

namespace tipli\Model\Addon;

use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Addon\Entities\UserSettings;

class UserSettingsManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createSettings(User $user, string $settings, ?\DateTime $createdAt = null)
	{
		$userSettings = new UserSettings($user, $settings, $createdAt);

		return $this->saveSettings($userSettings);
	}

	public function saveSettings(UserSettings $userSettings)
	{
		$this->em->persist($userSettings);
		$this->em->flush($userSettings);

		return $userSettings;
	}
}
