<?php

namespace tipli\Model\Addon\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Addon\Repositories\FeedbackRepository")
 * @ORM\Table(name="tipli_addon_feedback")
 */
class Feedback
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id", nullable=false)
	 * @var Localization
	 */
	private $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false, unique=true)
	 * @var User
	 */
	private $user;

	/**
	 * @ORM\Column(type="text")
	 * @var string
	 */
	private $content;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="checked_by_id", referencedColumnName="id")
	 * @var User|null
	 */
	private $checkedBy;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $checkedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	public function __construct(string $content, User $user, Localization $localization)
	{
		$this->content = $content;
		$this->user = $user;
		$this->localization = $localization;
		$this->createdAt = new \DateTime();
	}

	/**
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return string
	 */
	public function getContent(): string
	{
		return $this->content;
	}

	/**
	 * @return User
	 */
	public function getUser(): User
	{
		return $this->user;
	}

	/**
	 * @return Localization
	 */
	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	/**
	 * @return \DateTime
	 */
	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	/**
	 * @return \DateTime|null
	 */
	public function getCheckedAt()
	{
		return $this->checkedAt;
	}

	/**
	 * @param \DateTime $checkedAt
	 * @return Feedback
	 */
	public function setCheckedAt(\DateTime $checkedAt): Feedback
	{
		$this->checkedAt = $checkedAt;
		return $this;
	}

	/**
	 * @return User|null
	 */
	public function getCheckedBy()
	{
		return $this->checkedBy;
	}

	/**
	 * @param User $checkedBy
	 * @return Feedback
	 */
	public function setCheckedBy(User $checkedBy): Feedback
	{
		$this->checkedBy = $checkedBy;
		return $this;
	}
}
