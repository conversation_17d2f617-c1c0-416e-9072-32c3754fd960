<?php

namespace tipli\Model\GoogleExperiment\Layers;

use Nette\Application\Application;
use Nette\Application\Request;
use Nette\Database\Context;
use Nette\Security\User as NetteUser;
use tipli\Model\Account\UserFacade;
use tipli\Model\GoogleExperiment\GoogleExperimentVariant;
use tipli\Model\GoogleExperiment\GoogleExperimentVariantResolver;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Localization\LocalizationFacade;
use DateTime;
use tipli\Model\Account\Entities\User;
use tipli\Model\Queues\Entities\SqlQuery;
use tipli\Model\Queues\QueueFacade;
use Tracy\Debugger;

class GoogleExperimentLayer
{
	public const EVENT_SIGN_IN = 'sign_in';
	public const EVENT_SIGN_UP = 'sign_up';
	public const EVENT_PAGE_VIEW = 'page_view';

	private UserFacade $userFacade;
	private Request $request;

	public function __construct(
		private NetteUser $user,
		Application $application,
		private ClientLayer $clientLayer,
		private Context $context,
		private GoogleExperimentVariantResolver $googleExperimentVariantResolver,
		private LocalizationFacade $localizationFacade,
		private QueueFacade $queueFacade
	) {
		$this->request = $application->getRequests()[0] ?? null;
	}

	public function listenUserEvent(string $event, User $user): void
	{
		if ($user !== null) {
			$googleExperimentVariants = $this->googleExperimentVariantResolver->getExperimentVariants();

			if (empty($googleExperimentVariants) === false) {
				foreach ($googleExperimentVariants as $experimentId => $experimentVariant) {
					if ($experimentVariant instanceof GoogleExperimentVariant && $experimentVariant->isSaved() === false) {
						$this->createExperimentVariant($experimentVariant, $user->getId(), $event);

						$experimentVariant->setSaved();

						$this->googleExperimentVariantResolver->updateExperimentVariant($experimentVariant);

						$this->context->query(
							'UPDATE hash_experiment_variant
								SET user_id = ?
								WHERE hash = ? AND user_id IS NULL',
							$user->getId(),
							$this->getUserHash()
						);
					}
				}
			} else {
				$hashExperimentVariants = $this->context->query(
					'SELECT *
					FROM hash_experiment_variant
					WHERE hash = ? AND user_id IS NULL',
					$this->getUserHash()
				)->fetchAll();

				foreach ($hashExperimentVariants as $hashExperimentVariant) {
					$experimentVariant = new GoogleExperimentVariant(
						$hashExperimentVariant->experiment_id,
						$hashExperimentVariant->variant,
						$hashExperimentVariant->timestamp,
						$hashExperimentVariant->experiment_id === 'ro-001'
					);

					$this->createExperimentVariant($experimentVariant, $user->getId(), $event);

					$experimentVariant->setSaved();

					$this->googleExperimentVariantResolver->updateExperimentVariant($experimentVariant);

					$this->context->query(
						'UPDATE hash_experiment_variant
						SET user_id = ?
						WHERE hash = ? AND user_id IS NULL',
						$user->getId(),
						$this->getUserHash()
					);
				}
			}
		}
	}

	public function listenCreateExperiment(GoogleExperimentVariant $experimentVariant, string $event): void
	{
		if ($this->user->isLoggedIn()) {
			$this->createExperimentVariant($experimentVariant, $this->user->getId(), $event);

			$experimentVariant->setSaved();

			$this->googleExperimentVariantResolver->updateExperimentVariant($experimentVariant);
		} else {
			$this->createHashExperimentVariant($experimentVariant);
		}
	}

	private function createExperimentVariant(GoogleExperimentVariant $experimentVariant, int $userId, string $event): void
	{
		if ($experimentVariant->getExperimentId() === 'googleoptimize') {
			return;
		}

		$this->queueFacade->scheduleCreateSqlQuery(
			'INSERT INTO experiment_variant (user_id, experiment_id, variant, timestamp, event, created_at)
				SELECT ?, ?, ?, ?, ?, ?
				FROM dual
				WHERE NOT EXISTS (
					SELECT 1
					FROM experiment_variant
					WHERE experiment_id = ? AND user_id = ?
				)',
			[
				$userId,
				$experimentVariant->getExperimentId(),
				$experimentVariant->getVariant(),
				$experimentVariant->getTimestamp(),
				$event,
				(new DateTime())->format('Y-m-d H:i:s'),
				$experimentVariant->getExperimentId(),
				$userId,
			]
		);

		if ($event === self::EVENT_SIGN_UP) {
			Debugger::log('ExperimentVariant to create | ' . $userId . ' | ' . $experimentVariant->getExperimentId() . ' | ' . $experimentVariant->getVariant(), 'google-experiment');
		}
	}

	public function createHashExperimentVariant(GoogleExperimentVariant $experimentVariant): void
	{
		if ($experimentVariant->getExperimentId() === 'googleoptimize') {
			return;
		}

		if ($this->clientLayer->getIp() === null || $this->clientLayer->getUserAgent() === null) {
			return;
		}

		$hash = $this->getUserHash();

		$this->queueFacade->scheduleCreateSqlQuery(
			'INSERT INTO hash_experiment_variant (hash, experiment_id, variant, timestamp, created_at)
				SELECT ?, ?, ?, ?, ?
				FROM dual
				WHERE NOT EXISTS (
					SELECT 1
					FROM hash_experiment_variant
					WHERE experiment_id = ? AND hash = ? AND user_id IS NULL
				)',
			[
				$hash,
				$experimentVariant->getExperimentId(),
				$experimentVariant->getVariant(),
				$experimentVariant->getTimestamp(),
				(new DateTime())->format('Y-m-d H:i:s'),
				$experimentVariant->getExperimentId(),
				$hash,
			],
			SqlQuery::ROUTING_EXPERIMENT_VARIANT,
			priority: 50
		);
	}

	private function getUserHash(): string
	{
		return hash('sha256', $this->clientLayer->getIp() . $this->localizationFacade->getCurrentLocalization()->getDomain() . $this->clientLayer->getUserAgent()  . $this->clientLayer->getPlatform() . $this->clientLayer->isMobileDetected());
	}
}
