<?php

namespace tipli\Model\GoogleExperiment\Subscribers;

use Nette\SmartObject;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Events\UserCreatedEvent;
use tipli\Model\Account\Events\UserLoggedInEvent;
use tipli\Model\GoogleExperiment\Events\GoogleExperimentVariantCreatedEvent;
use tipli\Model\GoogleExperiment\Layers\GoogleExperimentLayer;

final class GoogleExperimentSubscriber implements EventSubscriberInterface
{
	use SmartObject;

	public function __construct(private GoogleExperimentLayer $googleExperimentLayer)
	{
	}

	public static function getSubscribedEvents(): array
	{
		return [
			GoogleExperimentVariantCreatedEvent::class => 'listenCreateExperiment',
			UserLoggedInEvent::class => 'listenUserSignIn',
			UserCreatedEvent::class => 'listenUserSignUp',
		];
	}

	public function listenUserSignIn(UserLoggedInEvent $userLoggedInEvent)
	{
		return;
//		$this->googleExperimentLayer->listenUserEvent(GoogleExperimentLayer::EVENT_SIGN_IN, $userLoggedInEvent->getUser());
	}

	public function listenUserSignUp(UserCreatedEvent $userCreatedEvent)
	{
		return;
//		$this->googleExperimentLayer->listenUserEvent(GoogleExperimentLayer::EVENT_SIGN_UP, $userCreatedEvent->getUser());
	}

	public function listenCreateExperiment(GoogleExperimentVariantCreatedEvent $experimentCreatedEvent)
	{
		return;
//		$this->googleExperimentLayer->listenCreateExperiment($experimentCreatedEvent->getExperimentVariant(), GoogleExperimentLayer::EVENT_PAGE_VIEW);
	}
}
