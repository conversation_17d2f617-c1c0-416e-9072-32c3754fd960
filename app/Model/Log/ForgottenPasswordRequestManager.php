<?php

namespace tipli\Model\Log;

use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Log\Entities\ForgottenPasswordRequest;

class ForgottenPasswordRequestManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createRequest(
		Localization $localization,
		?User $user,
		string $email,
		string $ip,
		?string $country = null
	): ForgottenPasswordRequest {
		return $this->saveForgottenPasswordRequest(
			new ForgottenPasswordRequest(
				$localization,
				$user,
				$email,
				$ip,
				$country
			)
		);
	}

	public function saveForgottenPasswordRequest(ForgottenPasswordRequest $request): ForgottenPasswordRequest
	{
		$this->em->persist($request);
		$this->em->flush($request);
		return $request;
	}
}
