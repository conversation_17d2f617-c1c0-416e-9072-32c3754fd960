<?php

namespace tipli\Model\Account\Events;

final class WebhookReceivedEvent
{
	/** @var string */
	public $action;

	/** @var array */
	public $requestArray;

	/**
	 * @param string $action
	 * @param array $requestArray
	 */
	public function __construct(string $action, array $requestArray)
	{
		$this->action = $action;
		$this->requestArray = $requestArray;
	}

	/**
	 * @return string
	 */
	public function getAction(): string
	{
		return $this->action;
	}

	/**
	 * @return array
	 */
	public function getRequestArray(): array
	{
		return $this->requestArray;
	}
}
