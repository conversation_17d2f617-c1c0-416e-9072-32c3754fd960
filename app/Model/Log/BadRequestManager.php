<?php

namespace tipli\Model\Log;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Log\Entities\BadRequest;

class BadRequestManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createBadRequest($localization, string $url, string $userAgent = null, string $referer = null, string $ip = null, int $errorCode)
	{
		$badRequest = new BadRequest($localization, $url, $userAgent, $referer, $ip, $errorCode);

		return $this->saveBadRequest($badRequest);
	}

	public function saveBadRequest(BadRequest $badRequest)
	{
		$this->em->persist($badRequest);
		$this->em->flush($badRequest);

		return $badRequest;
	}
}
