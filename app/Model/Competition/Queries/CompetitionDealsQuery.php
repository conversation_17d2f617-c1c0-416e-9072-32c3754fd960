<?php

namespace tipli\Model\Competition\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;
use tipli\Model\Localization\Entities\Localization;

class CompetitionDealsQuery extends QueryObject
{
	protected array $filters = [];

	private bool $optimized = false;

	public function optimized(): self
	{
		$this->optimized = true;

		return $this;
	}

	public function withLocalization(Localization $localization)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($localization) {
			$queryBuilder->andWhere('cd.localization = :localization')
				->setParameter('localization', $localization);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao): QueryBuilder
	{
		$queryBuilder = $this->createBasicDql($dao);

		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		return $queryBuilder;
	}

	protected function createBasicDql(Queryable $dao): QueryBuilder
	{
		$queryBuilder = $dao->createQueryBuilder('cd', 'cd.id');

		$queryBuilder->select('cd')
			->leftJoin('cd.shop', 'shop');

		if ($this->optimized) {
			$queryBuilder->select('
                PARTIAL d.{id, name, description, code, reward, exclusive, stateValidTill, lastSeenAt, listingUrl, detailUrl}
            ');
		} else {
			$queryBuilder->select('cd, shop');
		}

		return $queryBuilder;
	}
}
