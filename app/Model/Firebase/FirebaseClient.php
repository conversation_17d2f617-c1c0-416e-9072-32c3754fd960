<?php

namespace tipli\Model\Firebase;

use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use Kreait\Firebase\Messaging\RawMessageFromArray;
use tipli\Model\Queues\QueueFacade;
use Tracy\Debugger;

class FirebaseClient
{
	private const CREDENTIALS_FILE_PATH = __DIR__ . DIRECTORY_SEPARATOR . 'Credentials/tipli-977b3-2385b3c353c0.json';

	/** @var Factory|null */
	private $factory = null;

	public function __construct(private QueueFacade $queueFacade)
	{
		$this->factory = (new Factory())->withServiceAccount(self::CREDENTIALS_FILE_PATH);
	}

	public function pushNotificationToDeviceToken(
		$title,
		$body,
		array $deviceTokens,
		?string $notificationId = null,
		bool $webviewAllowed = true,
		?string $url = null,
		bool $showReviewPopup = false,
		?string $reviewPopupTitle = null,
		?string $reviewPopupText = null,
		bool $isAndroid = false
	): bool {
		Debugger::timer('create-message');

		$messaging = $this->factory->createMessaging();

		if ($isAndroid === false) {
			$data = [
				'title' => $title,
				'webviewAllowed' => $webviewAllowed ? 1 : 0,
				'url' => $url,
				'urlTitle' => $title,
				'showReviewPopup' => $showReviewPopup ? 1 : 0,
				'reviewPopupTitle' => $reviewPopupTitle,
				'reviewPopupText' => $reviewPopupText,
			];

			if ($notificationId) {
				$data['notificationId'] = $notificationId;
			}

			$message = (CloudMessage::new())
				->withNotification(FirebaseNotification::fromArray(['title' => strip_tags($title), 'body' => strip_tags($body)]))
				->withAndroidConfig($this->getDefaultAndroidConfig())
				->withApnsConfig($this->getDefaultApnsConfig())
				->withData($data)
			;
		} else {
			$data = [
				'title' => $title,
				'body' => $body,
			];

			if ($notificationId) {
				$data['notificationId'] = $notificationId;
			}

			if ($webviewAllowed) {
				$data['webviewAllowed'] = '1';
				$data['url'] = $url;
				$data['urlTitle'] = $title;
			}

			if ($showReviewPopup) {
				$data['showReviewPopup'] = '1';
				$data['reviewPopupTitle'] = $reviewPopupText; // na androidu nam prohodili title a text.
				$data['reviewPopupText'] = $reviewPopupTitle;
			}

			$message = new RawMessageFromArray(['data' => $data]);
		}

		//Debugger::log('Create message ' . Debugger::timer('create-message'), 'firebaseclient-process');

		try {
			Debugger::timer('send-message');
			$reports = $messaging->sendMulticast($message, $deviceTokens); // @todo report multicast

			echo "stav: " . $reports->hasFailures() ? "ne" : "ano";
			echo "\n";

//			foreach ($reports->failures()->getItems() as $failure) {
//				$token = $failure->target()->value();
//
//				$this->queueFacade->scheduleCreateSqlQuery(
//					'UPDATE tipli_account_user_device_token SET failed_at = NOW() WHERE token = ?',
//					[$token]
//				);
//			}

			//Debugger::log('Send message ' . Debugger::timer('send-message'), 'firebaseclient-process');
		} catch (MessagingException $e) {
			//Debugger::log($e->getMessage(), 'firebaseclient-errors');
			//Debugger::log('Send message ' . Debugger::timer('send-message'), 'firebaseclient-process');
			return false;
		} catch (FirebaseException $e) {
			//Debugger::log($e->getMessage(), 'firebaseclient-errors');
			//Debugger::log('Send message ' . Debugger::timer('send-message'), 'firebaseclient-process');

			return false;
		}

		return true;
	}

	private function getDefaultApnsConfig()
	{
		return ApnsConfig::fromArray([
			'payload' => [
				'aps' => [
					'sound' => 'default',
				],
			],
		]);
	}

	private function getDefaultAndroidConfig()
	{
		return AndroidConfig::fromArray([
			'ttl' => '3600s',
			'priority' => 'high',
			'notification' => [
				'sound' => 'default',
			],
		]);
	}
}
