<?php

namespace tipli\Model\Sazka;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\RequestOptions;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use tipli\Model\Sazka\Exceptions\SazkaClientException;
use InvalidArgumentException;

final class SazkaClient
{
	private const SUBSCRIPTION_KEY = '21a4ef906d364d70bb0f84ac4e391de4';
	public const GAME_SPORTKA = 'sportka';
	public const GAME_EUROJACKPOT = 'Eurojackpot';

	public Client $client;

	public function __construct()
	{
		$this->client =  new Client([
			RequestOptions::VERIFY => false,
			'timeout' => 30,
		]);
	}

	public function getGames(): array
	{
		try {
			$result = $this->client->request('GET', 'https://apigw.sazka.cz/drawinfo/Jackpots', [
				'headers' => [
					'ocp-apim-subscription-key' => self::SUBSCRIPTION_KEY,
					'X-BTMID' => $this->generateXBTMID(),
				],
				'query' => [
					'games' => [self::GAME_SPORTKA, self::GAME_EUROJACKPOT],
				],
			]);

			return Json::decode($result->getBody()->getContents());
		} catch (JsonException | InvalidArgumentException | RequestException | ClientException | ServerException $e) {
			throw new SazkaClientException($e->getMessage());
		}
	}

	public function getDraws(): array
	{
		try {
			$result = $this->client->request('GET', 'https://apigw.sazka.cz/drawinfo/Draws', [
				'headers' => [
					'ocp-apim-subscription-key' => self::SUBSCRIPTION_KEY,
					'X-BTMID' => $this->generateXBTMID(),
				],
				'query' => [
					'games' => [self::GAME_SPORTKA, self::GAME_EUROJACKPOT],
				],
			]);

			return Json::decode($result->getBody()->getContents());
		} catch (JsonException | InvalidArgumentException | RequestException | ClientException | ServerException $e) {
			throw new SazkaClientException($e->getMessage());
		}
	}

	public function getFutureDraws(): array
	{
		try {
			$result = $this->client->request('GET', 'https://apigw.sazka.cz/drawinfo/FutureDraws', [
				'headers' => [
					'ocp-apim-subscription-key' => self::SUBSCRIPTION_KEY,
					'X-BTMID' => $this->generateXBTMID(),
				],
				'query' => [
					'games' => [self::GAME_SPORTKA, self::GAME_EUROJACKPOT],
					'count' => 10,
				],
			]);

			return Json::decode($result->getBody()->getContents());
		} catch (JsonException | InvalidArgumentException | RequestException | ClientException | ServerException $e) {
			throw new SazkaClientException($e->getMessage());
		}
	}

	private function generateXBTMID(): string
	{
		return 'tipli-' . time();
	}
}
