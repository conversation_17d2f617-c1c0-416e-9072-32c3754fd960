<?php

namespace tipli\Model\Reviews\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Reviews\Entities\ReviewRequest;
use tipli\Model\Shops\Entities\Shop;

class ReviewRepository extends BaseRepository
{
	public function getReviews()
	{
		return $this->createQueryBuilder('r')
			->leftJoin('r.user', 'u');
	}

	public function findApprovedShopReviews(Shop $shop, $limit = null, $minimalRate = 1, $onlyWithText = false)
	{
		$qb = $this->getReviews();

		$qb->andWhere('r.shop = :shop')
			->setParameter('shop', $shop);

		$qb->andWhere('r.approvedAt IS NOT NULL');

		$qb->andWhere('r.rate >= :minimalRate')
			->setParameter('minimalRate', $minimalRate);

		if ($onlyWithText) {
			$qb->andWhere('r.text IS NOT NULL');
			$qb->andWhere('r.text != :empty')
				->setParameter('empty', ' ');
		} else {
			$qb->addSelect('(CASE WHEN r.text = \'\' THEN 1 ELSE 0 END) AS HIDDEN hasText');
			$qb->addOrderBy('hasText', 'ASC');
		}

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		$qb->addOrderBy('r.id', 'DESC');

		return $qb->getQuery()->getResult();
	}

	public function findApprovedShopReviewsCount(Shop $shop, int $minimalRate, bool $onlyWithText)
	{
		$qb = $this->getReviews();

		$qb->andWhere('r.shop = :shop')
			->setParameter('shop', $shop);

		$qb->andWhere('r.approvedAt IS NOT NULL');

		$qb->andWhere('r.rate >= :minimalRate')
			->setParameter('minimalRate', $minimalRate);

		if ($onlyWithText) {
			$qb->andWhere('r.text IS NOT NULL');
			$qb->andWhere('r.text != :empty')
				->setParameter('empty', ' ');
		}

		$qb->select('COUNT(r.id)');

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getUnApprovedReviews()
	{
		$qb = $this->getReviews()
		 ->andWhere('r.approvedAt IS NULL');

		return $qb->getQuery()->getResult();
	}

	public function findShopsToReview(User $user)
	{
		$qb = $this->createQueryBuilder();

		$qb->select('s')
			->from(Shop::class, 's')
			->innerJoin('s.transactions', 't');

		$qb->andWhere('t.user = :user');
		$qb->setParameter('user', $user);

		$qb->addGroupBy('s.id');

		return $qb->getQuery()->getResult();
	}

	public function findUserReviews(User $user, $type = null, $onlyPositive = null)
	{
		$qb = $this->getReviews()
			->andWhere('r.user = :user')
			->setParameter('user', $user);

		if ($type == 'shop') {
			$qb->andWhere('r.shop IS NOT NULL');
		} else {
			$qb->andWhere('r.shop IS NULL');
		}

		if ($onlyPositive) {
			$qb->andWhere('r.rate = 5');
		}

		$qb->leftJoin(ReviewRequest::class, 'rr', 'WITH', 'rr.review = r')
			->andWhere('rr.id IS NULL')
			->orderBy('r.createdAt', 'DESC');

		return $qb->getQuery()->getResult();
	}

	public function findAverageShopReview(Shop $shop): ?float
	{
		$qb = $this->createQueryBuilder('r')
		 ->select('(sum(r.rate) / count(r.id)) AS averageReview')
		 ->andWhere('r.approvedAt IS NOT NULL')
		 ->andWhere('r.shop = :shop')
		 ->setParameter('shop', $shop);

		$result = $qb->getQuery()->getOneOrNullResult();

		return $result['averageReview'] ?? null;
	}

	public function findUserShopReviews(User $user, Shop $shop): array
	{
		return $this->getReviews()
			->andWhere('r.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('r.user = :user')
			->setParameter('user', $user)
			->getQuery()
			->getResult();
	}

	public function findByUser(User $user, ?Shop $shop = null)
	{
		$qb = $this->getReviews()
			->andWhere('r.user = :user')
			->setParameter('user', $user);

		if ($shop) {
			$qb->andWhere('r.shop = :shop')
				->setParameter('show', $shop);
		} else {
			$qb->andWhere('r.shop IS NULL');
		}

		return $qb ->getQuery()
			->getOneOrNullResult();
	}
}
