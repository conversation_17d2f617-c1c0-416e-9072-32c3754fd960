<?php

namespace tipli\Shops\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;

class ReviewsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	public function withLocalization(Localization $localization)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($localization) {
			$queryBuilder->andWhere('r.localization = :localization')
				->setParameter('localization', $localization);
		};

		return $this;
	}

	public function withShop(Shop $shop)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shop) {
			$queryBuilder
				->andWhere('r.shop = :shop')
				->setParameter('shop', $shop);
		};

		return $this;
	}

	public function onlyTipli()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('r.shop IS NULL');
		};

		return $this;
	}

	public function withUserPictures()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('r.userPicture IS NOT NULL');
		};

		return $this;
	}

	public function onlyApproved()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('r.approvedAt IS NOT NULL');
		};

		return $this;
	}

	public function onlyPrioritized()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->orderBy('r.prioritizedAt', 'DESC');
		};

		return $this;
	}

	public function sortNewest()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->orderBy('r.createdAt', 'DESC');
		};

		return $this;
	}

	public function sortRandomly()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->orderBy('RAND()');
		};

		return $this;
	}

	public function withMinimalFacebookRate($minimalRate = 3)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($minimalRate) {
			$queryBuilder
				->andWhere('(r.uniqueId IS NULL OR (r.uniqueId IS NOT NULL AND r.rate >= :minimalRate))')
				->setParameter('minimalRate', $minimalRate);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('r', 'r.id')
			->select('r')
			->leftJoin('r.userPicture', 'i')->addSelect('i')
			->leftJoin('r.user', 'u')->addSelect('u');


		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		$queryBuilder->addOrderBy('r.id');

		return $queryBuilder;
	}

	public function postFetch(Queryable $repository, \Iterator $iterator)
	{
		$ids = array_keys(iterator_to_array($iterator, true));

		$repository->createQueryBuilder('r')
			->select('PARTIAL r.{id}, u')
			->leftJoin('r.user', 'u')
			->andWhere('r.id IN (:ids)')->setParameter('ids', $ids)
			->getQuery()->getResult();
	}
}
