<?php

namespace tipli\Model\Localization;

use Nette\Localization\Translator;
use DateTime;
use DateTimeInterface;

class TimeAgoFilter
{
	public function __construct(
		private readonly Translator $translator,
		private readonly LocalizationFacade $localizationFacade,
		private readonly DateTimeZoneResolver $dateTimeZoneResolver
	) {
	}

	/**
	 * Converts a DateTime object to a human-readable relative time format.
	 * For dates 4+ days old, shows regular date format instead of relative time.
	 *
	 * @param DateTimeInterface $dateTime The datetime to convert.
	 * @return string Translated time ago string or formatted date.
	 */
	public function __invoke(DateTimeInterface $dateTime): string
	{
		$now = new DateTime();
		$diff = $now->diff($dateTime);

		if ($diff->y > 0 || $diff->m > 0 || $diff->d >= 4) {
			return $this->formatDate($dateTime);
		}

		if ($diff->d > 0) {
			return $this->translator->translate('newFront.timeAgo.days', $diff->d);
		}
		if ($diff->h > 0) {
			return $this->translator->translate('newFront.timeAgo.hours', $diff->h);
		}
		if ($diff->i > 0) {
			return $this->translator->translate('newFront.timeAgo.minutes', $diff->i);
		}

		return $this->translator->translate('newFront.timeAgo.moments');
	}

	/**
	 * Format date according to current localization
	 *
	 * @param DateTimeInterface $dateTime
	 * @return string
	 */
	private function formatDate(DateTimeInterface $dateTime): string
	{
		$localization = $this->localizationFacade->getCurrentLocalization();
		$format = 'd.m.Y';

		if ($localization->isHungarian()) {
			$format = 'Y. n. j';
		}

		return $this->dateTimeZoneResolver->resolve($dateTime, $localization)->format($format);
	}
}
