<?php

namespace tipli\Model\Reports\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Reports\Repositories\CampaignMetricRepository")
 * @ORM\Table(name="tipli_reports_campaign_metric")
 */
class CampaignMetric
{
	public const INTERVAL_HOUR = 'h';
	public const INTERVAL_DAY = 'd';
	public const INTERVAL_WEEK = 'w';
	public const INTERVAL_MONTH = 'm';
	public const INTERVAL_QUARTER = 'q';
	public const INTERVAL_YEAR = 'y';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 * @var Localization
	 */
	private $localization;

	/**
	 * @ORM\Column(type="string", name="interval_name", length=1, nullable=true)
	 * @var string
	 */
	private $intervalName;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $startedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $endedAt;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 * @var string
	 */
	private $utmSource;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $utmMedium;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $utmCampaign;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfRegisteredUsers = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsers = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn1Day = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn1To3Days = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn3To7Days = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn7To14Days = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn14To30Days = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersIn30To90Days = 0;

	/**
	 * @ORM\Column(type="integer")
	 * @var integer|null
	 */
	private $countOfActiveUsersAfter90Days = 0;


	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float|null
	 */
	private $cost;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $lastRefreshedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $refreshAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $costProcessedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime|null
	 */
	private $createdAt;

	/**
	 * @param Localization $localization
	 * @param string $intervalName
	 * @param \DateTime $startedAt
	 * @param \DateTime $endedAt
	 * @param string $utmSource
	 * @param string|null $utmMedium
	 * @param string|null $utmCampaign
	 * @throws \Exception
	 */
	public function __construct(Localization $localization, string $intervalName, \DateTime $startedAt, \DateTime $endedAt, string $utmSource, ?string $utmMedium = null, ?string $utmCampaign = null)
	{
		$this->localization = $localization;
		$this->intervalName = $intervalName;
		$this->startedAt = $startedAt;
		$this->endedAt = $endedAt;
		$this->utmSource = $utmSource;
		$this->utmMedium = $utmMedium;
		$this->utmCampaign = $utmCampaign;
		$this->createdAt = new \DateTime();
	}

	public function getLastRefreshedAt(): ?\DateTime
	{
		return $this->lastRefreshedAt;
	}

	public function refreshed(): void
	{
		$this->lastRefreshedAt = new \DateTime();
		$this->refreshAt = null;
	}

	public function shouldByRefreshed(): ?\DateTime
	{
		return $this->refreshAt;
	}

	public function refresh(): void
	{
		$this->refreshAt = new \DateTime();
	}

	public function getIntervalName(): string
	{
		return $this->intervalName;
	}

	public static function getIntervals()
	{
		return [
			self::INTERVAL_HOUR,
			self::INTERVAL_DAY,
			self::INTERVAL_WEEK,
			self::INTERVAL_MONTH,
			self::INTERVAL_QUARTER,
			self::INTERVAL_YEAR,
		];
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @param \DateTime|null $refreshAt
	 */
	public function setRefreshAt(?\DateTime $refreshAt): void
	{
		$this->refreshAt = $refreshAt;
	}

	/**
	 * @return string
	 */
	public function getUtmSource(): string
	{
		return $this->utmSource;
	}

	/**
	 * @return string|null
	 */
	public function getUtmMedium(): ?string
	{
		return $this->utmMedium;
	}

	/**
	 * @return string|null
	 */
	public function getUtmCampaign(): ?string
	{
		return $this->utmCampaign;
	}

	/**
	 * @return \DateTime
	 */
	public function getStartedAt(): \DateTime
	{
		return $this->startedAt;
	}

	/**
	 * @return \DateTime
	 */
	public function getEndedAt(): \DateTime
	{
		return $this->endedAt;
	}

	/**
	 * @return Localization
	 */
	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	/**
	 * @param int|null $countOfRegisteredUsers
	 */
	public function setCountOfRegisteredUsers(?int $countOfRegisteredUsers): void
	{
		$this->countOfRegisteredUsers = $countOfRegisteredUsers;
	}

	/**
	 * @param int|null $countOfActiveUsers
	 */
	public function setCountOfActiveUsers(?int $countOfActiveUsers): void
	{
		$this->countOfActiveUsers = $countOfActiveUsers;
	}

	/**
	 * @param int|null $countOfActiveUsersIn1Day
	 */
	public function setCountOfActiveUsersIn1Day(?int $countOfActiveUsersIn1Day): void
	{
		$this->countOfActiveUsersIn1Day = $countOfActiveUsersIn1Day;
	}

	/**
	 * @param int|null $countOfActiveUsersIn1To3Days
	 */
	public function setCountOfActiveUsersIn1To3Days(?int $countOfActiveUsersIn1To3Days): void
	{
		$this->countOfActiveUsersIn1To3Days = $countOfActiveUsersIn1To3Days;
	}

	/**
	 * @param int|null $countOfActiveUsersIn3To7Days
	 */
	public function setCountOfActiveUsersIn3To7Days(?int $countOfActiveUsersIn3To7Days): void
	{
		$this->countOfActiveUsersIn3To7Days = $countOfActiveUsersIn3To7Days;
	}

	/**
	 * @param int|null $countOfActiveUsersIn7To14Days
	 */
	public function setCountOfActiveUsersIn7To14Days(?int $countOfActiveUsersIn7To14Days): void
	{
		$this->countOfActiveUsersIn7To14Days = $countOfActiveUsersIn7To14Days;
	}

	/**
	 * @param int|null $countOfActiveUsersIn14To30Days
	 */
	public function setCountOfActiveUsersIn14To30Days(?int $countOfActiveUsersIn14To30Days): void
	{
		$this->countOfActiveUsersIn14To30Days = $countOfActiveUsersIn14To30Days;
	}

	/**
	 * @param int|null $countOfActiveUsersIn30To90Days
	 */
	public function setCountOfActiveUsersIn30To90Days(?int $countOfActiveUsersIn30To90Days): void
	{
		$this->countOfActiveUsersIn30To90Days = $countOfActiveUsersIn30To90Days;
	}

	/**
	 * @param int|null $countOfActiveUsersAfter90Days
	 */
	public function setCountOfActiveUsersAfter90Days(?int $countOfActiveUsersAfter90Days): void
	{
		$this->countOfActiveUsersAfter90Days = $countOfActiveUsersAfter90Days;
	}

	/**
	 * @return float|null
	 */
	public function getCost(): ?float
	{
		return $this->cost;
	}

	/**
	 * @param float|null $cost
	 */
	public function setCost(?float $cost): void
	{
		$this->cost = $cost;
	}

	public function processCost()
	{
		$this->costProcessedAt = new \DateTime();
	}
}
