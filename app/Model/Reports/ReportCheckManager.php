<?php

namespace tipli\Model\Reports;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Reports\Entities\ReportCheck;

class ReportCheckManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function saveReportCheck(ReportCheck $reportCheck): ReportCheck
	{
		$this->em->persist($reportCheck);
		$this->em->flush($reportCheck);

		return $reportCheck;
	}
}
