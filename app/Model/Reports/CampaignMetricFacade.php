<?php

namespace tipli\Model\Reports;

use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Reports\Entities\CampaignMetric;
use tipli\Model\Reports\Repositories\CampaignMetricRepository;

class CampaignMetricFacade
{
	/** @var CampaignMetricCalculator */
	private $campaignMetricCalculator;

	/** @var CampaignMetricManager */
	private $campaignMetricManager;

	/** @var CampaignMetricRepository */
	private $campaignMetricRepository;

	public function __construct(CampaignMetricCalculator $campaignMetricCalculator, CampaignMetricManager $campaignMetricManager, CampaignMetricRepository $campaignMetricRepository)
	{
		$this->campaignMetricCalculator = $campaignMetricCalculator;
		$this->campaignMetricManager = $campaignMetricManager;
		$this->campaignMetricRepository = $campaignMetricRepository;
	}

	public function prepareCampaignMetrics(Localization $localization, \DateTime $dateTime, string $utmSource, ?string $utmMedium, ?string $utmCampaign)
	{
//		if (!in_array($utmSource, $this->getWhiteListedUtmSources())) {
//			return [];
//		}

		$intervals = CampaignMetric::getIntervals();
		$campaignMetrics = [];

		foreach ($intervals as $intervalName) {
			if ($intervalName === CampaignMetric::INTERVAL_HOUR && $dateTime < (new \DateTime())->modify('- 3 months')) {
				continue;
			}

			list ($startedAt, $endedAt) = self::getRangeFromDateTime($dateTime, $intervalName);

			$campaignMetrics[] = $this->campaignMetricManager->createOrReturnCampaignMetric($localization, $intervalName, $startedAt, $endedAt, $utmSource);

			if ($intervalName === CampaignMetric::INTERVAL_DAY && $utmMedium && $utmCampaign) { // specific campaigns only per days
				$campaignMetrics[] = $this->campaignMetricManager->createOrReturnCampaignMetric($localization, $intervalName, $startedAt, $endedAt, $utmSource, $utmMedium, $utmCampaign);
			}
		}

		return $campaignMetrics;
	}

	public function recalculateCampaignMetric(CampaignMetric $campaignMetric)
	{
		$this->campaignMetricCalculator->recalculateCampaignMetric($campaignMetric);

		$campaignMetric->refreshed();

		$this->campaignMetricManager->saveCampaignMetric($campaignMetric);
	}

	public function scheduleCampaignMetricRefresh(CampaignMetric $campaignMetric)
	{
		if (!$campaignMetric->shouldByRefreshed()) {
			$campaignMetric->refresh();

			$this->campaignMetricManager->saveCampaignMetric($campaignMetric);
		}
	}

	public function saveCampaignMetric(CampaignMetric $campaignMetric)
	{
		return $this->campaignMetricManager->saveCampaignMetric($campaignMetric);
	}

	public function findCampaignMetricsToRefresh($limit = 200)
	{
		return $this->campaignMetricRepository->findCampaignMetricsToRefresh($limit);
	}

	public function findCampaignMetricsToSpentRefresh(array $intervals, array $utmSources, $limit, \DateTime $toDate)
	{
		return $this->campaignMetricRepository->findCampaignMetricsToSpentRefresh($intervals, $utmSources, $limit, $toDate);
	}

	public function find($id)
	{
		return $this->campaignMetricRepository->find($id);
	}

	private function getWhiteListedUtmSources()
	{
		return ['facebook'];
	}

	private static function getRangeFromDateTime(\DateTime $dateTime, $interval)
	{
		$dateTime = clone $dateTime;
		$startedAt = clone $dateTime;
		$endedAt = clone $dateTime;

		if ($interval === CampaignMetric::INTERVAL_HOUR) {
			$startedAt->setTime($dateTime->format('H'), 0, 0);
			$endedAt->setTime($dateTime->format('H'), 59, 59);
		} elseif ($interval === CampaignMetric::INTERVAL_DAY) {
		} elseif ($interval === CampaignMetric::INTERVAL_WEEK) {
			$startedAt->modify(($startedAt->format('w') === '0') ? 'monday this week' : 'monday this week');
			$endedAt->modify(($endedAt->format('w') === '0') ? 'now' : 'sunday this week');
		} elseif ($interval === CampaignMetric::INTERVAL_MONTH) {
			$startedAt->setDate($dateTime->format('Y'), $dateTime->format('m'), 1);
			$endedAt->setDate($dateTime->format('Y'), $dateTime->format('m'), $dateTime->format('t'));
		} elseif ($interval === CampaignMetric::INTERVAL_QUARTER) {
			$month = $dateTime->format('m');

			if ($month < 4) {
				$startedAt->modify('first day of january ' . $dateTime->format('Y'));
				$endedAt->modify('last day of march ' . $dateTime->format('Y'));
			} elseif ($month < 7) {
				$startedAt->modify('first day of april ' . $dateTime->format('Y'));
				$endedAt->modify('last day of june ' . $dateTime->format('Y'));
			} elseif ($month < 10) {
				$startedAt->modify('first day of july ' . $dateTime->format('Y'));
				$endedAt->modify('last day of september ' . $dateTime->format('Y'));
			} elseif ($month > 9) {
				$startedAt->modify('first day of october ' . $dateTime->format('Y'));
				$endedAt->modify('last day of december ' . $dateTime->format('Y'));
			}
		} elseif ($interval === CampaignMetric::INTERVAL_YEAR) {
			$startedAt->modify('first day of january ' . $dateTime->format('Y'));
			$endedAt->modify('last day of december ' . $dateTime->format('Y'));
		}

		if ($interval !== CampaignMetric::INTERVAL_HOUR) {
			$startedAt->setTime(0, 0, 0);
			$endedAt->setTime(23, 59, 59);
		}

		return [$startedAt, $endedAt];
	}
}
