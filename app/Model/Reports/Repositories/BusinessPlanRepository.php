<?php

namespace tipli\Model\Reports\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;

class BusinessPlanRepository extends BaseRepository
{
	public function findBusinessPlans(?Localization $localization, $intervalName, \DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('b')
			->addSelect('sum(a.countOfUsers) AS countOfUsers')
			->addSelect('a.startedAt')
			->addSelect('a.endedAt')
			->andWhere('a.intervalName = :intervalName')
			->andWhere('a.intervalName = :intervalName')
			->setParameter('intervalName', $intervalName)
			->andWhere('a.startedAt >= :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('a.endedAt <= :endedAt')
			->setParameter('endedAt', $endedAt)
			->orderBy('a.startedAt');

		if ($localization) {
			$qb->andWhere('a.localization = :localization')
				->setParameter('localization', $localization);
		} else {
			$qb->andWhere('a.localization IS NULL')
				->addGroupBy('a.startedAt');
		}

		return $qb->getQuery()->getResult();
	}

	public function findBusinessPlan(?Localization $localization, $intervalName, \DateTime $startedAt, \DateTime $endedAt)
	{
		$qb = $this->createQueryBuilder('b')
			->select('b')
			->andWhere('b.intervalName = :intervalName')
			->setParameter('intervalName', $intervalName)
			->andWhere('b.startedAt = :startedAt')
			->setParameter('startedAt', $startedAt)
			->andWhere('b.endedAt = :endedAt')
			->setParameter('endedAt', $endedAt);

		if ($localization) {
			$qb->andWhere('b.localization = :localization')
				->setParameter('localization', $localization);
		} else {
			$qb->andWhere('b.localization IS NULL');
		}

		$qb->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}
}
