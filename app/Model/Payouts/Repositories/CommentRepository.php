<?php

namespace tipli\Model\Payouts\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Payouts\Entities\Payout;

class CommentRepository extends BaseRepository
{
	public function getComments(Payout $payout)
	{
		$qb = $this->createQueryBuilder('c')
			->leftJoin('c.payout', 'p');

		$qb->andWhere('c.payout = :payout')
			->setParameter('payout', $payout);

		return $qb;
	}

	public function findCountOfEmailComments(User $user)
	{
		$qb = $this->createQueryBuilder('c')
			->select('count(c.id)')
			->innerJoin('c.payout', 'p')
			->andWhere('p.user = :user')
			->setParameter('user', $user)
			->andWhere('c.emailBody IS NOT NULL');

		return $qb->getQuery()->getSingleScalarResult();
	}
}
