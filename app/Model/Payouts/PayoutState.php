<?php

namespace tipli\Model\Payouts;

use tipli\Model\Payouts\Entities\Payout;

class PayoutState
{
	/** @var Payout */
	private $payout;

	/** @var boolean */
	private $failed = false;

	/** @var string */
	private $message;

	public function __construct(Payout $payout)
	{
		$this->payout = $payout;
	}

	/**
	 * @return bool
	 */
	public function isFailed()
	{
		return $this->failed;
	}

	/**
	 * @param bool $failed
	 */
	public function setFailed($failed)
	{
		$this->failed = $failed;
	}

	/**
	 * @return string
	 */
	public function getMessage()
	{
		return $this->message;
	}

	/**
	 * @param string $message
	 */
	public function setMessage($message)
	{
		$this->message = $message;
	}

	/**
	 * @return Payout
	 */
	public function getPayout()
	{
		return $this->payout;
	}
}
