<?php

namespace tipli\Model\Questions;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Questions\Entities\Group;

class GroupManager
{
	/**
	 * @var EntityManager
	 */
	private $em;

	/**
	 * GroupManager constructor.
	 * @param EntityManager $em
	 */
	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	/**
	 * @param string $noteTitle
	 * @return Group
	 */
	public function createGroup(string $noteTitle): Group
	{
		$group = new Group($noteTitle);

		return $this->saveGroup($group);
	}

	/**
	 * @param Group $group
	 * @throws \Exception
	 */
	public function removeGroup(Group $group): void
	{
		$this->em->remove($group);
		$this->em->flush($group);
	}

	/**
	 * @param Group $group
	 * @return Group
	 */
	public function saveGroup(Group $group): Group
	{
		$this->em->persist($group);
		$this->em->flush($group);

		return $group;
	}
}
