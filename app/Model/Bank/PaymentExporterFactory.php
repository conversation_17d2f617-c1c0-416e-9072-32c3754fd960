<?php

namespace tipli\Model\Bank;

use tipli\Model\Bank\Exporters\BcrPaymentsExporter;
use tipli\Model\Bank\Exporters\ErstePaymentsExporter;
use tipli\Model\Bank\Exporters\FioEuroPaymentsExporter;
use tipli\Model\Bank\Exporters\FioNationalPaymentsExporter;
use tipli\Model\Bank\Exporters\IngPaymentsExporter;
use tipli\Model\Bank\Exporters\IPaymentsExporter;
use tipli\Model\Bank\Exporters\WisePaymentsExporter;
use tipli\Model\Currencies\Currency;

class PaymentExporterFactory
{
	public function __construct(
		private BcrPaymentsExporter $bcrPaymentsExporter,
		private WisePaymentsExporter $wisePaymentsExporter,
		private ErstePaymentsExporter $erstePaymentsExporter,
		private FioNationalPaymentsExporter $fioNationalPaymentsExporter,
		private FioEuroPaymentsExporter $fioEuroPaymentsExporter,
		private IngPaymentsExporter $ingPaymentsExporter
	) {
	}

	public function create(string $currency, bool $onlyForIban = false): IPaymentsExporter
	{
		if ($onlyForIban === true && $currency === Currency::CZK) {
			return $this->fioEuroPaymentsExporter;
		}

		return match ($currency) {
			Currency::RON => $this->bcrPaymentsExporter,
			Currency::HUF => $this->erstePaymentsExporter,
			Currency::BGN => $this->wisePaymentsExporter,
			Currency::PLN => $this->ingPaymentsExporter,
			Currency::CZK => $this->fioNationalPaymentsExporter,
			Currency::EUR => $this->fioEuroPaymentsExporter,
			default => throw new \Exception('Unsupported currency: ' . $currency),
		};
	}

	public static function getBankNameByCurrency(string $currency): string
	{
		$bankNames = [
			Currency::CZK => 'FIO CZK',
			Currency::EUR => 'FIO EUR',
			Currency::RON => 'BCR',
			Currency::HUF => 'Erste',
			Currency::BGN => 'Wise',
			Currency::PLN => 'ING',
		];

		return $bankNames[$currency];
	}
}
