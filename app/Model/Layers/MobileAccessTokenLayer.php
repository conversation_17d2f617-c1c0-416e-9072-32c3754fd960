<?php

namespace tipli\Model\Layers;

use Nette\Application\Application;
use Nette\Application\Request;
use Nette\Security\AuthenticationException;
use Nette\Security\User;
use tipli\Model\Account\Entities\DeviceToken;
use tipli\Model\Account\UserFacade;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Localization\LocalizationFacade;
use Tracy\Debugger;

class MobileAccessTokenLayer
{
	private Request $request;

	public function __construct(
		private User $user,
		private UserFacade $userFacade,
		private LocalizationFacade $localizationFacade,
		Application $application,
		private ClientLayer $clientLayer,
		private DatadogProducer $datadogProducer,
		private \Nette\Http\Request $httpRequest,
	) {
		$this->request = $application->getRequests()[0] ?? null;
	}

	public function listenRequest(): void
	{
		if (!$this->request || !$this->request instanceof Request) {
			return;
		}

		$rawSessionToken = $this->httpRequest->getHeader('x-access-token')
			? $this->httpRequest->getHeader('x-access-token')
			: $this->httpRequest->getHeader('x-user-access-token')
		;

		/** @var DeviceToken|null $deviceToken */
		if ($rawSessionToken !== null && $sessionToken = $this->userFacade->findValidMobileSessionByToken($this->localizationFacade->getCurrentLocalization(), $rawSessionToken)) {
			try {
				if ($this->isAllowedToUserLogin($sessionToken->getUser())) {
					$this->user->login($sessionToken->getUser());

					if ($sessionToken->getUser()->isAdmin()) {
						Debugger::log('Logged by token - ' . $rawSessionToken, 'mobile-token-request');
					}

					return;
				}
			} catch (AuthenticationException $e) {
				Debugger::log($this->clientLayer->getIp() . ' - ' . $rawSessionToken, 'mobile-token-bad-request');
				$this->datadogProducer->scheduleSendEvent('accessTokenBadLogin');
			}
		}
	}

	private function isAllowedToUserLogin(\tipli\Model\Account\Entities\User $user)
	{
		if (!$this->user->isLoggedIn() || $this->user->getIdentity() === $user) {
			return $user->isActive();
		}

		return $this->user->getIdentity()->isAdmin();
	}
}
