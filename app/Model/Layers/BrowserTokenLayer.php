<?php

namespace tipli\Model\Layers;

use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Random;
use tipli\Model\BrowserActivities\BrowserActivityFacade;

class BrowserTokenLayer
{
	public const COOKIE_BROWSER_TOKEN = 'browserToken';

	public const COOKIE_EXPIRATION = '365 days';

	/** @var \Nette\Http\Request */
	private $httpRequest;

	/** @var Response */
	private $httpResponse;

	/** @var BrowserActivityFacade */
	private $browserActivityFacade;

	public function __construct(Request $httpRequest, Response $httpResponse, BrowserActivityFacade $browserActivityFacade)
	{
		$this->httpRequest = $httpRequest;
		$this->httpResponse = $httpResponse;
		$this->browserActivityFacade = $browserActivityFacade;
	}

	/** @return \tipli\Model\BrowserActivities\Entities\BrowserToken */
	public function getBrowserToken()
	{
//		Debugger::log('getToken', 'browserTokenLayer');
		$browserToken = $this->httpRequest->getCookie(self::COOKIE_BROWSER_TOKEN);

		if (empty($browserToken)) {
			$browserToken = Random::generate(32, '0-9a-zA-Z');
			$this->httpResponse->setCookie(self::COOKIE_BROWSER_TOKEN, $browserToken, self::COOKIE_EXPIRATION);
		}

		return $this->browserActivityFacade->getBrowserToken($browserToken);
	}

	public function getBrowserTokenString()
	{
		$browserToken = $this->httpRequest->getCookie(self::COOKIE_BROWSER_TOKEN);

		if (empty($browserToken)) {
			$browserToken = Random::generate(32, '0-9a-zA-Z');
			$this->httpResponse->setCookie(self::COOKIE_BROWSER_TOKEN, $browserToken, self::COOKIE_EXPIRATION);

//			$this->browserActivityFacade->getBrowserToken($browserToken);
		}

		return $browserToken;
	}
}
