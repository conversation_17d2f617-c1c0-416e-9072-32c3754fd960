<?php

namespace tipli\Model\Refunds;

use Nette\Localization\Translator;
use Nette\Http\Url;
use Nette\Security\IIdentity;
use tipli\Model\Account\Entities\User;
use tipli\Model\Configuration;
use tipli\Model\Currencies\CurrencyFilter;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Routers\RouterFactory;

class RefundResponseProvider
{
	/** @var Translator */
	private $translator;

	/** @var IIdentity|User|null  */
	private $user;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var RefundGreetingsGenerator */
	private $refundGreetingsGenerator;

	/** @var Configuration */
	public $configuration;

	/** @var CurrencyFilter */
	public $currencyFilter;

	public function __construct(
		Translator $translator,
		\Nette\Security\User $user,
		LocalizationFacade $localizationFacade,
		Configuration $configuration,
		CurrencyFilter $currencyFilter,
		RefundGreetingsGenerator $refundGreetingsGenerator
	) {
		$this->translator = $translator;
		$this->user = $user->isLoggedIn() ? $user->getIdentity() : null;
		$this->localizationFacade = $localizationFacade;
		$this->refundGreetingsGenerator = $refundGreetingsGenerator;
		$this->configuration = $configuration;
		$this->currencyFilter = $currencyFilter;
	}

	public function getRefundReceivedResponse(Refund $refund)
	{
		$orderNumber = '';
		if ($orderId = $refund->getOrderId()) {
			$orderNumber = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.orderId',
				null,
				[
					'orderId' => $orderId,
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		return [
			'subject' => $this->translator->translate(
				'model.refunds.bot.refundReceived.subject',
				null,
				[
					'code' => $refund->getUniqueId(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderNumber' => $orderNumber,
				],
				null,
				$this->resolveResponseLocale($refund)
			),
			'body' => $this->translator->translate(
				'model.refunds.bot.refundReceived.' . ($refund->getOrderId() ? 'bodyWithOrderId' : 'body'),
				null,
				[
					'orderId' => $refund->getOrderId(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderNumber' => $orderNumber,
				],
				null,
				$this->resolveResponseLocale($refund)
			),
		];
	}

	public function getRefundApprovedResponses(Refund $refund): ?array
	{
		$responseLocale = $this->resolveResponseLocale($refund);

		if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION) {
			$responses = [];

			$responses["Varianta 1"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.variant1',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			$responses["Varianta 2"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.variant2',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			$parametersV3 = [
				'agent' => $this->getAgentName(),
				'linkFaqBlock2' => $this->getLinkByLocalization($refund->getLocalization(), 'faq', null, 'block-2'),
				'linkFaqBlock4' => $this->getLinkByLocalization($refund->getLocalization(), 'faq', null, 'block-4'),
				'shops' => $this->translator->translate('model.refunds.admin.missingCommission.approved.shops', null, [], null, $refund->getLocalization()->getLocale()),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			];

			$responses["Varianta 3 (první reklamace)"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.variant3',
				null,
				$parametersV3,
				null,
				$responseLocale
			);

			$parametersV4 = [
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			];

			$responses["Varianta 4 (opakovaná reklamace)"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.variant4',
				null,
				$parametersV4,
				null,
				$responseLocale
			);

			$parametersV5 = [
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			];

//			$responses["Varianta 5 (nedodržení doporučení)"] = $this->translator->translate(
//				'model.refunds.admin.missingCommission.approved.variant5',
//				null,
//				$parametersV5,
//				null,
//				$responseLocale
//			);

			$responses["varianta kupón"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.coupon',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			$responses["varianta aktivní adblock"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.approved.adblock',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			if ($refund->getLocalization()->isHungarian()) {
				$responses["Varianta 6 (HU)"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.approved.variant6',
					null,
					$parametersV5,
					null,
					$responseLocale
				);
			}

			if ($refund->getLocalization()->isRomanian()) {
				$responses["Varianta - Facebook (RO)"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.approved.variantWithFacebook',
					null,
					$parametersV5 + ['facebookLink' => 'https://www.facebook.com/tipliro/reviews'],
					null,
					$responseLocale
				);
			}

			if ($refund->getLocalization()->isCroatian()) {
				$responses["Varianta - Facebook (HR)"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.approved.variantWithFacebook',
					null,
					$parametersV5 + ['facebookLink' => 'https://www.facebook.com/tiplihrvatska/reviews'],
					null,
					$responseLocale
				);
			}

			if ($refund->getLocalization()->isSlovenian()) {
				$responses["Varianta - Facebook (SI)"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.approved.variantWithFacebook',
					null,
					$parametersV5 + ['facebookLink' => 'https://www.facebook.com/tiplislovenija/reviews'],
					null,
					$responseLocale
				);
			}

			if ($refund->getLocalization()->isBulgarian()) {
				$responses["Varianta - Facebook (BG)"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.approved.variantWithFacebook',
					null,
					$parametersV5 + ['facebookLink' => 'https://www.facebook.com/tiplibg/reviews'],
					null,
					$responseLocale
				);
			}

			return $responses;
		}

		if ($refund->getType() === Refund::TYPE_UNCONFIRMED_TRANSACTION) {
			$responses["varianta 1"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.approved.variant1',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			return $responses;
		}

		if ($refund->getType() === Refund::TYPE_MISSING_BONUS) {
			$responses["varianta 1"] = $this->translator->translate(
				'model.refunds.admin.missingBonus.approved.variant1',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'value' => $this->configuration->getDefaultConfirmationTreshold($refund->getLocalization()),
					'currency' =>  $this->currencyFilter->__invoke($refund->getLocalization()),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			return $responses;
		}

		return null;
	}

	public function getRefundDeclinedResponses(Refund $refund): ?array
	{
		$responseLocale = $this->resolveResponseLocale($refund);

		if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION) {
			$responses = [];

			$responses["Všeobecná odpověď"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.declined.variant1',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'linkFaqBlock2' => $this->getLinkByLocalization($refund->getLocalization(), 'faq', null, 'block-2'),
					'linkFaqBlock4' => $this->getLinkByLocalization($refund->getLocalization(), 'faq', null, 'block-4'),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			$responses["Zaplý adblock"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.declined.adblock',
				null,
				[
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$responseLocale
			);

			$responses["Chybí přesměrování"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.declined.redirect',
				null,
				[
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$responseLocale
			);

			$responses["Použitý kupón"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.declined.coupon',
				null,
				[
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$responseLocale
			);

			$responses["Nejedná se o první nákup"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.declined.notFirstOrder',
				null,
				[
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$responseLocale
			);

			if ($refund->getShop()->isTesco()) {
				$responses["Tesco"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.declined.tesco',
					null,
					[
						'agent' => $this->getAgentName(),
						'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
						'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					],
					null,
					$this->resolveResponseLocale($refund)
				);
			}

			if ($refund->getShop()->isAliexpress()) {
				$parameters = [
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'linkAliexpressCashbackCheck' => $this->getLinkByLocalization($refund->getLocalization(), 'shop', $refund->getShop()->getSlug(), 'cashbackcheck'),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				];

				$responses["Produkt bez cashbacku"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.declined.aliexpress',
					null,
					$parameters,
					null,
					$responseLocale
				);
			} else {
				$parameters = [
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'linkFaq' => $this->getLinkByLocalization($refund->getLocalization(), 'faq'),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				];

				$responses["Použitá aplikace obchodu"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.declined.app',
					null,
					$parameters,
					null,
					$responseLocale
				);
			}

			if ($refund->getShop()->isAllegro()) {
				$parameters = [
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'linkAllegro' => $this->getLinkByLocalization($refund->getLocalization(), 'shop', $refund->getShop()->getSlug()),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				];

				$responses["Kategorie bez cashbacku"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.declined.allegro',
					null,
					$parameters,
					null,
					$responseLocale
				);
			}

			if ($refund->getShop()->isBooking()) {
				$parameters = [
					'agent' => $this->getAgentName(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'orderNumber' => $refund->getOrderId(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'orderId' => $refund->getOrderId(),
				];

				$responses["Booking - rezervace není v affilu"] = $this->translator->translate(
					'model.refunds.admin.missingCommission.declined.bookingTipliHeaderMissing',
					null,
					$parameters,
					null,
					$responseLocale
				);
			}

			return $responses;
		}

		if ($refund->getType() === Refund::TYPE_UNCONFIRMED_TRANSACTION) {
			$responses["varianta 1"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.declined.variant1',
				null,
				[
					'agent' => $this->getAgentName(),
					'orderNumber' => $refund->getOrderId(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			$responses["bonus"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.declined.bonus',
				null,
				[
					'agent' => $this->getAgentName(),
					'value' => $this->configuration->getDefaultConfirmationTreshold($refund->getLocalization()),
					'currency' => $refund->getLocalization()->isSlovak() ? '€' : 'Kč',
					'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$responseLocale
			);

			return $responses;
		}

		if ($refund->getType() === Refund::TYPE_MISSING_BONUS) {
			if ($refund->getLocalization()->isHungarian() === false) {
				$responses["varianta 1"] = $this->translator->translate(
					'model.refunds.admin.missingBonus.declined.variant1',
					null,
					[
						'agent' => $this->getAgentName(),
						'greeting' => $this->refundGreetingsGenerator->getGreeting($responseLocale),
						'value' => $this->configuration->getDefaultConfirmationTreshold($refund->getLocalization()),
						'currency' =>  $this->currencyFilter->__invoke($refund->getLocalization()),
						'orderId' => $refund->getOrderId(),
						'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					],
					null,
					$responseLocale
				);
			}

			$responses["Nesprávný formulář"] = $this->translator->translate(
				'model.refunds.admin.missingBonus.declined.wrongForm',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);

			$responses["Podmínky bonusu za doporučení"] = $this->translator->translate(
				'model.refunds.admin.missingBonus.declined.missingBonusRecommendation',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$this->resolveResponseLocale($refund)
			);

			return $responses;
		}

		if ($refund->getType() === Refund::TYPE_CANCELED_COMMISSION) {
			$responses["Uplatnění kuponu"] = $this->translator->translate(
				'model.refunds.admin.canceledCommission.declined.invalidCoupon',
				null,
				[
					'agent' => $this->getAgentName(),
					'shop' => $refund->getRelatedTransaction()->getShop()->getName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		return null;
	}

	public function getRefundMessageResponses(Refund $refund): ?array
	{
		$responses = [];
		$orderNumber = '';

		$localization = $refund->getLocalization();

		if ($orderId = $refund->getOrderId()) {
			$orderNumber = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.orderId',
				null,
				[
					'orderId' => $orderId,
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		$responses["Doplnění odkazu na AliExpress"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.linkAliexpress',
			null,
			[
				'orderNumber' => $orderNumber,
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		if ($refund->getShop()) {
			$responses["Nákup v aplikaci obchodu"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.shopAppOrder',
				null,
				[
					'orderNumber' => $orderNumber,
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		$responses["Odkud pocházel kupón"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.coupon',
			null,
			[
				'orderNumber' => $orderNumber,
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Zaslat potvrzení objednávky"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.orderConfirmation',
			null,
			[
				'orderNumber' => $orderNumber,
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Zaslali jsme reklamaci do part. systému"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.waitingForPartner',
			null,
			[
				'orderNumber' => $orderNumber,
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Druh produktu"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.productType',
			null,
			[
				'orderNumber' => $orderNumber,
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Zrušená odměna"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.canceledCommission',
			null,
			[
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		if ($purchasedAt = $refund->getPurchasedAt()) {
			$responses["Upřesnění datum objednávky"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.orderDateCreatedInfo',
				null,
				[
					'orderNumber' => $orderNumber,
					'agent' => $this->getAgentName(),
					'purchasedAt' => $purchasedAt->format($localization->getDateFormat('d.m.Y')),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->getShop()?->isBooking()) {
			$responses["Booking: dodání potvrzení"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.bookingReservation',
				null,
				[
					'orderNumber' => $orderNumber,
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($shop = $refund->getShop()) {
			$responses["První objednávka"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.firstOrderInfo',
				null,
				[
					'orderNumber' => $orderNumber,
					'agent' => $this->getAgentName(),
					'shop' => $shop->getName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		$responses["Partner reklamaci ověřuje"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.stillWaitingForPartner',
			null,
			[
				'agent' => $this->getAgentName(),
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Doplnění potvrzení - celkově zaplacená suma"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.additionalInformation1',
			null,
			[
				'agent' => $this->getAgentName(),
				'orderNumber' => $orderNumber,
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Doplnění potvrzení - datum"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.additionalInformation2',
			null,
			[
				'agent' => $this->getAgentName(),
				'orderNumber' => $orderNumber,
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		$responses["Neúplné potvrzení"] = $this->translator->translate(
			'model.refunds.admin.missingCommission.message.additionalInformation3',
			null,
			[
				'agent' => $this->getAgentName(),
				'orderNumber' => $orderNumber,
				'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
				'orderId' => $refund->getOrderId(),
				'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
			],
			null,
			$this->resolveResponseLocale($refund)
		);

		if ($refund->getLocalization()->isCzech() || $refund->getLocalization()->isSlovak()) {
			$responses["Potvrzení - knihy dobrovský"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.additionalInformation4',
				null,
				[
					'agent' => $this->getAgentName(),
					'orderNumber' => $orderNumber,
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->isTypeMissingCommission()) {
			$responses["Doplnění údajů + kupon"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.orderConfirmationWithCoupon',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderNumber' => $orderNumber,
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->isTypeMissingBonus()) {
			$responses["Doplnění emailu doporučeného"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.reccomendedUserEmail',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'orderId' => $refund->getOrderId(),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->getType() === Refund::TYPE_UNCONFIRMED_TRANSACTION && $refund->getRelatedTransaction()) {
			$shop = $refund->getRelatedTransaction()->getShop();

			$responses["Čeká na ověření partnerem"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.message.waitingForAffil',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $shop ? $shop->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->getType() === Refund::TYPE_UNCONFIRMED_TRANSACTION && $refund->getLocalization()->isHungarian()) {
			$shop = $refund->getRelatedTransaction()->getShop();

			$responses["Čeká na ověření partnerem"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.message.confirmationInfo',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $shop ? $shop->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION && $refund->getLocalization()->isRomanian()) {
			$responses["Avon - potvrzení objednávky"] = $this->translator->translate(
				'model.refunds.admin.missingCommission.message.avon',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		if ($refund->isTypeUnconfirmedTransaction()) {
			$responses["Booking potvrzení"] = $this->translator->translate(
				'model.refunds.admin.unconfirmedTransaction.message.bookingConfirmation',
				null,
				[
					'agent' => $this->getAgentName(),
					'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
					'shop' => $refund->getShop() ? $refund->getShop()->getName() : '',
					'orderId' => $refund->getOrderId(),
				],
				null,
				$this->resolveResponseLocale($refund)
			);
		}

		return $responses;
	}

	public function getFirstRefundResponse(Refund $refund)
	{
		return $this->translator->translate('model.refunds.bot.missingCommission.firstRefundApproved', null, [
			'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
			'shop' => $refund->getShop() ? $refund->getShop() : '',
			'orderId' => $refund->getOrderId(),
		], null, $this->resolveResponseLocale($refund));
	}

	public function getSecondRefundResponse(Refund $refund)
	{
		return $this->translator->translate('model.refunds.bot.missingCommission.secondRefundApproved', null, [
			'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
			'shop' => $refund->getShop() ? $refund->getShop() : '',
			'orderId' => $refund->getOrderId(),
		], null, $this->resolveResponseLocale($refund));
	}

	public function getSecondRefundWithAdblockResponse(Refund $refund)
	{
		return $this->translator->translate('model.refunds.bot.missingCommission.secondRefundApprovedAdblock', null, [
			'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
			'shop' => $refund->getShop() ? $refund->getShop() : '',
			'orderId' => $refund->getOrderId(),
		], null, $this->resolveResponseLocale($refund));
	}

	public function getSecondRefundWithRedirectionResponse(Refund $refund)
	{
		$localization = $this->resolveResponseLocalization($refund);
		$link = $this->getLinkByLocalization($localization, 'faq', null, 'block-3');

		return $this->translator->translate('model.refunds.bot.missingCommission.secondRefundApprovedRedirection', null, [
			'link' => $link,
			'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
			'shop' => $refund->getShop() ? $refund->getShop() : '',
			'orderId' => $refund->getOrderId(),
		], null, $localization->getLocale());
	}

	public function getSecondRefundWithCouponResponse(Refund $refund)
	{
		return $this->translator->translate('model.refunds.bot.missingCommission.secondRefundApprovedCoupon', null, [
			'greeting' => $this->refundGreetingsGenerator->getGreeting($this->resolveResponseLocale($refund)),
			'shop' => $refund->getShop() ? $refund->getShop() : '',
			'orderId' => $refund->getOrderId(),
		], null, $this->resolveResponseLocale($refund));
	}

	private function getAgentName()
	{
		if ($this->user && $this->user->getFirstName()) {
			return $this->user->getFirstName();
		}

		return 'Tomáš';
	}

	private function resolveResponseLocalization(Refund $refund): Localization
	{
		$refundLocalization = $refund->getLocalization();

		if ($refundLocalization->isCzech() && $this->user && in_array($this->user->getId(), Refund::slovakResponders())) {
			return $this->localizationFacade->findOneByLocale(Localization::LOCALE_SLOVAK);
		} elseif ($refundLocalization->isSlovak() && $this->user && in_array($this->user->getId(), Refund::czechResponders())) {
			return $this->localizationFacade->findOneByLocale(Localization::LOCALE_CZECH);
		}

		return $refund->getLocalization();
	}

	private function resolveResponseLocale(Refund $refund): string
	{
		return $this->resolveResponseLocalization($refund)->getLocale();
	}

	public function getLinkByLocalization(Localization $localization, string $slug, ?string $additionalPath = null, ?string $fragment = null, array $queryParameters = [])
	{
		$url = new Url($this->translator->translate('front.links.homepage', null, [], null, $localization->getLocale()));

		$path = RouterFactory::getTranslation($slug, $localization);

		if ($additionalPath) {
			$path .= '/' . $additionalPath;
		}

		$url->setPath($path);
		$url->setFragment($fragment ?? '');

		foreach ($queryParameters as $name => $value) {
			$url->setQueryParameter($name, $value);
		}

		return $url->getAbsoluteUrl();
	}
}
