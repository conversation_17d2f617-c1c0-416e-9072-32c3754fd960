<?php

namespace tipli\Model\Refunds\Subscriber;

use Nette\Localization\Translator;
use Nette\Http\Url;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Configuration;
use tipli\Model\Currencies\CurrencyFilter;
use tipli\Model\Freshdesk\FreshdeskClient;
use tipli\Model\Freshdesk\FreshdeskFacade;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Refunds\Events\RefundApprovedEvent;
use tipli\Model\Refunds\Events\RefundCreatedEvent;
use tipli\Model\Refunds\Events\RefundDeclinedEvent;
use tipli\Model\Refunds\Events\RefundReceivedEvent;
use tipli\Model\Refunds\Events\RefundSendMessageEvent;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Refunds\RefundGreetingsGenerator;
use tipli\Model\Refunds\RefundResponseProvider;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;
use tipli\Routers\RouterFactory;
use Tracy\Debugger;

class RefundSubscriber implements EventSubscriberInterface
{
	/** @var RefundFacade */
	private $refundFacade;

	/** @var FreshdeskFacade */
	private $freshdeskFacade;

	/** @var TransactionFacade */
	private $transactionFacade;

	/** @var RefundResponseProvider */
	private $refundResponseProvider;

	/** @var Configuration */
	private $configuration;

	/** @var Translator */
	private $translator;

	/** @var CurrencyFilter */
	private $currencyFilter;

	/** @var RefundGreetingsGenerator */
	private $refundGreetingsGenerator;

	public function __construct(
		RefundFacade $refundFacade,
		FreshdeskFacade $freshdeskFacade,
		TransactionFacade $transactionFacade,
		RefundResponseProvider $refundResponseProvider,
		Configuration $configuration,
		Translator $translator,
		CurrencyFilter $currencyFilter,
		RefundGreetingsGenerator $refundGreetingsGenerator,
		private FreshdeskClient $freshdeskClient
	) {
		$this->refundFacade = $refundFacade;
		$this->freshdeskFacade = $freshdeskFacade;
		$this->transactionFacade = $transactionFacade;
		$this->refundResponseProvider = $refundResponseProvider;
		$this->configuration = $configuration;
		$this->translator = $translator;
		$this->currencyFilter = $currencyFilter;
		$this->refundGreetingsGenerator = $refundGreetingsGenerator;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			RefundReceivedEvent::class => 'refundReceived',
			RefundCreatedEvent::class => 'refundCreated',
			RefundApprovedEvent::class => 'refundApproved',
			RefundDeclinedEvent::class => 'refundDeclined',
			RefundSendMessageEvent::class => 'refundSendMessage',
		];
	}

	public function refundCreated(RefundCreatedEvent $refundCreatedEvent)
	{
		$refund = $refundCreatedEvent->getRefund();

		if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION && $this->configuration->getMode() !== 'test') {
			$this->createRefundSolution($refund);
		}

		if ($this->configuration->getMode() === 'test') {
			$this->refundReceived(new RefundReceivedEvent($refund));
		}
	}

	public function refundReceived(RefundReceivedEvent $refundReceivedEvent)
	{
		$refund = $refundReceivedEvent->getRefund();

		$ticket = $this->freshdeskFacade->createOutboundTicket(
			$refund->getUser(),
			$this->refundResponseProvider->getRefundReceivedResponse($refund)['subject'],
			$this->refundResponseProvider->getRefundReceivedResponse($refund)['body']
		);

		Debugger::log('r' . $refund->getId() . ' -  ticket: ' . $ticket->getId(), 'refund-debug');

		$this->freshdeskFacade->addNote($ticket, 'https://www.tipli.cz/admin/refunds.refund/refund/' . $refund->getId());

		if (!empty($refund->getMessage())) {
			$this->freshdeskFacade->addNote($ticket, "Původní zpráva od uživatele:<br />" . $refund->getMessage());
		}

		$this->refundFacade->assignFreshdeskTicketToRefund($refund, $ticket);
	}

	public function refundApproved(RefundApprovedEvent $refundApprovedEvent)
	{
		$refund = $refundApprovedEvent->getRefund();
		$message = $refundApprovedEvent->getMessage();
		$bonusAmount = $refundApprovedEvent->getBonusAmount();
		$confirmationTreshold = $refundApprovedEvent->getConfirmationTreshold();
		$byUser = $refundApprovedEvent->getByUser();

		try {
			if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION) {
				$this->createBonusRefundTransaction($refund, $bonusAmount, $confirmationTreshold);
			} elseif ($refund->getType() === Refund::TYPE_UNCONFIRMED_TRANSACTION) {
				$this->confirmTransaction($refund);
			} elseif ($refund->getType() === Refund::TYPE_INCORRECT_AMOUNT) {
				$this->createBonusRefundTransaction($refund, $bonusAmount, $confirmationTreshold);
			}
		} catch (\Exception $e) {
			Debugger::log($e->getMessage(), 'refund-exception');

			$refund->prepareForApprove();

			$this->refundFacade->saveRefund($refund);

			throw $e;
		}

		$this->refundFacade->saveRefund($refund);

		// send message to user
		if ($message) {
			$localization = $refund->getLocalization();

			$this->translator->setLocale($localization->getLocale());

			$this->freshdeskFacade->createMessage($refund->getFreshdeskTicket(), $message, $byUser);
		} else {
			$this->freshdeskFacade->addNote($refund->getFreshdeskTicket(), 'Reklamace byla vyřešena bez zprávy pro uživatele.');
		}

		$this->freshdeskClient->resolveFreshdeskTicket($refund->getFreshdeskTicket(), $byUser);
	}

	public function refundDeclined(RefundDeclinedEvent $refundDeclinedEvent)
	{
		$refund = $refundDeclinedEvent->getRefund();
		$message = $refundDeclinedEvent->getMessage();
		$byUser = $refundDeclinedEvent->getByUser();

		// send message to user
		if ($message) {
			$this->freshdeskFacade->createMessage($refund->getFreshdeskTicket(), $message, $byUser);
		} else {
			$this->freshdeskFacade->addNote($refund->getFreshdeskTicket(), 'Reklamace byla vyřešena bez zprávy pro uživatele.');
		}

		$this->freshdeskClient->resolveFreshdeskTicket($refund->getFreshdeskTicket(), $byUser);
	}

	private function createRefundSolution(Refund $refund)
	{
		$this->refundFacade->createRefundSolution($refund);
	}

	private function confirmTransaction(Refund $refund)
	{
		/** @var Transaction|null $transaction */
		$transaction = $refund->getRelatedTransaction();

		if (!$transaction) {
			return;
		}

		if ($transaction->isCommission()) {
			$this->transactionFacade->confirm($transaction);
		} elseif ($transaction->isBonus()) {
			$transaction->setConfirmationTreshold(0);
			$this->transactionFacade->confirm($transaction);
		}

		bdump("potvrzeno");
	}

	private function createBonusRefundTransaction(Refund $refund, float $bonusAmount, float $confirmationTreshold)
	{
		/** @var Transaction $transaction */
		$transaction = $this->transactionFacade->createRefundBonusTransaction(
			$refund->getUser(),
			$refund->getShop(),
			'Bonus',
			$bonusAmount,
			$confirmationTreshold
			//			$this->configuration->getDefaultConfirmationTreshold($refund->getLocalization())
		);

		$refund->setRefundTransaction($transaction);

		$this->refundFacade->saveRefund($refund);
	}

	public function refundSendMessage(RefundSendMessageEvent $refundSendMessageEvent)
	{
		$refund = $refundSendMessageEvent->getRefund();
		$message = $refundSendMessageEvent->getMessage();
		$byUser = $refundSendMessageEvent->getByUser();

		$this->freshdeskFacade->createMessage($refund->getFreshdeskTicket(), $message, $byUser);
	}

	public function getLinkByLocalization(Localization $localization, string $slug, array $queryParameters = [])
	{
		$url = new Url($this->translator->translate('front.links.homepage', null, [], null, $localization->getLocale()));

		$url->setPath(RouterFactory::getTranslation($slug, $localization));

		foreach ($queryParameters as $name => $value) {
			$url->setQueryParameter($name, $value);
		}

		return $url->getAbsoluteUrl();
	}
}
