<?php

namespace tipli\Model\Refunds\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use tipli\Model\Account\Entities\User;
use tipli\Model\Files\Entities\File;
use tipli\Model\Freshdesk\Entities\Ticket;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Refunds\RefundResolver;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\Transaction;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Refunds\Repositories\RefundRepository")
 * @ORM\Table(name="tipli_refunds_refund")
 */
class Refund
{
	public const TYPE_MISSING_COMMISSION = 'missing_commission';
	public const TYPE_MISSING_BONUS = 'missing_bonus';
	public const TYPE_INCORRECT_AMOUNT = 'incorrect_amount';
	public const TYPE_UNCONFIRMED_TRANSACTION = 'unconfirmed_transaction';
	public const TYPE_CANCELED_COMMISSION = 'canceled_commission';

	public const STATE_WAITING_FOR_PROCESS = 'waiting_for_process';
	public const STATE_WAITING_FOR_OPERATOR = 'waiting_for_operator';
	public const STATE_READY_FOR_APPROVE = 'ready_for_approve';
	public const STATE_APPROVED = 'approved';
	public const STATE_DECLINED = 'declined';

	public const DECLINE_REASON_HIGH_CASHBACK = 'high_cashback';
	public const DECLINE_REASON_TOO_MUCH_REFUNDS = 'too_much_refunds';
	public const DECLINE_REASON_MISSING_REDIRECT = 'missing_redirect';
	public const DECLINE_REASON_UNSUPPORTED_COUPON = 'unsupported_coupon';
	public const DECLINE_REASON_PRODUCT_WITHOUT_CASHBACK = 'product_without_cashback';
	public const DECLINE_REASON_SHOP_MOBILE_APP = 'shop_mobile_app';
	public const DECLINE_REASON_DUPLICITY_REFUND = 'duplicity_refund';
	public const DECLINE_REASON_BONUS_CONFIRMATION = 'bonus_confirmation';
	public const DECLINE_REASON_USER_IS_NOT_RESPONDING = 'user_is_not_responding';
	public const DECLINE_REASON_ADBLOCK = 'adblock';
	public const DECLINE_REASON_TRANSACTION_WAITING_FOR_CONFIRMATION = 'transaction_waiting_for_confirmation';
	public const DECLINE_REASON_BONUS_RULES = 'bonus_rules';
	public const DECLINE_REASON_SHOP_RULES = 'shop_rules';
	public const DECLINE_REASON_MISSING_REDIRECTION = 'missing_redirection';
	public const DECLINE_REASON_REWARD_CALCULATED_CORRECTLY = 'reward_calculated_correctly';
	public const DECLINE_REASON_REWARD_IS_CREDITED = 'reward_is_credited';
	public const DECLINE_REASON_MISSING_IN_AFFILIATE = 'missing_in_affiliate';
	public const DECLINE_REASON_USER_NOT_RESPONDING = 'user_not_responding';
	public const DECLINE_REASON_OTHER = 'other';
	public const DECLINE_REASON_WRONG_FORM = 'wrong_form';
	public const DECLINE_REASON_ANOTHER_PUBLISHER = 'another_publisher';
	public const DECLINE_REASON_RETURNED_PRODUCT = 'returned_product';
	public const RESOLVE_CHECK_REASON_REDIRECTION_ERROR = 'redirect_error';
	public const RESOLVE_CHECK_REASON_INCORRECT_CATEGORY = 'incorrect_category';
	public const RESOLVE_CHECK_REASON_COUPON_FROM_DIFFERENT_PORTAL = 'coupon_from_different_portal';
	public const RESOLVE_CHECK_REASON_ADBLOCK_ENABLED = 'adblock_enabled';
	public const RESOLVE_CHECK_REASON_NO_CASHBACK_FOR_PRODUCT = 'no_cashback_for_product';
	public const RESOLVE_CHECK_REASON_NOT_USER_FIRST_ORDER = 'not_user_first_order';
	public const RESOLVE_CHECK_REASON_USE_OF_SHOP_APP = 'use_of_shop_app';
	public const RESOLVE_CHECK_REASON_BOOKING_NO_CONFIRMATION = 'booking_no_confirmation';
	public const RESOLVE_CHECK_REASON_REWARD_DOES_NOT_APPLY_TO_PRODUCT = 'reward_does_not_apply_to_product';
	public const RESOLVE_CHECK_REASON_INCORRECT_AMOUNT = 'incorrect_amount';
	public const RESOLVE_CHECK_REASON_UNKNOWN_FIRST_PURCHASE = 'unknown_first_purchase';
	public const RESOLVE_CHECK_REASON_OTHER = 'other';
	public const RESOLVE_CHECK_STATE_APPROVED = 'approved';
	public const RESOLVE_CHECK_STATE_DECLINED = 'declined';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\Column(type="string", length=8)
	 * @var string
	 */
	private $uniqueId;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 * @var Localization
	 */
	private $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 * @var User
	 */
	private $user;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="assigned_operator_id", referencedColumnName="id")
	 * @var User|null
	 */
	private $assignedOperator;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 * @var Shop|null
	 */
	private $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="related_transaction_id", referencedColumnName="id")
	 * @var Transaction|null
	 */
	private $relatedTransaction;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="refund_transaction_id", referencedColumnName="id")
	 * @var Transaction|null
	 */
	private $refundTransaction;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Freshdesk\Entities\Ticket", inversedBy="refund")
	 * @ORM\JoinColumn(name="freshdesk_ticket_id", referencedColumnName="id")
	 * @var Ticket
	 */
	private $freshdeskTicket;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Files\Entities\File")
	 * @ORM\JoinColumn(name="invoice_id", referencedColumnName="id", nullable=true)
	 * @var File|null
	 */
	private $invoice;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Files\Entities\File")
	 * @ORM\JoinColumn(name="second_invoice_id", referencedColumnName="id", nullable=true)
	 * @var File|null
	 */
	private $secondInvoice;

	/**
	 * @ORM\OneToMany(targetEntity="RefundSolution", mappedBy="refund")
	 */
	private $refundSolution;

	/**
	 * @ORM\Column(type="string", length=32)
	 * @var string
	 */
	private $type;

	/**
	 * @ORM\Column(type="string", length=26)
	 * @var string
	 */
	private $state;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string|null
	 */
	private $message;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $orderId;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $purchasedAt;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float|null
	 */
	private $orderAmount;

	/**
	 * @ORM\Column(type="string", nullable=true, length=3)
	 * @var string|null
	 */
	private $orderCurrency;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float|null
	 */
	private $expectedAmount;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 * @var bool|null
	 */
	private $couponUsed;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $couponCode;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string
	 */
	private $productUrls;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $resolvedAt;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="resolved_by_user_id", referencedColumnName="id")
	 * @var User|null
	 */
	private $resolvedByUser;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="resolve_checked_by_user_id", referencedColumnName="id")
	 * @var User|null
	 */
	private $resolveCheckedByUser;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $resolveCheckedAt;

	/**
	 * @ORM\Column(type="string", nullable=true, length=8)
	 * @var string|null
	 */
	private $resolveCheckState;

	/**
	 * @ORM\Column(type="string", nullable=true, length=26)
	 * @var string|null
	 */
	private ?string $resolveCheckReasonType;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string|null
	 */
	private ?string $resolveCheckMessage;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string|null
	 */
	private $shopResponseMessage;

	/**
	 * @ORM\Column(type="string", length=26, nullable=true)
	 * @var string|null
	 */
	private $declineReasonType;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $declineReasonMessage;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $processAt;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $processErrorMessage;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $userLastResponseAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $operatorLastResponseAt;

	/**
	 * @ORM\OneToMany(targetEntity="RefundProcess", mappedBy="refund")
	 */
	private $refundProcess;

	/**
	 * @ORM\Column(type="string", nullable=true, length=8)
	 * @var string|null
	 */
	private $platform;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 * @var bool
	 */
	private $addonUsed;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 * @var bool
	 */
	private $mobileAppUsed;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="RefundComment", mappedBy="refund")
	 */
	private $comments;

	public function __construct(
		User $user,
		string $type,
		?Shop $shop = null,
		string $message = null,
		?\DateTime $purchasedAt = null,
		?string $orderId = null,
		?float $orderAmount = null,
		?string $orderCurrency = null,
		?float $expectedAmount = null,
		?Transaction $relatedTransaction = null,
		?string $platform = null,
		bool $addonUsed = false,
		bool $mobileAppUsed = false
	) {
		$this->uniqueId = Strings::lower(Random::generate(8));
		$this->localization = $user->getLocalization();
		$this->user = $user;
		$this->type = $type;
		$this->shop = $shop;
		$this->message = $message;
		$this->purchasedAt = $purchasedAt;
		$this->orderAmount = $orderAmount;
		$this->orderCurrency = $orderCurrency;
		$this->orderId = $orderId;
		$this->expectedAmount = $expectedAmount;
		$this->relatedTransaction = $relatedTransaction;
		$this->platform = $platform;
		$this->addonUsed = $addonUsed;
		$this->mobileAppUsed = $mobileAppUsed;

		$this->state = self::STATE_WAITING_FOR_PROCESS;
		$this->processAt = new \DateTime();
		$this->refundProcess = new ArrayCollection();
		$this->createdAt = new \DateTime();
	}

	public function getId()
	{
		return $this->id;
	}

	public function setCouponUsed(?bool $couponUsed): void
	{
		$this->couponUsed = $couponUsed;
	}

	public function setCouponCode(?string $couponCode): void
	{
		$this->couponCode = $couponCode;
	}

	public function setInvoice(?File $invoice): void
	{
		$this->invoice = $invoice;
	}

	public function getUser(): User
	{
		return $this->user;
	}

	public function setFreshdeskTicket(Ticket $freshdeskTicket): void
	{
		$this->freshdeskTicket = $freshdeskTicket;
	}

	public function getExpectedAmount(): ?float
	{
		return $this->expectedAmount;
	}

	public function getShop(): ?Shop
	{
		return $this->shop;
	}

	public function approve(?User $user = null)
	{
		$this->resolvedByUser = $user;
		$this->state = self::STATE_APPROVED;
		$this->resolvedAt = new \DateTime();
	}

	public function decline(?User $user = null)
	{
		$this->resolvedByUser = $user;
		$this->state = self::STATE_DECLINED;
		$this->resolvedAt = new \DateTime();
	}

	public function prepareForApprove()
	{
		$this->state = self::STATE_READY_FOR_APPROVE;
//		$this->processAt = (new \DateTime)->modify('+ ' . random_int(15, 30) . ' minutes');
		$this->processAt = (new \DateTime())->modify('+ ' . random_int(10, 35) . ' minutes');
	}

	public function assignToOperator()
	{
		$this->state = self::STATE_WAITING_FOR_OPERATOR;
	}

	public function getPurchasedAt(): ?\DateTime
	{
		return $this->purchasedAt;
	}

	public function getOrderCurrency(): ?string
	{
		return $this->orderCurrency;
	}

	public function getOrderAmount(): ?float
	{
		return $this->orderAmount;
	}

	public function getOrderAmountWithoutTax(): ?float
	{
		$tax = RefundResolver::TAXES[$this->getLocalization()->getId()];

		return $this->orderAmount - ($this->orderAmount * ($tax / (100 + $tax)));
	}

	public static function getTypes(): array
	{
		return [
			self::TYPE_MISSING_COMMISSION => 'nepřipsaná odměna',
			self::TYPE_MISSING_BONUS => 'chybějící bonus',
			self::TYPE_INCORRECT_AMOUNT => 'špatná výše odměny',
			self::TYPE_UNCONFIRMED_TRANSACTION => 'nepotvrzená odměna',
			self::TYPE_CANCELED_COMMISSION => 'zrušená odměna',
		];
	}

	public static function getStates(): array
	{
		return [
			self::STATE_WAITING_FOR_PROCESS => 'právě zpracovává robot',
			self::STATE_WAITING_FOR_OPERATOR => 'čeká na operátora',
			self::STATE_APPROVED => 'schválená',
			self::STATE_READY_FOR_APPROVE => 'předschválená',
			self::STATE_DECLINED => 'zamítnuta',
		];
	}

	public static function getDeclineReasons(): array
	{
		return [
			self::DECLINE_REASON_HIGH_CASHBACK => 'Vysoký cashback - neuznáno v affilu',
//			self::DECLINE_REASON_TOO_MUCH_REFUNDS => 'Vysoký počet reklamací/bonusu',
			self::DECLINE_REASON_MISSING_REDIRECT => 'Chybějící přesměrování',
			self::DECLINE_REASON_UNSUPPORTED_COUPON => 'Využití kupónu neslučitelného s CB',
			self::DECLINE_REASON_PRODUCT_WITHOUT_CASHBACK => 'Produkt, na který se nevztahuje CB',
			self::DECLINE_REASON_SHOP_MOBILE_APP => 'Využití mobilní aplikace obchodu',
			self::DECLINE_REASON_ADBLOCK => 'Aktivní blokátor reklamy',
			self::DECLINE_REASON_DUPLICITY_REFUND => 'Duplicitní reklamace',
//			self::DECLINE_REASON_BONUS_CONFIRMATION => 'Odmítnutí potvrzení bonusu',
			self::DECLINE_REASON_USER_IS_NOT_RESPONDING => 'Uživatel neodpovídá',
//			self::DECLINE_REASON_TRANSACTION_WAITING_FOR_CONFIRMATION => 'Odměna čeká na potvrzení - neuplynula dostatečná doba',
			self::DECLINE_REASON_BONUS_RULES => 'Nesplněné podmínky bonusu',
			self::DECLINE_REASON_SHOP_RULES => 'Porušení podmínek obchodu',
//			self::DECLINE_REASON_MISSING_REDIRECTION => 'Chybějící/dlouhé přesměrování',
			self::DECLINE_REASON_REWARD_CALCULATED_CORRECTLY => 'Odměna vypočítána správně',
			self::DECLINE_REASON_REWARD_IS_CREDITED => 'Odměna je připsána',
//			self::DECLINE_REASON_MISSING_IN_AFFILIATE => 'Nepropsáno v affilu',
//			self::DECLINE_REASON_USER_NOT_RESPONDING => 'Uživatel nereaguje',
			self::DECLINE_REASON_WRONG_FORM => 'Nesprávný formulář',
			self::DECLINE_REASON_ANOTHER_PUBLISHER => 'Připsáno jinému publisherovi',
			self::DECLINE_REASON_RETURNED_PRODUCT => 'Vrácený produkt',
			self::DECLINE_REASON_OTHER => 'Jiný důvod',
		];
	}

	public static function getResolveCheckReasons(): array
	{
		return [
			self::RESOLVE_CHECK_REASON_REDIRECTION_ERROR => "Redirection error",
			self::RESOLVE_CHECK_REASON_INCORRECT_CATEGORY => "Incorrect category",
			self::RESOLVE_CHECK_REASON_COUPON_FROM_DIFFERENT_PORTAL => "Coupon from different portal",
			self::RESOLVE_CHECK_REASON_ADBLOCK_ENABLED => "Adblock enabled",
			self::RESOLVE_CHECK_REASON_NO_CASHBACK_FOR_PRODUCT => "No cashback for this product",
			self::RESOLVE_CHECK_REASON_NOT_USER_FIRST_ORDER => "Not the user's first order",
			self::RESOLVE_CHECK_REASON_USE_OF_SHOP_APP => "Use of shop application",
			self::RESOLVE_CHECK_REASON_BOOKING_NO_CONFIRMATION => "Booking - no confirmation with 'in cooperation with Tipli' logo",
			self::RESOLVE_CHECK_REASON_REWARD_DOES_NOT_APPLY_TO_PRODUCT => "No reward for purchased product/products",
			self::RESOLVE_CHECK_REASON_INCORRECT_AMOUNT => "Incorrect amount/bonus",
			self::RESOLVE_CHECK_REASON_UNKNOWN_FIRST_PURCHASE => "Unknown first purchase status",
			self::RESOLVE_CHECK_REASON_OTHER => "Other",
		];
	}

	public static function getResolveCheckStates(): array
	{
		return [
			self::RESOLVE_CHECK_STATE_APPROVED => "Approved",
			self::RESOLVE_CHECK_STATE_DECLINED => "Declined",
		];
	}

	public function getStateLabel(): string
	{
		return self::getStates()[$this->getState()];
	}

	public function getTypeLabel(): string
	{
		return self::getTypes()[$this->getType()];
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getState(): string
	{
		return $this->state;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	public function getUniqueId(): string
	{
		return $this->uniqueId;
	}

	public function getType(): string
	{
		return $this->type;
	}

	public function isCouponUsed(): ?bool
	{
		return $this->couponUsed;
	}

	public function getCouponCode(): ?string
	{
		return $this->couponCode;
	}

	public function getOrderId(): ?string
	{
		return $this->orderId;
	}

	public function getInvoice(): ?File
	{
		return $this->invoice;
	}

	public function getRefundProcess(): ?RefundProcess
	{
		return $this->refundProcess->isEmpty() ? null : $this->refundProcess->current();
	}

	public function getResolvedByUser(): ?User
	{
		return $this->resolvedByUser;
	}

	public function getResolvedAt(): ?\DateTime
	{
		return $this->resolvedAt;
	}

	public function getRefundTransaction(): ?Transaction
	{
		return $this->refundTransaction;
	}

	public function setRefundTransaction(?Transaction $refundTransaction): void
	{
		$this->refundTransaction = $refundTransaction;
	}

	public function getMessage(): ?string
	{
		return $this->message;
	}

	public function getRelatedTransaction(): ?Transaction
	{
		return $this->relatedTransaction;
	}

	public function getFreshdeskTicket(): ?Ticket
	{
		return $this->freshdeskTicket;
	}

	public function isResolved(): bool
	{
		return $this->resolvedAt !== null;
	}

	public function isApproved(): bool
	{
		return $this->getState() === self::STATE_APPROVED;
	}

	public function isDeclined(): bool
	{
		return $this->getState() === self::STATE_DECLINED;
	}

	public function processed(): void
	{
		$this->processAt = null;
	}

	public function checkResolve(string $state, User $checkedByUser, ?string $reason, ?string $message): void
	{
		$this->resolveCheckedAt = new \DateTime();
		$this->resolveCheckedByUser = $checkedByUser;
		$this->resolveCheckState = $state;
		$this->resolveCheckReasonType = $reason;
		$this->resolveCheckMessage = $message;
	}

	public function isResolveChecked(): bool
	{
		return $this->resolveCheckedAt !== null;
	}

	public function getResolveCheckedByUser(): ?User
	{
		return $this->resolveCheckedByUser;
	}

	public function getResolveCheckState(): ?string
	{
		return $this->resolveCheckState;
	}

	public function getResolveCheckedAt(): ?\DateTime
	{
		return $this->resolveCheckedAt;
	}

	public function getComments()
	{
		return $this->comments;
	}

	public function getUserLastResponseAt(): ?\DateTime
	{
		return $this->userLastResponseAt;
	}

	public function getOperatorLastResponseAt(): ?\DateTime
	{
		return $this->operatorLastResponseAt;
	}

	public function assignOperator(?User $operator): void
	{
		$this->assignedOperator = $operator;
	}

	public function getAssignedOperator(): ?User
	{
		return $this->assignedOperator;
	}

	public function isWaitingForOperatorResponse(): bool
	{
		return $this->getUserLastResponseAt() && $this->getOperatorLastResponseAt() < $this->getUserLastResponseAt() && !$this->isResolved();
	}

	public function isWaitingForUserResponse(): bool
	{
		return $this->getOperatorLastResponseAt() && $this->getUserLastResponseAt() < $this->getOperatorLastResponseAt() && !$this->isResolved();
	}

	public function isPurchaseDateLessThanTwoDays(): bool
	{
		return
			$this->getPurchasedAt()->format('dmY') === (new \DateTime())->format('dmY')
			|| $this->getPurchasedAt()->format('dmY') === (new \DateTime())->modify('-1 day')->format('dmY')
		;
	}

	public function hasFreshdeskTicket(): bool
	{
		return $this->freshdeskTicket !== null;
	}

	public function getProductUrls(): ?string
	{
		return $this->productUrls;
	}

	public function setProductUrls(?string $productUrls)
	{
		$this->productUrls = $productUrls;
	}

	public function getProductUrlsAsArray()
	{
		return array_filter(explode("\n", $this->getProductUrls()));
	}

	/**
	 * @return RefundSolution|null
	 */
	public function getRefundSolution(): ?RefundSolution
	{
		return $this->refundSolution->first() ?: null;
	}

	public static function slovakResponders(): array
	{
		return [
			602842, 621425, // Tančáková
			757085, // Dudášová
			990964, // Pepa
			4361423, 4343639, // Lichonova
		];
	}

	public static function czechResponders(): array
	{
		return [
			4178717,
			4175772,
			4798033,
			4818962,
			5224947,
			5225943,
		];
	}

	public function getDeclineReasonType(): ?string
	{
		return $this->declineReasonType;
	}

	public function setDeclineReasonType(?string $declineReasonType): void
	{
		$this->declineReasonType = $declineReasonType;
	}

	public function setDeclineReasonMessage(?string $declineReasonMessage): void
	{
		$this->declineReasonMessage = $declineReasonMessage;
	}

	public function getSecondInvoice(): ?File
	{
		return $this->secondInvoice;
	}

	public function setSecondInvoice(?File $secondInvoice): void
	{
		$this->secondInvoice = $secondInvoice;
	}

	public function isTypeMissingCommission(): bool
	{
		return $this->type === $this::TYPE_MISSING_COMMISSION;
	}

	public function isTypeMissingBonus(): bool
	{
		return $this->type === $this::TYPE_MISSING_BONUS;
	}

	public function isTypeUnconfirmedTransaction(): bool
	{
		return $this->type === $this::TYPE_UNCONFIRMED_TRANSACTION;
	}

	public function isAddonUsed(): bool
	{
		return $this->addonUsed === true;
	}

	public function isMobileAppUsed(): bool
	{
		return $this->mobileAppUsed === true;
	}

	public function setProcessErrorMessage(?string $processErrorMessage): void
	{
		$this->processErrorMessage = $processErrorMessage ? Strings::truncate($processErrorMessage, 255) : null;
	}

	public function reopen(): void
	{
		$this->state = self::STATE_WAITING_FOR_OPERATOR;
		$this->resolvedByUser = null;
	}

	public function renewResolveCheck(): void
	{
		$this->resolveCheckState = null;
		$this->resolveCheckReasonType = null;
		$this->resolveCheckMessage = null;
		$this->resolveCheckedAt = null;
		$this->resolvedByUser = null;
	}

	public function getResolveCheckReasonType(): ?string
	{
		return $this->resolveCheckReasonType;
	}

	public function setResolveCheckReasonType(?string $resolveCheckReasonType): void
	{
		$this->resolveCheckReasonType = $resolveCheckReasonType;
	}

	public function getResolveCheckMessage(): ?string
	{
		return $this->resolveCheckMessage;
	}

	public function setResolveCheckMessage(?string $resolveCheckMessage): void
	{
		$this->resolveCheckMessage = $resolveCheckMessage;
	}

	public function isOrderOlderThan100days(): bool
	{
		return $this->getPurchasedAt() !== null && $this->getPurchasedAt() < (new \DateTime())->modify('-100 days');
	}
}
