<?php

namespace tipli\Model\Transactions\Subscribers;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Security\IIdentity;
use Nette\Security\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Entities\FavoriteShop;
use tipli\Model\Account\Producers\FavoriteShopsProducer;
use tipli\Model\Account\UserManager;
use tipli\Model\Configuration;
use tipli\Model\GoogleTagManager\GoogleTagManagerClient;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Redis\RedisStorageManager;
use tipli\Model\Shops\Producers\RedirectionsProducer;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\Events\ITransactionEvent;
use tipli\Model\Transactions\Events\TransactionAssignedToUserEvent;
use tipli\Model\Transactions\Events\TransactionConfirmedEvent;
use tipli\Model\Transactions\Events\TransactionPairedWithRedirectionEvent;
use tipli\Model\Transactions\Events\TransactionRegisteredEvent;
use tipli\Model\Transactions\Events\TransactionUpdatedEvent;
use tipli\Model\Transactions\Producers\TransactionNotificationsProducer;
use tipli\Model\Transactions\Producers\TransactionTriggersProducer;
use tipli\Model\Transactions\TransactionManager;
use tipli\Model\Transactions\TransactionProcessFacade;
use tipli\Model\Transactions\TransactionTriggerProcessor;
use tipli\Model\Triggers\Entities\Trigger;
use tipli\Model\Triggers\Producers\TriggersProducer;
use Tracy\Debugger;

final class TransactionSubscriber implements EventSubscriberInterface
{
	/** @var TransactionManager */
	private $transactionManager;

	/** @var \tipli\Model\Account\Entities\User|null|IIdentity */
	private $user;

	/** @var FavoriteShopsProducer */
	private $favoriteShopsProducer;

	/** @var Cache  */
	private $cache;

	/** @var TransactionNotificationsProducer */
	private $transactionNotificationsProducer;

	/** @var TriggersProducer */
	private $triggersProducer;

	/** @var TransactionTriggersProducer */
	private $transactionTriggersProducer;

	/** @var TransactionTriggerProcessor */
	private $transactionTriggerProcessor;

	/** @var TransactionProcessFacade */
	private $transactionProcessFacade;

	/** @var RedisStorageManager */
	private $redisStorageManager;

	/** @var GoogleTagManagerClient */
	private $googleTagManagerClient;

	/** @var Configuration */
	private $configuration;

	/** @var UserManager */
	private $userManager;

	/** @var RedirectionsProducer  */
	private $redirectionsProducer;

	public function __construct(
		TransactionManager $transactionManager,
		User $user,
		FavoriteShopsProducer $favoriteShopsProducer,
		Storage $storage,
		TransactionNotificationsProducer $transactionNotificationsProducer,
		TriggersProducer $triggersProducer,
		TransactionTriggersProducer $transactionTriggersProducer,
		TransactionTriggerProcessor $transactionTriggerProcessor,
		TransactionProcessFacade $transactionProcessFacade,
		RedisStorageManager $redisStorageManager,
		GoogleTagManagerClient $googleTagManagerClient,
		Configuration $configuration,
		UserManager $userManager,
		RedirectionsProducer $redirectionsProducer,
		private SqlQueryScheduler $sqlQueryScheduler
	) {
		$this->transactionManager = $transactionManager;
		$this->user = $user->getIdentity();
		$this->favoriteShopsProducer = $favoriteShopsProducer;
		$this->cache = new Cache($storage, self::class);
		$this->transactionNotificationsProducer = $transactionNotificationsProducer;
		$this->triggersProducer = $triggersProducer;
		$this->transactionTriggersProducer = $transactionTriggersProducer;
		$this->transactionTriggerProcessor = $transactionTriggerProcessor;
		$this->transactionProcessFacade = $transactionProcessFacade;
		$this->redisStorageManager = $redisStorageManager;
		$this->googleTagManagerClient = $googleTagManagerClient;
		$this->configuration = $configuration;
		$this->userManager = $userManager;
		$this->redirectionsProducer = $redirectionsProducer;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			TransactionRegisteredEvent::class => [
				['scheduleCheckBonuses'],
				['scheduleBonusRegistrationEmail'],
				['addRegisteredBy'],
				['refreshAccounting'],
				['refreshUser'],
				['scheduleSuspectedTransactionCheck'],
				['scheduleFavoriteShop'],
				['scheduleUpdateUserStatsOnRegisterTransaction'],
				['scheduleTransactionRegistrationNotification'],
				['scheduleUserAnalyze'],
				['rememberState'],
				['createTransactionProcess'],
				['sendGtmEvent'],
				['updateUserBalance'],
			],
			TransactionConfirmedEvent::class => [
				['scheduleCheckBonuses'],
				['addConfirmedBy'],
				['refreshAccounting'],
				['refreshUser'],
				['scheduleSuspectedTransactionCheck'],
				['scheduleUpdateUserStatsOnConfirmTransaction'],
				['scheduleTransactionConfirmationNotification'],
				['scheduleUserAnalyze'],
				['rememberState'],
				['updateUserBalance'],
			],
			TransactionPairedWithRedirectionEvent::class => [
				['schedulePairRedirection'],
			],
			TransactionUpdatedEvent::class => [
				['refreshAccounting'],
				['updateUserBalance'],
			],
			TransactionAssignedToUserEvent::class => [
				['scheduleTransactionRegistrationNotification'],
			],
		];
	}

	public function addRegisteredBy(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$transaction->setRegisteredByUser($this->user);
		$this->transactionManager->saveTransaction($transaction);
	}

	public function addConfirmedBy(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$transaction->setConfirmedByUser($this->user);
		$this->transactionManager->saveTransaction($transaction);
	}

	public function scheduleCheckBonuses(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$this->transactionTriggersProducer->scheduleTransactionTrigger(
			TransactionTriggerProcessor::TRIGGER_CHECK_BONUSES,
			$transaction
		);
	}

	public function scheduleBonusRegistrationEmail(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if (!$transaction->getUser() || $transaction->getAmount() == 0 || !$transaction->isBillable() || $transaction->isPayout() || !$transaction->isBonus()) {
			return;
		}

		if ($transaction->isBonusLuckyShop()) {
			return;
		}

		if ($transaction->getType() === Transaction::TYPE_BONUS_CAMPAIGN) {
			return;
		}

		$this->transactionTriggersProducer->scheduleTransactionTrigger(
			TransactionTriggerProcessor::TRIGGER_BONUS_REGISTRATION_EMAIL,
			$transaction
		);
	}

	public function refreshAccounting(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$this->transactionTriggerProcessor->computeAccounting($transaction);
	}

	public function refreshUser(ITransactionEvent $transactionEvent)
	{
//        if ($user = $transaction->getUser()) {
//            $this->userManager->saveUser($user);
//        }
	}

	public function scheduleSuspectedTransactionCheck(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$this->transactionTriggersProducer->scheduleTransactionTrigger(
			TransactionTriggerProcessor::TRIGGER_CHECK_SUSPECTED_TRANSACTION,
			$transaction
		);
	}

	public function scheduleFavoriteShop(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if ($transaction->hasUser() && $transaction->hasShop() && ($transaction->isCommission() || $transaction->isBonusRefund())) {
			$this->favoriteShopsProducer->scheduleFavoriteShop($transaction->getUser(), $transaction->getShop(), FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_TRANSACTION, new \DateTime());
		}
	}

	public function scheduleUpdateUserStatsOnRegisterTransaction(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if (!$transaction->getUser()) {
			return;
		}

		$userId = $transaction->getUser()->getId();

		$this->sqlQueryScheduler->updateUserDataOnRegisterTransaction($userId);

		if ($transaction->isCommissionOrRefund() && !$transaction->getUser()->isActiveUser()) {
			$this->sqlQueryScheduler->setUserActiveSegmet($userId);
		}
	}

	public function scheduleUpdateUserStatsOnConfirmTransaction(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if (!$transaction->getUser()) {
			return;
		}

		$this->sqlQueryScheduler->updateUserAccountBalance($transaction->getUser()->getId());
	}

	public function schedulePairRedirection(TransactionPairedWithRedirectionEvent $transactionPairedWithRedirectionEvent)
	{
		// @todo jirka: tady by to podle me slo do budoucna predelat do rabbita, neni treba pouzivat trigger
		$this->transactionTriggersProducer->scheduleTransactionTrigger(
			TransactionTriggerProcessor::TRIGGER_PAIR_REDIRECTION,
			$transactionPairedWithRedirectionEvent->getTransaction(),
			$transactionPairedWithRedirectionEvent->getRedirection()
		);

		$redirection = $transactionPairedWithRedirectionEvent->getRedirection();

		Debugger::log($redirection->getSyncId() !== null ? $redirection->getSyncId() : 0, 'transaction-paired-with-redirection');
		if ($redirection->getSyncId() === null) {
			return;
		}

		$this->redirectionsProducer->scheduleUpdateRedirection(
			$redirection->getSyncId(),
			null,
			true
		);
	}

	public function scheduleTransactionRegistrationNotification(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$user = $transaction->getUser();

		if (
			!$transaction->hasUser()
			|| !$transaction->getShop()
			|| $user->isWhiteLabelUser()
			|| $transaction->isConfirmed()
			|| !$transaction->isCommission()
		) {
			bdump("a1");
			return;
		}

		if ($transaction->getUserCommissionAmount() == 0) {
			bdump("a2");
			return;
		}

		if ($transaction->isAliexpressTransaction()) {
			$fromDate = (clone $transaction->getCreatedAt());
			$toDate = (clone $transaction->getCreatedAt())->modify('+ 5 minutes');
			$cacheKey = $user->getId() . '-' . $fromDate->format('dmYHi');

			if ($this->cache->load($cacheKey)) {
				return;
			}

			$this->triggersProducer->scheduleCreateTrigger($user, Trigger::TYPE_TRANSACTIONS_NOTIFICATION, [
				'type' => 'registration',
				'shopId' => $transaction->getShop()->getId(),
				'fromDate' => $fromDate->format('d.m.Y H:i:s'),
				'toDate' => $toDate->format('d.m.Y H:i:s'),
			], $toDate);

			$this->cache->save($cacheKey, true);
		} else {
			bdump("a3");
			$this->transactionNotificationsProducer->scheduleTransactionRegistrationNotification($transaction);
		}
	}

	public function scheduleTransactionConfirmationNotification(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$user = $transaction->getUser();

		if (
			!$transaction->hasUser()
			|| !$transaction->getShop()
			|| $user->isWhiteLabelUser()
			|| !$transaction->isConfirmed()
			|| !$transaction->isCommission()
		) {
			return;
		}

		if ($transaction->getUserCommissionAmount() == 0) {
			return;
		}

		if ($transaction->isAliexpressTransaction()) {
			$fromDate = (clone $transaction->getConfirmedAt());
			$toDate = (clone $transaction->getConfirmedAt())->modify('+ 5 minutes');
			$cacheKey = $user->getId() . '-' . $fromDate->format('dmYHi');

			if ($this->cache->load($cacheKey)) {
				return;
			}

			$this->triggersProducer->scheduleCreateTrigger($user, Trigger::TYPE_TRANSACTIONS_NOTIFICATION, [
				'type' => 'confirmation',
				'shopId' => $transaction->getShop()->getId(),
				'fromDate' => $fromDate->format('d.m.Y H:i:s'),
				'toDate' => $toDate->format('d.m.Y H:i:s'),
			], $toDate);

			$this->cache->save($cacheKey, true);
		} else {
			$this->transactionNotificationsProducer->scheduleTransactionConfirmationNotification($transaction);
		}
	}

	public function scheduleUserAnalyze(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if (!$transaction->hasUser() || !$transaction->isCommission()) {
			return;
		}

		$this->sqlQueryScheduler->updateUserAnalyze($transaction->getUser()->getId());
	}

	public function rememberState(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if (!$transaction->isCommission()) {
			return;
		}

		if (in_array($this->configuration->getMode(), ['test', 'dev'])) {
			return;
		}

		$key = !$transaction->isConfirmed() ? 'registered_' : 'confirmed_';
		$key .= ($transaction->getPartnerSystem() ? $transaction->getPartnerSystem()->getId() : 0) . '__' . $transaction->getTransactionId();

		if ($transaction->isConfirmed() && $transaction->getOriginalConfirmedCommissionAmount() !== null) {
			if (!$transaction->isCancelled()) {
				$key .= '__' . round($transaction->getOriginalConfirmedCommissionAmount(), 5);
			} else {
				$key .= '__0';
			}
		}

		if ($transaction->getPartnerSystem() !== null && $transaction->getPartnerSystem()->getId() === 7) {
			$key .= 'ehub-fix';
		}

		if ($this->redisStorageManager->read($key)) {
			return;
		}

		$this->redisStorageManager->write($key, true);
	}

	public function createTransactionProcess(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		$this->transactionProcessFacade->createTransactionProcess($transaction);
	}

	public function sendGtmEvent(ITransactionEvent $transactionEvent): void
	{
		$transaction = $transactionEvent->getTransaction();

		if ($transaction->getType() !== Transaction::TYPE_COMMISSION) {
			return;
		}

		if ($transaction->hasUser() === false) {
			return;
		}

		$this->googleTagManagerClient->sendEvent('transactionRegistered', [
			'userId' => $transaction->getUser()->getId(),
			'commissionAmount' => $transaction->getCommissionAmount(),
		]);
	}

	public function updateUserBalance(ITransactionEvent $transactionEvent)
	{
		$transaction = $transactionEvent->getTransaction();

		if ($transaction->hasUser() === false) {
			return;
		}

		$user = $transaction->getUser();
		$balane = $this->transactionManager->getBalance($user);

		$this->userManager->updateUserBalance(
			$user,
			$balane === null ? 0 : $balane
		);
	}
}
