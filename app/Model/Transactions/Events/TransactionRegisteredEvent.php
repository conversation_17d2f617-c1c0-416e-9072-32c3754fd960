<?php

namespace tipli\Model\Transactions\Events;

use tipli\Model\Transactions\Entities\Transaction;

final class TransactionRegisteredEvent implements ITransactionEvent
{
	/** @var Transaction */
	private $transaction;

	public function __construct(Transaction $transaction)
	{
		$this->transaction = $transaction;
	}

	/**
	 * @return Transaction
	 */
	public function getTransaction(): Transaction
	{
		return $this->transaction;
	}
}
