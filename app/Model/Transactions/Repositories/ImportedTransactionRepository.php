<?php

namespace tipli\Model\Transactions\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Transactions\Entities\ImportedTransaction;

class ImportedTransactionRepository extends BaseRepository
{
	public function getImportedTransactions()
	{
		return $this->createQueryBuilder('it');
	}

	public function findByUniqueIdentifier(string $uniqueIdentifier): ?ImportedTransaction
	{
		return $this->getImportedTransactions()
			->andWhere('it.uniqueIdentifier = :uniqueIdentifier')
			->setParameter('uniqueIdentifier', $uniqueIdentifier)
			->getQuery()
			->setMaxResults(1)
			->getOneOrNullResult();
	}

	public function findDuplicityByUniqueIdentifier(string $uniqueIdentifier, int $withoutId): ?ImportedTransaction
	{
		return $this->getImportedTransactions()
			->andWhere('it.uniqueIdentifier = :uniqueIdentifier')
			->setParameter('uniqueIdentifier', $uniqueIdentifier)
			->andWhere('it.id != :id')
			->setParameter('id', $withoutId)
			->getQuery()
			->setMaxResults(1)
			->getOneOrNullResult();
	}

	public function findImportedTransactions(int $limit, int $offset)
	{
		return $this->getImportedTransactions()
			->setMaxResults($limit)
			->setFirstResult($offset)
			->getQuery()
			->getResult();
	}
}
