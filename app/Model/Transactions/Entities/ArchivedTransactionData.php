<?php

namespace tipli\Model\Transactions\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="tipli_transactions_archived_transaction_data")
 */
class ArchivedTransactionData
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\OneToOne(targetEntity="tipli\Model\Transactions\Entities\ArchivedTransaction", inversedBy="transactionData")
	 * @ORM\JoinColumn(name="archived_transaction_id", referencedColumnName="id")
	 * @var \tipli\Model\Transactions\Entities\Transaction
	 */
	private $transaction;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="related_transaction_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Transactions\Entities\Transaction|null
	 */
	private $relatedTransaction;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="related_recommended_user_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Account\Entities\User|null
	 */
	private $relatedRecommendedUser;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="registered_by_user_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Account\Entities\User|null
	 */
	private $registeredByUser;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="confirmed_by_user_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Account\Entities\User|null
	 */
	private $confirmedByUser;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string
	 */
	private $name;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float
	 */
	private $shareCoefficient;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $orderAmount = 0;

	/**
	 * @ORM\Column(type="string", length=3, nullable=true)
	 * @var string
	 */
	private $originalCurrency;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $originalCommissionAmount = 0;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 * @var float
	 */
	private $originalConfirmedCommissionAmount = 0;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $confirmationTreshold = 0;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $recommendationBonusTreshold = 0;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $preparedForConfirm = true;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $originalTurnover = 0;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $originalIncome = 0;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $turnover = 0;

	/**
	 * @ORM\Column(type="float")
	 * @var float
	 */
	private $income = 0;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $confirmedByScoring = false;

	public function __construct()
	{
		// @todo: zde se nakopiruji data z transakce a vytvori se tak kopie
	}
}
