<?php

namespace tipli\Model\Rewards\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use tipli\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Rewards\Repositories\MoneyRewardRepository")
 * @ORM\Table(name="tipli_rewards_money_reward", indexes={
 *    @ORM\Index(name="valid_till_idx", columns={"valid_till"})
 * })
 */
class MoneyReward
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Rewards\Entities\MoneyRewardCampaign")
	 * @ORM\JoinColumn(name="money_reward_campaign_id", referencedColumnName="id")
	 */
	private $moneyRewardCampaign;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User", inversedBy="moneyRewards")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	private $user;

	/**
	 * @ORM\Column(type="float")
	 */
	private $amount = 0;

	/**
	 * @ORM\Column(type="float")
	 */
	private $minimumCommissionBalance = 0;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $usedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(MoneyRewardCampaign $moneyRewardCampaign, User $user, $amount, $minimumCommissionBalance, $validTill)
	{
		$this->moneyRewardCampaign = $moneyRewardCampaign;
		$this->user = $user;
		$this->amount = $amount;
		$this->minimumCommissionBalance = $minimumCommissionBalance;
		$this->validTill = $validTill;
		$this->createdAt = new DateTime();
	}

  /**
   * @return int
   */
	public function getId(): int
	{
		return $this->id;
	}

  /**
   * @return User
   */
	public function getUser(): User
	{
		return $this->user;
	}

	public function getMoneyRewardCampaign(): ?MoneyRewardCampaign
	{
		return $this->moneyRewardCampaign;
	}

	/**
	 * @return bool
	 */
	public function isValid()
	{
		return $this->validTill >= (new DateTime());
	}

	/**
	 * @return bool
	 */
	public function isShopAllowed(Shop $shop)
	{
		if ($this->moneyRewardCampaign->getShops()->isEmpty()) {
			return true;
		}

		$allowed = false;
		foreach ($this->moneyRewardCampaign->getShops() as $allowedShop) {
			if ($shop == $allowedShop) {
				return true;
			}
		}

		return $allowed;
	}

	public function useReward()
	{
		$this->usedAt = new DateTime();
	}

	/**
	 * @return float
	 */
	public function getAmount()
	{
		return $this->amount;
	}

	/**
	 * @return string
	 */
	public function getName()
	{
		return $this->moneyRewardCampaign->getName();
	}

	/**
	 * @return Shop[]
	 */
	public function getShops()
	{
		return $this->moneyRewardCampaign->getShops();
	}

  /**
   * @return DateTime
   */
	public function getValidTill(): \DateTime
	{
		return $this->validTill;
	}

	/**
	 * @return mixed
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getUsedAt()
	{
		return $this->usedAt;
	}

	public function isUsed()
	{
		return $this->getUsedAt() ? true : false;
	}

	/**
	 * @return mixed
	 */
	public function getMinimumCommissionBalance()
	{
		return $this->minimumCommissionBalance;
	}

	/**
	 * @param mixed $minimumCommissionBalance
	 */
	public function setMinimumCommissionBalance($minimumCommissionBalance)
	{
		$this->minimumCommissionBalance = $minimumCommissionBalance;
	}

	public function isInTimePressure()
	{
		return $this->isValid()
			&& !$this->isUsed()
			&& $this->getMinimumCommissionBalance() > 0;
	}

	public function getDaysLeftToUse()
	{
		if (!$this->isValid()) {
			return 0;
		}

		return (new \DateTime())->diff(
			$this->getValidTill()
		)->days;
	}

	public function getConfirmationTreshold(): ?float
	{
		return $this->moneyRewardCampaign?->getConfirmationTreshold();
	}

	public function getGoal(): string
	{
		return $this->moneyRewardCampaign !== null ? $this->moneyRewardCampaign->getGoal() : MoneyRewardCampaign::GOAL_TRANSACTION;
	}

	public function isGoalTransaction(): bool
	{
		return $this->getGoal() === MoneyRewardCampaign::GOAL_TRANSACTION;
	}

	public function isGoalAddon(): bool
	{
		return $this->getGoal() === MoneyRewardCampaign::GOAL_ADDON;
	}

	public function isGoalRecommendation(): bool
	{
		return $this->getGoal() === MoneyRewardCampaign::GOAL_RECOMMENDATION;
	}
}
