<?php

namespace tipli\Model\Mailkit\Consumers;

use Contributte\RabbitMQ\Consumer\IConsumer;
use Bunny\Message;
use tipli\Model\Mailkit\MailkitClient;
use tipli\Model\Mailkit\MailkitMember;
use tipli\Model\Mailkit\MailkitUserProcessor;
use tipli\Model\Mailkit\Messages\MailkitEventMessage;
use tipli\Model\Mailkit\Messages\MailkitPrepareUserForSynchronizationMessage;
use tipli\Model\Mailkit\Messages\MailkitSynchronizeMemberMessage;
use tipli\Model\Mailkit\Producers\MailkitProducer;
use tipli\Model\Messages\MailkitEventManager;
use tipli\Model\RabbitMq\BaseConsumer;

class MailkitConsumer extends BaseConsumer
{
	public function __construct(
		private MailkitClient $mailkitClient,
		private MailkitUserProcessor $mailkitUserProcessor,
		private MailkitEventManager $mailkitEventManager
	) {
	}

	// v $data muze chodit bud array $messages pokud jde o bulk zpracovani nebo jednotlive messages - pokud jde o delete nebo trackovani eventu atd.
	public function consume($data)
	{
		if ($data instanceof Message && $data->routingKey === MailkitProducer::ROUTING_KEY_PREPARE_FOR_SYNCHRONIZATION) {
			$mailkitPrepareUserForSynchronizationMessage = MailkitPrepareUserForSynchronizationMessage::fromJson($data->content);
			$this->mailkitUserProcessor->prepareUserForSynchronization($mailkitPrepareUserForSynchronizationMessage->getUserId());
			return IConsumer::MESSAGE_ACK;
		}

		if ($data instanceof Message && $data->routingKey === MailkitProducer::ROUTING_KEY_PROCESS_EMAIL_EVENTS) {
			$mailkitEventMessage = MailkitEventMessage::fromJson($data->content);
			$this->mailkitEventManager->processEvent($mailkitEventMessage->getMailkitEvent());
			return IConsumer::MESSAGE_ACK;
		}

		if (is_array($data) === true) {
			$result = [];
			$membersToSynchronize = [];

			foreach ($data as $message) {
				$result[$message->deliveryTag] = IConsumer::MESSAGE_ACK;

				if ($message->routingKey === MailkitProducer::ROUTING_KEY_SYNCHRONIZE_MEMBER) {
					$mailkitSynchronizeMemberMessage = MailkitSynchronizeMemberMessage::fromJson($message->content);
					$membersToSynchronize[] = $mailkitSynchronizeMemberMessage->getMailkitMember();
				}
			}

			if (count($membersToSynchronize) > 0) {
				$this->mailkitClient->synchronizeMembers($membersToSynchronize);
				$this->mailkitUserProcessor->markUsersAsSynchronized($membersToSynchronize);
			}

			return $result;
		} else {
			if ($data->routingKey === MailkitProducer::ROUTING_KEY_SYNCHRONIZE_MEMBER) {
				$mailkitSynchronizeMemberMessage = MailkitSynchronizeMemberMessage::fromJson($data->content);

				$member = $mailkitSynchronizeMemberMessage->getMailkitMember();
				if ($member->getStatus() === MailkitMember::STATUS_DELETED) {
					$this->mailkitClient->deleteMember($member);
				} else {
					$this->mailkitClient->synchronizeMembers([$member]);
				}

				$this->mailkitUserProcessor->markUserAsSynchronized($member);

				return IConsumer::MESSAGE_ACK;
			}
		}
	}
}
