<?php

namespace tipli\Model\Groups\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;

class GroupUsersMessage implements IMessage
{
	public const ACTION_ADD_USERS = 'addUsers';
	public const ACTION_COMPLETE_GROUP = 'completeGroup';

	/** @var string */
	private $action;

	/** @var int */
	private $groupId;

	/** @var array|null */
	private $userIds;

	public function __construct(string $action, int $groupId, ?array $userIds = null)
	{
		$this->action = $action;
		$this->groupId = $groupId;
		$this->userIds = $userIds;
	}

	public function getAction(): string
	{
		return $this->action;
	}

	public function getGroupId(): int
	{
		return $this->groupId;
	}

	public function getUserIds(): ?array
	{
		return $this->userIds;
	}

	public function __toString(): string
	{
		return Json::encode([
			'action' => $this->getAction(),
			'groupId' => $this->getGroupId(),
			'userIds' => $this->getUserIds(),
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);

		return new self(
			$data->action,
			$data->groupId,
			$data->userIds ? (array) $data->userIds : null
		);
	}
}
