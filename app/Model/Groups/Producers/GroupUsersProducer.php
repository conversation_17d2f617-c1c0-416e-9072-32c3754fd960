<?php

namespace tipli\Model\Groups\Producers;

use tipli\Model\Groups\Entities\Group;
use tipli\Model\Groups\Messages\GroupUsersMessage;
use tipli\Model\RabbitMq\BaseProducer;

class GroupUsersProducer extends BaseProducer
{
	public function scheduleGroupAddUsers(Group $group, array $usersIds)
	{
		$this->producer->publish(
			new GroupUsersMessage(
				GroupUsersMessage::ACTION_ADD_USERS,
				$group->getId(),
				$usersIds
			)
		);
	}

	public function scheduleCompleteGroup(Group $group)
	{
		$this->producer->publish(
			new GroupUsersMessage(
				GroupUsersMessage::ACTION_COMPLETE_GROUP,
				$group->getId()
			)
		);
	}
}
