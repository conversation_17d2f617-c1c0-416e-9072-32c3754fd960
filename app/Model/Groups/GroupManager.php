<?php

namespace tipli\Model\Groups;

use tipli\Model\Doctrine\EntityManager;
use Nette\Database\Context;
use Nette\SmartObject;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Repositories\UserRepository;
use tipli\Model\Groups\Entities\Group;
use tipli\Model\Groups\Producers\GroupUsersProducer;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Mailkit\MailkitMember;

class GroupManager
{
	use SmartObject;

	private const USERS_IN_BATCH = 5 * 1000;

	/** @var EntityManager */
	private $em;

	/** @var GroupAudienceProvider */
	private $groupAudienceProvider;

	/** @var UserRepository */
	private $userRepository;

	/** @var GroupUsersProducer */
	private $groupUsersProducer;

	/** @var Context */
	private $context;

	public function __construct(EntityManager $em, GroupAudienceProvider $groupAudienceProvider, UserRepository $userRepository, GroupUsersProducer $groupUsersProducer, Context $context)
	{
		$this->em = $em;
		$this->groupAudienceProvider = $groupAudienceProvider;
		$this->userRepository = $userRepository;
		$this->groupUsersProducer = $groupUsersProducer;
		$this->context = $context;
	}

	public function createGroup(Localization $localization, string $name, string $slug, User $author)
	{
		return $this->saveGroup(
			new Group($localization, $name, $slug, $author)
		);
	}

	public function saveGroup(Group $group)
	{
		$this->em->persist($group);
		$this->em->flush($group);

		return $group;
	}

	public function processGroup(Group $group)
	{
		if ($group->isRemoved()) {
			throw new InvalidArgumentException('Trying to process removed group!');
		}

		$group->setProcessAt($group->getProcessTimeShift() ? (new \DateTime())->modify($group->getProcessTimeShift()) : null);
		$group->unComplete();
		$group->clearUsers();

		$this->saveGroup($group);

		$usersIds = $this->groupAudienceProvider->getUsersIdsByGroup($group);

		foreach (array_chunk($usersIds, self::USERS_IN_BATCH) as $usersIdsBatch) {
			$this->groupUsersProducer->scheduleGroupAddUsers($group, $usersIdsBatch);
		}

		$this->groupUsersProducer->scheduleCompleteGroup($group);
	}

	public function addUsersBatchToGroup(Group $group, array $usersIds)
	{
		$groupId = $group->getId();
		$rows = array_map(
			static function ($userId) use ($groupId) {
				return ['group_id' => (int) $groupId, 'user_id' => (int) $userId];
			},
			$usersIds
		);

		// @todo doctrine multi insert
		foreach ($rows as $row) {
			$this->context->query('DELETE FROM tipli_groups_group_user WHERE group_id = ? AND user_id = ?', $row['group_id'], $row['user_id']);
		}

		$this->context->query('INSERT INTO tipli_groups_group_user', $rows);
		$countOfUsers = $this->context->query('SELECT count(gu.user_id) AS c FROM tipli_groups_group_user gu WHERE gu.group_id=?', $group->getId())->fetch()->c;
		$this->context->query('UPDATE tipli_groups_group g SET g.count_of_users = ? WHERE g.id=?', $countOfUsers, $group->getId());
		$this->context->query('UPDATE tipli_account_user_mailkit SET synchronize_at = NOW() WHERE user_id IN (?) AND status = ?', $usersIds, [MailkitMember::STATUS_SUBSCRIBED]);
	}

	public function completeGroup(Group $group)
	{
		$group->complete();

		$this->saveGroup($group);
	}

	public function removeGroup(Group $group)
	{
		$group->remove();
		$group->unComplete();
		$group->setProcessAt(null);

		$this->context->query('DELETE FROM tipli_groups_group_user WHERE group_id=?', $group->getId());

		$this->saveGroup($group);
	}
}
