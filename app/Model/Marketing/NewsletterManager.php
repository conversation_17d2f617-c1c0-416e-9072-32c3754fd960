<?php

namespace tipli\Model\Marketing;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Marketing\Entities\Newsletter;
use tipli\Model\Marketing\Entities\NewsletterBlock;

class NewsletterManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function publish(Newsletter $newsletter): void
	{
		$newsletter->publish();
		$this->em->persist($newsletter);
		$this->em->flush();
	}

	public function unPublish(Newsletter $newsletter): void
	{
		$newsletter->unPublish();
		$this->em->persist($newsletter);
		$this->em->flush();
	}

	public function archive(Newsletter $newsletter): void
	{
		$newsletter->archive();
		$this->em->persist($newsletter);
		$this->em->flush();
	}

	public function createNewsletter(Localization $localization, User $author, string $name, string $subject, string $preHeader, string $type, ?string $scenario, string $colorTheme): Newsletter
	{
		$newsletter = new Newsletter($localization, $author, $name, $subject, $preHeader, $type, $scenario, $colorTheme);

		return $this->saveNewsletter($newsletter);
	}

	public function saveNewsletter(Newsletter $newsletter): Newsletter
	{
		$this->em->persist($newsletter);
		$this->em->flush();

		return $newsletter;
	}

	public function createNewsletterBlock(Newsletter $newsletter, string $type, ?string $title): NewsletterBlock
	{
		return $this->saveNewsletterBlock(new NewsletterBlock($newsletter, $type, $title));
	}

	public function saveNewsletterBlock(NewsletterBlock $block): NewsletterBlock
	{
		$this->em->persist($block);
		$this->em->flush($block);

		return $block;
	}

	public function saveNewsletterBlocksCompactly(array $blocks): void
	{
		foreach ($blocks as $block) {
			$this->em->persist($block);
		}

		$this->em->flush();
	}

	public function removeNewsletterBlock(NewsletterBlock $block)
	{
		$this->em->remove($block);
		$this->em->flush();
	}
}
