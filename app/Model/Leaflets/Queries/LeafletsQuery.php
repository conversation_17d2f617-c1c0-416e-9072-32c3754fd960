<?php

namespace tipli\Leaflets\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\Entities\ShopIndex;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Regions\Entities\Region;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Tags\Entities\Tag;

class LeafletsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	public function withLocalization(Localization $localization)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($localization) {
			$queryBuilder->andWhere('l.localization = :localization')
				->setParameter('localization', $localization);
		};

		return $this;
	}

	public function excludeLeaflet(Leaflet $leaflet)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($leaflet) {
			$queryBuilder->andWhere('l.id != :leaflet')
				->setParameter('leaflet', $leaflet);
		};

		return $this;
	}

	public function onlyValidMoreThanDays($days = 2)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($days) {
			$queryBuilder->andWhere('l.validSince <= :datetime')
				->andWhere('l.validTill >= :datetime')
				->setParameter('datetime', (new \DateTime())->modify('+ ' . $days . ' days'));
		};

		return $this;
	}

	public function onlyValid()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.validSince <= :datetime')
				->andWhere('l.validTill >= :datetime')
				->setParameter('datetime', new \DateTime())
				->andWhere('l.removedAt IS NULL AND l.archivedAt IS NULL');
		};

		return $this;
	}

	public function onlyScheduled()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.validSince >= :datetime')
				->setParameter('datetime', new \DateTime());
		};

		return $this;
	}

	public function onlyValidOrScheduled()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.validTill >= :datetime')
				->setParameter('datetime', new \DateTime());
		};

		return $this;
	}

	public function onlyExpired()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.validTill <= :datetime')
				->setParameter('datetime', new \DateTime());
		};

		return $this;
	}

	public function onlyConfirmed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.confirmedAt IS NOT NULL');
		};

		return $this;
	}

	public function onlyPrimary()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.primary = true');
		};

		return $this;
	}

	public function notOld()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.validTill > :date')
				->setParameter('date', (new \DateTime())->modify('- 9 days'));
		};

		return $this;
	}

	public function newestFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->orderBy('l.id', 'desc');
		};

		return $this;
	}

	public function onlyWithShop()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.shop IS NOT NULL');
		};

		return $this;
	}

	public function onlyWithoutStores()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->leftJoin('l.stores', 'st')
				->andWhere('st.id IS NULL');
		};

		return $this;
	}

	public function readyToRemove()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.removeAt < :now')
				->setParameter('now', new \DateTime());
		};

		return $this;
	}

	public function readyToArchive()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.archiveAt < :now')
				->setParameter('now', new \DateTime())
				->andWhere('l.archivedAt IS NULL')
			;
		};

		return $this;
	}

	public function notRemoved()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.removedAt IS NULL');
		};

		return $this;
	}

	public function removed(\DateTime $afterDate = null)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($afterDate) {
			if ($afterDate) {
				$queryBuilder
					->andWhere('l.removedAt >= :afterDate')
					->setParameter('afterDate', $afterDate)
				;
			} else {
				$queryBuilder->andWhere('l.removedAt IS NOT NULL');
			}
		};

		return $this;
	}

	public function withTags(array $tags)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($tags) {
			$queryBuilder->leftJoin('l.tags', 'lt')
				->andWhere('lt IN (:tags)')
				->setParameter('tags', $tags)
				->groupBy('l.id');
		};

		return $this;
	}

	public function withTag(Tag $tag)
	{
		$this->withTags([$tag]);

		return $this;
	}

	public function withShop(Shop $shop)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shop) {
			$queryBuilder->andWhere('l.shop = :shop')
				->setParameter('shop', $shop);
		};

		return $this;
	}

	public function withShops(array $shops)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($shops) {
			$queryBuilder->andWhere('l.shop IN(:shops)')
				->setParameter('shops', $shops);
		};

		return $this;
	}

	public function withRegion(Region $region)
	{
		$this->withRegions([$region]);

		return $this;
	}

	public function withRegions(array $regions)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($regions) {
			$queryBuilder->innerJoin('l.stores', 'ls')
				->innerJoin('ls.region', 'sr')
				->andWhere('sr.id IN (:regions)')
				->setParameter('regions', $regions)
				->groupBy('l.id');
		};

		return $this;
	}

	public function sortByTopShops()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('IFNULL (s.priorityLeaflets, 0)', 'DESC');
		};

		return $this;
	}

	public function sortByValidTill()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('l.validTill', 'ASC');
		};

		return $this;
	}

	public function expireFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('l.validTill', 'ASC');
		};

		return $this;
	}

	public function lastCreatedFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('l.createdAt', 'ASC');
		};

		return $this;
	}

	public function lastConfirmedFirst()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('l.confirmedAt', 'DESC');
		};

		return $this;
	}

	public function onlyVisible()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.visible = true');
		};

		return $this;
	}

	public function onlyIndexable()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->leftJoin(ShopIndex::class, 'si', 'WITH', 'si.shop = s')
				->andWhere('si.id IS NOT NULL');
		};

		return $this;
	}

	public function withoutNewsletters()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.type IS NULL OR l.type != :newsletter')
				->setParameter('newsletter', Leaflet::TYPE_NEWSLETTER);
		};

		return $this;
	}

	public function withoutScreenshots()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('l.type IS NULL OR l.type != :screenshot')
				->setParameter('screenshot', Leaflet::TYPE_SCREENSHOT);
		};

		return $this;
	}

	public function onlyOnePerShop()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->groupBy('l.shop');
		};

		return $this;
	}

	public function createdBetween(\DateTime $createdFrom, \DateTime $createdTill)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($createdFrom, $createdTill) {
			$queryBuilder
				->andWhere('l.createdAt >= :createdFrom')->setParameter('createdFrom', $createdFrom)
				->andWhere('l.createdAt <= :createdTill')->setParameter('createdTill', $createdTill);
		};

		return $this;
	}

	public function onlyAllowed()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->join('s.shopData', 'sd')
				->andWhere('sd.showLeaflets = :showLeaflets')
				->setParameter('showLeaflets', true);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('l', 'l.id')
			->leftJoin('l.shop', 's')
			->select('l');


		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		$queryBuilder->addOrderBy('l.id');

		return $queryBuilder;
	}

	public function postFetch(Queryable $repository, \Iterator $iterator)
	{
	}
}
