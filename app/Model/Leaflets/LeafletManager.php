<?php

namespace tipli\Model\Leaflets;

use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;
use tipli\Model\Doctrine\EntityManager;
use Nette\Localization\Translator;
use Nette\SmartObject;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use tipli\Model\Deals\Events\LeafletSavedEvent;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Images\ImageStorage;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\Entities\LeafletPage;
use tipli\Model\Leaflets\Repositories\LeafletPageRepository;
use tipli\Model\Leaflets\Repositories\LeafletRepository;
use tipli\Model\Leaflets\Repositories\LeafletShopIndexRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Shops\StoreFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;

class LeafletManager
{
	use SmartObject;

	private const DEFAULT_LEAFLET_TAG_SLUG = [
		'cs' => 'hypermarkety-a-supermarkety',
		'sk' => 'hypermarkety-a-supermarkety',
		'pl' => 'hipermarkety-i-supermarkety',
	];

	/** @var EntityManager */
	private $em;

	/** @var LeafletClient */
	private $leafletClient;

	/** @var LeafletRepository */
	private $leafletRepository;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var StoreFacade */
	private $storeFacade;

	/** @var Translator */
	private $translator;

	/** @var LeafletPageRepository */
	private $leafletPageRepository;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var TagFacade */
	private $tagFacade;

	/** @var EventDispatcher */
	private $eventDispatcher;

	public function __construct(
		EntityManager $em,
		LeafletClient $leafletClient,
		LeafletRepository $leafletRepository,
		ImageStorage $imageStorage,
		ShopFacade $shopFacade,
		StoreFacade $storeFacade,
		Translator $translator,
		LeafletPageRepository $leafletPageRepository,
		private LeafletShopIndexRepository $leafletShopIndexRepository,
		ImageFilter $imageFilter,
		TagFacade $tagFacade,
		EventDispatcher $eventDispatcher
	) {
		$this->em = $em;
		$this->leafletClient = $leafletClient;
		$this->leafletRepository = $leafletRepository;
		$this->imageStorage = $imageStorage;
		$this->shopFacade = $shopFacade;
		$this->storeFacade = $storeFacade;
		$this->translator = $translator;
		$this->leafletPageRepository = $leafletPageRepository;
		$this->imageFilter = $imageFilter;
		$this->tagFacade = $tagFacade;
		$this->eventDispatcher = $eventDispatcher;
	}

	public function processLeaflets(Localization $localization)
	{
		$rawLeaflets = $this->leafletClient->fetchLeaflets($localization);
		$defaultTagBySlug = null;

		if (isset(self::DEFAULT_LEAFLET_TAG_SLUG[$localization->getLocale()])) {
			$defaultTagBySlug = $this->tagFacade->findBySlug(self::DEFAULT_LEAFLET_TAG_SLUG[$localization->getLocale()], $localization, Tag::TYPE_LEAFLET);
		}

		foreach ($rawLeaflets as $rawLeaflet) {
			if (isset($rawLeaflet->checked) && $rawLeaflet->checked === false) {
				continue;
			}

			$shop = null;
			if ($tipliShopId = $rawLeaflet->tipliId) {
				$shop = $this->shopFacade->find($tipliShopId);
			} elseif (isset($rawLeaflet->shopDomain)) {
				$shop = $this->shopFacade->findShopByDomain($localization, $rawLeaflet->shopDomain);

				if ($shop === null) {
					$leafletWithShop = $this->leafletRepository->findLeafletWithShopByShopDomain($localization, $rawLeaflet->shopDomain);

					if ($leafletWithShop !== null) {
						$shop = $leafletWithShop->getShop();
					}
				}
			}

			if ($shop === null) {
				continue;
			}

			if ($this->leafletShopIndexRepository->findByShop($shop) === null) {
				continue;
			}

			$leaflet = $this->leafletRepository->findOneBy(['localization' => $localization, 'leafletId' => $rawLeaflet->leafletId]);

			$validSince = $rawLeaflet->validSince ? new \DateTime($rawLeaflet->validSince) : new \DateTime();
			$validTill = $rawLeaflet->validTill ? new \DateTime($rawLeaflet->validTill) : new \DateTime();

			if ($leaflet === null) {
				if ($validTill < new \DateTime()) { //  do not create expired leaflets
					continue;
				}

				if ($rawLeaflet->cancelledAt !== null) { // do not create cancelled leaflets
					continue;
				}

				$slug = Strings::webalize($rawLeaflet->name);
				$slug .= '-' . Strings::webalize($this->translator->translate('front.leaflets.fromDay.' . $validSince->format('w'), null, [], null, $localization->getLocale()) . ' ' . $validSince->format('d-m-Y'));

				$slugIsUsed = !empty($this->leafletRepository->findBySlug($slug, $localization));

				$leaflet = $this->createLeaflet(
					$rawLeaflet->leafletId,
					$localization,
					$rawLeaflet->name,
					$rawLeaflet->description,
					$rawLeaflet->note,
					$validSince,
					$validTill,
					$slugIsUsed ? ('leaflet-' . Random::generate(10)) : $slug,
					$shop
				);

				if ($slugIsUsed) {
					$leaflet->setSlug($slug . '-' . $leaflet->getId());

					$this->saveLeaflet($leaflet);
				}

				foreach ($rawLeaflet->pages as $rawPage) {
					$this->createLeafletPage($leaflet, null, $rawPage->pageNumber, $rawPage->absoluteFile);
				}
			} else {
				if (!$leaflet->getTitle()) {
					$leaflet->setTitle($rawLeaflet->name);
				}

				if (!$leaflet->getDescription()) {
					$leaflet->setDescription($rawLeaflet->description);
				}

				if (!$leaflet->getNote()) {
					$leaflet->setNote($rawLeaflet->note);
				}

				if (!$leaflet->getValidSince()) {
					$leaflet->setValidSince($validSince);
				}

				if (!$leaflet->getValidTill()) {
					$leaflet->setValidTill($validTill);
				}

				$this->saveLeaflet($leaflet);
			}

			if ($removeAt = $rawLeaflet->removeAt) {
				$leaflet->setRemoveAt(new \DateTime($removeAt));
			}

			if ($archiveAt = $rawLeaflet->archiveAt) {
				$leaflet->setArchiveAt(new \DateTime($archiveAt));
			}

			if ($archivationPages = $rawLeaflet->archivationPages) {
				$leaflet->setArchivationPages($archivationPages);
			}

			if ($partnerLink = $rawLeaflet->partnerLink) {
				$leaflet->setPartnerLink($partnerLink);
			}

			if (isset($rawLeaflet->primary)) {
				$leaflet->setPrimary($rawLeaflet->primary);
			}

			if ($rawLeaflet->removedAt !== null) {
				$leaflet->remove();
			}

			if ($rawLeaflet->cancelledAt !== null) {
				$this->cancelLeaflet($leaflet);
			}

			if ($leaflet->getTags()->isEmpty() && !$shop->getShopLeafletTags()->isEmpty()) {
				$tags = $leaflet->getTags()->toArray();
				foreach ($shop->getShopLeafletTags() as $shopLeafletTag) {
					$tags[] = $shopLeafletTag->getTag();
				}

				$leaflet->setTags($tags);
			}

			if (isset($rawLeaflet->shopDomain)) {
				$leaflet->setShopDomain($rawLeaflet->shopDomain);
			}

			if (isset($rawLeaflet->type)) {
				$leaflet->setType($rawLeaflet->type);
			}

			if ($defaultTagBySlug && !$leaflet->hasTag($defaultTagBySlug)) {
				$leaflet->setTags([$defaultTagBySlug]);
			}

			$this->saveLeaflet($leaflet);

			/** @var LeafletPage $leafletPage */
			foreach ($leaflet->getLeafletPages() as $leafletPage) {
				if (!$leafletPage->getDownloadUrl() && isset($rawLeaflet->pages[$leafletPage->getPageNumber()])) {
					$leafletPage->setDownloadUrl($rawLeaflet->pages[$leafletPage->getPageNumber()]->absoluteFile);

					$this->saveLeafletPage($leafletPage);
				}
			}

			// automaticky schválit leták s obchodem
			if ($leaflet->getShop() && !$leaflet->isConfirmed() && $rawLeaflet->confirmedAt !== null) {
				$this->confirmLeaflet($leaflet);
			}
		}
	}

	public function createLeaflet($leafletId, Localization $localization, $title, $description, $note, \DateTime $validSince, \DateTime $validTill, $slug, Shop $shop)
	{
		$leaflet = new Leaflet($leafletId, $localization, $title, $description, $note, $validSince, $validTill, $slug, $shop);

		return $this->saveLeaflet($leaflet);
	}

	public function createLeafletPage(Leaflet $leaflet, Image $image = null, $pageNumber, $downloadUrl)
	{
		$leafletPage = new LeafletPage($leaflet, $image, $pageNumber, $downloadUrl);

		return $this->saveLeafletPage($leafletPage);
	}

	public function saveLeaflet(Leaflet $leaflet)
	{
		$this->em->persist($leaflet);
		$this->em->flush($leaflet);

		$this->eventDispatcher->dispatch(
			new LeafletSavedEvent($leaflet)
		);

		return $leaflet;
	}

	public function saveLeafletPage(LeafletPage $leafletPage)
	{
		$this->em->persist($leafletPage);
		$this->em->flush($leafletPage);

		return $leafletPage;
	}

	public function confirmLeaflet(Leaflet $leaflet)
	{
		$leaflet->confirm();

		return $this->saveLeaflet($leaflet);
	}

	public function unConfirmLeaflet(Leaflet $leaflet)
	{
		$leaflet->unConfirm();

		return $this->saveLeaflet($leaflet);
	}

	public function cancelLeaflet(Leaflet $leaflet)
	{
		$leaflet->cancel();

		return $this->saveLeaflet($leaflet);
	}

	public function removeLeaflet(Leaflet $leaflet)
	{
		/** @var LeafletPage $leafletPage */
		foreach ($this->leafletPageRepository->findPagesByLeaflet($leaflet) as $leafletPage) {
			if ($leafletPage->getImage()) {
				$this->imageStorage->deleteImageFiles($leafletPage->getImage());
			}

			$leaflet->removeLeafletPage($leafletPage);
			$this->removeLeafletPage($leafletPage);
		}

		$leaflet->remove();

		$this->saveLeaflet($leaflet);

		return $this->saveLeaflet($leaflet);
	}

	public function removeLeafletPage(LeafletPage $leafletPage)
	{
		$this->em->remove($leafletPage);
	}

	public function assignShopToLeafletsByShopDomain(Localization $localization, $shopDomain, Shop $shop)
	{
		foreach ($this->leafletRepository->findLeafletsWithoutShopByShopDomain($localization, $shopDomain) as $leaflet) {
			$leaflet->setShop($shop);

			$this->saveLeaflet($leaflet);
		}
	}
}
