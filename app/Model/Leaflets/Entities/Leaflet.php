<?php

namespace tipli\Model\Leaflets\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\Store;
use tipli\Model\Tags\Entities\Tag;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Leaflets\Repositories\LeafletRepository")
 * @ORM\Table(name="tipli_leaflets_leaflet", indexes={
 *    @ORM\Index(name="slug_idx", columns={"slug"})
 * })
 */
class Leaflet
{
	public const TYPE_NEWSLETTER = 'newsletter';
	public const TYPE_LEAFLET = 'leaflet';
	public const TYPE_SCREENSHOT = 'screenshot';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string")
	 */
	private $leafletId;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private $localization;

	/**
	 * @ORM\OneToOne(targetEntity="tipli\Model\Seo\Entities\PageExtension", inversedBy="leaflet")
	 * @ORM\JoinColumn(name="page_extension_id", referencedColumnName="id")
	 * @var \tipli\Model\Seo\Entities\PageExtension|null
	 */
	private $pageExtension;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Leaflets\Entities\LeafletPage", mappedBy="leaflet")
	 */
	private $leafletPages;

	/**
	 * @ORM\Column(type="string")
	 */
	private $slug;

	/**
	 * @ORM\Column(type="string")
	 */
	private $title;

	/**
	 * @ORM\Column(type="string", length=500, nullable=true)
	 */
	private $description;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $note;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $type;

	/**
	 * @ORM\Column(type="boolean", name="is_primary")
	 */
	private $primary = true;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop", inversedBy="leaflets")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 */
	private $shop;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $shopDomain;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string|null
	 */
	private $partnerLink;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Shops\Entities\Store", inversedBy="leaflets")
	 * @ORM\JoinTable(name="tipli_leaflets_leaflet_store")
	 */
	private $stores;

	/**
	* @ORM\Column(type="datetime")
	*/
	private $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $confirmedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $cancelledAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	protected $removedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	protected $removeAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	protected $archiveAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	protected $archivedAt;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 * @var int|null
	 */
	protected $archivationPages;

	/**
	 * @ORM\ManyToMany(targetEntity="\tipli\Model\Tags\Entities\Tag")
	 * @ORM\JoinTable(name="tipli_leaflets_leaflet_tag")
	 */
	private $tags;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 * @var int|null
	 */
	protected $countOfPages;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $visible = true;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct($leafletId, Localization $localization, $title, $description, $note, \DateTime $validSince, \DateTime $validTill, $slug, Shop $shop)
	{
		$this->leafletId = $leafletId;
		$this->localization = $localization;
		$this->title = $title;
		$this->description = $description;
		$this->note = $note;
		$this->validSince = $validSince;
		$this->validTill = $validTill;
		$this->slug = $slug;
		$this->shop = $shop;
		$this->stores = new ArrayCollection();
		$this->leafletPages = new ArrayCollection();
		$this->tags = new ArrayCollection();
		$this->createdAt = new \DateTime();
	}

	public function remove()
	{
		$this->removedAt = new \DateTime();
	}

  /**
   * @return mixed
   */
	public function getId()
	{
		return $this->id;
	}

  /**
   * @return mixed
   */
	public function getLeafletId()
	{
		return $this->leafletId;
	}

  /**
   * @return String
   */
	public function getTitle(): string
	{
		return $this->title;
	}

  /**
   * @param String $title
   */
	public function setTitle(string $title)
	{
		$this->title = $title;
	}

  /**
   * @return mixed
   */
	public function getDescription()
	{
		return $this->description;
	}

  /**
   * @param mixed $description
   */
	public function setDescription($description)
	{
		$this->description = $description;
	}

  /**
   * @return mixed
   */
	public function getNote()
	{
		return $this->note;
	}

  /**
   * @param mixed $note
   */
	public function setNote($note)
	{
		$this->note = $note;
	}

  /**
   * @return mixed
   */
	public function getType()
	{
		return $this->type;
	}

  /**
   * @return mixed
   */
	public function getShop()
	{
		return $this->shop;
	}

  /**
   * @param mixed $shop
   */
	public function setShop($shop)
	{
		$this->shop = $shop;
	}

  /**
   * @return \DateTime
   */
	public function getValidSince(): \DateTime
	{
		return $this->validSince;
	}

  /**
   * @param \DateTime $validSince
   */
	public function setValidSince(\DateTime $validSince)
	{
		$this->validSince = $validSince;
	}

  /**
   * @return \DateTime
   */
	public function getValidTill(): \DateTime
	{
		return $this->validTill;
	}

  /**
   * @param \DateTime $validTill
   */
	public function setValidTill(\DateTime $validTill)
	{
		$this->validTill = $validTill;
	}

  /**
   * @return String
   */
	public function getSlug(): string
	{
		return $this->slug;
	}

  /**
   * @param String $slug
   */
	public function setSlug(string $slug)
	{
		$this->slug = $slug;
	}

  /**
   * @return Localization
   */
	public function getLocalization(): Localization
	{
		return $this->localization;
	}

  /**
   * @return mixed
   */
	public function getShopDomain()
	{
		return $this->shopDomain;
	}

  /**
   * @param mixed $shopDomain
   */
	public function setShopDomain($shopDomain)
	{
		$this->shopDomain = $shopDomain;
	}

	public function getIdsOfStores()
	{
		$ids = [];
		foreach ($this->stores as $store) {
			$ids[] = $store->getId();
		}

		return $ids;
	}

	public function setStores(array $stores)
	{
		$currentStores = $this->stores;

		foreach ($this->stores as $store) {
			if (!in_array($store, $stores)) {
				$this->removeStore($store);
			}
		}

		foreach ($stores as $store) {
			if (!in_array($store, $currentStores->toArray())) {
				$this->addStore($store);
			}
		}
	}

  /**
   * @return mixed
   */
	public function getStores()
	{
		return $this->stores;
	}

  /**
   * @param Store $store
   */
	public function addStore(Store $store)
	{
		if (!$this->stores->contains($store)) {
			$this->stores->add($store);
		}
	}

  /**
   * @param Store $store
   */
	public function removeStore(Store $store)
	{
		if ($this->stores->contains($store)) {
			$this->stores->removeElement($store);
		}
	}

	public function isConfirmed()
	{
		return $this->confirmedAt !== null;
	}

	public function isCancelled()
	{
		return $this->cancelledAt !== null;
	}

	public function confirm()
	{
		$this->cancelledAt = null;
		$this->confirmedAt = new \DateTime();
	}

	public function unConfirm()
	{
		$this->confirmedAt = null;
	}

	public function cancel()
	{
		$this->unConfirm();
		$this->cancelledAt = new \DateTime();
	}

	public function getFirstLeafletPage()
	{
		return !$this->leafletPages->isEmpty() ? $this->leafletPages->first() : null;
	}

	public function getIdsOfTags()
	{
		$ids = [];
		foreach ($this->tags as $tag) {
			$ids[] = $tag->getId();
		}

		return $ids;
	}

	public function setTags(array $tags)
	{
		$idsOfCurrentTags = $this->getIdsOfTags();
		$idsOfTags = array_map(static function ($tag) {
			return $tag->getId();
		}, $tags);

		foreach ($this->getTags() as $tag) {
			if (!in_array($tag->getId(), $idsOfTags)) {
				$this->removeTag($tag);
			}
		}

		foreach ($tags as $tag) {
			if (!in_array($tag->getId(), $idsOfCurrentTags)) {
				$this->addTag($tag);
			}
		}
	}

  /**
   * @param Tag $tag
   */
	public function addTag(Tag $tag)
	{
		if (!$this->tags->contains($tag)) {
			$this->tags->add($tag);
		}
	}

	public function hasTag(Tag $tag): bool
	{
		return in_array($tag->getId(), $this->getIdsOfTags());
	}

  /**
   * @param Tag $tag
   */
	public function removeTag(Tag $tag)
	{
		if ($this->tags->contains($tag)) {
			$this->tags->removeElement($tag);
		}
	}

	public function isReadyToConfirm()
	{
		return !$this->isConfirmed() && !$this->isCancelled() && $this->shop !== null;
	}

	public function isVisible()
	{
		return $this->getVisible();
	}

  /**
   * @return mixed
   */
	public function getTags()
	{
		return $this->tags;
	}

  /**
   * @return bool
   */
	public function getVisible(): bool
	{
		return $this->visible;
	}

  /**
   * @param bool $visible
   */
	public function setVisible(bool $visible)
	{
		$this->visible = $visible;
	}

	/**
	 * @return mixed
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getConfirmedAt()
	{
		return $this->confirmedAt;
	}

	/**
	 * @return mixed
	 */
	public function getLeafletPages()
	{
		return $this->leafletPages;
	}

	/**
	 * @param LeafletPage $leafletPage
	 */
	public function removeLeafletPage(LeafletPage $leafletPage)
	{
		if ($this->leafletPages->contains($leafletPage)) {
			$this->leafletPages->removeElement($leafletPage);
		}
	}

	/**
	 * @return mixed
	 */
	public function isPrimary()
	{
		return $this->primary;
	}

	/**
	 * @return mixed
	 */
	public function getCancelledAt()
	{
		return $this->cancelledAt;
	}

	/**
	 * @param mixed $primary
	 */
	public function setPrimary($primary)
	{
		$this->primary = $primary;
	}

	/**
	 * @param mixed $type
	 */
	public function setType($type)
	{
		$this->type = $type;
	}

	/**
	 * @return null|\tipli\Model\Seo\Entities\PageExtension
	 */
	public function getPageExtension()
	{
		return $this->pageExtension;
	}

	/**
	 * @param null|\tipli\Model\Seo\Entities\PageExtension $pageExtension
	 */
	public function setPageExtension($pageExtension)
	{
		$this->pageExtension = $pageExtension;
	}

	public function isValid()
	{
		return ($this->validTill > new \DateTime() && $this->validSince < new \DateTime());
	}

	public function isScheduled()
	{
		return ($this->validSince > new \DateTime());
	}

	public function isExpired()
	{
		return ($this->validTill < new \DateTime());
	}

	/**
	 * @return int|null
	 */
	public function getCountOfPages(): ?int
	{
		return $this->countOfPages;
	}

	/**
	 * @param int|null $countOfPages
	 */
	public function setCountOfPages(?int $countOfPages): void
	{
		$this->countOfPages = $countOfPages;
	}

	public function isRemoved()
	{
		return $this->removedAt !== null;
	}

	public function getPartnerLink(): ?string
	{
		return $this->partnerLink;
	}

	public function setPartnerLink(?string $partnerLink): void
	{
		$this->partnerLink = $partnerLink;
	}

	public function getArchivationPages(): ?int
	{
		return $this->archivationPages;
	}

	public function setArchivationPages(?int $archivationPages): void
	{
		$this->archivationPages = $archivationPages;
	}

	public function getRemoveAt()
	{
		return $this->removeAt;
	}

	public function setRemoveAt($removeAt): void
	{
		$this->removeAt = $removeAt;
	}

	public function getArchiveAt()
	{
		return $this->archiveAt;
	}

	public function setArchiveAt($archiveAt): void
	{
		$this->archiveAt = $archiveAt;
	}

	public function archive(): void
	{
		$this->archivedAt = new \DateTime();
	}

	public function isArchived(): bool
	{
		return $this->archivedAt !== null;
	}
}
