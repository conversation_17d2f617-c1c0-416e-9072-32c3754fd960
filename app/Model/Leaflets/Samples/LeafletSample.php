<?php

namespace tipli\Model\Leaflets\Samples;

use Doctrine\ORM\EntityManagerInterface;
use Nette\Utils\Random;
use tipli\Model\Layers\ISamplePart;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\Entities\LeafletPage;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;

class LeafletSample implements ISamplePart
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var ShopFacade */
	private $shopFacade;

	public function __construct(
		LocalizationFacade $localizationFacade,
		ShopFacade $shopFacade
	) {
		$this->localizationFacade = $localizationFacade;
		$this->shopFacade = $shopFacade;
	}

	public function fillDatabase(EntityManagerInterface $em): void
	{
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			/** @var Shop $shop */
			$shop = $this->shopFacade->findBySlug('dr-max', $localization);

			$leaflet = new Leaflet(
				Random::generate(32),
				$localization,
				$this->getLeafletName($shop),
				'Lorem ipsum dolor sit amet consectetuer - ' . $localization->getLocale(),
				null,
				new \DateTime('-1 day'),
				new \DateTime('+1 month'),
				'leaflet-' . Random::generate(10),
				$shop
			);

			$tags = [];
			foreach ($shop->getShopLeafletTags() as $shopLeafletTag) {
				$tags[] = $shopLeafletTag->getTag();
			}

			$leafletPages = $this->getLeafletPages();
			foreach ($leafletPages as $pageNumber => $downloadUrl) {
				$leafletPage = new LeafletPage($leaflet, null, $pageNumber, $downloadUrl);

				$em->persist($leafletPage);
			}

			$leaflet->setShop($shop);
			$leaflet->setShopDomain($shop->getDomain());
			$leaflet->setType(Leaflet::TYPE_LEAFLET);
			$leaflet->setTags($tags);
			$leaflet->setCountOfPages(count($leafletPages));

			$leaflet->confirm();

			$em->persist($leaflet);
		}

		$em->flush();
	}

	private function getLeafletName(Shop $shop): string
	{
		$label = 'Leták';

		if ($shop->getLocalization()->isPolish()) {
			$label = 'Gazetka';
		}
		if ($shop->getLocalization()->isHungarian()) {
			$label = 'Akciós újság';
		}
		if ($shop->getLocalization()->isRomanian()) {
			$label = 'Catalog';
		}

		return $label . ' ' . $shop->getName();
	}

	private function getLeafletPages(): array
	{
		return [
			 1 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/n1y5061a6c7e7ge8tf0s0t3p.jpg',
			 2 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/8ug3a9r3h958cwn2xyjf9dr7.jpg',
			 3 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/7ad30f3tpu32i0j0sf83izv7.jpg',
			 4 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/dir82eiq8e2wz0x576e6xrgl.jpg',
			 5 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/3vkce1eduyhld8chqhlc2d7q.jpg',
			 6 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/4fgrqhrktrfd8cjtu29lg3gk.jpg',
			 7 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/1t8bkvxplyvei3xft68tw566.jpg',
			 8 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/qqem9yn0zhmykbtd349qius2.jpg',
			 9 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/eb38d99dy33fxydup4h0j7lp.jpg',
			10 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/prkwdt9m4w6bw8hd56ecilaz.jpg',
			11 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/4th289nuz1ir47v0xy1vsimy.jpg',
			12 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/wb71q1207183sdb8ze5crpnh.jpg',
			13 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/nm67sjpug66b7bk9yebbfbft.jpg',
			14 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/6skg9ib08hhruzsyscxk1gu7.jpg',
			15 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/9vqy8flx1dxmccp29ka9pfip.jpg',
			16 => 'https://www.tipli.czlocal/images/samples/leaflets/dr-max/38ktbqj66j2okix8yu4xxxsh.jpg',
		];
	}
}
