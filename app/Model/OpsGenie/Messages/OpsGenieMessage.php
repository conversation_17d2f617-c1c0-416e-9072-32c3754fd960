<?php

namespace tipli\Model\OpsGenie\Messages;

use Nette\Utils\Json;
use Nette\Utils\JsonException;
use tipli\Model\RabbitMq\IMessage;

final class OpsGenieMessage implements IMessage
{
	public const TYPE_ALERT = 'alert';
	public const TYPE_PING = 'ping';

	/** @var string */
	private $type;

	/** @var string|null */
	private $priority;

	/** @var string|null */
	private $alias;

	/** @var string|null */
	private $message;

	/** @var string|null */
	private $description;

	/** @var string|null */
	private $name;

	public function __construct(string $type, ?string $priority, ?string $alias, ?string $message, ?string $description = null, ?string $name = null)
	{
		$this->type = $type;
		$this->priority = $priority;
		$this->alias = $alias;
		$this->message = $message;
		$this->description = $description;
		$this->name = $name;
	}

	public function getType(): string
	{
		return $this->type;
	}

	public function getPriority(): ?string
	{
		return $this->priority;
	}

	public function getAlias(): ?string
	{
		return $this->alias;
	}

	public function getMessage(): ?string
	{
		return $this->message;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function getName(): ?string
	{
		return $this->name;
	}

	/**
	 * @throws JsonException
	 */
	public function __toString(): string
	{
		return Json::encode([
			'type' => $this->getType(),
			'priority' => $this->getPriority(),
			'alias' => $this->getAlias(),
			'message' => $this->getMessage(),
			'description' => $this->getDescription(),
			'name' => $this->getName(),
		]);
	}

	/**
	 * @throws JsonException
	 */
	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);

		return new self(
			$data->type,
			$data->priority,
			$data->alias,
			$data->message,
			$data->description,
			$data->name
		);
	}
}
