<?php

namespace tipli\Model\Utm\Samples;

use Doctrine\ORM\EntityManagerInterface;
use tipli\Model\Layers\ISamplePart;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Utm\Entities\Utm;

class UtmSample implements ISamplePart
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	public function __construct(LocalizationFacade $localizationFacade)
	{
		$this->localizationFacade = $localizationFacade;
	}

	public function fillDatabase(EntityManagerInterface $em): void
	{
		$utmSourceItems = [
			'addon', 'facebook', 'newsletter', 'google', 'seznam',
		];

		$utmMediumItems = [
			'email', 'cpc', 'web', 'cashback', 'onesignal',
		];

		$utmCampaignItems = [
			'huraslevy_cz', 'bersleva_cz', 'bigsale_cz',
		];

		foreach ($this->localizationFacade->findLocalizations() as $localization) {
			foreach ($utmSourceItems as $utmSource) {
				foreach ($utmMediumItems as $utmMedium) {
					foreach ($utmCampaignItems as $utmCampaign) {
						$utm = new Utm($localization, $utmSource, $utmMedium, $utmCampaign, null, null);

						$em->persist($utm);
					}
				}
			}
		}

		$em->flush();
	}
}
