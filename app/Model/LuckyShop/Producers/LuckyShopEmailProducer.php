<?php

namespace tipli\Model\LuckyShop\Producers;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\LuckyShopEmailProvider;
use tipli\Model\LuckyShop\Messages\UserLuckyShopEmailMessage;
use tipli\Model\RabbitMq\BaseProducer;

class LuckyShopEmailProducer extends BaseProducer
{
	public function scheduleSendLuckyShopCreatedEmail(User $user, LuckyShop $luckyShop): void
	{
		$this->producer->publish(
			new UserLuckyShopEmailMessage(LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_CREATED, $user->getId(), $luckyShop->getId())
		);
	}

	public function scheduleSendLuckyShopRewardCreatedEmail(User $user, LuckyShop $luckyShop): void
	{
		$this->producer->publish(
			new UserLuckyShopEmailMessage(LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_REWARD_CREATED, $user->getId(), $luckyShop->getId())
		);
	}

	public function scheduleSendLuckyWinConfirmationEmail(User $user, LuckyShop $luckyShop): void
	{
		$this->producer->publish(
			new UserLuckyShopEmailMessage(LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_WIN_CONFIRMATION, $user->getId(), $luckyShop->getId())
		);
	}
}
