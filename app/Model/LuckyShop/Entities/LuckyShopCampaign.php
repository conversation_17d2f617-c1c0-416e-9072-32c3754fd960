<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\LuckyShopCampaignRepository")
 * @ORM\Table(name="tipli_lucky_shop_lucky_shop_campaigns")
 */
class LuckyShopCampaign
{
	public const NAME_DEFAULT = 'default';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private Localization $localization;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $name;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $totalRewardAmount;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $lastLuckyShopValidSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $processAt;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $processTimeShift;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $processedAt = null;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $userRewardRequestsTimeShift;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="UserLuckyShopCheck", mappedBy="luckyShopCampaign")
	 */
	private Collection $userLuckyShopChecks;

	public function __construct(Localization $localization, string $name, int $totalRewardAmount, \DateTime $processAt)
	{
		$this->localization = $localization;
		$this->name = $name;
		$this->totalRewardAmount = $totalRewardAmount;
		$this->processAt = $processAt;
		$this->processTimeShift = '+ 1 day';
		$this->userRewardRequestsTimeShift = '+ 12 hours';
		$this->createdAt = new \DateTime();

		$this->userLuckyShopChecks = new ArrayCollection();
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getProcessedAt(): ?\DateTime
	{
		return $this->processedAt;
	}

	public function nextLuckyShopAfter()
	{
		$nextLuckyShop = clone $this->getLastLuckyShopValidSince();
		$nextLuckyShop = $nextLuckyShop->modify($this->getProcessTimeShift());

		return $nextLuckyShop->diff(new \DateTime());
	}

	public function process(): void
	{
		$this->processedAt = new \DateTime();
		$processAt = clone $this->processAt;
		$this->processAt = $processAt->modify($this->processTimeShift);
	}

	public function getRewardAmountPerUser($countOfUsers)
	{
		if ($countOfUsers == 0) {
			return $this->totalRewardAmount;
		}

		return $this->totalRewardAmount / $countOfUsers;
	}

	public function getTotalRewardAmount(): int
	{
		return $this->totalRewardAmount;
	}

	public function getProcessAt(): \DateTime
	{
		return $this->processAt;
	}

	public function getProcessTimeShift(): string
	{
		return $this->processTimeShift;
	}

	public function getUserRewardRequestsTimeShift(): string
	{
		return $this->userRewardRequestsTimeShift;
	}

	public function getLastLuckyShopValidSince(): \DateTime
	{
		return $this->lastLuckyShopValidSince;
	}

	public function setLastLuckyShopValidSince(\DateTime $lastLuckyShopValidSince): void
	{
		$this->lastLuckyShopValidSince = $lastLuckyShopValidSince;
	}
}
