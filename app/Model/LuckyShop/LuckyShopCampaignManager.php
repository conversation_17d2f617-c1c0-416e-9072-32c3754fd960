<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;

class LuckyShopCampaignManager
{
	public function __construct(private EntityManager $em)
	{
	}

	public function saveLuckyShopCampaign(LuckyShopCampaign $luckyShopCampaign): LuckyShopCampaign
	{
		$this->em->persist($luckyShopCampaign);
		$this->em->flush();

		return $luckyShopCampaign;
	}
}
