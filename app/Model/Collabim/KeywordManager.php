<?php

namespace tipli\Model\Collabim;

use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Collabim\Entities\Keyword;
use tipli\Model\Collabim\Repositories\KeywordRepository;

class KeywordManager
{
	private const SEARCH_ENGINES = [
		'google' => [1, 7, 9],
		'seznam' => [2],
	];

	/** @var EntityManager */
	private $em;

	/** @var KeywordRepository */
	private $keywordRepository;

	public function __construct(EntityManager $em, KeywordRepository $keywordRepository)
	{
		$this->em = $em;
		$this->keywordRepository = $keywordRepository;
	}

	public function createOrUpdateKeyword(\stdClass $rawKeyword, $projectId)
	{
		$tags = implode(';', $rawKeyword->tags);
		$positionGoogleCurrent = 0;
		$positionSeznamCurrent = 0;

		foreach ($rawKeyword->positions as $position) {
			if (in_array($position->searchEngineId, self::SEARCH_ENGINES['google'])) {
				$positionGoogleCurrent = $position->position;
			} elseif (in_array($position->searchEngineId, self::SEARCH_ENGINES['seznam'])) {
				$positionSeznamCurrent = $position->position;
			}
		}

		/** @var Keyword|null $entry */
		if ($keyword = $this->keywordRepository->findByKeywordId($rawKeyword->id)) {
			$keyword->setKeyword($rawKeyword->keyword);
			$keyword->setSearchesGoogleCurrent($rawKeyword->searchesGoogleCurrent);
			$keyword->setSearchesSeznamCurrent($rawKeyword->searchesSeznamCurrent);
			$keyword->setPositionGoogleCurrent($positionGoogleCurrent);
			$keyword->setPositionSeznamCurrent($positionSeznamCurrent);
			$keyword->setTags($tags);
			$keyword->setStarred((bool) $rawKeyword->starred);
			$keyword->update();
		} else {
			$keyword = new Keyword(
				$rawKeyword->id,
				(bool) $rawKeyword->keyword,
				$projectId,
				$rawKeyword->searchesGoogleCurrent,
				$rawKeyword->searchesSeznamCurrent,
				$positionGoogleCurrent,
				$positionSeznamCurrent,
				$tags,
				$rawKeyword->starred
			);
		}

		return $this->saveKeyword($keyword);
	}

	public function saveKeyword(Keyword $keyword)
	{
		$this->em->persist($keyword);
		$this->em->flush($keyword);

		return $keyword;
	}
}
