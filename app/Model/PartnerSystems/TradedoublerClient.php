<?php

namespace tipli\Model\PartnerSystems;

use GuzzleHttp\Client;
use Nette\Utils\Json;
use tipli\InvalidStateException;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;

class TradedoublerClient
{
	private const FEED_URL = 'https://publishers.tradedoubler.com/tools/transactions?format=json&transactionSearchForm[site_id]=&transactionSearchForm[creative_id]=&transactionSearchForm[lead_number]=&transactionSearchForm[period]=:dateFrom+-+:dateTo&transactionSearchForm[epi1]=&transactionSearchForm[epi2]=&DataTables_Table_0_length=-1.json';

	/** @var PartnerSystem */
	private $partnerSystem;

	/** @var Client|void */
	private $client;

	public function __construct(PartnerSystem $partnerSystem)
	{
		$this->partnerSystem = $partnerSystem;
		$this->client = $this->login();
	}

	public function getTransactions(\DateTime $dateFrom, \DateTime $dateTo): array
	{
		$feedUrl = strtr(self::FEED_URL, [
			':dateFrom' => $dateFrom->format('d/m/Y'),
			':dateTo' => $dateTo->format('d/m/Y'),
		]);

		$response = $this->client->request('GET', $feedUrl);

		return Json::decode($response->getBody()->getContents())->items;
	}

	public function login()
	{
		$client = new Client(['cookies' => true]);

		$response = $client->request('GET', 'https://publishers.tradedoubler.com/login');

		preg_match('/name="userLoginForm\[csrf_token\]" value="(\w+)"/', $response->getBody()->getContents(), $csrfToken);

		if (!$csrfToken) {
			throw new InvalidStateException('Unknown csrf token');
		}

		$csrfToken = $csrfToken[1];

		$client->request('POST', 'https://publishers.tradedoubler.com/login', [
			'headers' => [
				'Content-Type' => 'application/x-www-form-urlencoded',
			],
			'form_params' => [
				'userLoginForm[username]' => $this->partnerSystem->getOption('username'),
				'userLoginForm[password]' => $this->partnerSystem->getOption('password'),
				'userLoginForm[csrf_token]' => $csrfToken,
			],
		]);

		return $client;
	}
}
