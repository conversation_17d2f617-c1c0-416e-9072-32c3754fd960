<?php

namespace tipli\PartnerSystems\Queries;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\QueryObject\QueryObject;
use tipli\Model\Doctrine\QueryObject\Persistence\Queryable;

class PartnerSystemsQuery extends QueryObject
{
	/** @var callable[] */
	protected $filters = [];

	public function automated()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('p.automated = true');
		};

		return $this;
	}

	public function readyToProcess()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder
				->andWhere('p.processedAt <= :now')
				->setParameter('now', new \DateTime())
				->addOrderBy('p.processedAt', 'asc');
		};

		return $this;
	}

	public function onlyAbleToProcess()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('p.processedAt <= :now')
				->setParameter('now', new \DateTime());
		};

		return $this;
	}

	public function onlyCommissions()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('p.hasCommissions = true');
		};

		return $this;
	}

	public function onlyWithSales(): PartnerSystemsQuery
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('p.hasSales = true');
		};

		return $this;
	}

	public function sortByLastPivotReset()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('p.pivotLastResetDate');
		};

		return $this;
	}

	public function sortByCountOfTransactions()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('p.countOfTransactions', 'DESC');
		};

		return $this;
	}

	/**
	 * @return $this
	 */
	public function sortByProcessSalesAt()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->addOrderBy('p.processSalesAt', 'DESC');
		};

		return $this;
	}

	/**
	 * @return $this
	 */
	public function readyToProcessSales()
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) {
			$queryBuilder->andWhere('p.processSalesAt <= :now OR p.processSalesAt IS NULL')
				->setParameter('now', new \DateTime());
		};

		return $this;
	}

	public function withType(string $type)
	{
		$this->filters[] = static function (QueryBuilder $queryBuilder) use ($type) {
			$queryBuilder->andWhere('p.type = :type')->setParameter('type', $type);
		};

		return $this;
	}

	protected function doCreateQuery(Queryable $dao)
	{
		$queryBuilder = $dao->createQueryBuilder('p', 'p.id')
			->select('p');


		foreach ($this->filters as $filter) {
			$filter($queryBuilder);
		}

		$queryBuilder->addOrderBy('p.id');

		return $queryBuilder;
	}

	public function postFetch(Queryable $repository, \Iterator $iterator)
	{
	}
}
