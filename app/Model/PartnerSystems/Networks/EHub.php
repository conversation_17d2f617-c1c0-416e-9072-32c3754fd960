<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Producers\DealImporterProducer;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;
use DateTime;
use Exception;
use Nette\Database\Context;

interface IEHubFactory
{
	/**
	 * @return EHub
	 */
	public function create(PartnerSystem $partnerSystem);
}

class EHub extends Network
{
	public const DEAL_TYPE_VOUCHER = 'voucher';
	public const DEAL_TYPE_ACTION = 'action';

	/** @var DealImporterProducer $dealImporterProducer @inject */
	public $dealImporterProducer;

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	/** @var Context $context @inject */
	public Context $context;

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		// vyjimka pro mbank (neni povoleny deep link)
		if ($deepUrl === 'https://ehub.cz/system/scripts/click.php?a_aid=c8a839f7&a_bid=0f8a2721') {
			$url = 'https://ehub.cz/system/scripts/click.php?a_aid=c8a839f7&a_bid=0f8a2721';
			$deepUrl = null;
		}

		$shop = $this->shopFacade->findShopByPartnerSystemUrl($url);

		// vyjimka pro PetExpert
		if ($shop && in_array($shop->getId(), [6632, 12383])) {
			$deepUrl = null;
		}

		$url = new Url($url);
		$url->setQueryParameter('data1', $userId . 'x' . $redirectionId);

		$destUrl = $deepUrl !== null ? (new Url($deepUrl))->getQueryParameter('desturl') : null;

		if ($destUrl !== null) {
			$url->setQueryParameter('desturl', $destUrl);
		} elseif ($deepUrl) {
			$url->setQueryParameter('desturl', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$requiredColumns = ['datum-a-cas', 'program', 'klik-data-1', 'id-objednavky', 'hodnota-objednavky', 'provize', 'vyplata', 'stav', 'id'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('eHub import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$transactionId = explode('-', $row['id'])[0];
			$userId = $row['klik-data-1'];
			$orderAmount = (float) str_replace([',', ' '], ['.', ''], $row['hodnota-objednavky']);

			$registeredAt = new \DateTime($row['datum-a-cas']);

			$status = $this->resolveStatus(Strings::webalize($row['stav']));
			$commissionAmount = (float) str_replace([',', ' '], ['.', ''], $row['provize']);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::CZK, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$importedTransaction->setShopDomain(Strings::lower(Strings::toAscii($row['program'])));

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		// nemazat, transakce se musi pozdeji stahnout se stavem billable = 0
		if ($from !== null && $from < (new DateTime())->modify('-3 months')) {
			echo 'from date is too old' . PHP_EOL;
			return;
		}

		$records = [];
		if ($from && $to) {
			for ($i = 1; $i < 150000; $i++) {
				$foundRecords = $this->getRecords($from, $to, $i);

				if (empty($foundRecords)) {
					break;
				}

				$data = [];
				foreach ($foundRecords as $record) {
					$data[] = [
						'transaction_id' => $record->id,
						'commission' => $record->commission,
						'amount' => $record->amount,
						'order_id' => $record->orderId,
						'found_refund_id' => null,
						'status' => $record->status,
						'campaign_id' => $record->campaignId,
						'type' => $record->type,
						'code' => $record->code,
						'inserted_at' => $record->dateInserted,
						'resolved_at' => $record->resolutionDateTime,
						'created_at' => new DateTime(),
					];
				}

				if (!empty($data)) {
					$this->context->query('INSERT IGNORE INTO ehub_refund_transactions', $data);
				}

				$records = array_merge($records, $foundRecords);
			}
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$partnerSystemKey = $record->campaignId;
			$transactionId = $record->id;
			$userId = $record->lastClickData1;
			$commissionAmount = (float) $record->commission;
			$orderAmount = (float) $record->amount;
			$registeredAt = new \DateTime($record->dateInserted, new \DateTimeZone('America/Los_Angeles'));
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));

			if (
				$partnerSystemKey === 'b39ba7a4' &&
				$commissionAmount === 0.0 &&
				$registeredAt > (new \DateTime())->modify('-1 day')
			) {
				continue;
			}

			$status = $this->resolveStatus($record->status);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::CZK, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processSales(): void
	{
		$client = new Client();

		for ($i = 1; $i <= 10; $i++) {
			try {
				$response = $client->request('GET', 'https://api.ehub.cz/v3/publishers/' . $this->partnerSystem->getOption('publisherId') . '/vouchers', [
					'query' => [
						'apiKey' => $this->partnerSystem->getOption('apiKey'),
						'isValid' => true,
						'page' => $i,
						'perPage' => 100,
					],
				]);
				$data = Json::decode($response->getBody()->getContents());

				Debugger::log('VOUCHERS: ' . count($data->vouchers), 'eHub-process-sales');

				if (count($data->vouchers) < 1) {
					break;
				}

				foreach ($data->vouchers as $voucher) {
					$id = $voucher->id;
					$uniqueId = Strings::webalize($this->partnerSystem->getName() . '--' . $id);
					$campaignId = $voucher->campaignId;
					$type = $voucher->type;
					$code = $voucher->code;
					$rules = $voucher->rules;
					$value = null;
					$unit = null;
					$slug = Strings::webalize($rules);
					$validSince = (new DateTime($voucher->validFrom))->setTime(0, 0);
					$validTill = (new DateTime($voucher->validTill))->setTime(23, 59, 59);

//					if (!in_array($type, [self::DEAL_TYPE_VOUCHER, self::DEAL_TYPE_ACTION])) {
//						Debugger::log('cant identify type: ' . $type, 'eHub-process-sales');
//						continue;
//					}

					/** @var Shop|null $shop */
					$shop = $this->shopFacade->findShopByPartnerSystemKey($voucher->campaignId, $this->partnerSystem);
//					if ($shop === null) {
//						Debugger::log('[' . $this->partnerSystem->getName() .  '][' . $voucher->campaignName . '] not found for campaignId ' . $campaignId, 'deals-not-paired-shops');
//						Debugger::log('cant identify shop by campaignId: ' . $campaignId, 'eHub-process-sales');
//						continue;
//					}

					$valuePattern = '/\b(\d+(?:[.,]\d+)?\s*(?:%|KČ|Kč|kč|eur|€|czk)?)\b/i';
					$unitPattern = '/(%|KČ|Kč|kč|eur|€|czk)/';

					// values
					if (preg_match_all($valuePattern, $rules, $matches)) {
						$value = $matches[0][0];
					}

					// units
					if (preg_match_all($unitPattern, $rules, $matches)) {
						$symbol = trim($matches[0][0]);

						$unit = Deal::getUnitBySymbol(strtolower($symbol));
					}

					$importedDeal = new ImportedDeal($shop->getLocalization()->getId(), $shop ? $shop->getId() : null, null, $voucher->rules, $slug, $validSince, $validTill, null, null);
					$importedDeal->setUniqueId($uniqueId);
					$importedDeal->setSourceName($this->partnerSystem->getFeedName());
					$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
					$importedDeal->setValidSince($validSince);
					$importedDeal->setValidTill($validTill);
					$importedDeal->setName($rules);
					$importedDeal->setDeepUrl($voucher->url);

					if ($type === self::DEAL_TYPE_VOUCHER) {
						$importedDeal->setCode($code);
						$importedDeal->setType(Deal::TYPE_COUPON);
					} elseif ($type === self::DEAL_TYPE_ACTION) {
						if (
							Strings::contains(Strings::lower($rules), 'doprava zdarma') ||
							Strings::contains(Strings::lower($rules), 'doprava zadarmo')
						) {
							$importedDeal->setType(Deal::TYPE_FREE_SHIPPING);
						} elseif ($value !== null && $unit !== null) {
							$importedDeal->setType(Deal::TYPE_SALE);
						} else {
							$importedDeal->setType(Deal::TYPE_TIP);
						}
					} else {
						$importedDeal->setType(null);
					}

					if ($unit !== null && $value !== null) {
						$value = str_replace(',', '.', $value);
						$importedDeal->setUnit($unit);
						$importedDeal->setValue($value);
					}

					Debugger::log(Json::encode($importedDeal->toStaticArray()), 'eHub-process-sales');

					$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);
				}
			} catch (Exception $e) {
				Debugger::log($e, 'eHub-process-sales');
			}
		}
	}

	public function resolveStatus($status)
	{
		switch ($status) {
			case 'predschvaleno':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'nevyreseno':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'P':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'A':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'schvaleno':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'D':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'zamitnuto':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'pre-approved':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'declined':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}

	public function getRecords(\DateTime $from, \DateTime $to, $page = 1)
	{
		$client = new Client();
		$response = $client->request('GET', $this->partnerSystem->getOption('apiUrl') . '/publishers/' . $this->partnerSystem->getOption('publisherId') . '/transactions', [
			'query' => [
				'apiKey' => $this->partnerSystem->getOption('apiKey'),
				'page' => $page,
				'perPage' => 100,
				'dateInsertedFrom' => $from->format('Y-m-d') . 'T' . $to->format('H:i:s.000000'),
				'dateInsertedTo' => $to->format('Y-m-d') . 'T' . $to->format('H:i:s.000000'),
			],
		]);

		$contents = $response->getBody()->getContents();

		$result = Json::decode($contents);

		$records = $result->transactions;

		return $records;
	}

	public function importRefunds(File $file): array
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = $this->resolveRefundSolutionStatus($row['stav']);

			if ($status === null) {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				$row['id-objednavky'],
				$status
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): ?string
	{
		$status = Strings::lower($status);

		return match ($status) {
			'zamítnuto' => RefundSolution::STATE_PARTNER_DECLINED,
			'schváleno' => RefundSolution::STATE_PARTNER_APPROVED,
			'v řešení', 'nevyřešeno' => RefundSolution::STATE_PARTNER_PENDING,
			default => null,
		};
	}
}
