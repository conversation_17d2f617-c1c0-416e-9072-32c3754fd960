<?php

namespace tipli\Model\PartnerSystems\Networks;

use Nette\Http\Url;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;

interface IMyLeadFactory
{
	/**
	 * @return Booking
	 */
	public function create(PartnerSystem $partnerSystem);
}

class MyLead extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$redirectionUrl = new Url($url);
		$redirectionUrl->setQueryParameter('ml_sub1', $userId . 'x' . $redirectionId);

		return $redirectionUrl->getAbsoluteUrl();
	}
}
