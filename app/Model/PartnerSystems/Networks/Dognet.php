<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Database\Context;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Producers\DealImporterProducer;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface IDognetFactory
{
	/**
	 * @return Dognet
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Dognet extends Network
{
	/** @var DealImporterProducer $dealImporterProducer @inject */
	public $dealImporterProducer;

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	/** @var Context @inject */
	public Context $context;

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null): string
	{
		$url = new Url($url);
		$url->setQueryParameter('d1', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$url->setQueryParameter('url', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to, 1000));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records['data'] as $row) {
			$transactionId = $row['original_id'];

			if (Strings::contains($transactionId, '_test')) {
				continue;
			}

			$partnerSystemKey = $row['campaign_id'];
			$userId = $row['last_click_data1'];
			$commissionAmount = (float) $row['commission'];
			$orderAmount = (float) $row['total_cost'];
			$registeredAt = new \DateTime($row['created_at']);

			$status = $this->resolveStatus($row['rstatus']);

			$confirmedAt = null;

			if ($status === ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime($row['date_approved']);
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::EUR, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($status)
	{
		switch ($status) {
			case 'P':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'A':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'D':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	private function getAuthenticationToken()
	{
		$client = new Client();
		$url = 'https://api.app.dognet.com/api/v1/auth/login';

		$body = [
			'email' => $this->partnerSystem->getOption('username'),
			'password' => $this->partnerSystem->getOption('password'),
		];

		try {
			$response = $client->post($url, [
				'headers' => [
					'Content-Type' => 'application/json',
				],
				'json' => $body,
			]);

			$data = json_decode($response->getBody()->getContents(), true);

			if (!isset($data['token'])) {
				throw new \RuntimeException('Authentication token not found in response');
			}

			return $data['token'];
		} catch (\Exception $e) {
			throw new \RuntimeException('Error during authentication: ' . $e->getMessage());
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to, int $limit = 100)
	{
		$client = new Client();
		$authToken = $this->getAuthenticationToken();

		$url = 'https://api.app.dognet.com/api/v1/raw-transactions/filter';

		$body = [
			'filter' => [
				['created_at' => ['gte' => $from->format('Y-m-d 00:00:00')]],
				['created_at' => ['lte' => $to->format('Y-m-d 23:59:59')]],
			],
			'per-page' => $limit,
		];

		try {
			$response = $client->post($url, [
				'headers' => [
					'Authorization' => 'Bearer ' . $authToken,
					'Content-Type' => 'application/json',
				],
				'json' => $body,
			]);

			$data = json_decode($response->getBody()->getContents(), true);
			return $data;
		} catch (\Exception $e) {
			Debugger::log($e->getMessage(), 'dognet-error');

			throw $e;
		}
	}

	public function processSales()
	{
		$data = Json::decode(file_get_contents($this->partnerSystem->getOption('salesUrl')));

		foreach ($data->vouchers as $voucher) {
			if (empty($voucher->campaignLang)) {
				Debugger::log('cant identify localization', 'Dognet-processSales');
				continue;
			}

			if (!isset($voucher->id) || !isset($voucher->uid) || !isset($voucher->campaignId) || !isset($voucher->type) || !isset($voucher->code) || !isset($voucher->validFrom) || !isset($voucher->validTo) || !isset($voucher->url)) {
				continue;
			}

			$id = $voucher->id;
			$uniqueId = $voucher->uid;
			$campaignId = $voucher->campaignId;
			$type = $voucher->type;
			$code = 'voucher' === $type ? $voucher->code : null;
			$rules = $voucher->rules ?? null;
			$validSince = (new \DateTime($voucher->validFrom))->setTime(0, 0);
			$validTill = (new \DateTime($voucher->validTo))->setTime(23, 59, 59);
			$locale = $voucher->campaignLang[0];

			if (!in_array($locale, Localization::getLocales())) {
				continue;
			}

			$url = $voucher->url;
			$localization = $this->localizationFacade->findOneByLocale($locale);
			$shop = $this->shopFacade->findByPartnerSystem($this->partnerSystem, $campaignId, $localization);
			$rewardLabel = $this->resolveRewardLabel($rules);
			$name = Strings::truncate($voucher->description ?? $rules, 255);
			$description = $voucher->description2 ?? null;
			$slug = Strings::webalize($name);
			$voucherDiscountValue = null;
			$voucherDiscountUnit = null;
			$isFreeShipping = $this->resolveIsFreeShipping($rules . $description . $name);

//			if ($validTill < new \DateTime()) {
//				continue;
//			}
//
//			if (!$name) {
//				continue;
//			}

			if (!$localization) {
				Debugger::log('cant identify localization', 'Dognet-processSales');
				continue;
			}

//			if (!in_array($type, ['voucher', 'action'])) {
//				Debugger::log('cant identify type: ' . $type, 'Dognet-processSales');
//				continue;
//			}

//			if (!$shop) {
//				Debugger::log('[' . $this->partnerSystem->getName() .  '][' . $voucher->campaignName . '] not found for campaignId ' . $campaignId, 'deals-not-paired-shops');
//				Debugger::log('cant identify shop by campaignId: ' . $campaignId, 'Dognet-processSales');
//				continue;
//			}

			preg_match('/(\d+)(\w+|[^\w\+])/u', Strings::lower(str_replace(' ', '', $rules)), $matches);

			$voucherDiscountValue = null;
			$voucherDiscountUnit = null;

			if ($matches) {
				[$match, $voucherDiscountValue, $unitSymbol] = $matches;
				$voucherDiscountUnit = Deal::getUnitBySymbol($unitSymbol);
			}

			if (!$voucherDiscountValue || !$voucherDiscountUnit) {
				Debugger::log('cant identify value of voucher: ' . $rules, 'Dognet-processSales');
				continue;
			}

			$importedDeal = new ImportedDeal($localization->getId(), $shop ? $shop->getId() : null, null, $name, $slug, $validSince, $validTill, null, null);

			$importedDeal->setUniqueId($uniqueId);
			$importedDeal->setDescription($description);
			$importedDeal->setRewardLabel($rewardLabel);
			$importedDeal->setSourceName($this->partnerSystem->getFeedName());
			$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
			$importedDeal->setFreeShipping($isFreeShipping);
			$importedDeal->setDeepUrl($url);

			if ($type === 'voucher') {
				$importedDeal->setType(Deal::TYPE_COUPON);
				$importedDeal->setCode($code);

				if (isset($voucherDiscountUnit) && isset($voucherDiscountValue)) {
					$importedDeal->setUnit($voucherDiscountUnit);
					$importedDeal->setValue($voucherDiscountValue);
				}
			} elseif ($type === 'action' && !$isFreeShipping) {
				$importedDeal->setType(Deal::TYPE_TIP);
			} else {
				$importedDeal->setType(null);
			}

			//$this->dealImporterProducer->scheduleImportedDeal($importedDeal);
			$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);
		}
	}

	public function resolveRewardLabel($type)
	{
		if ($type === null) {
			return null;
		}

		$type = Strings::lower($type);
		$types = [
			'1+1' => Deal::REWARD_LABEL_ONE_PLUS_ONE,
			'darček' => Deal::REWARD_LABEL_GIFT,
			'dárek' => Deal::REWARD_LABEL_GIFT,
		];

		if (isset($types[$type])) {
			return $types[$type];
		}

		return null;
	}

	public function resolveIsFreeShipping($text): bool
	{
		if (empty($text)) {
			return false;
		}

		$text = Strings::lower($text);

		preg_match_all('/(doprava|poštovné|zdarma|zadarmo)/', $text, $matches);

		if (count($matches[0]) > 2) {
			return true;
		}

		return false;
	}
}
