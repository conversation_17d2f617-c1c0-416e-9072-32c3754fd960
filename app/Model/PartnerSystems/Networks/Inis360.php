<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;

interface IInis360Factory
{
	public function create(PartnerSystem $partnerSystem): Inis360;
}

class Inis360 extends Network
{
	public const SOURCE = 'Tipli.pl';
	private const SECURE_CODE = 'gN0y2vx1c9S4enQcUWNso1pVM7cRPeak';
	private const AFFILIATE_LINK_URL = 'https://system.inis360.com/api-publisher/v1.0/programs/%s/downloads/affiliate-links?secureCode=%s';

	public function createRedirectionLink(string $url, $userId, $redirectionId, $deepUrl = null): string
	{
		if (preg_match('~.*/(\d+)$~', $url, $matches)) {
			$programId = $matches[1];
		} else {
			throw new \RuntimeException('ID not found in URL.');
		}

		$url = $this->getRedirectionLink($programId, $userId . 'x' . $redirectionId, $deepUrl);

		return (new Url($url))->getAbsoluteUrl();
	}

	public function getRedirectionLink(int $programId, string $subId, ?string $deepUrl = null)
	{
		$client = new Client();

		$url = sprintf(self::AFFILIATE_LINK_URL, $programId, self::SECURE_CODE);

		$response = $client->post($url, [
			'headers' => [
				'Accept' => 'application/json',
				'Content-Type' => 'application/json',
			],
			'json' => [
				'subId1' => $subId,
				'deepLink' => $deepUrl ?? null,
				'Source' => self::SOURCE,
			],
		]);

		$data = Json::decode((string) $response->getBody(), true);

		if (!isset($data['banners'][0]['link'])) {
			throw new \RuntimeException('No banner link found in the response.');
		}

		return $data['banners'][0]['link'];
	}
}
