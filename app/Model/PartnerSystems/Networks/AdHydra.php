<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Transactions\ImportedTransaction;

interface IAdHydraFactory
{
	/**
	 * @param PartnerSystem $partnerSystem
	 * @return AdHydra
	 */
	public function create(PartnerSystem $partnerSystem);
}

class AdHydra extends Network
{
	private const API_KEY = '7WlbJF5QUSAu+ZO58U6iWIDZf7nVedHS';
	private const API_URL = 'https://api.targetcircle.com/api/v1/transactions';

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);

		$url->setQueryParameter('source_id', $userId . 'x' . $redirectionId);
		$url->setQueryParameter('ref1', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$url->setQueryParameter('r', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$requiredColumns = ['touchpoint-status', 'currency', 'transaction-amount', 'payout', 'offer-sid', 'touchpointstatusid', 'source-name'];

		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('AdHydra import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $offset => $row) {
			if ($offset === 0) {
				continue;
			}

			$userId = null;

			if (Strings::contains($row['source-id'], 'x') !== false) {
				[$userId, $redirectionId] = explode('x', $row['source-id']);
			}

			if ($userId === null) {
				continue;
			}

			$status = $this->resolveStatus($row['touchpoint-status']);

			$transactionId = $row['transaction-id'];
			$partnerSystemKey = $row['offer-sid'];
			$commissionAmount = (float) $row['payout'];
			$orderAmount = (float) $row['transaction-amount'];
			$registeredAt = new \DateTime($row['transaction-saved-at']);

			$currency = $row['currency'];

			$confirmedAt = null;
			if ($status === ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime($row['touchpoint-validation-date']);
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			dumpe($record);
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$client = new Client();
		$response = $client->request('GET', self::API_URL, [
			'headers' => [
				'X-Api-Token' => self::API_KEY,
			],
			'query' => [
				'savedFrom' => $from->format('Y-m-d'),
				'savedTo' => $to->format('Y-m-d'),
				'limit' => 100,
			],
		]);

		$contents = $response->getBody()->getContents();

		$results = Json::decode($contents);

		return $results->data;
	}

	private function resolveStatus(string $status)
	{
		$status = Strings::lower($status);

		if (Strings::contains($status, 'approved')) {
			$status = 'approved';
		}

		switch ($status) {
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'rejected':
			case 'declined':
			case 'failed':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'approved':
			case 'confirmed':
			case 'invoiced':
			case 'approved, paid out':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}
}
