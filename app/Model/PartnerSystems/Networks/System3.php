<?php

namespace tipli\Model\PartnerSystems\Networks;

use DateTime;
use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Transactions\ImportedTransaction;

interface ISystem3Factory
{
	/**
	 * @return System3
	 */
	public function create(PartnerSystem $partnerSystem);
}

class System3 extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		$url->setQueryParameter('s2', $userId . 'x' . $redirectionId);

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize($item);
		}, $headers);

		$requiredColumns = ['conversion-id', 'date', 'sub-id-2', 'price', 'order-total', 'disposition', 'campaign'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('System3 import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$transactionId = $row['conversion-id'];
			$partnerSystemKey = Strings::webalize($row['campaign']);
			$userId = $row['sub-id-2'];
			$commissionAmount = (float) preg_replace('/[^0-9,]/', '', $row['price']);
			$orderAmount = (float) preg_replace('/[^0-9,]/', '', $row['order-total']);
			$registeredAt = DateTime::createFromFormat('d/n/y g:i:s A', $row['date']);
			//$confirmedAt = new \DateTime($row['vyrizena']);
			$currency = Currency::PLN;

			$status = $this->resolveStatus($row['disposition'], $registeredAt);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt ? : null, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$transactionId = $record->conversion_id;
			$partnerSystemKey = Strings::webalize($record->offer_name);
			$userId = $record->subid_2;
			$commissionAmount = (float) $record->price;
			$orderAmount = (float) $record->order_total;
			$registeredAt = new \DateTime($record->conversion_date);
			$currency = Currency::PLN;

			$status = $this->resolveStatus($record->disposition, $registeredAt);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$client = new Client();
		$response = $client->request('GET', 'https://login.system3.pl/affiliates/api/Reports/Conversions', [
			'query' => [
				//'page' => 0
				'start_date' => $from->format('Y-m-d') . 'T' . $to->format('H:i:s') . '.000Z',
				'end_date' =>  $to->format('Y-m-d') . 'T' . $to->format('H:i:s') . '.000Z',
				'start_at_row' => 1,
				'row_limit' => 100,
				'fields' => [
					'conversion_id', 'conversion_date',
				],
				'api_key' => $this->partnerSystem->getOption('apiKey'),
				'affiliate_id' => $this->partnerSystem->getOption('affiliateId'),
			],
		]);

		$contents = $response->getBody()->getContents();

		$responseData = Json::decode($contents);

		$results = $responseData->data;

		return $results;
	}

	private function resolveStatus($status, \DateTime $registeredAt)
	{
		if (clone $registeredAt->modify('+ 31 days') <= new DateTime()) {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		$status = Strings::webalize($status);

		switch ($status) {
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'rejected':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'returned':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}
}
