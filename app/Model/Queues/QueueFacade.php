<?php

namespace tipli\Model\Queues;

use Nette\SmartObject;
use tipli\Model\Queues\Entities\SqlQuery;
use tipli\Model\Queues\Producers\QueueSqlQueryProducer;
use tipli\Model\Queues\Repositories\SqlQueryRepository;

final class QueueFacade
{
	use SmartObject;

	public function __construct(
		private SqlQueryRepository $sqlQueryRepository,
		private SqlQueryProcessor $sqlQueryProcessor,
		private QueueSqlQueryProducer $queueSqlQueryProducer
	) {
	}

	public function findSqlQueriesToProcess(string $routing, int $limit = 5000): array
	{
		return $this->sqlQueryRepository->findSqlQueriesToProcess($routing, $limit);
	}

	public function scheduleCreateSqlQuery(
		string $query,
		?array $parameters = null,
		string $routing = SqlQuery::ROUTING_DEFAULT,
		int $priority = 100,
		?\DateTime $validTill = null
	): void {
		$this->queueSqlQueryProducer->scheduleCreateSqlQuery($query, $parameters, $routing, $priority, $validTill);
	}

	public function createSqlQuery(string $sqlQuery, ?array $parameters, string $routing, int $priority, ?\DateTime $validTill = null): void
	{
		$this->sqlQueryProcessor->createSqlQuery($sqlQuery, $parameters, $routing, $priority, $validTill);
	}

//	public function scheduleProcessSqlQuery(SqlQuery $sqlQuery): void
//	{
//		$this->queueSqlQueryProducer->scheduleProcessSqlQuery($sqlQuery->getId());
//
//		$this->sqlQueryProcessor->markSqlQueryAsScheduled($sqlQuery->getId(), new \DateTime());
//	}

//	public function markQueriesAsScheduled(array $sqlQueryIds): void
//	{
//		$this->sqlQueryProcessor->markQueriesAsScheduled($sqlQueryIds);
//	}

	public function processSqlQuery(SqlQuery $sqlQuery): void
	{
		$sqlQueryResult = $this->sqlQueryProcessor->processSqlQuery($sqlQuery);

		if ($sqlQueryResult->isCompleted()) {
			$this->sqlQueryProcessor->markSqlQueryAsCompleted($sqlQuery->getId(), $sqlQueryResult->getDateTime(), $sqlQueryResult->getConsumedTime());
//			$this->queueSqlQueryProducer->scheduleCompleteSqlQuery($sqlQuery->getId(), $sqlQueryResult->getDateTime(), $sqlQueryResult->getConsumedTime());
		} else {
			$this->sqlQueryProcessor->markSqlQueryAsFailed($sqlQuery->getId(), $sqlQueryResult->getDateTime(), $sqlQueryResult->getFailMessage(), $sqlQueryResult->getConsumedTime());
//			$this->queueSqlQueryProducer->scheduleFailSqlQuery($sqlQuery->getId(), $sqlQueryResult->getDateTime(), $sqlQueryResult->getFailMessage());
		}
	}

	public function markSqlQueryAsCompleted(int $sqlQueryId, \DateTime $dateTime, ?int $consumedTime = null): void
	{
		$this->sqlQueryProcessor->markSqlQueryAsCompleted($sqlQueryId, $dateTime, $consumedTime);
	}

	public function markSqlQueryAsFailed(int $sqlQueryId, \DateTime $dateTime, ?string $failMessage, ?int $consumedTime = null): void
	{
		$this->sqlQueryProcessor->markSqlQueryAsFailed($sqlQueryId, $dateTime, $failMessage, $consumedTime);
	}

	public function findSqlQuery(int $getSqlQueryId)
	{
		return $this->sqlQueryRepository->find($getSqlQueryId);
	}

	public static function now(): string
	{
		return (new \DateTime())->format('Y-m-d H:i:s');
	}
}
