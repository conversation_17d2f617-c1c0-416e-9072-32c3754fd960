<?php

namespace tipli\Model\Queues\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Queues\Repositories\SqlQueryRepository")
 * @ORM\Table(name="tipli_queues_sql_query", indexes={
 *     @ORM\Index(name="sql_query_identifier", columns={"sql_query_identifier"}),
 *     @ORM\Index(name="valid_till", columns={"valid_till"}),
 *     @ORM\Index(name="routing", columns={"routing"})
 * })
 */
class SqlQuery
{
	public const ROUTING_DEFAULT = 'default';
	public const ROUTING_STATS = 'stats';
	public const ROUTING_MAILCHIMP = 'mailchimp';
	public const ROUTING_MAILKIT = 'mailkit';
	public const ROUTING_SEGMENT_DATA = 'segment_data';
	public const ROUTING_METRICS = 'metrics';
	public const ROUTING_NOTIFICATIONS = 'notifications';
	public const ROUTING_EXPERIMENT_VARIANT = 'experiment_variant';
	public const ROUTING_TRANSACTIONS = 'transactions';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\Column(type="text")
	 */
	private string $sqlQuery;

	/**
	 * @ORM\Column(type="string", length=16)
	 */
	private ?string $sqlQueryIdentifier = null;

	/**
	 * @ORM\Column(type="string", length=2048)
	 */
	private ?string $parameters = null;

	/**
	 * @ORM\Column(type="string", length=32)
	 */
	private string $routing = self::ROUTING_DEFAULT;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $priority = 100;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $consumedTime = null; // in seconds

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $validTill = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $scheduledAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $completedAt = null;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $failedAt = null;

	/**
	 * @ORM\Column(type="string", length=512, nullable=true)
	 */
	private ?string $failMessage = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(
		string $sqlQuery,
		?array $parameters = null,
		string $routing = self::ROUTING_DEFAULT,
		int $priority = 100,
		?\DateTime $validTill = null
	) {
		$this->sqlQuery = $sqlQuery;
		$this->parameters = $parameters ? json_encode($parameters) : null;
		$this->routing = $routing;
		$this->priority = $priority;
		$this->validTill = $validTill;
		$this->createdAt = new DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getSqlQuery(): string
	{
		return $this->sqlQuery;
	}

	public function getParameters(): ?array
	{
		return $this->parameters ? json_decode($this->parameters, true) : null;
	}
}
