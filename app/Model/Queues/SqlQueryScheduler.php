<?php

namespace tipli\Model\Queues;

use Nette\Database\Context;
use tipli\Model\Account\Entities\User;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Queues\Entities\SqlQuery;
use tipli\Model\Redirections\Entities\Redirection;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Transactions\Entities\ImportedTransactionFail;

final class SqlQueryScheduler
{
	public function __construct(
		private QueueFacade $queueFacade,
		private Context $context,
	) {
	}

	public function createImportedTransactionFail(?PartnerSystem $partnerSystem, $transactionId, string $note)
	{
		$token = ImportedTransactionFail::generateToken($partnerSystem, $transactionId, $note);

		$this->queueFacade->scheduleCreateSqlQuery(
			'
			INSERT INTO tipli_transactions_imported_transaction_fail (partner_system_id, transaction_id, note, token, created_at)
			VALUES (
		        ' . ($partnerSystem?->getId()) . ',
		        "' . $transactionId . '",
		        "' . $note . '",
		        "' . $token . '",
		        "' . (new \DateTime())->format('Y-m-d H:i:s') . '"
		    ) ON DUPLICATE KEY UPDATE transaction_id = transaction_id
		',
			routing: SqlQuery::ROUTING_TRANSACTIONS
		);
	}

	public function updateUserDataOnRegisterTransaction($userId)
	{
		$countOfAddonTransactions = $this->context->query('SELECT COUNT(id) AS c FROM tipli_transactions_transaction t WHERE t.type = "bonus_addon" AND t.user_id=?', $userId)->fetch()->c;

		if ($countOfAddonTransactions > 0) {
			$this->queueFacade->scheduleCreateSqlQuery(' UPDATE tipli_account_segment_data SET addon_installed = 1 WHERE user_id = ' . $userId . '', [], SqlQuery::ROUTING_SEGMENT_DATA);
		}

		$firstTransactionAt = $this->context->query('SELECT MIN(t.registered_at) AS first_transaction_at FROM tipli_transactions_transaction t WHERE (t.type = "commission" OR t.type = "bonus_refund") AND t.user_id = ?', $userId)->fetch()->first_transaction_at;
		$lastTransactionAt = $this->context->query('SELECT MAX(t.registered_at) AS last_transaction_at FROM tipli_transactions_transaction t WHERE (t.type = "commission" OR t.type = "bonus_refund") AND t.user_id = ?', $userId)->fetch()->last_transaction_at;
		$countOfRecommendedUsers = $this->context->query('SELECT count(u.id) AS c FROM tipli_account_user u WHERE u.parent_id = ?', $userId)->fetch()->c;
		$lastRecommendedUserAt = $this->context->query('SELECT MAX(u.created_at) AS last_recommended_user_at FROM tipli_account_user u WHERE u.parent_id = ?', $userId)->fetch()->last_recommended_user_at;
		$hasMoneyRewardBonus = $this->context->query('SELECT DISTINCT t.user_id AS user_id FROM tipli_transactions_transaction t WHERE t.type = "bonus_money_reward" AND t.user_id=?', $userId)->fetch();
		$hasMoneyRewardBonus = $hasMoneyRewardBonus && $hasMoneyRewardBonus->user_id !== null;

		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data SET first_transaction_at = ? WHERE user_id = ' . $userId . '', [$firstTransactionAt?->format('Y-m-d H:i:s')], SqlQuery::ROUTING_SEGMENT_DATA, 200);
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data SET last_transaction_at = ? WHERE user_id = ' . $userId . '', [$lastTransactionAt?->format('Y-m-d H:i:s')], SqlQuery::ROUTING_SEGMENT_DATA, 200);
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data SET count_of_recommended_users = ? WHERE user_id = ' . $userId . '', [$countOfRecommendedUsers], SqlQuery::ROUTING_SEGMENT_DATA, 200);
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data SET last_recommended_user_at = ? WHERE user_id = ' . $userId . ' ', [$lastRecommendedUserAt?->format('Y-m-d H:i:s')], SqlQuery::ROUTING_SEGMENT_DATA, 200);

		if ($hasMoneyRewardBonus) {
			$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data SET has_money_reward_bonus = 1 WHERE user_id = ' . $userId . ' ', [], SqlQuery::ROUTING_SEGMENT_DATA, 200);
		}

		// updateLifetimeCommissionAmount
		$this->queueFacade->scheduleCreateSqlQuery('
          UPDATE tipli_account_segment_data d
          INNER JOIN (
            SELECT t.user_id AS user_id, SUM(t.commission_amount) AS total_amount, (SUM(t.user_commission_amount) + SUM(t.bonus_amount)) as total_user_amount
            FROM tipli_transactions_transaction t
            WHERE t.type != \'payout\'
            GROUP BY t.user_id
          ) data ON data.user_id = d.user_id
          SET lifetime_commission_amount = total_amount, lifetime_user_commission_amount = total_user_amount
          WHERE data.user_id = ' . $userId . ';
        ', [], SqlQuery::ROUTING_SEGMENT_DATA);

		// updateAccountBalance
		$this->updateUserAccountBalance($userId);

		// number of purhcases
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_account_segment_data s
            INNER JOIN (
              SELECT COUNT(dateCreated) AS cnt, t_user_id
              FROM (
                SELECT DATE(t.created_at) AS dateCreated, t.user_id AS t_user_id
                FROM tipli_transactions_transaction t
                WHERE t.shop_id IS NOT NULL AND t.type IN (\'commission\', \'commission_erabat\', \'bonus_refund\')
                GROUP BY t.shop_id, dateCreated, t_user_id
              ) purchases
              GROUP BY t_user_id
            ) number_of_purchases ON number_of_purchases.t_user_id = s.user_id
            SET s.number_of_purchases = number_of_purchases.cnt
            WHERE s.user_id = ' . $userId . '
        ', [], SqlQuery::ROUTING_SEGMENT_DATA, 90);
	}

	public function updateUserAccountBalance(int $userId)
	{
		$confirmedBalance = $this->context->query('SELECT IFNULL((SUM(t.user_commission_amount) + SUM(t.bonus_amount)), 0) AS balance FROM tipli_transactions_transaction t WHERE t.confirmed_at IS NOT NULL AND t.billable = true AND t.user_id = ?', $userId)->fetch()->balance;
		$registeredBalance = $this->context->query('SELECT IFNULL(SUM(t.user_commission_amount), 0) AS balance FROM tipli_transactions_transaction t WHERE t.confirmed_at IS NULL AND t.billable = true AND t.user_id = ?', $userId)->fetch()->balance;
		$bonusBalance = $this->context->query('SELECT IFNULL(SUM(t.bonus_amount), 0) AS balance FROM tipli_transactions_transaction t WHERE t.confirmed_at IS NULL AND t.billable = true AND t.user_id = ?', $userId)->fetch()->balance;

		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_segment_data d SET d.confirmed_balance = ?, d.registered_balance = ?, d.bonus_balance = ? WHERE d.user_id = ' . $userId . '', [$confirmedBalance, $registeredBalance, $bonusBalance], SqlQuery::ROUTING_SEGMENT_DATA);
	}

	public function setUserActiveSegmet(int $userId)
	{
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_user SET segment = "active" WHERE id = ' . $userId . '');
	}

	public function scheduleUpdateLastWebVisitAt($userId, ?\DateTime $dateTime = null)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_account_segment_data d
            SET d.last_web_visit_at = "' . (($dateTime ?: new \DateTime())->format('Y-m-d H:i:s')) . '"
			WHERE d.user_id = ' . $userId . '
        ', [], SqlQuery::ROUTING_SEGMENT_DATA);
	}

	public function scheduleUpdateLastShopRedirectionAt($userId, ?\DateTime $dateTime = null)
	{
		$dateTimeFormatted = (($dateTime ?: new \DateTime())->format('Y-m-d H:i:s'));
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_account_segment_data d
            SET d.last_shop_redirection_at = "' . $dateTimeFormatted . '"
			WHERE d.user_id = ' . $userId . ' AND (d.last_shop_redirection_at IS NULL OR d.last_shop_redirection_at < "' . $dateTimeFormatted . '")
        ', [], SqlQuery::ROUTING_SEGMENT_DATA);
	}

	public function scheduleUpdateLastMobileAppVisitAt($userId, ?\DateTime $dateTime = null)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_account_segment_data d
            SET d.last_mobile_app_visit_at = "' . (($dateTime ?: new \DateTime())->format('Y-m-d H:i:s')) . '"
			WHERE d.user_id = ' . $userId . '
        ', [], SqlQuery::ROUTING_SEGMENT_DATA);
	}

	public function updateUserAnalyze($userId)
	{
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_account_user_synchronization SET analyze_at = CURRENT_TIMESTAMP() WHERE user_id = ' . $userId);
	}

	public function updateUserShareCoefficient($userId)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
		UPDATE tipli_account_segment_data sd
		SET share_coefficient = IFNULL(
		(
		SELECT MAX(r.share_coefficient)
			FROM tipli_rewards_share_reward r
			WHERE r.user_id IS NOT NULL
			AND r.user_id = sd.user_id
			AND r.valid_since <= NOW()
			AND r.valid_till >= NOW()
			AND r.shop_id IS NULL
			AND r.id NOT IN (SELECT s.share_reward_id FROM tipli_rewards_share_reward_shops s)
		), 0.5)
		WHERE sd.user_id = ' . $userId . '
		', [], SqlQuery::ROUTING_SEGMENT_DATA, 200);
	}

	public function updateTransactionProcess($transactionId, string $column)
	{
		$column .= '_processed_at';
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_transactions_transaction_process SET ' . $column . '="' . (new \DateTime())->format('Y-m-d H:i:s') . '" WHERE transaction_id=' . $transactionId,
			routing: SqlQuery::ROUTING_TRANSACTIONS
		);
	}

	public function scheduleSynchronizeRefundTicket(Refund $refund)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_refunds_refund r
            SET r.user_last_response_at = (
                SELECT MAX(m.message_created_at)
                FROM tipli_freshdesk_message m
                WHERE m.ticket_id = r.freshdesk_ticket_id
                AND m.is_incoming = 1
            ) WHERE r.id = ' . $refund->getId() . '
        ');

		$this->queueFacade->scheduleCreateSqlQuery('
            UPDATE tipli_refunds_refund r
            INNER JOIN tipli_freshdesk_ticket t ON t.id = r.freshdesk_ticket_id
            SET r.operator_last_response_at = (
                SELECT MAX(m.message_created_at)
                FROM tipli_freshdesk_message m
                WHERE m.ticket_id = r.freshdesk_ticket_id
                AND m.is_incoming = 0
                AND DATE_FORMAT(m.message_created_at, \'%Y-%m-%d %H:%i\') > DATE_FORMAT(t.opened_at, \'%Y-%m-%d %H:%i\')
            ) WHERE r.id = ' . $refund->getId() . '
        ');
	}

	public function updateProductImageStateByProductChecker(int $productId, bool $isValid)
	{
		if ($isValid) {
			$this->queueFacade->scheduleCreateSqlQuery('
				UPDATE tipli_products_product
				SET count_of_image_check_errors = 0,
				    product_check_at = "' . (new \DateTime('+ 48 hours'))->format('Y-m-d H:i:s') . '"
				WHERE id = ' . $productId . '
			', []);
		} else {
			$this->queueFacade->scheduleCreateSqlQuery('
				UPDATE tipli_products_product
				SET count_of_image_check_errors = (count_of_image_check_errors + 1),
				    product_check_at =
				        CASE
				            WHEN count_of_image_check_errors > 2
				                THEN NULL
				            ELSE "' . (new \DateTime('+ 1 hour'))->format('Y-m-d H:i:s') . '"
				        END
				WHERE id = ' . $productId . '
			', []);
		}
	}

	public function redirectionTrackClick(Redirection $redirection)
	{
		$this->queueFacade->scheduleCreateSqlQuery('UPDATE tipli_redirections_redirection SET count_of_clicks = count_of_clicks + 1 WHERE id = ' . $redirection->getId());
	}

	public function createFixTransaction($transactionId, $oldUserCommissionAmount, $newUserCommissionAmount, $isPaid, $currency)
	{
		$this->queueFacade->scheduleCreateSqlQuery('INSERT INTO transaction_fix (transaction_id, old_user_commission_amount, new_user_commission_amount, is_paid, currency, created_at)
		VALUES (
		  ' . $transactionId . ',
		  ' . $oldUserCommissionAmount . ',
		  ' . $newUserCommissionAmount . ',
		  ' . ($isPaid ? 1 : 0) . ',
		  "' . $currency . '",
		  NOW()
		) ON DUPLICATE KEY UPDATE transaction_id = transaction_id');
	}

	public function logCjLockTransaction(int $partnerSystemId, string $transactionId, \DateTime $lockedDate)
	{
		$this->queueFacade->scheduleCreateSqlQuery('
            INSERT INTO transaction_cj_lock (transaction_id, locked_date)
            SELECT t.id, "' . $lockedDate->format('Y-m-d H:i:s') . '" as locked_date
            FROM tipli_transactions_transaction t
            WHERE t.partner_system_id = ' . $partnerSystemId . '
            AND t.transaction_id = "' . $transactionId . '"
            ON DUPLICATE KEY UPDATE locked_date = locked_date
        ');
	}

	public function trackOpenedAt(User $user, \DateTime $dateTime)
	{
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_account_segment_data SET opened_at = ? WHERE user_id = ? AND (opened_at < ? OR opened_at IS NULL)',
			[$dateTime->format('Y-m-d H:i:s'), $user->getId(), $dateTime->format('Y-m-d H:i:s')],
			SqlQuery::ROUTING_SEGMENT_DATA
		);
	}

	public function trackClickedAt(User $user, \DateTime $dateTime)
	{
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_account_segment_data SET clicked_at = ? WHERE user_id = ? AND (clicked_at < ? OR clicked_at IS NULL)',
			[$dateTime->format('Y-m-d H:i:s'), $user->getId(), $dateTime->format('Y-m-d H:i:s')],
			SqlQuery::ROUTING_SEGMENT_DATA
		);
	}

	public function trackAddonFeedDownloadedAt(User $user, \DateTime $dateTime)
	{
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_account_segment_data SET addon_feed_downloaded_at = ? WHERE user_id = ? AND (addon_feed_downloaded_at < ? OR addon_feed_downloaded_at IS NULL)',
			[$dateTime->format('Y-m-d H:i:s'), $user->getId(), $dateTime->format('Y-m-d H:i:s')],
			SqlQuery::ROUTING_SEGMENT_DATA
		);
	}

	public function trackLastLoggedInAt(User $user, \DateTime $dateTime)
	{
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_account_segment_data SET last_logged_in_at = ? WHERE user_id = ? AND (last_logged_in_at < ? OR last_logged_in_at IS NULL)',
			[$dateTime->format('Y-m-d H:i:s'), $user->getId(), $dateTime->format('Y-m-d H:i:s')],
			SqlQuery::ROUTING_SEGMENT_DATA
		);
	}

	public function markNotificationAsPushed(int $notificationId): void
	{
		$this->queueFacade->scheduleCreateSqlQuery(
			'UPDATE tipli_inbox_notification SET pushed_at = ? WHERE id = ?',
			[(new \DateTime())->format('Y-m-d H:i:s'), $notificationId],
			SqlQuery::ROUTING_NOTIFICATIONS
		);
	}
}
