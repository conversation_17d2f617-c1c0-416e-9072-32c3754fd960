<?php

namespace tipli\Model\Queues;

use Nette\Database\Context;
use Nette\Utils\Strings;
use tipli\Model\Queues\Entities\SqlQuery;

final class SqlQueryProcessor
{
	public function __construct(private Context $context)
	{
	}

	public function createSqlQuery(string $sqlQuery, ?array $parameters = null, string $routing = SqlQuery::ROUTING_DEFAULT, int $priority = 100, ?\DateTime $validTill = null): void
	{
		$this->context->query(
			'INSERT INTO tipli_queues_sql_query (sql_query, sql_query_identifier, parameters, routing, priority, valid_till, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
			trim($sqlQuery),
			Strings::substring(md5(preg_replace('/[0-9]+/', '', $sqlQuery)), 0, 16),
			$parameters ? json_encode($parameters) : null,
			$routing,
			$priority,
			$validTill,
			new \DateTime()
		);
	}

	public function processSqlQuery(SqlQuery $sqlQuery): SqlQueryResult
	{
		$startTime = time();

		try {
			if ($sqlQuery->getParameters() !== null) {
				$this->context->query($sqlQuery->getSqlQuery(), ...$sqlQuery->getParameters());
			} else {
				$this->context->query($sqlQuery->getSqlQuery());
			}
		} catch (\Exception $e) {
			return new SqlQueryResult(SqlQueryResult::RESULT_FAILED, new \DateTime(), time() - $startTime, $e->getMessage());
		}

		return new SqlQueryResult(SqlQueryResult::RESULT_COMPLETED, new \DateTime(), time() - $startTime);
	}

	public function markSqlQueryAsScheduled(int $sqlQueryId, \DateTime $dateTime)
	{
		$this->context->query('UPDATE tipli_queues_sql_query SET scheduled_at = ? WHERE id = ?', $dateTime, $sqlQueryId);
	}

	public function markSqlQueryAsCompleted(int $sqlQueryId, \DateTime $dateTime, ?int $consumedTime = null)
	{
		$this->context->query('UPDATE tipli_queues_sql_query SET completed_at = ?, consumed_time = ? WHERE id = ?', $dateTime, $consumedTime, $sqlQueryId);
	}

	public function markSqlQueryAsFailed(int $sqlQueryId, \DateTime $dateTime, ?string $failMessage, ?int $consumedTime = null)
	{
		$this->context->query('UPDATE tipli_queues_sql_query SET failed_at = ?, consumed_time = ?, fail_message = ? WHERE id = ?', $dateTime, $consumedTime, $failMessage, $sqlQueryId);
	}

//	public function markQueriesAsScheduled(array $sqlQueryIds)
//	{
//		$this->context->query('UPDATE tipli_queues_sql_query SET scheduled_at = ? WHERE id IN (?)', new \DateTime(), $sqlQueryIds);
//	}
}
