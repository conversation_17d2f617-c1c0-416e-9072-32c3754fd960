<?php

namespace tipli\ApiModule\MobileModule\Presenters;

use Nette\Utils\DateTime;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Account\AccountNumberValidator;
use tipli\Model\Account\DeviceTokenFacade;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserData;
use tipli\Model\Account\PhoneNumberVerificationManager;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Mobile\UserObject;
use tipli\TooManyRequestsException;
use Tracy\Debugger;

class UserPresenter extends BasePresenter
{
	use TRequireUserAuthorization;

	/** @var array */
	private const ALLOWED_PHONE_NUMBER_COUNTRY_CODES = ['+421', '+420', '+48', '+40', '+36', '+359', '+385', '+386'];

	/** @var MessageFacade @inject */
	public $messageFacade;

	/** @var DeviceTokenFacade @inject */
	public DeviceTokenFacade $deviceTokenFacade;

	/** @var AccountNumberValidator @inject */
	public $accountNumberValidator;

	/** @var PhoneNumberVerificationManager @inject */
	public $phoneNumberVerificationManager;

	public function actionDeviceToken()
	{
		$this->requireMethod(self::METHOD_POST);
		$this->requireDataParameters(['token', 'platform']);

		//Debugger::log(json_encode($this->getHttpRequest()->getHeaders()), 'device-token-headers');

		try {
			$user = $this->getUserFromRequest();

			if ($user->getEmail() === '<EMAIL>') {
				Debugger::log($this->getRequestData('token'), 'action-device-token');
			}

			if ($this->getHttpRequest()->getHeader('x-app-version') !== null) {
				$version = $this->getHttpRequest()->getHeader('x-app-version');
			} else {
				$version = null;
			}

			$this->deviceTokenFacade->scheduleCreateDeviceToken($user, $this->getRequestData('token'), $this->getRequestData('platform'), $version);

			$this->sendSuccess();
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	public function actionMe()
	{
		$this->requireMethod(self::METHOD_GET);

		$this->sendUserObject();
	}

	public function actionPassword()
	{
		$this->requireMethod(self::METHOD_POST);
		$this->requireDataParameters(['newPassword']);

		try {
			$user = $this->getUserFromRequest();
			$data = $this->getRequestData();

			if (isset($data->oldPassword) && !empty($data->oldPassword)) {
				if ($user->hasFilledPassword() && !$this->userFacade->validatePassword($user, $data->oldPassword)) {
					throw new InvalidArgumentException('Old password is incorrect');
				}
			}

			if (strlen($data->newPassword) < User::MINIMAL_PASSWORD_LENGTH) {
				throw new InvalidArgumentException('Minimium password length ' . User::MINIMAL_PASSWORD_LENGTH . ' characters not been reached');
			}

			$this->userFacade->setPassword($user, $data->newPassword);
			$this->userFacade->saveUser($user);

			$this->sendUserObject();
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	public function actionName()
	{
		$this->requireMethod(self::METHOD_POST);

		$user = $this->getUserFromRequest();

		$this->requireDataParameters(['firstName', 'lastName']);

		$user->setFirstName($this->getRequestData('firstName'));
		$user->setLastName($this->getRequestData('lastName'));

		$this->userFacade->saveUser($user);

		$this->sendUserObject();
	}

	public function actionBirthdate()
	{
		$this->requireMethod(self::METHOD_POST);

		$user = $this->getUserFromRequest();

		$this->requireDataParameters(['birthdate']);
		$birthdate = $this->getRequestData('birthdate');

		if ($birthdate !== null) {
			try {
				$birthdate = new \DateTime($birthdate);
			} catch (\Exception $e) {
				$this->sendError('Invalid birthdate format.');
			}
		}

		$user->setBirthDate($birthdate);

		$this->userFacade->saveUser($user);

		$this->sendUserObject();
	}

	public function actionGender()
	{
		$this->requireMethod(self::METHOD_POST);

		try {
			$user = $this->getUserFromRequest();
			$data = $this->getRequestData();
			$gender = $data->gender;

			if (empty($gender)) {
				$user->setGender(null);
			} elseif (in_array($gender, [UserData::GENDER_FEMALE, UserData::GENDER_MALE])) {
				$user->setGender($gender);
			} else {
				throw new InvalidArgumentException('Invalid gender');
			}

			$this->userFacade->saveUser($user);

			$this->sendUserObject();
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	public function actionAccountNumber()
	{
		$this->requireMethod(self::METHOD_POST);

		try {
			$user = $this->getUserFromRequest();

			$this->requireDataParameters(['accountNumber']);

			$accountNumber = $this->getRequestData('accountNumber');
			$accountNumber = Strings::upper(Strings::replace(trim($accountNumber, '-'), '~\s+~u', ''));

			if (!empty($accountNumber) && !$this->accountNumberValidator->checkAccountNumber($user->getLocalization(), $accountNumber)) {
				Debugger::log('Pokus o zadání neplatného čísla účtu : ' . $accountNumber . ', userId:' . $user->getId() . ' z mobilni aplikace #biv4', 'invalid-account-number');
				throw new InvalidArgumentException(
					$this->translator->translate('front.account.user.settings.form.validator.accountNumber')
				);
			}

//			if ($user->isCzech()) {
//				if ((strlen(implode(preg_split("/\d{0,6}\-{0,1}\d{3,16}\/\d{4}/", $accountNumber))) != 0) &&
//					!(Strings::startsWith($accountNumber, 'SK') && ((UserFacade::isIbanValid($accountNumber)) == true)))
//				{
//					throw new InvalidArgumentException(
//						$this->translator->translate('front.account.user.settings.form.validator.accountNumber')
//					);
//				}
//			} else if ($user->isSlovak()) {
//				if (!(Strings::startsWith($accountNumber, 'SK') && ((UserFacade::isIbanValid($accountNumber)) == true)) &&
//					(strlen(implode(preg_split("/\d{0,6}\-{0,1}\d{3,16}\/\d{4}/", $accountNumber))) != 0)) {
//					throw new InvalidArgumentException(
//						$this->translator->translate('front.account.user.settings.form.validator.accountNumber'));
//				}
//			} else if ($user->isPolish()) {
//				if ((strlen(implode(preg_split("/[0-9\s]*/", $accountNumber))) != 0)) { //zde povolime pouze PL ucty
//					throw new InvalidArgumentException(
//						$this->translator->translate('front.account.user.settings.form.validator.iban'));
//				}
//			}

			$needVerification = !empty($accountNumber) && (($accountNumber !== $user->getAccountNumber()) || !$user->hasVerifiedAccountNumber());

			if (empty($accountNumber)) {
				$user->setAccountNumber(null);
				$this->userFacade->saveUser($user, false);
			} elseif ($this->configuration->getMode() === 'test') {
				$user->setAccountNumber($accountNumber);
				$this->userFacade->saveUser($user, false);
			} elseif ($needVerification) {
				$change = $this->userFacade->createChange($user, null, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent(), true);
				$change->setAccountNumber($accountNumber);
				$this->userFacade->saveChange($change);

				$user->setAccountNumber($accountNumber);
				$user->requestAccountNumberVerification();
				$this->userFacade->saveUser($user);

				$this->messageFacade->sendAccountNumberVerificationEmail($user);
			}

			$this->userFacade->saveUser($user);

			$this->sendUserObject();
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	public function actionPhoneNumber()
	{
		$this->requireMethod(self::METHOD_POST);

		try {
			$user = $this->getUserFromRequest();
			$oldPhoneNumber = $user->getPhoneNumber();

			$this->requireDataParameters(['phoneNumber', 'phoneCountryCode']);

			if ($this->getRequestData('phoneNumber') && !in_array($this->getRequestData('phoneCountryCode'), self::ALLOWED_PHONE_NUMBER_COUNTRY_CODES)) {
				throw new InvalidArgumentException('Invalid phone number country code!');
			}

			if (empty($this->getRequestData('phoneNumber'))) {
				$user->clearPhoneNumber();
			} else {
				$phoneNumberCountryCode = $this->getRequestData('phoneCountryCode');
				$user->setPhoneNumber($phoneNumberCountryCode, $this->getRequestData('phoneNumber'));
			}

			$this->userFacade->saveUser($user);

//			if ($this->getRequestData('phoneNumber') && $this->getParameter('test')) { // @todo test cleevio, pak smazat
//				$this->userFacade->verifyUserPhoneNumber($user);
//
//				$this->sendData(['isVerificationNeeded' => false]);
//			}

			$needPhoneNumberVerification = $user->getPhoneNumber() && (($oldPhoneNumber !== $user->getPhoneNumber()) || !$user->hasVerifiedPhoneNumber());

			if ($needPhoneNumberVerification === true && ($user->isSlovenian() || Strings::startsWith($user->getPhoneNumber(), '+386'))) {
				$userWithVerifiedPhoneNumber = $this->userFacade->findUserWithVerifiedPhoneNumber($user->getPhoneNumber());

				if ($userWithVerifiedPhoneNumber !== null) {
					$user->verifyPhoneNumber();
					$this->userFacade->saveUser($user);

					$this->sendData([
						'isPhoneNumberVerified' => $user->hasVerifiedPhoneNumber(),
						'phoneNumber' => $user->getPhoneNumberWithoutCountryCode(),
						'phoneCountryCode' => $user->getPhoneNumberCountryCode(),
					]);
				}
			}

			if ($needPhoneNumberVerification || !$user->getPhoneNumber()) {
				$this->userFacade->cancelPhoneNumberVerification($user);

				if ($user->getPhoneNumber()) {
					$this->phoneNumberVerificationManager->requestVerification($user, true, $this->clientLayer->getIp(), $this->clientLayer->getPlatform());
				} else {
					$this->sendData(['isVerificationNeeded' => false]);
				}
			}

			$this->sendData([
				'isPhoneNumberVerified' => $user->hasVerifiedPhoneNumber(),
				'phoneNumber' => $user->getPhoneNumberWithoutCountryCode(),
				'phoneCountryCode' => $user->getPhoneNumberCountryCode(),
			]);
		} catch (TooManyRequestsException $e) {
			$this->sendError($e->getMessage(), 429);
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	public function actionPhoneNumberVerifyCode()
	{
		$this->requireMethod(self::METHOD_POST);

		try {
			$user = $this->getUserFromRequest();

			$this->requireDataParameters(['phoneCountryCode', 'phoneNumber', 'code']);

			$phoneNumberCountryCode = $this->getRequestData('phoneCountryCode');
			$phoneNumber = $phoneNumberCountryCode . ' ' . $this->getRequestData('phoneNumber');
			$code = $this->getRequestData('code');

			if (!empty($code) && $verificationCode = $this->phoneNumberVerificationManager->findValidVerificationCode($user, $phoneNumber, $code)) {
				$this->phoneNumberVerificationManager->useVerificationCode($verificationCode);
				$user->setPhoneNumberVerifiedAt(new DateTime());
				$this->userFacade->saveUser($user);

				$this->sendUserObject();
			} else {
				$this->sendError('Invalid verification code');
			}
		} catch (InvalidArgumentException $e) {
			$this->sendError($e->getMessage());
		}
	}

	private function sendUserObject()
	{
		/** @var UserObject $userObject */
		$userObject = $this->objectFactory->createObject($this->getUserFromRequest());
		$userObject->setMobileSession($this->mobileSession);

		$this->sendData($userObject->toArray());
	}
}
