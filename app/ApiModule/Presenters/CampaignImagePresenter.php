<?php

namespace tipli\ApiModule\Presenters;

use Nette\Utils\Image;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Messages\ImageGenerator;

class CampaignImagePresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var ImageGenerator @inject */
	public $imageGenerator;

	public function actionCampaignStart($v = 1)
	{
		$this->imageGenerator->getImage(
			ImageGenerator::TYPE_CAMPAIGN_START,
			(int) $v === 1 ? 1 : 2
		)->send(Image::PNG);

		die();
	}

	public function actionCampaignFinish($v = 1)
	{
		$this->imageGenerator->getImage(
			ImageGenerator::TYPE_CAMPAIGN_FINISH,
			(int) $v === 1 ? 1 : 2
		)->send(Image::PNG);

		die();
	}
}
