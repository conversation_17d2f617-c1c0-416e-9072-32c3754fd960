<?php

namespace tipli\ApiModule\Presenters;

use Nette\Localization\Translator;
use Nette\Application\Responses\JsonResponse;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\Entities\Shop;

class RewardPresenter extends BasePresenter
{
	/** @var UtmLayer @inject */
	public $utmLayer;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var Translator @inject */
	public $translator;

	/** @var Storage @inject */
	public $storage;

	public function renderReward(?Shop $shop = null)
	{
		if ($shop === null) {
			$this->sendResponse(new JsonResponse([]));
		}

		$cache = new Cache($this->storage, self::class);
		$cacheKey = $this->getCashbackCacheKey();

		if ($cacheKey) {
			$cacheKey .= $shop->getId();

			if ($data = $cache->load($cacheKey)) {
				#$this->sendResponse(new JsonResponse($data));
			}
		}

		$data = $this->rewardFilter->__invoke($shop, false, 'array');

		if (is_array($data) && !$shop->isPaused() && $shop->isCashbackActive()) {
			$data['cashback'] = true;
			$data['slug'] = $shop->getSlug();
		} else {
			$countOfLeaflets = $shop->getCountOfLeaflets();
			$countOfCouponDeals = $shop->getCountOfCouponDeals();
			$countOfDeals = $shop->getCountOfDeals() - $countOfCouponDeals;

			$data = [
				'cashback' => false,
				'slug' => $shop->getSlug(),
				'countOfLeaflets' => $countOfLeaflets,
				'countOfCouponDeals' => $countOfCouponDeals,
				'countOfDeals' => $countOfDeals,
			];
		}

		if ($cacheKey) {
			$cache->save($cacheKey, $data, [Cache::EXPIRE => '4 hours']);
		}

		$this->sendResponse(new JsonResponse($data));
	}
}
