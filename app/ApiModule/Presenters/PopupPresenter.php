<?php

namespace tipli\ApiModule\Presenters;

use tipli\Model\Layers\BrowserTokenLayer;
use tipli\Model\Popups\Entities\PopupInteraction;
use tipli\Model\Popups\PopupFacade;

class PopupPresenter extends BasePresenter
{
	public const INTERACTION_EVENT_CLOSE = 'close';

	/** @var PopupFacade @inject */
	public $popupFacade;

	/** @var BrowserTokenLayer @inject */
	public $browserTokenLayer;

	public function renderInteraction($id, $event)
	{
		/** @var PopupInteraction|null $popupInteraction */
		$popupInteraction = $id ? $this->popupFacade->findPopupInteraction($id) : null;

		if (
			$event !== self::INTERACTION_EVENT_CLOSE
			|| !$popupInteraction
			|| ($this->getUser()->isLoggedIn() && $popupInteraction->getUser() !== $this->getUserIdentity())
			|| (!$this->getUser()->isLoggedIn() && $popupInteraction->getBrowserToken() !== $this->browserTokenLayer->getBrowserToken())
		) {
			$this->sendJson(['status' => 'ok']);
		}

		if ($event === self::INTERACTION_EVENT_CLOSE && !$popupInteraction->isClosed()) {
			$this->popupFacade->trackPopupClose($popupInteraction);
		}

		$this->sendJson(['status' => 'ok']);
	}
}
