<?php

namespace tipli\ApiModule\AddonModule\Presenters;

use Nette\Application\LinkGenerator;
use Nette\Localization\Translator;
use tipli\Model\Addon\AlternativeOfferFacade;
use tipli\Model\Addon\Entities\AlternativeOffer;
use tipli\Model\Addon\Entities\AlternativeOfferShop;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Transactions\TransactionFacade;

class AlternativeOfferPresenter extends BasePresenter
{
	/** @var AlternativeOfferFacade @inject */
	public AlternativeOfferFacade $alternativeOfferFacade;

	/** @var Translator @inject */
	public Translator $translator;

	/** @var ShopFacade @inject */
	public ShopFacade $shopFacade;

	/** @var TransactionFacade @inject */
	public TransactionFacade $transactionFacade;

	/** @var LinkGenerator @inject */
	public LinkGenerator $linkGenerator;

	public function actionDefault(string $id)
	{
		$this->requireMethod(self::METHOD_GET);

		$alternativeOffer = $this->alternativeOfferFacade->findByUniqueId($id);

		if (!$alternativeOffer) {
			$this->sendError('no alternative offers', 404);
		}

		$data = [];

		$data['popupType'] = $alternativeOffer->getPopupType();
		$data['popup']['title'] = $alternativeOffer->getTitle();

		if ($alternativeOffer->getPopupType() === AlternativeOffer::POPUP_TYPE_SHOPS_LIST) {
			$data['popup']['sadText'] = $alternativeOffer->getSadText();
			$data['popup']['shopLogoUrl'] = $alternativeOffer->getShop() ? $this->imageFilter->__invoke($alternativeOffer->getShop()->getLogo(), 300) : null;
			$data['popup']['shops'] = $this->getShopsData($alternativeOffer);

			if ($alternativeOffer->getBottomTitle() !== null && $alternativeOffer->getBottomUrl() !== null) {
				$data['popup']['bottomButton'] = ['title' => $alternativeOffer->getBottomTitle(), 'url' => $alternativeOffer->getBottomUrl()];
			}
		}

		if ($alternativeOffer->getPopupType() === AlternativeOffer::POPUP_TYPE_BETTER_OFFER) {
			if ($alternativeOffer->getBetterShop() && $alternativeOffer->getBetterShop()->isHidePromoAfterFirstTransaction()) {
				$user = $this->getUserIdentity();

				if ($user && count($this->transactionFacade->findTransactionsByShopForUser($alternativeOffer->getBetterShop(), $user)) > 0) {
					$this->sendData([]);
				}
			}

			$data['popup']['description'] = $alternativeOffer->getDescription();
			$data['popup']['shopLogoUrl'] = $alternativeOffer->getBetterShop() ? $this->imageFilter->__invoke($alternativeOffer->getBetterShop()->getLogo(), 300) : null;

			if ($alternativeOffer->getChallengeTitle() !== null && $alternativeOffer->getChallengeButtonTitle() !== null) {
				$data['popup']['challenge'] = ['title' => $alternativeOffer->getChallengeTitle(), 'button' => $alternativeOffer->getChallengeButtonTitle()];
			} else {
				$data['popup']['challenge'] = null;
			}

			$data['popup']['reward'] = [
				'offer' => $this->rewardFilter->__invoke($alternativeOffer->getBetterShop(), false, 'pure', $this->getUserIdentity()),
				'description' => $alternativeOffer->getBetterShop()->getShopData()->getRewardLabel() ?
					$alternativeOffer->getBetterShop()->getShopData()->getRewardLabel() :
					$this->translator->translate('model.shops.rewardFilter.suffixExtended'),
			];

			if ($alternativeOffer->getBottomButtonTitle() !== null) {
				$url = $this->linkGenerator->link('NewFront:Shops:Shop:default', [
					'shop' => $alternativeOffer->getBetterShop(),
					'shortcut' => 1,
					'fromAddon' => true,
					'deepUrl' => $alternativeOffer->getBetterShopDeepUrl() ? $alternativeOffer->getBetterShopDeepUrl() : null,
					'utm_source' => 'addon',
					'utm_medium' => 'alternative-offers',
					'utm_campaign' => $alternativeOffer->getUrlMask(),
					'utm_content' => $alternativeOffer->getBetterShop()->getSlug(),
				]);

				$data['popup']['bottomButton'] = ['title' => $alternativeOffer->getBottomButtonTitle(), 'url' => $url];
			}
		}

		$this->sendData($data);
//		if ($id === 'onec4u-tz1g8x-ko57p7') {
//			$this->sendData([
//				'popupType' => 'shopsList',
//				'popup' => [
//					'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/8FXmvTRjOq-88iRJRlSrWR3qLJH5x8wd2HHfcPcLw1I/resize:fit:340:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzcxNzM2LnBuZw.png',
//					'sadText' => 'Datart nenabizi pro televize cashback.',
//					'title' => 'V těchto obchodech koupíte ale telku s cashbackem.',
//					'shops' => [
//						[
//							'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/5btbLLFVfZ4UeGhYBWM_kd2zdOUcZbS0JWJ0io-0yQ4/resize:fit:200:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzkzNDc5LnBuZw.png',
//							'reward' => '500 Kč',
//							'redirectUrl' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress/',
//						],
//						[
//							'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/9apupWxK2GP0t6pCScdxlop81sPRwHZmP2hfFgBAm5A/resize:fit:200:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNDUxMS5wbmc.png',
//							'reward' => '100 Kč',
//							'redirectUrl' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress',
//						],
//						[
//							'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/2lUyOpahIkETtfT3RbYi1YDPKFWBhMem9b-k0-C-Bu4/resize:fit:200:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNTAwNjAucG5n.png',
//							'reward' => 'až 5 %',
//							'redirectUrl' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress/',
//						],
//					],
//					'bottomButton' =>  ['title' => 'Spodní odkaz nullable',  'url' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress'],
//				],
//			]);
//		} elseif ($id === 'iv5byi-qhg1f0-h8s9qh') {
//			$this->sendData([
//				'popupType' => 'betterOffer',
//				'popup' => [
//					'challenge' => ['title' => 'Uvažujete nad investováním s účtem u Fondee?', 'button' => 'Ano, uvažuji'],
//					'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/5btbLLFVfZ4UeGhYBWM_kd2zdOUcZbS0JWJ0io-0yQ4/resize:fit:200:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzkzNDc5LnBuZw.png',
//					'title' => 'Podívejte se i na investiční účty v Portu, kde navíc získate:',
//					'reward' => [
//						'offer' => '200 Kč',
//						'description' => 'za uzavření smlouvy',
//					],
//					'description' => 'Za samotnou registraci získate 75 kč. V případě uzavření smlouvy se vaše odměna zvýší na 200 Kč.',
//					'bottomButton' =>  ['title' => 'Získat 200 Kč v Portu',  'url' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress'],
//				],
//			]);
//		} elseif ($id === '1ic6sj-3z558h-8gb7y7') {
//			$this->sendData([
//				'popupType' => 'betterOffer',
//				'popup' => [
//					'challenge' => null,
//					'shopLogoUrl' => 'https://img.tiplicdn.com/zoh4eiLi/IMG/7200/5btbLLFVfZ4UeGhYBWM_kd2zdOUcZbS0JWJ0io-0yQ4/resize:fit:200:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzkzNDc5LnBuZw.png',
//					'title' => 'Podívejte se i na investiční účty v Portu, kde navíc získate:',
//					'reward' => [
//						'offer' => '200 Kč',
//						'description' => 'za uzavření smlouvy',
//					],
//					'description' => 'Za samotnou registraci získate 75 kč. V případě uzavření smlouvy se vaše odměna zvýší na 200 Kč.',
//					'bottomButton' =>  ['title' => 'Získat 200 Kč v Portu',  'url' => 'https://www.tipli.cz/obchod/aliexpress?shortcut=1&fromAddon=1&utm_source=addon&utm_medium=cashback&utm_campaign=aliexpress'],
//				],
//			]);
//		} else {
//			$this->sendError('Offer not found', 404);
//		}
	}

	private function getRewardText(Shop $shop): string
	{
		$reward = '';

		if ($shop->isCashbackActive()) {
			$reward = html_entity_decode($this->rewardFilter->__invoke($shop, false, 'pure', $this->getUserIdentity()));
			$rewardCommon = html_entity_decode($this->rewardFilter->__invoke($shop, false, 'common', $this->getUserIdentity()));

			if (preg_match('~[0-9]~', $reward)) {
				$reward = str_replace($reward, '**' . $reward . '**', $rewardCommon);
			}

			if ($shop->getRewardLabel()) {
				$reward .= ' ' . $shop->getRewardLabel();
			} elseif ($shop->isGamble() === false) {
				$reward .= ' ' . $this->translator->translate('model.shops.rewardFilter.suffixExtended');
			}
		} elseif ($shop->getCountOfCouponDeals() > 0) {
			$reward = '**' . $this->translator->translate('model.shops.rewardFilter.countOfCoupons', ['count' => $shop->getCountOfCouponDeals()]) . '**';
//			$rewardSuffix = $this->translator->translate('api.addon.page.defaultRewardSuffix');
		}

		return $reward;
	}

	private function getShopsData(AlternativeOffer $alternativeOffer): array
	{
		$shopsData = [];

		if ($alternativeOffer->getGenerateShops()) {
			/** @var Shop $shop */
			foreach ($this->shopFacade->findTopShops($alternativeOffer->getCountOfShops() ?? 5, $alternativeOffer->getLocalization()) as $shop) {
				$shopsData[] =  ['shop' => $shop, 'deepUrl' => null, 'skipIfUserHasTransaction' => $shop->isHidePromoAfterFirstTransaction()];
			}
		} else {
			/** @var AlternativeOfferShop $alternativeOfferShop */
			foreach ($alternativeOffer->getShops() as $alternativeOfferShop) {
				$shopsData[] = [
					'shop' => $alternativeOfferShop->getShop(),
					'deepUrl' => $alternativeOfferShop->getDeepUrl(),
					'skipIfUserHasTransaction' => (
						($alternativeOfferShop->getShop() && $alternativeOfferShop->getShop()->isHidePromoAfterFirstTransaction())
						|| $alternativeOffer->shouldSkipShopsWhereUserHasTransaction()
					),
				];
			}
		}

		$user = $this->getUserIdentity();

		$data = [];
		foreach ($shopsData as $shopsDataRow) {
			/** @var Shop $shop */
			$shop = $shopsDataRow['shop'];
			$skipIfUserHasTransaction = $shopsDataRow['skipIfUserHasTransaction'];

			if (
				$user !== null
				&& $skipIfUserHasTransaction === true
				&& count($this->transactionFacade->findTransactionsByShopForUser($shop, $user)) > 0
			) {
				continue;
			}

			$data[] = [
				'shopLogoUrl' => $this->imageFilter->__invoke($shop->getLogo(), 200),
				'reward' => $this->getRewardText($shop),
				'redirectUrl' => $this->linkGenerator->link('NewFront:Shops:Shop:default', [
					'shop' => $shop,
					'shortcut' => 1,
					'fromAddon' => true,
					'deepUrl' => $shopsDataRow['deepUrl'],
					'utm_source' => 'addon',
					'utm_medium' => 'alternative-offers',
					'utm_campaign' => $alternativeOffer->getShop()->getSlug(),
					'utm_content' => $shop->getSlug(),
				]),
			];
		}

		return $data;
	}
}
