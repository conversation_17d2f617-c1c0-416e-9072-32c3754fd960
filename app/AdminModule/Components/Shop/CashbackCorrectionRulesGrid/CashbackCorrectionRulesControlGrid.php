<?php

namespace tipli\AdminModule\Components\Shop;

use tipli\AdminModule\Components\IDataGridFactory;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\Shops\Entities\CashbackCorrectionRule;
use tipli\Model\Shops\Entities\Shop;
use Nette\Application\UI\Control;
use tipli\Model\Shops\ShopFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;

class CashbackCorrectionRulesControlGrid extends Control
{
	/** @var IDataGridFactory */
	public $dataGridFactory;

	/** @var ShopFacade */
	public $shopFacade;

	/** @var PartnerSystemFacade  */
	private $partnerSystemFacade;

	/** @var Shop */
	private $shop;

	public function __construct(Shop $shop, IDataGridFactory $dataGridFactory, ShopFacade $shopFacade, PartnerSystemFacade $partnerSystemFacade)
	{
		$this->dataGridFactory = $dataGridFactory;
		$this->shopFacade = $shopFacade;
		$this->partnerSystemFacade = $partnerSystemFacade;
		$this->shop = $shop;
	}

	/**
	 * @throws \Ublaboo\DataGrid\Exception\DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{
		$grid = $this->dataGridFactory->create()->getSimpleGrid($this->shopFacade->getShopCashbackCorrectionRules($this->shop));

		$grid->addColumnText('name', 'Label')
			->setRenderer(static function (CashbackCorrectionRule $cashbackCorrectionRule) {
				return $cashbackCorrectionRule->getName() ?: '-';
			});

		$grid->addColumnText('cashback', 'Cashback from-to')
			->setRenderer(static function (CashbackCorrectionRule $cashbackCorrectionRule) {
				return ($cashbackCorrectionRule->getCashbackFrom() * 100) . ' %' . ' - ' . ($cashbackCorrectionRule->getCashbackTo() * 100) . ' %';
			});

		$grid->addColumnText('validSince', 'Valid since-till')
			->setRenderer(static function (CashbackCorrectionRule $cashbackCorrectionRule) {
				return ($cashbackCorrectionRule->getValidSince()->format('d.m.Y H:i') . ' - ' . $cashbackCorrectionRule->getValidTill()->format('d.m.Y H:i'));
			});

		$grid->addColumnText('targetCashback', 'Cashback limit')
			->setRenderer(static function (CashbackCorrectionRule $cashbackCorrectionRule) {
				return ($cashbackCorrectionRule->getTargetCashback() * 100) . ' %';
			});

		$grid->addAction('removeCashbackCorrectionRule', '', 'removeCashbackCorrectionRule!', ['id' => 'shop.id', 'correctionId' => 'id'])
			->setClass('btn btn-xs btn-danger ajax')
			->setIcon('times')
			->setConfirmation(new StringConfirmation('Are you sure?'));

		return $grid;
	}

	public function handleRemoveCashbackCorrectionRule(int $id, int $correctionId)
	{
		/** @var CashbackCorrectionRule $cashbackCorrectionRule */
		$cashbackCorrectionRule = $this->shopFacade->findShopCashbackCorrectionRule($correctionId);

		$this->shopFacade->removeShopCashbackCorrectionRule(
			$cashbackCorrectionRule
		);

		$this['grid']->reload();
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setFile(__DIR__ . '/control.latte');
		$template->render();
	}
}
