{block head}
	<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/cs.js"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
	<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
{/block}

{snippet}
    <div id="spinner" class="hide">Loading</div>
    <div class="kt-subheader__wrapper">
        <a href="#" class="btn btn-sm btn-outline-dark font-weight-bold" id="calendar">
            {if !$shortcut}
                <span class="kt-font-bold" id="calendar-date">
                    {if $from->format('Y') == $to->format('Y') && $from->format('Y') == date('Y')}
                        {$from->format('d.m.')} - {$to->format('d.m.')}
                    {else}
                        {$from->format('d.m.Y')} - {$to->format('d.m.Y')}
                    {/if}
                </span>
            {else}
                <span class="kt-font-bold" id="calendar-title">{$shortcuts[$shortcut]}</span>&nbsp;
            {/if}
            <i class="flaticon-calendar-with-a-clock-time-tools kt-padding-l-5 kt-padding-r-0"></i>
        </a>
    </div>
<script>
    $(document).ready(function () {
        var picker = $('#calendar');
        var labels = {
            "Dnes": "today",
            "Včera": "yesterday",
            "Tento týden": "this week",
            "Minulý týden": "last week",
            "Posledních 7 dní": "last 7 days",
            "Posledních 31 dní": "last 31 days",
            "Posledních 90 dní": "last 90 days",
            "Posledních 365 dní": "last 365 days",
            "Tento měsíc": "this month",
            "Minulý měsíc": "last month",
            "Předminulý měsíc": "month before",
            "Tento rok": "this year",
            "Minulý rok": "last year",
            "Vše": "all",
            "Vlastní": 0
        };

        function getShortcutByLabel(label) {
            return labels[label];
        }
        function getLabelByShortcut(shortcut) {
            var label = Object.keys(labels).filter(function(key) {
                return labels[key] == shortcut;
            });
            return label;
        }

        picker.daterangepicker({
            direction: false,
            startDate: {$from->format('d/m/Y')},
            endDate: {$to->format('d/m/Y')},
            opens: 'left',
            applyClass: "btn btn-sm btn-primary",
            cancelClass: "btn btn-sm btn-secondary",
            ranges: {
                'Dnes': [moment(), moment()],
                'Včera': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Tento týden': [moment().startOf('week').add(1, 'days'), moment().endOf('week').add(1, 'days')],
                'Minulý týden': [moment().subtract(1, 'week').startOf('week').add(1, 'days'), moment().subtract(1, 'week').endOf('week').add(1, 'days')],
                'Posledních 7 dní': [moment().subtract(7, 'days'), moment()],
                'Posledních 31 dní': [moment().subtract(31, 'days'), moment()],
                'Posledních 90 dní': [moment().subtract(90, 'days'), moment()],
                'Posledních 365 dní': [moment().subtract(365, 'days'), moment()],
                'Tento měsíc': [moment().startOf('month'), moment().endOf('month')],
                'Minulý měsíc': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'Předminulý měsíc': [moment().subtract(2, 'months').startOf('month'), moment().subtract(2, 'months').endOf('month')],
                'Tento rok': [moment().startOf('year'), moment().endOf('year')],
                'Minulý rok': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                'Vše': [moment().subtract(10, 'year'), moment()],
            },
            "locale": {
                "format": "DD.MM.YYYY",
                "separator": " - ",
                "applyLabel": "Potvrdit",
                "cancelLabel": "Zrušit",
                "fromLabel": "Od",
                "toLabel": "Do",
                "customRangeLabel": "Vlastní",
                "weekLabel": "W",
                "daysOfWeek": [
                    "Ne",
                    "Po",
                    "Út",
                    "St",
                    "Čt",
                    "Pá",
                    "So"
                ],
                "monthNames": [
                    "Leden",
                    "Únor",
                    "Březen",
                    "Duben",
                    "Květen",
                    "Červen",
                    "Červenec",
                    "Srpen",
                    "Září",
                    "Říjen",
                    "Listopad",
                    "Prosinec"
                ],
                "firstDay": 1
            },
        }).on('apply.daterangepicker', function(ev, picker) {
        var label = picker.chosenLabel;
        var shortcut = getShortcutByLabel(label);

        $("#calendar-title").html(label);

        $("#spinner").removeClass("hide");

        $.nette.ajax({
            url: {link changeDate!},
            data: {
                "calendar-dateFrom": picker.startDate.format('YYYY-MM-DD'),
                "calendar-dateTo": picker.endDate.format('YYYY-MM-DD'),
                "calendar-dateShortcut": shortcut
            },
            type: "GET",
            success: function () {
                $("#spinner").addClass("hide");
            }
        });
    });
    });
</script>
{/snippet}
