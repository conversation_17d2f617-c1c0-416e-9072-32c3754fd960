<?php

namespace tipli\AdminModule\Components;

use Nette\Application\IPresenter;
use Nette\Application\UI\Presenter;
use Nette\Bridges\ApplicationLatte\Template;
use tipli\Model\Currencies\CurrencyFilter;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Localization\FlagFilter;
use tipli\Model\Shops\RewardFilter;
use tipli\Model\Transactions\AmountFilter;
use Ublaboo\DataGrid\DataGrid as UblabooDataGrid;

final class DataGrid extends UblabooDataGrid
{
	/** @var FlagFilter */
	private $flagFilter;

	/** @var AmountFilter */
	private $amountFilter;

	/** @var CurrencyFilter */
	private $currencyFilter;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var RewardFilter */
	private $rewardFilter;

	public function __construct(FlagFilter $flagFilter, AmountFilter $amountFilter, CurrencyFilter $currencyFilter, ImageFilter $imageFilter, RewardFilter $rewardFilter)
	{
		$this->flagFilter = $flagFilter;
		$this->amountFilter = $amountFilter;
		$this->currencyFilter = $currencyFilter;
		$this->imageFilter = $imageFilter;
		$this->rewardFilter = $rewardFilter;
	}

	public function getSimpleGrid($source = [], int $itemsPerPage = 50): UblabooDataGrid
	{
		$grid = new UblabooDataGrid();

		$grid->setStrictSessionFilterValues(false);
		$grid->setRememberState(false);
		$grid->setRefreshUrl(false);
		$grid->setItemsPerPageList([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 200, 300, 500]);
		$grid->setDefaultPerPage($itemsPerPage);

		$grid->setDataSource($source);

		return $grid;
	}

	/**
	 * @param Presenter $presenter
	 * @param string $name
	 * @param mixed $source
	 * @param int $itemsPerPage
	 * @return \Ublaboo\DataGrid\DataGrid
	 */
	public function getGrid(IPresenter $presenter, $name, $source, $itemsPerPage = 50): UblabooDataGrid
	{
		$grid = new UblabooDataGrid();

		$presenter->addComponent($grid, $name);

		/** @var Template $template */
		$template = $grid->getTemplate();
		$template->addFilter('flag', $this->flagFilter);
		$template->addFilter('amount', $this->amountFilter);
		$template->addFilter('currency', $this->currencyFilter);
		$template->addFilter('image', $this->imageFilter);
		$template->addFilter('reward', $this->rewardFilter);

		$grid->setStrictSessionFilterValues(false); // @todo odstranit, po migraci se zmenili nazvy filtru a padalo to adminum
		$grid->setRememberState(false);
		$grid->setRefreshUrl(false);
		$grid->setItemsPerPageList([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 200, 300, 500]);
		$grid->setDefaultPerPage($itemsPerPage);

		$grid->setDataSource($source);

		return $grid;
	}
}
