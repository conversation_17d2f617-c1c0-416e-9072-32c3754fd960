{var $freeContent = true}

{block headerContent}
	<a n:href="default" class="btn btn-light btn-sm" back-button> < zpět</a>
{/block}

{block title}Kampaň{/block}
{block content}

{snippet errors}
	<div class="row" n:ifset="$error">
		<div class="col-md-6 offset-md-3">
			<div class="alert alert-warning">{$error |noescape}</div>
		</div>
		<script>window.location="#"</script>
	</div>
{/snippet}

{form rewardCampaignControl-form, class => "ajax-2"}
	<div class="row">
		<div class="col-md-6 offset-md-3">
			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Základní nastavení kampaně</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-4">
							<label>Lokalizace</label>
							{input localization, class => "form-control"}
							{$form['localization']->getCustomHtml() |noescape}
						</div>

						<div class="form-group col-md-8">
							<label>Název kampaně</label>
							{input name, class => "form-control"}
						</div>
					</div>
					<div class="row">
						<div class="form-group col-md-4">
							<label>Obrázek</label>
							{if !$rewardCampaign || !$rewardCampaign->getImage()}
								<img src="{$basePath}/images/admin-image-placeholder-square.png" alt="" id="image-preview" style="max-width:200px;max-height:200px;border:1px dashed #eee; margin:auto;margin-bottom:20px;cursor:pointer" />
							{else}
								<img src="{$rewardCampaign->getImage() |image: 300}" alt="" id="image-preview" style="max-width:200px;max-height:200px;border:1px dashed #eee; margin:auto;margin-bottom:20px;cursor:pointer" />
							{/if}
							<div class="text-center">
								{input image, accept => "image/*"}
							</div>
						</div>
						<div class="form-group col-md-8">
							<label>Popis</label>
							{input description, class => "form-control", style => "min-height:240px;"}
						</div>

					</div>

				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Přiřazení kampaně</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-4">
							<label>Segment uživatelů</label>
							<span data-toggle="tooltip" data-placement="top" title='Pokud se jedná o aktivační kampaň, je segment "neaktivní uživatelé", pokud o reaktivační, je segment "aktivní uživatelé". V případě emailových kampaní, kde je již skupina vyselektována a kampaň přiřazena díky UTM, můžeme nastavit "všichni uživatelé".'><i class="fa fa-question-circle"></i></span>
							{input segment, class => "form-control"}
						</div>
						<div class="form-group col-md-4">
							<label>Kód pro přiřazení </label><div style="float:right"><small><a href="javascript:void(0);" onclick="javascript:$('[name=code]').val('TIPLI' + getRandomString(5));">vygenerovat náhodně</a></small></div>
							{input code, class => "form-control"}
						</div>
					</div>

					<div class="row">
						<div class="form-group col-md-8">
							<label>UTM </label>
							<span data-toggle="tooltip" data-placement="top" title="Formát: souce/medium/campaign"><i class="fa fa-question-circle"></i></span>
							<div id="utms-container">
								<div class="input-group">
									<input type="text" class="form-control" name="utm[]" data-input="utm">
									{*<div class="input-group-append">*}
										{*<a href="javascript:void(0);" class="input-group-text remove-utm"><span class="fa fa-times"></span></a>*}
									{*</div>*}
								</div>
							</div>
							<a href="javascript:void(0);" class="btn btn-sm btn-outline-primary mt-1" id="add-utm-button">+ přidat další UTM</a>
						</div>
					</div>
					<div class="row">
						<div class="form-group col-md-12">
							<hr />
						</div>
					</div>
					<div class="row">
						<div class="form-group col-md-3">
							<label>Kampaň je platná od</label>
							{input validSince, class => "form-control datetimepicker"}
						</div>
						<div class="form-group col-md-3">
							<label>do</label>
							{input validTill, class => "form-control datetimepicker"}
						</div>
					</div>
				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Aktivace kampaně</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-3">
							<label>Maximální počet aktivací</label>
							<span data-toggle="tooltip" data-placement="top" title="Při zadání 0 nebude počet aktivací nijak limitován."><i class="fa fa-question-circle"></i></span>
							{input maximumCountOfActivations, class => "form-control"}
						</div>

						<div class="form-group col-md-4">
							<label>Podmínka se musí splnit do data</label>
							{input ableToUseTill, class => "form-control datetimepicker"}
						</div>
						<div class="form-group col-md-4">
							<label>nebo do počtu dnů od aktivace</label>
							{input ableToUseInDays, class => "form-control"}
						</div>
					</div>

					<div class="row">
						<div class="form-group col-md-6">
							<label>
								<input type="checkbox" n:name="activateAfterAssign">
								kampaň se aktivuje všem po přiřazení
							</label>
						</div>
					</div>
				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Pravidla pro získání odměny</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-3">
							<label>Typ pravidla</label>
							{input conditionType, class => "form-control"}
						</div>
					</div>
					<div class="hide" data-condition-type-settings="commission_single">
						<div class="row">
							<div class="form-group col-md-12">
								<span>Transakce musí pocházet z</span>
								<div style="display:inline-block;width:300px">
									{input commissionSingleSubtype, class => "noselectpicker"}:
								</div>
								<div data-commisison-single-subtype-settings="shops">
									{input shops, class => "form-control"}
									{$form['shops']->getCustomHtml() |noescape}
								</div>
								<div data-commisison-single-subtype-settings="tags" n:snippet="tags">
									{input $control['rewardCampaignControl-form']['tags'], class => 'form-control', id=>"test"}
								</div>
								<div data-commisison-single-subtype-settings="excludedShops">
									{input excludedShops, class => "form-control"}
									{$form['excludedShops']->getCustomHtml() |noescape}
								</div>
							</div>
						</div>
					</div>
					<div class="hide" data-condition-type-settings="commission_cumular">
						<div class="row">
							<div class="form-group col-md-4">
								<span>Minimálně potvrzeno na účtu:</span>
								{input minimalConfirmedBalance, class => "form-control"}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Forma odměny</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-4">
							<label>Pevná částka v dané měně</label>
							{input rewardBonusAmount, class => "form-control"}
						</div>
						<div class="form-group col-md-4">
							<label>nebo multiplikátor pro odměnu</label>
							<span data-toggle="tooltip" data-placement="top" title="Odměna = multiplikátor × výše cashback odměny."><i class="fa fa-question-circle"></i></span>
							{input rewardCoefficient, class => "form-control"}
						</div>
					</div>
				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-12 text-center">
							{input submit, class => "btn btn-primary"}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	{input utms, class => "hide"}

<script>
	$(document).ready(function() {
	    // image
        $("[name=image]").change(function() {
            if (this.files && this.files[0]) {
                let reader = new FileReader();
                reader.onload = function(e) {
                    $('#image-preview').attr('src', e.target.result);
                };

                reader.readAsDataURL(this.files[0]);
            }
        });

        $("#image-preview").on("click", function() {
            $("[name=image]").trigger("click");
		});

        // localization switch
        $("[name=localization]").change(function() {
			let localizationId = $(this).val();

            $.nette.ajax({
				type: 'GET',
				url: {link rewardCampaignControl-loadTags!},
				data: {
                    "rewardCampaignControl-localizationId": localizationId
				},
				success: function(data, payload) {
				    console.log(data, payload);
				}
            });
		});


        // add utm
        $("#add-utm-button").on("click", addUtmInput);

        // load utms
		let utms = $("[name=utms]").val();
		if (utms !== "") {
		    setUtms(utms);
		}

		// remove utms binding
		bindRemoveUtm();

		// condition type switch
        $("[name=conditionType]").change(function() {
            let type = $(this).val();

            $("[data-condition-type-settings]").addClass("hide");
            $("[data-condition-type-settings='" + type + "']").removeClass("hide");
//            $("[data-condition-type-settings='" + type + "']").find("input, select").reset();
		}).trigger("change");

        $("[name=commissionSingleSubtype]").change(function() {
            let type = $(this).val();

            $("[data-commisison-single-subtype-settings]").addClass("hide");
            $("[data-commisison-single-subtype-settings='" + type + "']").removeClass("hide");
		}).trigger("change");
	});

	function getRandomString(length) {
		var result = '';
		var characters  = '1234567890';
		var charactersLength = characters.length;
		for ( var i = 0; i < length; i++ ) {
			result += characters.charAt(Math.floor(Math.random() * charactersLength));
		}

		return result;
	}

	function addUtmInput() {
        $("#utms-container").append('<div class="input-group mt-1"> <input type="text" class="form-control" name="utm[]" data-input="utm"> <div class="input-group-append"> <a href="javascript:void(0);" class="input-group-text remove-utm"><span class="fa fa-times"></span></a> </div> </div>');
        bindRemoveUtm();
	}

	function setUtms(utmsString)
	{
	    let utms = utmsString.split("\n");

	    if (utms.length === 0) {
	        return;
		} else if (utms.length === 1) {
            $("[data-input=utm]").val(utms[0]);
		} else {
	        for (var c = 0; c < utms.length -1; c++ ) {
	            addUtmInput();
			}

			$("[data-input=utm]").each(function(i) {
			    $(this).val(utms[i]);
			});
		}
	}

	function bindRemoveUtm()
	{
        $(".remove-utm").off("click").on("click", function () {
            $(this).parents(".input-group").remove();
        })
	}

</script>
<style>
	.utms-container .utm {
		position: relative;
	}
	.utm-input {
		display:inline-block;
		margin-top:5px;
	}
	.remove-utm:hover {
		background:#eee;
		color:#000000;
		text-decoration: none;
	}
</style>
{/form}
