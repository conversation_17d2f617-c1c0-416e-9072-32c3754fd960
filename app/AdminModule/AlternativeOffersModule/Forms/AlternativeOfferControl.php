<?php

namespace tipli\AdminModule\AlternativeOffersModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Addon\AlternativeOfferFacade;
use tipli\Model\Addon\Entities\AlternativeOffer;
use tipli\Model\Addon\Entities\AlternativeOfferShop;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\ShopFacade;

class AlternativeOfferControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	public $onError = [];

	public function __construct(
		private ?AlternativeOffer $alternativeOffer,
		private AlternativeOfferFacade $alternativeOfferFacade,
		private LocalizationFacade $localizationFacade,
		private ShopFacade $shopFacade,
		private Nette\Security\User $netteUser
	) {
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$alternativeOffer = $this->alternativeOffer;

		$form = new Form();

		$form->addLocalizationSelect('localization')
			->setRequired('Localization is required.');

		$form->addText('name')
			->setRequired('Name is required.');

		$form->addShopSelect('shop')
			->setLocalizationInput($form['localization'])
			->setRequired('Shop is required.');

		$form->addSelect('urlMaskType')
			->setRequired('Url mask type is required.')
			->setItems([
				AlternativeOffer::URL_MASK_TYPE_STARTS_WITH => 'url starts with',
				AlternativeOffer::URL_MASK_TYPE_DOMAIN_CONTAINS => 'url contains',
			]);

		$form->addText('urlMask')
			->setRequired('Url mask is required.');

		$form->addSelect('frequency')
			->setRequired('Frequency is required.')
			->setItems([
				AlternativeOffer::FREQUENCY_UNLIMITED => 'unlimited',
				AlternativeOffer::FREQUENCY_1_HOUR => '1 hour',
				AlternativeOffer::FREQUENCY_24_HOURS => '24 hours',
				AlternativeOffer::FREQUENCY_7_DAYS => '7 days',
				AlternativeOffer::FREQUENCY_10_YEARS => 'only once',
			]);

		$form->addSelect('popupType')
			->setRequired('Popup type is required.')
			->setItems([
				AlternativeOffer::POPUP_TYPE_BETTER_OFFER => 'Better offer',
				AlternativeOffer::POPUP_TYPE_SHOPS_LIST => 'Shops list',
			]);

		$form->addSelect('visibility')
			->setRequired('Visibility is required.')
			->setItems([
				AlternativeOffer::VISIBILITY_HIDDEN => 'Hidden (draft)',
				AlternativeOffer::VISIBILITY_ADMIN => 'Visible for admins',
				AlternativeOffer::VISIBILITY_PUBLIC => 'Public',
			]);

		$form->addText('sadText')
			->setNullable()
			->setMaxLength(64);

		$form->addText('shopsListTitle')
			->setMaxLength(64)
			->addConditionOn($form['popupType'], Form::EQUAL, AlternativeOffer::POPUP_TYPE_SHOPS_LIST)
			->setRequired('Title is required');

		$form->addText('betterOfferTitle')
			->setMaxLength(64)
			->addConditionOn($form['popupType'], Form::EQUAL, AlternativeOffer::POPUP_TYPE_BETTER_OFFER)
			->setRequired('Title is required');

		$form->addText('bottomTitle')
			->setMaxLength(64);

		$form->addText('description')
			->setNullable();

		$form->addText('bottomUrl')
			->setNullable();

		$form->addText('bottomButtonTitle')
			->setNullable()
			->setMaxLength(64);

		$form->addCheckbox('generateShops');

		$form->addCheckbox('skipShopsWhereUserHasTransaction');

		$form->addText('countOfShops')
		->addCondition(Form::FILLED)
			->addRule(Form::INTEGER, 'Count of shops must be a number.')
			->addConditionOn($form['generateShops'], Form::EQUAL, true)
			->setRequired('Count of shops is required if generating shops is enabled');

		$form->addShopsSelect('shops')
			->setLocalizationInput($form['localization']);

		$form->addShopSelect('betterShop')
			->setLocalizationInput($form['localization'])
			->addConditionOn($form['popupType'], Form::EQUAL, AlternativeOffer::POPUP_TYPE_BETTER_OFFER)
			->setRequired('Better shop is required if popup type is "Better offer"');

		$form->addText('challengeTitle')
			->setNullable()
			->setMaxLength(64);

		$form->addText('challengeButtonTitle')
			->setNullable()
			->setMaxLength(64);

		$form->addText('betterShopDeepUrl')
			->setNullable()
			->setMaxLength(255);

		if ($alternativeOffer !== null) {
			$form['shopsListTitle']->setDefaultValue($alternativeOffer->getTitle());
			$form['localization']->setDefaultValue($alternativeOffer->getLocalization());
			$form['name']->setDefaultValue($alternativeOffer->getName());
			$form['visibility']->setDefaultValue($alternativeOffer->getVisibility());
			$form['shop']->setDefaultValue($alternativeOffer->getShop());
			$form['urlMaskType']->setDefaultValue($alternativeOffer->getUrlMaskType());
			$form['urlMask']->setDefaultValue($alternativeOffer->getUrlMask());
			$form['popupType']->setDefaultValue($alternativeOffer->getPopupType());
			$form['frequency']->setDefaultValue($alternativeOffer->getFrequency());

			if ($alternativeOffer->getPopupType() === AlternativeOffer::POPUP_TYPE_BETTER_OFFER) {
				$form['betterOfferTitle']->setDefaultValue($alternativeOffer->getTitle());
				$form['betterShop']->setDefaultValue($alternativeOffer->getBetterShop());
				$form['challengeTitle']->setDefaultValue($alternativeOffer->getChallengeTitle());
				$form['challengeButtonTitle']->setDefaultValue($alternativeOffer->getChallengeButtonTitle());
				$form['description']->setDefaultValue($alternativeOffer->getDescription());
				$form['bottomButtonTitle']->setDefaultValue($alternativeOffer->getBottomButtonTitle());
				$form['betterShopDeepUrl']->setDefaultValue($alternativeOffer->getBetterShopDeepUrl());
			} elseif ($alternativeOffer->getPopupType() === AlternativeOffer::POPUP_TYPE_SHOPS_LIST) {
				$form['sadText']->setDefaultValue($alternativeOffer->getSadText());
				$form['shopsListTitle']->setDefaultValue($alternativeOffer->getTitle());
				$form['generateShops']->setDefaultValue($alternativeOffer->getGenerateShops());
				$form['skipShopsWhereUserHasTransaction']->setDefaultValue($alternativeOffer->shouldSkipShopsWhereUserHasTransaction());
				$form['countOfShops']->setDefaultValue($alternativeOffer->getCountOfShops());
				$form['bottomTitle']->setDefaultValue($alternativeOffer->getBottomTitle());
				$form['bottomUrl']->setDefaultValue($alternativeOffer->getBottomUrl());

				$shops = [];

				/** @var AlternativeOfferShop $alternativeOfferShop */
				foreach ($alternativeOffer->getShops() as $alternativeOfferShop) {
					$shops[] = $alternativeOfferShop->getShop();
				}

				$form['shops']->setDefaultValue($shops);
			}
		}

		$form->addSubmit('submit');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formValidate(Form $form, \stdClass $values)
	{
		if ($form->hasErrors()) {
			foreach ($form->getErrors() as $error) {
				if (is_string($error)) {
					$this->onError($error);
					return;
				}

				$this->onError($error->getMessage());
				return;
			}
		}
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$alternativeOffer = $this->alternativeOffer;

			$localization = $values->localization;
			$name = $values->name;
			$visibility = $values->visibility;
			$shop = $values->shop;
			$urlMaskType = $values->urlMaskType;
			$urlMask = $values->urlMask;
			$popupType = $values->popupType;
			$frequency = $values->frequency;

			$title = $popupType === AlternativeOffer::POPUP_TYPE_BETTER_OFFER
				? $values->betterOfferTitle
				: $values->shopsListTitle;

			if ($alternativeOffer === null) {
				$alternativeOffer = $this->alternativeOfferFacade->createAlternativeOffer(
					$localization,
					$shop,
					$name,
					$urlMaskType,
					$urlMask,
					$frequency,
					$popupType,
					$title
				);
			} else {
				$alternativeOffer->setShop($shop);
				$alternativeOffer->setName($name);
				$alternativeOffer->setUrlMaskType($urlMaskType);
				$alternativeOffer->setUrlMask($urlMask);
				$alternativeOffer->setFrequency($frequency);
				$alternativeOffer->setPopupType($popupType);
				$alternativeOffer->setTitle($title);
			}

			$alternativeOffer->setVisibility($visibility);

			if ($popupType === AlternativeOffer::POPUP_TYPE_SHOPS_LIST) {
				$alternativeOffer->setSadText($values->sadText);
				$alternativeOffer->setGenerateShops($values->generateShops);
				$alternativeOffer->setCountOfShops($values->generateShops ? $values->countOfShops : count($values->shops));
				$alternativeOffer->setSkipShopsWhereUserHasTransaction($values->skipShopsWhereUserHasTransaction);
				$alternativeOffer->setBottomTitle($values->bottomTitle);
				$alternativeOffer->setBottomUrl($values->bottomUrl);

				$this->alternativeOfferFacade->setShopsToAlternativeOffer($alternativeOffer, $values->shops);
			} else {
				$alternativeOffer->setSadText(null);
				$alternativeOffer->setGenerateShops(null);
				$alternativeOffer->setCountOfShops(null);
				$alternativeOffer->setSkipShopsWhereUserHasTransaction(null);
				$alternativeOffer->setBottomTitle(null);
				$alternativeOffer->setBottomUrl(null);
				$this->alternativeOfferFacade->setShopsToAlternativeOffer($alternativeOffer, []);
			}

			if ($popupType === AlternativeOffer::POPUP_TYPE_BETTER_OFFER) {
				$alternativeOffer->setBetterShop($values->betterShop);
				$alternativeOffer->setChallengeTitle($values->challengeTitle);
				$alternativeOffer->setChallengeButtonTitle($values->challengeButtonTitle);
				$alternativeOffer->setDescription($values->description);
				$alternativeOffer->setBottomButtonTitle($values->bottomButtonTitle);
				$alternativeOffer->setBetterShopDeepUrl($values->betterShopDeepUrl);
			} else {
				$alternativeOffer->setBetterShop(null);
				$alternativeOffer->setChallengeTitle(null);
				$alternativeOffer->setChallengeButtonTitle(null);
				$alternativeOffer->setDescription(null);
				$alternativeOffer->setBottomButtonTitle(null);
				$alternativeOffer->setBetterShopDeepUrl(null);
			}

			$this->alternativeOfferFacade->saveAlternativeOffer($alternativeOffer);

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
			$this->onError($e->getMessage());
		}
	}
}

interface IAlternativeOfferControlFactory
{
	/** @return AlternativeOfferControl */
	public function create(AlternativeOffer $alternativeOffer = null);
}
