{var $freeContent = true}

{block headerContent}
	<a n:href="AlternativeOffers:alternativeOffers" class="btn btn-light btn-sm back-button"> < back</a>
{/block}

{block title}Alternative offer{/block}
{block content}

{snippet errors}
	<div class="row justify-content-center" n:ifset="$error">
		<div class="col-md-7">
			<div class="alert alert-danger">{$error |noescape}</div>
		</div>
		<script>window.location="#"</script>
	</div>
{/snippet}

{form alternativeOfferControl-form, class => "ajax-2"}
	<div class="row justify-content-center">
		<div class="col-md-7 ">
			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>General settings</strong>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-4">
							<label>Localization: <span class="text-danger">*</span></label>
							<div class="{$alternativeOffer ? hide}">
								{input localization, class => "form-control"}
								{$form['localization']->getCustomHtml() |noescape}
							</div>
							<div>
								{$alternativeOffer ? $alternativeOffer->getLocalization()->getName()}
							</div>
						</div>

						<div class="form-group col-md-8">
							<label>Name: <span class="text-danger">*</span></label>
							{input name, class => "form-control"}
						</div>
					</div>
					<div class="row">
						<div class="form-group col-md-4">
							<label>Visibility:</label>
							{input visibility, class => "form-control"}
						</div>
						<div class="form-group col-md-8">
							<label>Shop: <span class="text-danger">*</span></label>
							{input shop, class => "form-control"}
							{$form['shop']->getCustomHtml() |noescape}
						</div>
					</div>
					<div class="row">
						<div class="col-md-4">
							<div class="form-group">
								<label>URL Mask type: <span class="text-danger">*</span></label>
								{input urlMaskType, class => "form-control"}
							</div>
						</div>
						<div class="col-md-8">
							<div class="form-group">
								<label>URL Mask: <span class="text-danger">*</span></label>
								{input urlMask, class => "form-control"}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-4">
							<div class="form-group">
								<label>Popup type: <span class="text-danger">*</span></label>
								{input popupType, class => "form-control", data-input => "popup-type"}
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group">
								<label>Frequency: <span class="text-danger">*</span></label>
								{input frequency, class => "form-control"}
							</div>
						</div>
					</div>
					{*					<div class="row">*}
{*						<div class="form-group col-md-12">*}
{*							<label>Podmínka - popis: <span class="text-danger">*</span></label>*}
{*							{input description, class => "form-control", style => "min-height:120px;"}*}
{*						</div>*}
{*					</div>*}

{*					<div class="row">*}
{*						<div class="form-group col-md-3">*}
{*							<label>Voucher je platný od: <span class="text-danger">*</span></label>*}
{*							{input validSince, class => "form-control datetimepicker", autocomplete => "off"}*}
{*						</div>*}
{*						<div class="form-group col-md-3">*}
{*							<label>do: <span class="text-danger">*</span></label>*}
{*							{input validTill, class => "form-control datetimepicker", autocomplete => "off"}*}
{*						</div>*}
{*						<div class="form-group col-md-3">*}
{*							<label>Voucher je viditelný od: <span class="text-danger">*</span></label>*}
{*							{input publicSince, class => "form-control datetimepicker", autocomplete => "off"}*}
{*						</div>*}
{*						<div class="form-group col-md-3">*}
{*							<label>Limit počet dnů od aktivace:</label>*}
{*							{input validDaysFromActivation, class => "form-control"}*}
{*						</div>*}
{*					</div>*}

{*					<div class="row">*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="isHidden">*}
{*								Voucher je skrytý - dostupný pouze přes odkaz*}
{*							</label>*}
{*						</div>*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="activateAfterAssign">*}
{*								Aktivovat voucher po přiřazení*}
{*							</label>*}
{*						</div>*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="isReusableForUser">*}
{*								Uživatel může voucher využít vícekrát*}
{*							</label>*}
{*						</div>*}
{*					</div>*}
				</div>
			</div>

			{*			<div class="card card-accent-primary">*}
{*				<div class="card-header">*}
{*					<strong>Kdo může být k voucheru přiřazen</strong>*}
{*				</div>*}
{*				<div class="card-body">*}
{*					<div class="row">*}
{*						<div class="form-group col-md-4">*}
{*							<label>Segment uživatelů:</label>*}
{*							{input segment, class => "form-control"}*}
{*						</div>*}
{*					</div>*}
{*					<div class="row">*}
{*						<div class="form-group col-md-4">*}
{*							<label>Uživatel s poslední transakcí před datem:</label>*}
{*							{input lastTransactionBefore, class => "form-control datetimepicker"}*}
{*						</div>*}
{*						<div class="form-group col-md-4">*}
{*							<label>Uživatel má alespoň 1 nákup v obchodech:</label>*}
{*							{input requiredTransactionInShops, class => "form-control"}*}
{*							{$form['requiredTransactionInShops']->getCustomHtml() |noescape}*}
{*						</div>*}
{*						<div class="form-group col-md-4">*}
{*							<label>Uživatel nemá nákup v obchodech:</label>*}
{*							{input noTransactionInShops, class => "form-control"}*}
{*							{$form['noTransactionInShops']->getCustomHtml() |noescape}*}
{*						</div>*}
{*					</div>*}
{*					<div class="row">*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="onlyNewUsers">*}
{*								Uživatelé starší méně než 24h*}
{*							</label>*}
{*						</div>*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="onlyUsersRegisteredAfter7Days">*}
{*								Uživatelé starší více než 7 dní*}
{*							</label>*}
{*						</div>*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="withoutAddonInstalled">*}
{*								Uživatelé bez addon doplňku*}
{*							</label>*}
{*						</div>*}
{*						<div class="form-group col-md-12">*}
{*							<label>*}
{*								<input type="checkbox" n:name="withoutRecommendedUser">*}
{*								Uživatelé, kteří zatím nikoho nedoporučili*}
{*							</label>*}
{*						</div>*}
{*					</div>*}
{*				</div>*}
{*			</div>*}

{*			<div class="card card-accent-primary">*}
{*				<div class="card-header">*}
{*					<strong>Odměna</strong>*}
{*				</div>*}
{*				<div class="card-body">*}
{*					<div class="row">*}
{*						<div class="form-group col-md-4">*}
{*							<label>Typ odměny: <span class="text-danger">*</span></label>*}
{*							{ifset $form['rewardType']}*}
{*								{input rewardType, class => "form-control"}*}
{*							{elseif $voucherCampaign}*}
{*								<br />*}
{*								{$voucherCampaign->getRewardTypeLabel()}*}
{*								<input name="rewardType" value="{$voucherCampaign->getRewardType()}" type="hidden" />*}
{*							{/ifset}*}
{*						</div>*}
{*						<div class="form-group col-md-4" data-reward-type-settings="static_bonus|dynamic_bonus">*}
{*							<label>Transakce musí pocházet z obchodu:</label>*}
{*							{input shop, class => "form-control"}*}
{*							{$form['shop']->getCustomHtml() |noescape}*}
{*						</div>*}
{*						<div class="form-group col-md-4" data-reward-type-settings="static_bonus|dynamic_bonus">*}
{*							<label>Minimálně výše objednávky:</label>*}
{*							{input minimalOrderAmount, class => "form-control"}*}
{*						</div>*}
{*						<div class="form-group col-md-4" data-reward-type-settings="static_bonus|dynamic_bonus|recommendation_bonus_boost|addon_bonus_boost">*}
{*							<label>Pevná částka v dané měně:</label>*}
{*							{if $voucherCampaign}*}
{*								{input rewardBonusAmount, class => "form-control disabled", readonly => readonly}*}
{*							{else}*}
{*								{input rewardBonusAmount, class => "form-control"}*}
{*							{/if}*}
{*						</div>*}
{*						<div class="form-group col-md-4" data-reward-type-settings="dynamic_bonus">*}
{*							<label>nebo multiplikátor pro odměnu:</label>*}
{*							<span data-toggle="tooltip" data-placement="top" title="Odměna = multiplikátor × výše cashback odměny."><i class="fa fa-question-circle"></i></span>*}
{*							{if $voucherCampaign}*}
{*								{input rewardMultiplier, class => "form-control disabled", readonly => readonly}*}
{*							{else}*}
{*								{input rewardMultiplier, class => "form-control"}*}
{*							{/if}*}
{*						</div>*}
{*					</div>*}
{*					<div class="row">*}
{*						<div class="form-group col-md-4" data-reward-type-settings="dynamic_bonus">*}
{*							<label>Celkový budget: <span class="text-danger">*</span></label>*}
{*							{input totalBonusBudget, class => "form-control"}*}
{*						</div>*}
{*						<div class="form-group col-md-8" data-reward-type-settings="dynamic_bonus">*}
{*							<label class="mt-4">*}
{*								<input type="checkbox" n:name="isOneTimeBonus">*}
{*								Platí pouze na 1 odměnu - po připsání odměny voucher expiruje*}
{*							</label>*}
{*						</div>*}
{*					</div>*}
{*				</div>*}
{*			</div>*}
		</div>
	</div>

	<div class="row justify-content-center">
		<div class="col-md-7 ">
			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Popup settings</strong>
				</div>
				<div class="card-body">
					<div class="row" data-type-settings="shopsList">
						<div class="col-12">
							<div class="form-group mt-3">
								<label>Title: <span class="text-danger">*</span></label>
								{input shopsListTitle, class => "form-control"}
							</div>
							<div class="form-group mt-3">
								<label>"Sad box" text:</label>
								{input sadText, class => "form-control"}
							</div>
							<hr />
							<strong>Shops</strong>
							<div class="form-check mt-3">
								<input n:name="generateShops" type="checkbox" class="form-check-input" id="generate-shops" data-input="generate-shops">
								<label class="form-check-label" for="generate-shops">Auto generate shops list</label>
							</div>
							<div class="form-group mt-3" data-container="manual-shops">
								<label>Shops:</label>
								{input shops, class => "form-control"}
								{$form['shops']->getCustomHtml() |noescape}
							</div>
							<div class="form-group mt-3" data-container="generate-shops">
								<label>Count of shops: <strong class="text-danger">*</strong></label>
								{input countOfShops, class => "form-control"}
							</div>
							<div class="form-check mt-3">
								<input n:name="skipShopsWhereUserHasTransaction" type="checkbox" class="form-check-input" id="skip-shops-where-user-has-transaction">
								<label class="form-check-label" for="skip-shops-where-user-has-transaction">Skip shops where the user has a transaction</label>
							</div>
							<hr />
							<strong>Bottom</strong>
							<div class="form-group mt-3">
								<label>Bottom title:</label>
								{input bottomTitle, class => "form-control"}
							</div>
							<div class="form-group mt-3">
								<label>Bottom URL:</label>
								{input bottomUrl, class => "form-control"}
							</div>
						</div>
					</div>
					<div class="row" data-type-settings="betterOffer">
						<div class="col-12">
							<div class="form-group">
								<label>"Better" shop:</label>
								{input betterShop, class => "form-control"}
								{$form['betterShop']->getCustomHtml() |noescape}
							</div>
							<hr />
							<strong>Challenge</strong>
							<div class="form-group mt-3">
								<label>Challenge title:</label>
								{input challengeTitle, class => "form-control"}
							</div>
							<div class="form-group mt-3">
								<label>Challenge CTA:</label>
								{input challengeButtonTitle, class => "form-control"}
							</div>
							<hr />
							<strong>Popup</strong>
							<div class="form-group mt-3">
								<label>Title: <span class="text-danger">*</span></label>
								{input betterOfferTitle, class => "form-control"}
							</div>
							<hr />
							<strong>Bottom</strong>
							<div class="form-group mt-3">
								<label>Bottom description:</label>
								{input description, class => "form-control"}
							</div>
							<div class="form-group mt-3">
								<label>Bottom CTA:</label>
								{input bottomButtonTitle, class => "form-control"}
							</div>
							<div class="form-group mt-3">
								<label>Better shop deep URL:</label>
								{input betterShopDeepUrl, class => "form-control"}
							</div>

						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group col-md-12 text-center">
					{input submit, class => "btn btn-primary", value => "Save"}
				</div>
			</div>
		</div>
	</div>

	<script>
		$(document).ready(function() {
			$("[data-input=popup-type]").change(function() {
				let type = $(this).val();

				$("[data-type-settings]").each(function() {
					let types = $(this).data("type-settings");
					if (types.includes(type)) {
						$(this).removeClass("hide");
					} else {
						$(this).addClass("hide");
					}
				});
			}).trigger("change");

			$("[data-input=generate-shops]").change(function() {
				if ($(this).is(":checked")) {
					$("[data-container=manual-shops]").addClass("hide");
					$("[data-container=generate-shops]").removeClass("hide");
				} else {
					$("[data-container=manual-shops]").removeClass("hide");
					$("[data-container=generate-shops]").addClass("hide");
				}
			}).trigger("change");
		});
	</script>
	<style>
		.select2-search__field { line-height: 26px !important;}
		.select2.select2-container.select2-container--default { position: relative; top:1px }
	</style>
{/form}
