<?php

namespace tipli\AdminModule\TransactionsModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Account\UserFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Transactions\TransactionFacade;

class MarketingBonusControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	/**
	 * @var TransactionFacade
	 */
	private $transactionFacade;

	/**
	 * @var UserFacade
	 */
	private $userFacade;

	public function __construct(LocalizationFacade $localizationFacade, TransactionFacade $transactionFacade, UserFacade $userFacade)
	{
		$this->localizationFacade = $localizationFacade;
		$this->transactionFacade = $transactionFacade;
		$this->userFacade = $userFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addSelect('localizationId', 'Localization:', $this->localizationFacade->findPairs(false))
			->setPrompt('-- select --')
			->setAttribute('class', 'selectpicker');

		$form->addText('user', 'User:')
			->setAttribute('placeholder', 'ID or e-mail')
			->setRequired('Enter ID or e-mail.');

		$form->addText('name', 'Transaction name:');

		$form->addText('amount', 'Amount:')
			->setRequired('Enter amount.')
			->addRule(Form::FLOAT, 'Amount must be number.');

		$form->addSubmit('submit', 'Save');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if (filter_var($values->user, FILTER_VALIDATE_INT)) {
				$user = $this->userFacade->find($values->user);
			} elseif (filter_var($values->user, FILTER_VALIDATE_EMAIL)) {
				if ($values->localizationId) {
					$localization = $this->localizationFacade->find($values->localizationId);
					$user = $this->userFacade->findOneByEmail($values->user, $localization);
				} else {
					$user = $this->userFacade->findUsersByEmail($values->user);

					if (count($user) > 1) {
						throw new InvalidArgumentException('User not found, enter valid user ID');
					}
				}
			} else {
				throw new InvalidArgumentException('User not found, enter valid user ID');
			}

			if (is_array($user)) {
				$user = $user[0];
			}

			if ($user === null) {
				throw new InvalidArgumentException('User not found');
			}

			$transaction = $this->transactionFacade->createMarketingBonusTransaction($user, $values->name, $values->amount);
			$this->onSuccess($transaction);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IMarketingBonusControlFactory
{
	/**
	 * @return MarketingBonusControl
	 */
	public function create();
}
