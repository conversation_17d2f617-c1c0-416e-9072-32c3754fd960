{var $title = 'Transactions'}

{block title}{$title}{/block}

{block head}
    <style>
        *[data-datagrid-reset-filter-by-column="date"] {
            display: none;
        }
    </style>
{/block}

{block content}
<h1 class="pull-left">{$title}</h1>

<div class="pull-right">
    <a n:href=":Admin:Transactions:Transaction:transaction" class="btn btn-primary">New commission transaction</a>
    <a n:href=":Admin:Transactions:Transaction:refundBonus" class="btn btn-primary">New refund transaction</a>
    <a n:href=":Admin:Transactions:Transaction:dealBonus" class="btn btn-primary">New "deal-report" bonus transaction</a>
    <a n:href=":Admin:Transactions:Transaction:marketingBonus" class="btn btn-primary">New marketing bonus transaction</a>
    <a n:href=":Admin:PartnerSystems:PartnerSystem:affilbox" class="btn btn-primary">Add Affilbox</a>
    <a n:href=":Admin:Transactions:Transaction:imports" class="btn btn-warning">Import</a>
</div>
<div class="clearfix"></div>

<br />
{form externalFilterForm}
	<div class="row">
	<div class="col-md-6">
		<div class="row form-group">
			<div class="col-md-5">
				Transaction ID:
			</div>
			<div class="col-md-7">
				{input transactionId, class => "form-control"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Type:
			</div>
			<div class="col-md-7">
				{input type, class => "form-control"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				State:
			</div>
			<div class="col-md-7">
				{input status, class => "form-control"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Country:
			</div>
			<div class="col-md-7">
				{input localization, data-input => "localization", class => "form-control"}
				{$form['localization']->getCustomHtml() |noescape}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				User:
			</div>
			<div class="col-md-7">
				{input user, data-input => "user", class => "form-control"}
				{$form['user']->getCustomHtml() |noescape}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Shop:
			</div>
			<div class="col-md-7">
				{input shop, data-input => "shop", class => "form-control"}
				{$form['shop']->getCustomHtml() |noescape}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Partner system:
			</div>
			<div class="col-md-7">
				{input partnerSystemId, class => "form-control"}
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="row form-group">
			<div class="col-md-5">
				Created from-to:
			</div>
			<div class="col-md-3">
				{input createdAtFrom, class => "form-control datepicker"}
			</div>
			<div class="col-md-1 text-center">
				-
			</div>
			<div class="col-md-3">
				{input createdAtTo, class => "form-control datepicker"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Registered from-to:
			</div>
			<div class="col-md-3">
				{input registeredAtFrom, class => "form-control datepicker"}
			</div>
			<div class="col-md-1 text-center">
				-
			</div>
			<div class="col-md-3">
				{input registeredAtTo, class => "form-control datepicker"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Sort:
			</div>
			<div class="col-md-7">
				{input sort, class => "form-control"}
			</div>
		</div>
		<div class="row form-group">
			<div class="col-md-5">
				Billable:
			</div>
			<div class="col-md-7">
				{input billable, class => "form-control"}
			</div>
		</div>
	</div>
	</div>
	<div class="row">
		<div class="col-12 text-center">
			{input submit, class => "btn btn-primary", value => "Apply filter"}
		</div>
	</div>
{/form}
<br />


{control transactionsGrid}
