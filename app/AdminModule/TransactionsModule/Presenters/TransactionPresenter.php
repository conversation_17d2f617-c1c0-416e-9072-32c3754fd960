<?php

namespace tipli\AdminModule\TransactionsModule\Presenters;

use Nette\Application\UI\Form;
use tipli\AdminModule\Presenters\BasePresenter;
use tipli\AdminModule\TransactionsModule\Forms\DealBonusControl;
use tipli\AdminModule\TransactionsModule\Forms\IAssignUserToTransactionControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\IDealBonusControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\IImportTransactionsControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\IMarketingBonusControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ImportTransactionsControl;
use tipli\AdminModule\TransactionsModule\Forms\IRefundBonusControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ITransactionBonusEditControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ITransactionConfirmControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ITransactionControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ITransactionShareCoefficientControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\ITransactionUncancelControlFactory;
use tipli\AdminModule\TransactionsModule\Forms\MarketingBonusControl;
use tipli\AdminModule\TransactionsModule\Forms\RefundBonusControl;
use tipli\AdminModule\TransactionsModule\Forms\TransactionConfirmControl;
use tipli\AdminModule\TransactionsModule\Forms\TransactionControl;
use tipli\Model\Account\Entities\User;
use tipli\Model\Datagrid\QueryBuilderDataSource;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\Column\ColumnText;

class TransactionPresenter extends BasePresenter
{
	/** @var TransactionFacade @inject */
	public $transactionFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var PartnerSystemFacade @inject */
	public $partnerSystemFacade;

	/** @var ITransactionControlFactory @inject */
	public $transactionControlFactory;

	/** @var ITransactionConfirmControlFactory @inject */
	public $transactionConfirmControlFactory;

	/** @var ITransactionShareCoefficientControlFactory @inject */
	public $transactionShareCoefficientControlFactory;

	/** @var IRefundBonusControlFactory @inject */
	public $refundBonusControlFactory;

	/** @var IDealBonusControlFactory @inject */
	public $dealBonusControlFactory;

	/** @var IMarketingBonusControlFactory @inject */
	public $marketingBonusControlFactory;

	/** @var IImportTransactionsControlFactory @inject */
	public $importTransactionsControlFactory;

	/** @var ITransactionBonusEditControlFactory @inject */
	public $transactionBonusEditControlFactory;

	/** @var ITransactionUncancelControlFactory @inject */
	public $transactionUncancelControlFactory;

	/** @var IAssignUserToTransactionControlFactory @inject */
	public $assignUserToTransactionControlFactory;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var StatisticDataProvider @inject */
	public $statisticDataProvider;

	/** @var array @persistent */
	public $transactionsFilter = [];

	public function actionTransactionConfirm($id)
	{
		$transaction = $id ? $this->transactionFacade->find($id) : null;

		if (!$id || !$transaction) {
			$this->error('Transaction not found.');
		}
	}

	public function actionTransactionRecomputeShareCoefficient($id)
	{
		$transaction = $id ? $this->transactionFacade->find($id) : null;

		if (!$id || !$transaction) {
			$this->error('Transaction not found.');
		}
	}

	public function actionTransactionBonusEdit($id)
	{
		$transaction = $id ? $this->transactionFacade->find($id) : null;

		if (!$id || !$transaction) {
			$this->error('Transaction not found.');
		}
	}

	public function actionTransactionUncancel($id)
	{
		$transaction = $this->transactionFacade->find($id);

		if (!$transaction) {
			$this->error('Transaction not found.');
		}

		if ($transaction->getType() != Transaction::TYPE_COMMISSION) {
			$this->flashMessage('Transaction cannot be changed. This is not commission transaction.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		}
	}

	public function actionAssignUserToTransaction($id)
	{
		/** @var Transaction|null $transaction */
		$transaction = $id ? $this->transactionFacade->find($id) : null;

		if (!$id || !$transaction) {
			$this->error('Transaction not found.');
		}

		if ($transaction->getUser() || !$transaction->isCommission()) {
			$this->error('Transaction cannot be assigned to user.');
		}
	}

	public function actionRefundBonus($id = null)
	{
		if ($id) {
			$refundBonus = $this->transactionFacade->find($id);

			if (!$refundBonus) {
				$this->error('Refund not found.');
			}

			$this->template->refundBonus = $refundBonus;
		}

		$this->template->shops = $this->shopFacade->findPairsLocalizationShop();
	}

	public function actionDealBonus($id = null)
	{
		if ($id) {
			/** @var Transaction $dealBonus */
			$dealBonus = $this->transactionFacade->find($id);

			if (!$dealBonus || $dealBonus->isBonusDeal()) {
				$this->error('Bonus not found.');
			}

			$this->template->dealBonus = $dealBonus;
		}
	}

	public function handleConfirm($id = null)
	{
		if ($id) {
			$transaction = $this->transactionFacade->find($id);

			if (!$transaction) {
				$this->error('Transaction not found.');
			}

			$this->transactionFacade->confirm($transaction);

			$this->redirect('this');
		}
	}

	public function handleReactivateTransaction($id = null)
	{
		if ($id) {
			$transaction = $this->transactionFacade->find($id);

			if (!$transaction) {
				$this->error('Transaction not found.');
			}

			$transaction->setBillable(true);

			$this->transactionFacade->saveTransaction($transaction);

			$this->redirect('this');
		}
	}

	public function handleCancel($id = null)
	{
		if ($id) {
			$transaction = $this->transactionFacade->find($id);

			if (!$transaction) {
				$this->error('Transaction not found.');
			}

			$this->transactionFacade->cancel($transaction);

			$this->redirect('this');
		}
	}

	public function handleGetOffers($shopId, $userIdOrEmail, $localizationId)
	{
		if (is_numeric($userIdOrEmail)) {
			$user = $this->userFacade->find($userIdOrEmail);
		} else {
			/** @var Localization $localization */
			$localization = $this->localizationFacade->find($localizationId);
			$user = $this->userFacade->findOneByEmail($userIdOrEmail, $localization);
		}

		$shop = $this->shopFacade->find($shopId);

		$this->template->shop = $shop;
		$this->template->shopOffers = $this->offerFacade->resolveOffersForShop($shop);
		$this->template->user = $user;

		$this->redrawControl('offers');
	}

	public function handleGetUserLocalizationId($userId)
	{
		$userId = $userId ? : $this->getRequest()->getPost('userId');
		$user = $this->userFacade->find($userId);

		if ($user) {
			$this->sendJson(['localizationId' => $user->getLocalization()->getId()]);
		} else {
			$this->sendJson(['localizationId' => null]);
		}
	}

	/**
	 * @return TransactionControl
	 */
	protected function createComponentTransactionControl()
	{
		$control = $this->transactionControlFactory->create();
		$control->onSuccess[] = function () {
			$this->flashMessage('Saved.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return TransactionConfirmControl
	 */
	protected function createComponentTransactionConfirmControl()
	{
		$transaction = $this->transactionFacade->find($this->getParameter('id'));

		$control = $this->transactionConfirmControlFactory->create($transaction);
		$control->onSuccess[] = function () {
			$this->flashMessage('Trasanction has been confirmed.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return \tipli\AdminModule\TransactionsModule\Forms\TransactionShareCoefficientControl
	 */
	protected function createComponentTransactionShareCoefficientControl()
	{
		$transaction = $this->transactionFacade->find($this->getParameter('id'));

		$control = $this->transactionShareCoefficientControlFactory->create($transaction);
		$control->onSuccess[] = function () {
			$this->flashMessage('Transaction has been changed.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return RefundBonusControl
	 */
	protected function createComponentRefundBonusControl()
	{
		$control = $this->refundBonusControlFactory->create($this->getParameter('userId'), $this->getUserIdentity());
		$control->onSuccess[] = function ($entity) {
			$this->flashMessage('Saved.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return DealBonusControl
	 */
	protected function createComponentDealBonusControl(): DealBonusControl
	{
		$control = $this->dealBonusControlFactory->create($this->getParameter('userId'), $this->getUserIdentity());
		$control->onSuccess[] = function ($entity) {
			$this->flashMessage('Saved.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return MarketingBonusControl
	 */
	protected function createComponentMarketingBonusControl()
	{
		$control = $this->marketingBonusControlFactory->create();
		$control->onSuccess[] = function ($entity) {
			$this->flashMessage('Saved.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	/**
	 * @return ImportTransactionsControl
	 */
	protected function createComponentImportTransactionsControl()
	{
		$control = $this->importTransactionsControlFactory->create();
		$control->onSuccess[] = function () {
			$this->flashMessage('Import has been uploaded and will be process within few minutes.');
			$this->redirect(':Admin:Transactions:Transaction:imports');
		};

		return $control;
	}

	protected function createComponentTransactionBonusEditControl()
	{
		/** @var Transaction $transaction */
		$transaction = $this->transactionFacade->find($this->getParameter('id'));

		$control = $this->transactionBonusEditControlFactory->create($transaction);
		$control->onSuccess[] = function () {
			$this->flashMessage('Transaction has been saved.');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	protected function createComponentTransactionUncancelControl()
	{
		$transaction = $this->transactionFacade->find($this->getParameter('id'));

		$control = $this->transactionUncancelControlFactory->create($transaction);
		$control->onSuccess[] = function () {
			$this->flashMessage('Transaction has been saved..');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	protected function createComponentAssignUserToTransactionControl()
	{
		$transaction = $this->transactionFacade->find($this->getParameter('id'));

		$control = $this->assignUserToTransactionControlFactory->create($transaction);
		$control->onSuccess[] = function () {
			$this->flashMessage('Transaction has been saved..');
			$this->redirect(':Admin:Transactions:Transaction:default');
		};

		return $control;
	}

	public function renderDefault()
	{
	}

	public function createComponentExternalFilterForm()
	{
		$form = new Form();

		$form->addText('transactionId');

		$form->addSelect('type')->setItems(['' => 'all'] + Transaction::getTypes());

		$form->addSelect('status')->setItems(['registered' => 'Registered', 'confirmed' => 'Confirmed', 'cancelled' => 'Canceled', 'expired' => 'Expired'])
			->setPrompt('all');

		$form->addLocalizationSelect('localization');

		$form->addUserSelect('user')
			->setLocalizationInput($form['localization']);

		$form->addShopSelect('shop')
			->setLocalizationInput($form['localization']);

		$form->addSelect('partnerSystemId', 'Partner system', $this->partnerSystemFacade->findPairs())
			->setPrompt('- select -');

		$form->addText('createdAtFrom');

		$form->addText('createdAtTo');

		$form->addText('registeredAtFrom');

		$form->addText('registeredAtTo');

		$form->addSelect('sort', 'Sort by:', ['t.id,desc' => 'Newest', 't.confirmedAt,desc' => 'Confirmed first', 't.registeredAt,asc' => 'Latest by date of registration']);

		$form->addSelect('billable', 'Billable', ['' => 'all', '1' => 'yes', '0' => 'no']);

		$form->addSubmit('submit');

		$form->setDefaults($this->transactionsFilter);

		if ($this->getExternalFilter('localizationId') !== null) {
			$form['localization']->setValue($this->localizationFacade->find($this->getExternalFilter('localizationId')));
		}

		if ($this->getExternalFilter('userId') !== null) {
			$form['user']->setValue($this->userFacade->find($this->getExternalFilter('userId')));
		}

		if ($this->getExternalFilter('shopId') !== null) {
			$form['shop']->setValue($this->shopFacade->find($this->getExternalFilter('shopId')));
		}

		$form->onSuccess[] = function (Form $form, $values) {
			$filter = [];

			if (empty($values->transactionId) === false) {
				$filter['transactionId'] = $values['transactionId'];
			} else {
				$filter['transactionId'] = null;
			}

			if (empty($values->type) === false) {
				$filter['type'] = $values['type'];
			} else {
				$filter['type'] = null;
			}

			if ($values->localization instanceof Localization) {
				$filter['localizationId'] = $values['localization']->getId();
			} else {
				$filter['localizationId'] = null;
			}

			if ($values->user instanceof User) {
				$filter['userId'] = $values['user']->getId();
			} else {
				$filter['userId'] = null;
			}

			if ($values->shop instanceof Shop) {
				$filter['shopId'] = $values['shop']->getId();
			} else {
				$filter['shopId'] = null;
			}

			if (empty($values->partnerSystemId) === false) {
				$filter['partnerSystemId'] = $values['partnerSystemId'];
			} else {
				$filter['partnerSystemId'] = null;
			}

			if (empty($values->createdAtFrom) === false) {
				$filter['createdAtFrom'] = $values['createdAtFrom'];
			} else {
				$filter['createdAtFrom'] = null;
			}

			if (empty($values->createdAtTo) === false) {
				$filter['createdAtTo'] = $values['createdAtTo'];
			} else {
				$filter['createdAtTo'] = null;
			}

			if (empty($values->registeredAtFrom) === false) {
				$filter['registeredAtFrom'] = $values['registeredAtFrom'];
			} else {
				$filter['registeredAtFrom'] = null;
			}

			if (empty($values->registeredAtTo) === false) {
				$filter['registeredAtTo'] = $values['registeredAtTo'];
			} else {
				$filter['registeredAtTo'] = null;
			}

			if (empty($values->sort) === false) {
				$filter['sort'] = $values['sort'];
			} else {
				$filter['sort'] = null;
			}

			if ($values->billable === 0 || $values->billable === 1) {
				$filter['billable'] = $values['billable'];
			} else {
				$filter['billable'] = null;
			}

			if (empty($values->status) === false) {
				$filter['status'] = $values['status'];
			} else {
				$filter['status'] = null;
			}

			$this->redirect('this', ['transactionsFilter' => $filter]);
		};

		return $form;
	}

	public function createComponentTransactionsGrid($name)
	{
		$qb = $this->transactionFacade->getTransactions()
			->innerJoin('t.transactionData', 'transactionData')
			->leftJoin('s.partnerSystems', 'shopPartnerSystem', 'WITH', 'shopPartnerSystem.partnerSystem = t.partnerSystem')
		;

		list($sort, $order) = explode(',', $this->getExternalFilter('sort'));

		$qb->addOrderBy($sort, $order);

		if ($type = $this->getExternalFilter('type')) {
			$qb->andWhere('t.type = :type')->setParameter('type', $type);
		}

		if ($transactionId = $this->getExternalFilter('transactionId')) {
			$qb->andWhere('t.transactionId = :transactionId')->setParameter('transactionId', $transactionId);
		}

		if ($partnerSystemId = $this->getExternalFilter('partnerSystemId')) {
			$qb->andWhere('t.partnerSystem = :partnerSystem')->setParameter('partnerSystem', $this->partnerSystemFacade->find($partnerSystemId));
		}

		if ($localizationId = $this->getExternalFilter('localizationId')) {
			$qb->andWhere('u.localization = :localization')->setParameter('localization', $this->localizationFacade->find($localizationId));
		}

		if ($userId = $this->getExternalFilter('userId')) {
			$qb->andWhere('u.id = :userId')->setParameter('userId', $userId);
		}

		if ($this->getExternalFilter('shopId') !== null && $shop = $this->shopFacade->find($this->getExternalFilter('shopId'))) {
			$qb->andWhere('t.shop = :shop')->setParameter('shop', $shop);
		}

		if ($createdAtFrom = $this->getExternalFilter('createdAtFrom')) {
			$qb->andWhere('t.createdAt >= :createdAtFrom')->setParameter('createdAtFrom', new \DateTime($createdAtFrom));
		}

		if ($createdAtTo = $this->getExternalFilter('createdAtTo')) {
			$qb->andWhere('t.createdAt <= :createdAtTo')->setParameter('createdAtTo', new \DateTime($createdAtTo));
		}

		if ($registeredAtFrom = $this->getExternalFilter('registeredAtFrom')) {
			$qb->andWhere('t.registeredAt >= :registeredAtFrom')->setParameter('registeredAtFrom', new \DateTime($registeredAtFrom));
		}

		if ($registeredAtTo = $this->getExternalFilter('registeredAtTo')) {
			$qb->andWhere('t.registeredAt <= :registeredAtTo')->setParameter('registeredAtTo', new \DateTime($registeredAtTo));
		}

		if ($this->getExternalFilter('billable') !== null) {
			$billable = $this->getExternalFilter('billable');
			$qb->andWhere('t.billable = :billable')->setParameter('billable', (bool) $billable);
		}

		if ($status = $this->getExternalFilter('status')) {
			if ($status === 'registered') {
				$qb->andWhere('t.confirmedAt IS NULL');
			} elseif ($status === 'confirmed') {
				$qb->andWhere('t.confirmedAt IS NOT NULL AND t.userCommissionAmount > 0');
			} elseif ($status === 'cancelled') {
				$qb->andWhere('t.confirmedAt IS NOT NULL AND t.userCommissionAmount = 0');
			} elseif ($status === 'expired') {
				$qb->andWhere('transactionData.expiredAt IS NOT NULL');
			}
		}

		$totalCount = empty($this->transactionsFilter) ? 13 * 1000 * 1000 : null;

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, new QueryBuilderDataSource($qb, $totalCount), 50);

		$grid->setAutoSubmit(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Transaction/grid/default.latte');

		$grid->addColumnText('transactionId', 'Transaction ID');

		$grid->addColumnText('type', 'Type');

		$grid->addColumnText('shop', 'Shop')->setRenderer(static function ($item) {
			return $item->getShop() ? $item->getShop()->getName() : null;
		});

		$grid->addColumnText('user', 'User');

		$grid->addColumnText('commission', 'User commission / Total commission');

		$grid->addColumnText('order', 'Order amount / Cashback')->setDefaultHide(true);

		$grid->addColumnDateTime('date', 'Created at / Confirmed at');

		$grid->addColumnDateTime('createdAt', 'Created At');

		$grid->addColumnText('status', 'Status');

		$shopFacade = $this->shopFacade;

		$grid->addAction('confirm', '', 'confirm!')->setClass('btn btn-xs btn-success')->setIcon('check')->setConfirmation(new StringConfirmation('Are you sure you want to confirm this transaction?'))->setTitle('Confirm');
		$grid->addAction('transactionConfirm', '', 'transactionConfirm')->setClass('btn btn-xs btn-primary')->setIcon('check-square')->setTitle('Edit & confirm');
		$grid->addAction('transactionRecomputeShareCoefficient', '', 'transactionRecomputeShareCoefficient')->setClass('btn btn-xs btn-primary')->setIcon('sync')->setTitle('Recalcualte coeficient');
		$grid->addAction('cancel', '', 'cancel!')->setClass('btn btn-xs btn-danger')->setIcon('times')->setConfirmation(new StringConfirmation('Are you sure you want to cancel this transaction?'))->setTitle('Cancel');
		$grid->addAction('transactionBonusEdit', '', 'transactionBonusEdit')->setClass('btn btn-xs btn-primary')->setIcon('pen')->setTitle('Edit bonus');
		$grid->addAction('assignUserToTransaction', '', 'assignUserToTransaction')->setClass('btn btn-xs btn-warning')->setIcon('user-plus')->setTitle('Assign to user');
		$grid->addAction('transactionUncancel', '', 'transactionUncancel')->setClass('btn btn-xs btn-warning')->setIcon('undo')->setTitle('Edit');
		$grid->addAction('reactivateTransaction', '', 'reactivateTransaction!')->setClass('btn btn-xs btn-primary')->setIcon('wallet')->setTitle('Make billable');

		$grid->allowRowsAction('confirm', static function (Transaction $transaction) {
			return $transaction->isAllowedConfirm();
		});
		$grid->allowRowsAction('transactionConfirm', static function (Transaction $transaction) {
			return $transaction->isAllowedConfirm();
		});
		$grid->allowRowsAction('transactionRecomputeShareCoefficient', static function (Transaction $transaction) {
			return $transaction->isAllowedConfirm();
		});
		$grid->allowRowsAction('cancel', static function (Transaction $transaction) {
			return $transaction->isAllowedCancel();
		});
		$grid->allowRowsAction('transactionBonusEdit', static function (Transaction $transaction) {
			return $transaction->isBonus();
		});
		$grid->allowRowsAction('assignUserToTransaction', static function (Transaction $transaction) {
			return (!$transaction->getUser() && !$transaction->isConfirmed() && $transaction->isCommission());
		});
		$grid->allowRowsAction('transactionUncancel', static function (Transaction $transaction) {
			return $transaction->isCancelled();
		});
		$grid->allowRowsAction('reactivateTransaction', static function (Transaction $transaction) {
			return $transaction->isBillable() === false;
		});

		if ($this->getExternalFilter('type') === 'commission') {
			$grid->addExportCsv('Csv export', 'transactions.csv')
				->setTitle('Csv export')
				->setColumns([
					new ColumnText($grid, 'transactionId', 'transactionId', 'Transaction'),
					(new ColumnText($grid, 'registeredAt', 'registeredAt', 'Date'))
						->setRenderer(static function (Transaction $transaction) {
							return $transaction->getRegisteredAt()->format('Y-m-d H:i:s');
						}),
					(new ColumnText($grid, 'shopPartnerSystem', 'shopPartnerSystem', 'Shop'))
						->setRenderer(static function (Transaction $transaction) {
							$shop = $transaction->getShop();
							$shopPartnerSystem = $shop->getShopPartnerSystem($transaction->getPartnerSystem());

							return $shopPartnerSystem?->getPartnerSystemKey();
						}),
					new ColumnText($grid, 'shopName', 'shop.name', 'Shop name'),
					new ColumnText($grid, 'orderAmount', 'transactionData.orderAmount', 'Order amount'),
					new ColumnText($grid, 'orderAmount', 'transactionData.originalCommissionAmount', 'Commission amount'),
					new ColumnText($grid, 'currency', 'transactionData.originalCurrency', 'Currency'),
				]);
		}
	}

	private function getExternalFilter($key)
	{
		$filters = $this->transactionsFilter;

		if (isset($filters[$key])) {
			return $filters[$key];
		}

		if ($key == 'sort') {
			return 't.id,desc';
		}

		return null;
	}

	public function createComponentPartnerSystemsGrid($name)
	{
		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $this->partnerSystemFacade->getPartnerSystems()->addOrderBy('p.lastImportAt', 'desc'));

		$grid->setTemplateFile(__DIR__ . '/templates/Transaction/grid/imports.latte');

		$grid->addColumnText('partnerSystem', 'Partner system')->setRenderer(static function ($item) {
			return $item->getName() . ' (' . $item->getType() . ')';
		});

		$grid->addColumnText('createdAt', 'Last import at')->setRenderer(static function ($item) {
			return $item->getLastImportAt() ? $item->getLastImportAt()->format('d.m.Y H:i:s') : '- none -';
		});
	}
}
