{var $title = 'Nastavení odměny'}
{var $freeContent = true}

{block title}{$title}{/block}

{block content}

{form constantRewardControl-form}
    <div class="row">
        <div class="col-md-6 offset-md-3">
            {if $constantReward && $constantReward->getParentConstantReward()}
                <a n:href="ConstantReward:constantReward, 'id' => $constantReward->getParentConstantReward()->getId(), 'parentId' => null" style="display: block; margin-bottom: 5px">zpět</a>
            {else}
                <a n:href="ConstantReward:default" style="display: block; margin-bottom: 5px">zpět</a>
            {/if}

            <div class="card card-accent-primary">
                <div class="card-header">
                    <strong n:if="$constantReward === null || $constantReward->getParentConstantReward()">Akční odměna</strong>
                    <strong n:if="$constantReward && $constantReward->getParentConstantReward() === null">Výchozí odměna</strong>
                </div>
                <div class="card-body">
                    <div n:if="$form->hasErrors()">
                        <ul class="errors">
                            <li n:foreach="$form->errors as $error">{$error |noescape}</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="form-group col-md-4">
                            <label>Lokalizace</label>
                            {input localization, class => "form-control"}
						    {$form['localization']->getCustomHtml() |noescape}
                        </div>

                        <div class="form-group col-md-8">
                            {label name /}
						    {input name, class => "form-control"}
                        </div>
                    </div>

                    <div class="form-group">
                        {label amount /}
					    {input amount, class => "form-control"}
                    </div>

                    {if !$constantReward || !$constantReward->isDefault()}
                        <div class="row">
                            <div class="form-group col-md-4">
                                <label>Platnost od</label>
                                {input validSince, class => "form-control datetimepicker"}
                            </div>

                            <div class="form-group col-md-4">
                                <label>Platnost do</label>
                                {input validTill, class => "form-control datetimepicker"}
                            </div>
                        </div>
                    {/if}
                </div>
            </div>

            <div class="card card-accent-primary" n:if="$constantReward && $constantReward->isDefault()">
                <div class="card-header">
                    <strong>Akční odměny</strong>
                    <div class="float-right">
                        <a n:href="constantReward, parentId => $constantReward->getId()" class="badge badge-success">Vytvořit odměnu</a>
                    </div>
                </div>

                <div class="card-body">
                    {control specialConstantRewardsGrid}
                </div>
            </div>

            <div class="card card-accent-primary" n:if="!$constantReward || !$constantReward->isDefault()">
                <div class="card-body">
                    <div class="row">
                        <div class="form-group col-md-12 text-center">
                            {input submit, class => "btn btn-primary"}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{/form}
