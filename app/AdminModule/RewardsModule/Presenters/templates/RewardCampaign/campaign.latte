{var $title = 'Kampaň'}
{var $freeContent = true}

{block title}{$title}{/block}

{block content}

<div class="row">
	<div class="col-md-7">
		<div class="card card-accent-primary">
			<div class="card-body">
				{include '../../../../Forms/bootstrap-form-title.latte', title => $title}
				{control rewardCampaignControl}
			</div>
		</div>
	</div>
	<div class="col-md-5">
		<div class="card card-accent-primary">
			<div class="card-body">
				<div id="utms-editor"></div>
			</div>
		</div>
	</div>
</div>

<script n:syntax="off">
    var editor = new JSONEditor(document.getElementById('utms-editor'),{
        "schema" :{
            "title": "UTM parametry",
            "type": "array",
            "format": "table",
            "items": {
                "type": "object",
                "properties": {
                    "utm_source": { "type": "string" }, "utm_medium": { "type": "string" }, "utm_campaign": { "type": "string" }
                }
            }
        },
        "collapsed": "true",
        "theme": "bootstrap3",
        "disable_array_reorder": true,
        "disable_edit_json": true,
        "disable_collapse": true,
        "disable_properties": true,
        "disable_array_delete_last_row": true,
    });

    editor.on('ready',function() {
        editor.setValue(JSON.parse($("#utms").val()));
    });
    editor.on('change',function() {
        $("#utms").val(JSON.stringify(editor.getValue()));
    });
</script>
<style>
    #utms-editor table { width:100% !important; }
    #utms-editor table thead tr th:nth-child(1) { width:10% !important; }
    #utms-editor table thead tr th:nth-child(2) { width:15% !important; }
    #utms-editor table thead tr th:nth-child(3) { width:55% !important; }
    #utms-editor table thead tr th:nth-child(4) { width:5% !important; }
</style>
