<?php

namespace tipli\AdminModule\RewardsModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use tipli\AdminModule\Presenters\BasePresenter;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Rewards\MoneyRewardFacade;
use tipli\Model\Transactions\TransactionFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;

class MoneyRewardPresenter extends BasePresenter
{
	/** @var MoneyRewardFacade @inject */
	public $moneyRewardFacade;

	/** @var TransactionFacade @inject */
	public $transactionFacade;

	public function actionUseReward($id = null) // vyexpiruje
	{
		if ($id) {
			$moneyReward = $this->moneyRewardFacade->findMoneyReward($id);

			if (!$moneyReward) {
				$this->error('Odměna nebyla nalezena.');
			}

			$this->moneyRewardFacade->useMoneyReward($moneyReward);

			$this->redirect(':Admin:Rewards:MoneyReward:default');
		}
	}

	public function actionForceApplyReward($id = null) // manuálně aplikuje odměnu
	{
		if ($id) {
			/** @var MoneyReward $moneyReward */
			$moneyReward = $this->moneyRewardFacade->findMoneyReward($id);

			if (!$moneyReward) {
				$this->error('Odměna nebyla nalezena.');
			}

			$moneyReward->setMinimumCommissionBalance(0);

			$this->moneyRewardFacade->useMoneyReward($moneyReward);

			$this->transactionFacade->createMoneyRewardBonusTransaction($moneyReward->getUser(), $moneyReward->getName(), $moneyReward->getAmount());

			$this->redirect(':Admin:Account:User:userCard', ['id' => $moneyReward->getUser()->getId()]);
		}
	}

	public function createComponentRewardsGrid($name)
	{
		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $this->moneyRewardFacade->getMoneyRewards()->addOrderBy('mr.id', 'desc'));

		$grid->addColumnText('email', 'E-mail', 'user.email');

		$grid->addColumnText('amount', 'Částka')->setRenderer(static function ($item) {
			return $item->getAmount() . ' ' . $item->getUser()->getLocalization()->getCurrency();
		});

		$grid->addColumnText('treshold', 'Min. naspořeno')->setRenderer(static function (MoneyReward $moneyReward) {
			return $moneyReward->getMinimumCommissionBalance() . ' ' . $moneyReward->getUser()->getLocalization()->getCurrency();
		});

		$grid->addColumnText('createdAt', 'Vytvořeno')->setRenderer(static function ($item) {
			return $item->getCreatedAt()->format('d.m.Y H:i:s');
		});

		$grid->addColumnText('validTill', 'Platnost do')->setRenderer(static function ($item) {
			return $item->getValidTill()->format('d.m.Y H:i:s');
		});

		$grid->addColumnText('usedAt', 'Využito')->setRenderer(static function ($item) {
			return $item->getUsedAt() ? $item->getUsedAt()->format('d.m.Y H:i:s') : (!$item->isValid() ? '- vypršel -' : '');
		});

		$grid->addFilterText('email', 'Uživatel:')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('u.email LIKE :email')->setParameter('email', '%' . $value . '%');
			});

		$grid->addAction('useReward', '', 'useReward')->setClass('btn btn-xs btn-primary')->setIcon('times');
		$grid->addAction('forceApplyReward', '', 'forceApplyReward')->setClass('btn btn-xs btn-primary')->setIcon('check')
			->setConfirmation(
				new StringConfirmation('Určitě chcete bonus převést do bonusové transakce?')
			)
		;

		$grid->allowRowsAction('useReward', static function ($item) {
			return !$item->isUsed();
		});
		$grid->allowRowsAction('forceApplyReward', static function ($item) {
			return !$item->isUsed();
		});
	}
}
