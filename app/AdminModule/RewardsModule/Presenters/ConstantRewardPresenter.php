<?php

namespace tipli\AdminModule\RewardsModule\Presenters;

use tipli\AdminModule\Presenters\BasePresenter;
use tipli\AdminModule\RewardsModule\Forms\ConstantRewardControl\IConstantRewardControlFactory;
use tipli\Model\Rewards\Entities\ConstantReward;
use tipli\Model\Rewards\RewardFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;

class ConstantRewardPresenter extends BasePresenter
{
	/** @var RewardFacade @inject */
	public $rewardFacade;

	/** @var IConstantRewardControlFactory @inject */
	public $constantRewardControlFactory;

	/** @var int @persistent */
	public $parentId;

	public function renderDefault()
	{
	}

	public function renderConstantReward(?int $id = null, ?int $parentId = null)
	{
		$this->template->constantReward = $id ? $this->rewardFacade->findConstantReward($id) : null;
	}

	public function createComponentConstantRewardGrid($name): DataGrid
	{
		$grid = $this->dataGridFactory->create()
			->getGrid(
				$this,
				$name,
				$this->rewardFacade->getConstantRewards()
					->addOrderBy('c.type', 'DESC')
					->andWhere('c.parentConstantReward IS NULL')
			)
		;

		$grid->addColumnText('localization', 'Jazyk')
			->setRenderer(static function (ConstantReward $constantReward) {
				return $constantReward->getLocalization()->getName();
			})->setFilterSelect(['' => 'vše'] + $this->localizationFacade->findPairs())
		;

		$grid->addColumnText('name', 'Název')
			->setFilterText()
		;

		$grid->addColumnText('type', 'Typ')
			->setFilterText()
		;

		$grid->addColumnText('amount', 'Výchozí výše odměny')
			->setRenderer(function (ConstantReward $constantReward) {
				return $constantReward->getAmount() . ' ' . $this->currencyFilter->__invoke($constantReward->getCurrency());
			})
		;

		$grid->addColumnText('currentAmount', 'Právě aktuální výše odměny')
			->setTemplateEscaping(false)
			->setRenderer(function (ConstantReward $constantReward) {
				$currentSpecialConstantReward = $this->rewardFacade->findValidSpecialConstantRewardByType($constantReward->getLocalization(), $constantReward->getType(), $constantReward);

				if ($currentSpecialConstantReward !== null) {
					$html = '<span class="badge badge-success">' . $currentSpecialConstantReward->getAmount() . ' ' . $this->currencyFilter->__invoke($currentSpecialConstantReward->getCurrency()) . '</span>';
					$html .= ' Platí do ' . $currentSpecialConstantReward->getValidTill()->format('d.m.Y H:i');

					return $html;
				}

				return $constantReward->getAmount() . ' ' . $this->currencyFilter->__invoke($constantReward->getCurrency());
			})
		;

		$grid->addAction('constantReward', '', 'constantReward')
			->setClass('btn btn-xs btn-primary')
			->setIcon('pen')
		;

		return $grid;
	}

	public function createComponentSpecialConstantRewardsGrid($name): DataGrid
	{
		$parentConstantReward = $this->rewardFacade->findConstantReward($this->getParameter('id'));

		$grid = $this->dataGridFactory->create()
			->getGrid(
				$this,
				$name,
				$this->rewardFacade->getConstantRewards()
					->addOrderBy('c.createdAt', 'DESC')
					->andWhere('c.parentConstantReward = :parentConstantReward')
				->setParameter('parentConstantReward', $parentConstantReward)
			)
		;

		$grid->addColumnText('name', 'Název');

		$grid->addColumnText('amount', 'Výše odměny')
			->setRenderer(function (ConstantReward $constantReward) {
				return $constantReward->getAmount() . ' ' . $this->currencyFilter->__invoke($constantReward->getCurrency());
			})
		;

		$grid->addColumnDateTime('validSince', 'Platnost od');

		$grid->addColumnDateTime('validTill', 'Platnost do');

		$grid->addAction('constantReward', '', 'constantReward', ['id' => 'id', 'parentId' => 'parentId'])
			->setClass('btn btn-xs btn-primary')
			->setIcon('pen')
		;

		$grid->addAction('removeConstantReward', '', 'removeConstantReward!')
			->setConfirmation(new StringConfirmation('Opravdu? Po smazání odměny uživatelé, kteří měli odměnu získat namísto této obdrží výchozí odměnu.'))
			->setClass('btn btn-xs btn-danger')
			->setIcon('times')
		;

		return $grid;
	}

	public function handleRemoveConstantReward(int $id)
	{
		$constantReward = $this->rewardFacade->findConstantReward($id);
		$parentConstantReward = $constantReward->getParentConstantReward();

		if ($parentConstantReward === null) {
			$this->redirect('this');
		}

		$this->rewardFacade->removeConstantReward($constantReward);

		$this->redirect('constantReward', ['id' => $parentConstantReward->getId()]);
	}

	protected function createComponentConstantRewardControl()
	{
		$constantReward = $this->getParameter('id') ? $this->rewardFacade->findConstantReward($this->getParameter('id')) : null;
		$parentConstantReward = $this->getParameter('parentId') ? $this->rewardFacade->findConstantReward($this->getParameter('parentId')) : null;

		$control = $this->constantRewardControlFactory->create($constantReward, $parentConstantReward);

		$control->onSuccess[] = function (ConstantReward $constantReward, ConstantReward $parentConstantReward) {
			$this->flashMessage('Úspěšně uloženo.');
			$this->redirect('constantReward', ['id' => $constantReward->getId(), 'parentId' => $parentConstantReward->getId()]);
		};

		return $control;
	}
}
