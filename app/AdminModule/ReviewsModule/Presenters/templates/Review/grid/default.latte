{extends $originalTemplate}

{define col-user}
    {$item->getUserName()}
    {if $item->getUser()}
        <br />
        {$item->getUser()->getEmail()} ({$item->getUser()->getId()})
    {/if}
{/define}
{define col-shop}
    {if $item->getShop()}
        {$item->getShop()->getLocalization()->getLocale()}
        {$item->getShop()->getName()}
    {else}
        -
    {/if}
{/define}
{define col-date}
    {$item->getCreatedAt()->format('d.m.Y H:i:s')}
    {if $item->isApproved()}
        <br />
        {$item->getApprovedAt()->format('d.m.Y H:i:s')}<br />
    {/if}
{/define}
{define col-status}
    {if $item->isApproved()}
        <span class="badge badge-success">confirmed</span>
    {elseif $item->isDeleted()}
        <span class="badge badge-danger">declined</span>
    {else}
        <span class="badge badge-warning">waiting</span>
    {/if}
{/define}
{define col-prioritized}
    {if $item->isApproved()}
        {if $item->isPrioritized()}
            <span class="badge badge-success">prioritized</span>
        {else}
            <span class="badge badge-danger">without priority</span>
        {/if}
    {/if}
{/define}
{define col-picture}
    {if $item->getUserPicture()}
        <img src="{$item->getUserPicture() |image:100 |noescape}" />
    {else}
        -
    {/if}
{/define}
{define col-state}
    {if $item->getState() === 'resolved'}
        <span class="badge badge-success">Resolved</span>
    {elseif $item->getState() === 'user_not_responding'}
        <span class="badge badge-primary">Not responding</span>
    {elseif $item->getState() === 'unresolved'}
        <span class="badge badge-danger">Unresolved</span>
    {elseif $item->getState() === 'mistake'}
        <span class="badge badge-primary">Mistake</span>
    {elseif $item->getState() === 'waiting'}
        <span class="badge badge-warning">Waiting for answer</span>
    {/if}
{/define}