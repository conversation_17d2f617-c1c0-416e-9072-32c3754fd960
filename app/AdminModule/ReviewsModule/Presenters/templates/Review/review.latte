{var $title = 'Nastavení review'}
{var $freeContent = true}

{block title}{$title}{/block}

{block content}

{form reviewControl-form}
	<div class="row">
		<div class="col-md-6 offset-md-3">
			<div class="card card-accent-primary">
				<div class="card-header">
					<strong>Nastavení review</strong>
				</div>
				<div class="card-body">
					<div n:if="$form->hasErrors()">
						<ul class="errors">
							<li n:foreach="$form->errors as $error">{$error |noescape}</li>
						</ul>
					</div>
					<div class="row">
						<div class="form-group col-md-4">
							<label>Lokalizace: </label>
							{input localization, class => "form-control"}
							{$form['localization']->getCustomHtml() |noescape}
						</div>

						<div class="form-group col-md-8">
							<label>Obchod: </label>
							{input shop, class => "form-control"}
							{$form['shop']->getCustomHtml() |noescape}
						</div>
					</div>
					<div class="row">
						<div class="form-group col-md-6">
							<label>Uživatelské jméno: </label>
							{input userName, class => "form-control"}
						</div>

						<div class="form-group col-md-6">
							<label>Uživatel: </label>
							{input user, class => "form-control"}
							{$form['user']->getCustomHtml() |noescape}
						</div>
					</div>

					<div class="row">
						<div class="form-group col-md-12">
							<label>Text: </label>
							{input text, class => "form-control"}
						</div>
					</div>

					<div class="row">
						<div class="form-group col-md-12">
							<div class="radio-w" style="display: none">
								{foreach $form[rate]->items as $key => $label}
									<div class="radio-b">
										<input
												n:name="rate:$key"
												type="radio"
												id="rate-{$key}"
												name="rate"
												value="{$key}"
												class="rate-input"
										>
										<label for="rate-{$key}" class="radiolabel">{$label}</label>
									</div>
								{/foreach}
							</div>
							<div class="score-star">
								{foreach range(1, 5) as $i}
									<i
											class="fa fa-star star"
											data-value="{$i}"
											aria-hidden="true"
									></i>
								{/foreach}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="card card-accent-primary">
				<div class="card-body">
					<div class="row">
						<div class="form-group col-md-12 text-center">
							{input submit, class => "btn btn-primary"}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{/form}

<script>
	document.addEventListener('DOMContentLoaded', function () {
		const stars = document.querySelectorAll('.score-star .star');
		const radioInputs = document.querySelectorAll('.rate-input');

		function updateStars(selectedValue) {
			stars.forEach(star => {
				if (parseInt(star.dataset.value) <= selectedValue) {
					star.classList.add('active');
				} else {
					star.classList.remove('active');
				}
			});
		}

		const preselectedInput = Array.from(radioInputs).find(input => input.checked);
		if (preselectedInput) {
			updateStars(parseInt(preselectedInput.value));
		}

		stars.forEach(star => {
			star.addEventListener('click', function () {
				const selectedValue = parseInt(star.getAttribute('data-value'));
				document.getElementById("rate-" + selectedValue).checked = true;
				updateStars(selectedValue);
			});
		});
	});
</script>

<style>
	.fa-star {
		color: gray;
		cursor: pointer;
		font-size: 28px;
	}
	.fa-star.active {
		color: yellow;
	}
</style>
