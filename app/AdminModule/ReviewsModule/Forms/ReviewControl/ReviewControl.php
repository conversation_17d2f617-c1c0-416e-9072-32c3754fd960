<?php

namespace tipli\AdminModule\ReviewsModule\Forms\ReviewControl;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Reviews\Entities\Review;
use tipli\Model\Reviews\ReviewFacade;

class ReviewControl extends Nette\Application\UI\Control
{
	/** @var array<mixed> */
	public array $onSuccess = [];

	public function __construct(
		private ?Review $review,
		private ReviewFacade $reviewFacade,
	) {
	}

	/**
	 * @return Form
	 */
	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addLocalizationSelect('localization')
			->setRequired('Zvolte lokalizaci');

		$form->addShopSelect('shop')
			->setLocalizationInput($form['localization'])
			->setRequired('Zvolte obchod');

		$form->addText('userName')
			->setRequired()
			->setRequired('Zvolte jméno');

		$form->addUserSelect('user')
			->setLocalizationInput($form['localization'])
			->setRequired();

		$form->addRadioList('rate', null, [1 => 'r1', 2 => 'r2', 3 => 'r3', 4 => 'r4', 5 => 'r5'])
			->setDefaultValue(5)
			->setRequired();

		$form->addTextArea('text');

		$form->addSubmit('submit', 'Uložit');

		if ($this->review) {
			$form['localization']->setDisabled();

			$form->setDefaults([
				'localization' => $this->review->getLocalization(),
				'shop' => $this->review->getShop(),
				'userName' => $this->review->getUserName(),
				'user' => $this->review->getUser(),
				'rate' => $this->review->getRate(),
				'text' => $this->review->getText(),
			]);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values): void
	{
		try {
			if ($this->review) {
				$localization = $this->review->getLocalization();
			} else {
				$localization = $values->localization;
			}


			$this->reviewFacade->createAdminReview(
				$localization,
				$this->review,
				$values->user,
				$values->shop,
				$values->userName,
				$values->rate,
				$values->text
			);
			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render(): void
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}
