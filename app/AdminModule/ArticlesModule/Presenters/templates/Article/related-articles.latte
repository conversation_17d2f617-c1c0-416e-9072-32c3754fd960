{var $relatedArticles = $article->getRelatedArticles()}
{if !$relatedArticles->isEmpty()}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <th>
                    <PERSON><PERSON><PERSON><PERSON>
                </th>
                <th>
                    Priorita
                </th>
                <th width="77px">
                </th>
            </thead>
            <tbody>
            <tr n:foreach="$relatedArticles as $relatedArticle">
                <td>
                    {$relatedArticle->getRelatedArticle()->getName()}
                </td>
                <td>
                    {$relatedArticle->getPriority()}
                </td>
                <td>
                    <a n:href="removeRelatedArticle!, $relatedArticle->getId()" class="btn btn-danger btn-xs ajax" onclick="return confirm('Skutečně smazat?');">
                        <span class="fa fa-times"></span>
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
{else}
    <div class="alert alert-info">Žádný záznam.</div>
{/if}

{form addRelatedArticleControl-form, class => "ajax"}
    {input articleId}
    {input priority, placeholder => "Priorita v pořadí"}
    {input submit, class => "btn btn-success"}
{/form}
