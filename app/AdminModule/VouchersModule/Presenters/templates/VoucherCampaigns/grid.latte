{extends $originalTemplate}

{define col-state}
	{varType tipli\Model\Vouchers\Entities\VoucherCampaign $item}

	{if $item->getValidSince() < (new DateTime()) && $item->getValidTill() > (new DateTime())}
		<span class="badge badge-success">aktivní</span>
	{/if}
	{$item->getValidSince()|date:'d.m.Y H:i'} - {$item->getValidTill()|date:'d.m.Y H:i'}
{/define}

{define col-publicSince}
	{varType tipli\Model\Vouchers\Entities\VoucherCampaign $item}

	{if $item->getPublicSince() < (new DateTime())}
		<span class="badge badge-success">viditelný</span>
	{/if}
	{$item->getPublicSince()|date:'d.m.Y H:i'}
{/define}
