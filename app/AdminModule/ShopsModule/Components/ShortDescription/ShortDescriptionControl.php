<?php

namespace tipli\AdminModule\ShopsModule\Components\ShortDescription;

use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\Template;
use tipli\Model\Account\Entities\User;
use tipli\Model\ChatGPT\ChatGPTClient;
use tipli\Model\Shops\Entities\DescriptionBlock;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopExternalData;
use tipli\Model\Shops\ShopFacade;
use stdClass;
use Tracy\Debugger;

/**
 * @property-read Template|\stdClass $template
 */
class ShortDescriptionControl extends Control
{
	public array $onSuccess = [];
	public array $onDelete = [];

	public function __construct(
		private Shop $shop,
		private ShopExternalData $shopExternalData,
		private User $user,
		private ShopFacade $shopFacade,
		private ChatGPTClient $chatGPTClient
	) {
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addTextArea('description')
			->setOption('description', 'Generated description')
			->setHtmlAttribute('class', 'form-control')
			->setHtmlAttribute('rows', '15')
			->setHtmlAttribute('width', '100%');

		$form->addSubmit('confirm', 'Approve and add description')
			->setHtmlAttribute('class', 'btn btn-success');
		$form->addSubmit('decline', 'Deny')
			->setHtmlAttribute('class', 'btn btn-danger');
		$form->onSuccess[] = function (Form $form, stdClass $values) {

			$descriptionBlock = $this->shop->getDescriptionBlock(DescriptionBlock::TYPE_SHORT_DESCRIPTION);

			if ($descriptionBlock === null) {
				$descriptionBlock = $this->shopFacade->createDescriptionBlock($this->shop, DescriptionBlock::TYPE_SHORT_DESCRIPTION, $values->description);
			}

			if ($form['confirm']->isSubmittedBy()) {
				$descriptionBlock->setDescription($values->description);
				$descriptionBlock->unCancel();
				$this->shopFacade->saveDescriptionBlock($descriptionBlock);
				$this->onSuccess($this->shop, true);
			} elseif ($form['decline']->isSubmittedBy()) {
				$descriptionBlock->setDescription(null);
				$descriptionBlock->cancel();
				$this->shopFacade->saveDescriptionBlock($descriptionBlock);
				$this->onSuccess($this->shop, false);
			}
		};
		return $form;
	}

	public function handleGenerateDescription(): void
	{
		$title = $this->shopExternalData->getTitle() ? $this->shopExternalData->getTitle() : $this->shop->getName();

		$prompt = 'vygeneruj popisek bez HTML tagů, emoji, tagů a speciálních znaků, použij marketingové slova spojená se slevami, s maximální délkou 255 znaků POUZE v jazyce ' . strtoupper($this->shop->getLocalization()->getLocale() === 'cs' ? 'CZ' : $this->shop->getLocalization()->getLocale()) .
			', z názvu ' . $title .
			', z domény ' . $this->shop->getDomain();

		if ($this->shopExternalData->getDescription() !== null) {
			$prompt .= ', z popisku ' . $this->shopExternalData->getDescription();
		}

		if ($this->shopExternalData->getKeywords() !== null) {
			$prompt .= ', z klíčových slov ' . $this->shopExternalData->getKeywords();
		}

		$prompt .= '. Klíčové slova se pokus použít v první třetině textu';

		bdump($prompt);

		$description = str_replace('"', '', $this->chatGPTClient->getCompletion($prompt));

		Debugger::log('Generated description for shop ' . $this->shop->getId(), 'short-description-generate');

		$translatedDescription = null;
		if ($description !== null && $description !== '' && $this->shop->getLocalization()->isCzech() === false) {
			$translatedDescription = str_replace('"', '', $this->chatGPTClient->translateText($description, $this->shop->getLocalization()->getLocale(), 'cs'));
			Debugger::log('Generated translated description for shop ' . $this->shop->getId(), 'short-description-generate');
		}

		$this->getPresenter()->payload->prompt = $prompt;
		$this->getPresenter()->payload->description = $description;
		$this->getPresenter()->payload->translatedDescription = $translatedDescription;

		$this->getPresenter()->sendPayload();
	}

	public function render(): void
	{
		$this->template->notes = $this->shopFacade->getShopNotes($this->shop);
		$this->template->render(__DIR__ . '/control.latte');
	}
}
