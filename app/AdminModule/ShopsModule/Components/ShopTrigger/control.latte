{snippet form}
{form form, class => 'ajax'}
	<div class="row">
		<div n:if="$form->hasErrors()">
			<ul class="errors">
				<li n:foreach="$form->errors as $error">{$error |noescape}</li>
			</ul>
		</div>
		<div class="form-group col-md-6">
			<label n:name="monetization">{$form['monetization']->caption}</label>
			<br />
			<div class="btn-group btn-group-toggle" data-toggle="buttons" style="margin-bottom:15px;">
				<label class="btn btn-outline-primary" n:foreach="$form['monetization']->items as $key => $label">
					<input type="radio" n:name="monetization:$key" autocomplete="off"> {$label}
				</label>
			</div>
		</div>

		<div class="form-group col-md-6">
			<label n:name="scheduledAt">{$form['scheduledAt']->caption}</label>
			<br />
			<input n:name="scheduledAt" class="form-control hour-datetimepicker">
		</div>

		<div class="form-group col-md-6" id="partnerSystem">
			<label n:name="partnerSystem">{$form['partnerSystem']->caption}</label>
			<br />
			{input partnerSystem, class => "selectpicker", data-width => "100%"}
		</div>

		<div class="form-group col-md-6" id="partnerSystemRedirectUrl">
			<label n:name="partnerSystemRedirectUrl">{$form['partnerSystemRedirectUrl']->caption}</label>
			{input partnerSystemRedirectUrl, class => "form-control"}
		</div>

		<div class="form-group col-md-12">
			<label n:name="reason">{$form['reason']->caption}</label>
			<br />
			<textarea n:name="reason" class="form-control">	</textarea>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<input n:name="schedule" class="btn btn-success">
		</div>
	</div>
{/form}
{/snippet}

{snippet table}
	<table class="table table-striped mt-5 pt-1">
		<thead>
		<tr>
			<th>
				Reason
			</th>
			<th>
				Data
			</th>
			<th>
				Scheduled at
			</th>
			<th>
				Processed at
			</th>
			<th>
				Created by
			</th>
			<th>
				Created at
			</th>
			<th>
				Action
			</th>
		</tr>
		</thead>
		<tbody>
		<tr n:foreach="$shopTriggers as $trigger">
			{varType tipli\Model\Shops\Entities\ShopTrigger $trigger}
			<td>
				{$trigger->getReason()}
			</td>
			<td>
				{var $shopTriggerData = $trigger->getShopTriggerData()}

				{if $shopTriggerData->getMonetization() === 'none'}
					<b>Monetization:</b> None
				{elseif $shopTriggerData->getMonetization() === 'affiliate'}
					<b>Monetization:</b> Affiliate <br />
					<b>Partner system:</b> {$partnerSystems[$shopTriggerData->getPartnerSystem()]}<br />
					<b>Partner system URL:</b> {$shopTriggerData->getPartnerSystemRedirectUrl()}
				{elseif $shopTriggerData->getMonetization() === 'cashback'}
					<b>Monetization:</b> Cashback <br />
					<b>Partner system:</b> {$partnerSystems[$shopTriggerData->getPartnerSystem()]}<br />
					<b>Partner system URL:</b> {$shopTriggerData->getPartnerSystemRedirectUrl()}
				{/if}
			</td>
			<td>
				{$trigger->getScheduledAt()|date:"d.m.Y H:i:s"}
			</td>
			<td>
				{if $trigger->getProcessedAt()}
					<span class="badge badge-success">{$trigger->getProcessedAt()|date:"d.m.Y H:i:s"}</span>
				{else}
					<span class="badge badge-warning">Not processed yet</span>
				{/if}
			</td>
			<td>
				{$trigger->getCreatedBy()->getFullName()}
			</td>
			<td>
				{$trigger->getCreatedAt()|date:"d.m.Y H:i:s"}
			</td>
			<td>
				<a n:if="$trigger->isProcessed() === false" class="btn btn-danger btn-xs ajax" onclick="return confirm('Do you really want to remove scheduled update?');" href="{link remove, $trigger->getId()}">
					<span class="fa fa-times" aria-hidden="true"></span>
				</a>
			</td>
		</tr>
		</tbody>
	</table>
{/snippet}

{snippet script}
<script>
	$(document).ready(function(){
		$('#partnerSystem, #partnerSystemRedirectUrl').hide();
		$('input[name^="monetization"]').change(function(){
			if ($(this).val() === 'none') {
				$('#partnerSystem, #partnerSystemRedirectUrl').hide();
			} else {
				$('#partnerSystem, #partnerSystemRedirectUrl').show();
			}
		});
	});
</script>
{/snippet}
