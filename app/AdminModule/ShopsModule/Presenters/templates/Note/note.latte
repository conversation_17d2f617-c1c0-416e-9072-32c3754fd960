{var $title = 'Note'}

{block headerContent}
    <a n:href="Note:, id: null" class="btn btn-light btn-sm back-button back-button"> < zpět</a>
{/block}


{block title}{$title}{/block}

{block scripts}
    <script type="text/javascript">
        var searchInputs = $('.search-input');

        searchInputs.each(function(){
            var searchInputTargetUrl = $(this).data('searchUrl');

            $(this).select2({
                minimumInputLength: 2,
                closeOnSelect: false,
                ajax: {
                    url: searchInputTargetUrl,
                    dataType: 'json',
                    delay: 500,
                    type: 'GET',
                    cache: true,
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item, index) {
                                return {
                                    text:  item,
                                    id: index
                                }
                            })
                        };
                    }
                }
            });
        });
    </script>
{/block}

{block content}

<div class="row justify-content-md-center">
    <div class="col-xl-6">
        <div class="card card-h-100">
            <div class="card-header justify-content-between d-flex align-items-center">
                <h4 class="card-title">Update note</h4>
            </div><!-- end card header -->
            <div class="card-body">
                <div class="">
                    {form internalNoteForm-form}
                        <div>
                            <label class="form-label">Shop's</label>
                            {input shopIds, class: "form-control search-input select-items"}
                        </div>

                        <div class='text-secondary my-2'>or</div>

                        <div>
                            <label class="form-label" for="formrow-firstname-input">Partner system</label>
                            {input partnerSystemId, class: "form-control"}
                        </div>

                        <div class='mt-3'>
                            <label class="form-label" for="formrow-firstname-input">Note</label>
                            {input note, class: "form-control", rows: 3}
                        </div>

                        <div class='mt-3'>
                            <label class="form-label" for="formrow-firstname-input">Group</label>
                            {input group, class: "form-control"}
                        </div>

                        <div class="form-group">
                            <div class="form-check mt-3">
                                <input n:name="validityDate" type="checkbox" class="form-check-input">
                                <label class="form-check-label" for="frm-internalNoteForm-form-validityDate">Set validity date</label>
                            </div>
                        </div>

                        <div id='validTillContainer'>
                            {input validTill, class: "form-control"}
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary w-md">Submit</button>
                        </div>
                    {/form}
                </div>
            </div><!-- end card body -->
        </div><!-- end card -->
    </div>
</div>

{if $internalNote && $internalNote->getChildrenInternalNotes()->isEmpty() === false}
    <div class="row justify-content-md-center">
        <div class="col-xl-6">
            <div class="card card-h-100">
                <div class="card-header justify-content-between d-flex align-items-center">
                    <h4 class="card-title">Note history</h4>
                </div><!-- end card header -->
                <div class="card-body">
                    <div>
                        {foreach $internalNote->getChildrenInternalNotes() as $childNote}
                            <div class='border-left pl-2'>
                                {$childNote->getCreatedAt() |date: 'd.m.Y H:i:s'}
                                <p>{$childNote->getNote()}</p>
                            </div>
                        {/foreach}
                    </div>
                </div><!-- end card body -->
            </div><!-- end card -->
        </div>
    </div>
{/if}