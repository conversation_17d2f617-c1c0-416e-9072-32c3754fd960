<?php

namespace tipli\AdminModule\ShopsModule\Presenters;

use tipli\AdminModule\Presenters\BasePresenter;
use tipli\AdminModule\ShopsModule\Forms\ISortDealTagSelectControlFactory;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopTag;
use tipli\Model\Shops\Events\ShopsUpdatedEvent;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;

class SortPresenter extends BasePresenter
{
	private const TYPE_DEALS = 'deals';
	private const TYPE_SHOPS = 'shops';
	private const TYPE_LEAFLET_SHOPS = 'leaflet-shops';

	/** @var DealFacade @inject */
	public $dealFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var ISortDealTagSelectControlFactory @inject */
	public $sortDealTagSelectControl;

	/** @var EventDispatcher @inject */
	public EventDispatcher $eventDispatcher;

	public function renderDefault($type = null, $localizationId = null)
	{
		$this->template->localizations = $this->localizationFacade->findLocalizations();
	}

	public function renderDeals($localizationId, $limit = null)
	{
		$this->setView('items');

		$localization = $this->localizationFacade->find($localizationId);
		$limit = $limit ?? 50;

		$dealsQuery = $this->dealFacade->createDealsQuery($localization);

		if ($tagId = $this->getParameter('tagId')) {
			$tag = $this->tagFacade->find($this->getParameter('tagId'));
			$dealsQuery->withTag($tag);
		}

		$dealsQuery->sortTop();
		$dealsQuery->onlyValid(true);

		$deals = $this->dealFacade->fetch($dealsQuery);

		if ($limit != 0) {
			$deals->applyPaging(0, $limit);
		}

		$this->template->tagId = $tagId;
		$this->template->deals = $deals;
		$this->template->limit = $limit;
		$this->template->type = self::TYPE_DEALS;
		$this->template->localization = $localization;
		$this->template->startPriority = $deals->getTotalCount();
	}

	public function createComponentSortDealTagSelectControl()
	{
		$control = $this->sortDealTagSelectControl->create($this->localizationFacade->find($this->getParameter('localizationId')), $this->getParameter('tagId') ? $this->tagFacade->find($this->getParameter('tagId')) : null);

		$control->onSuccess[] = function ($selectedTagId) {
			$this->redirect('this', ['tagId' => $selectedTagId]);
		};

		return $control;
	}

	public function renderShops($localizationId, $limit = null, $tagId = null, $backLink = null, $withCashbackAllowed = true)
	{
		$this->setView('items');

		$localization = $this->localizationFacade->find($localizationId);
		$limit = $limit ?? 200;

		/** @var Tag $tag */
		$tag = $tagId ? $this->tagFacade->find($tagId) : null;

		if ($tag) {
			$shops = array_map(static function (ShopTag $shopTag) {
				return $shopTag->getShop();
			}, $tag->getShopTags()->toArray());

			$startPriority = count($shops);
		} else {
			$shopsQuery = $this->shopFacade->createShopsQuery($localization);
			$shopsQuery->onlyPublished();
			$shopsQuery->sortTop();

			if ($withCashbackAllowed === true) {
				$shopsQuery->onlyWithCashbackAllowed();
			} else {
				$shopsQuery->onlyWithCashbackDisabled();
			}

			$shops = $this->shopFacade->fetch($shopsQuery);

			if ($limit != 0) {
				$shops->applyPaging(0, $limit);
			}

			$startPriority = $shops->getTotalCount();
		}

		$this->template->tagId = $tagId;
		$this->template->shops = $shops;
		$this->template->limit = $limit;
		$this->template->type = self::TYPE_SHOPS;
		$this->template->localization = $localization;
		$this->template->tag = $tag;
		$this->template->backLink = $backLink;
		$this->template->startPriority = $startPriority;
		$this->template->withCashbackAllowed = $withCashbackAllowed;
	}

	public function renderLeafletShops($localizationId, $limit = null)
	{
		$this->setView('items');

		$localization = $this->localizationFacade->find($localizationId);
		$limit = $limit ?? 50;

		$shopsQuery = $this->shopFacade->createShopsQuery($localization);

		$shopsQuery->onlyPublished();
		$shopsQuery->sortByLeafletsPriority();
		$shopsQuery->onlyWithLeafletTag();
//        $shopsQuery->optimized();

		$shops = $this->shopFacade->fetch($shopsQuery);

		if ($limit != 0) {
			$shops->applyPaging(0, $limit);
		}

		$this->template->shops = $shops;
		$this->template->limit = $limit;
		$this->template->type = self::TYPE_LEAFLET_SHOPS;
		$this->template->localization = $localization;
		$this->template->startPriority = $shops->getTotalCount();
	}

	public function handleSort($type, $startPriority, $tagId = null)
	{
		$ids = $this->getHttpRequest()->getPost('ids');
		$tag = null;

		if ($tagId) {
			$tag = $this->tagFacade->find($tagId);
		}

		$priority = $startPriority;

		if ($type === self::TYPE_DEALS) {
			$dealsQuery = $this->dealFacade->createDealsQuery()->in($ids);
			$deals = [];

			/** @var Deal $deal */
			foreach ($this->dealFacade->fetch($dealsQuery) as $deal) {
				$deals[$deal->getId()] = $deal;
			}

			foreach ($ids as $id) {
				$deal = $deals[$id];

				if ($tag) {
					$deal->getDealTag($tag)->setPriority($priority);
				} else {
					$deal->setPriority($priority);
				}

				$priority--;
			}

			$this->dealFacade->saveDeals($deals);
		} elseif ($type === self::TYPE_SHOPS) {
			$shopsQuery = $this->shopFacade->createShopsQuery()->in($ids);

			$shops = [];

			/** @var Shop $shop */
			foreach ($this->shopFacade->fetch($shopsQuery) as $shop) {
				$shops[$shop->getId()] = $shop;
			}

			$tag = null;
			if ($tagId = $this->getParameter('tagId')) {
				/** @var Tag $tag */
				$tag = $this->tagFacade->find($tagId);
			}

			foreach ($ids as $id) {
				$shop = $shops[$id];

				if ($tag) {
					if ($tag->isShopTag()) {
						$shop->getShopTag($tag)->setPriority($priority);
					} elseif ($tag->isLeafletTag()) {
						$shop->getShopLeafletTag($tag)->setPriority($priority);
					}
				} else {
					if ($priority !== $shop->getPriority()) {
						$shop->update();
					}

					$shop->setPriority($shop->isCashbackAllowed() . $priority);
				}

				$priority--;
			}

			$this->shopFacade->saveShops($shops);

			$this->eventDispatcher->dispatch(
				new ShopsUpdatedEvent()
			);
		} elseif ($type === self::TYPE_LEAFLET_SHOPS) {
			$shopsQuery = $this->shopFacade->createShopsQuery()->in($ids);
			$shops = [];

			/** @var Shop $shop */
			foreach ($this->shopFacade->fetch($shopsQuery) as $shop) {
				$shops[$shop->getId()] = $shop;
			}

			foreach ($ids as $id) {
				$shop = $shops[$id];
				$shop->setPriorityLeaflets($priority);

				$priority--;
			}

			$this->shopFacade->saveShops($shops);
		}
	}
}
