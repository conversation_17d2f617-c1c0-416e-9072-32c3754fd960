{varType tipli\Model\Shops\Entities\Shop $shop}
{snippet}
<label>Konk<PERSON><PERSON><PERSON><PERSON> obchody</label>
<div style="float:right">
	<select data-input="relatedShopId" class="selectpicker">
		<option n:foreach="$shopsList as $id => $name" value="{$id}">
			{$name}
		</option>
	</select>

	<a href="javascript:void(0);" data-action="add-related-shop" class="btn btn-primary">Přidat</a>
</div>

{if count($relatedShops)}
	<div class="table-responsive" style="margin-top:15px;">
		<table class="table">
			<thead>
				<th width="120px">Obchod</th>
				<td>&nbsp;</td>
				<th>Priorita</th>
				<th width="100px"></th>
			</thead>
			<tbody id="related-shops">
			<tr n:foreach="$relatedShops as $relatedShop" data-id="{$relatedShop->getId()}">
				<td><img src="{$shop->getLogo() |image:100,30}" alt="" /></td>
				<td>{$relatedShop->getRelatedShop()->getName()}</td>
				<td>{$relatedShop->getPriority()}</td>
				<td>
					<a href="javascript:void(0);" class="btn btn-primary btn-xs sort-handle">
						<span class="fa fa-sort"></span>
					</a>

					<a href="javascript:void(0);" data-url="{link remove!, $relatedShop->getId()}" class="btn btn-danger btn-xs" data-action="remove-related-shop" onclick="return confirm('Skutečně smazat?');">
						<span class="fa fa-times"></span>
					</a>
				</td>
			</tr>
			</tbody>
		</table>
	</div>
{else}
	<div style="margin-top:30px" class="alert alert-info">Zatím žádný obchod</div>
{/if}
	<br /><em>Všechny změny budou ihned uloženy.</em>
	<script>
		$(document).ready(function() {
			// add
			hideSpinner();
			$("[data-action=add-related-shop]").off("click").on("click", function() {
				let id = $("[data-input=relatedShopId]").val();

				if (id) {
					showSpinner()
					$.nette.ajax({
						url: {link add},
						type: "POST",
						data: { id: id }
					});
				}
			});

			// remove
			$("[data-action=remove-related-shop]").off("click").on("click", function() {
				showSpinner();

				$.nette.ajax({
					url: $(this).data("url")
				});
			});

			// sort
			$("#related-shops").sortable({
				items: "> tr",
				placeholder: "ui-state-highlight",
				handle: ".sort-handle",
				helper: function(e, tr) {
					var originals = tr.children();
					let helper = tr.clone();
					helper.children().each(function(index) {
						$(this).width(originals.eq(index).width());
					});
					helper.css("background-color", "white");

					return helper;
				},
				update: function(event, ui) {
					showSpinner();

					var ids = $("#related-shops").find("tr").map(function() {
						return $(this).data("id");
					}).get();

					$.nette.ajax({
						method: "POST",
						url: {link sort!},
						data: { ids: ids },
						success: function() {
							// $("#loading").addClass("hide");
						}
					});
				}
			});
		});
	</script>

{/snippet}
