<?php

namespace tipli\AdminModule\ShopsModule\Forms;

use League\Csv\Reader;
use Nette;
use Nette\Application\UI\Form;
use Nette\Http\Url;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Files\Entities\File;
use tipli\Model\Files\FileStorage;
use tipli\Model\Images\ImageStorage;
use tipli\Model\Seo\SeoFacade;
use tipli\Model\Shops\ShopFacade;
use Nette\Utils\Strings;
use tipli\Routers\RouterFactory;

class ShopsImportControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var array */
	public $onShopExists = [];

	/**
	 * @var FileStorage
	 */
	private $fileStorage;

	/**
	 * @var ShopFacade
	 */
	private $shopFacade;

	/**
	 * @var SeoFacade
	 */
	private $seoFacade;

	/**
	 * @var User
	 */
	private $user;

	/**
	 * @var ImageStorage
	 */
	private $imageStorage;

	public function __construct(User $user, FileStorage $fileStorage, ShopFacade $shopFacade, SeoFacade $seoFacade, ImageStorage $imageStorage)
	{
				$this->fileStorage = $fileStorage;
		$this->shopFacade = $shopFacade;
		$this->seoFacade = $seoFacade;
		$this->user = $user;
		$this->imageStorage = $imageStorage;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addLocalizationSelect('localization', 'Lokalizace:')
			->setRequired('Vyberte lokalizaci!');

		$form->addUpload('file', 'CSV soubor:')
			->setRequired('Nahrajte soubor.');

		$form->addText('delimiter', 'Oddělovač:')
			->setRequired('Zadejte oddělovač.')
			->setDefaultValue(';');

		$form->addSubmit('submit', 'Nahrát');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$localization = $values->localization;
			$file = $this->fileStorage->saveFile($values->file, File::NAMESPACE_SHOPS_IMPORT);

			$reader = Reader::createFromPath($this->fileStorage->getFile($file));
			$reader->setDelimiter($values->delimiter);

			$headers = [];
			foreach ($reader->fetchOne(0) as $columnIndex => $columnName) {
				$headers[] = $columnName;
			}

			foreach (['name', 'domain'] as $requiredColumn) {
				if (!in_array($requiredColumn, $headers)) {
					throw new InvalidArgumentException('Chybějící sloupec: ' . $requiredColumn);
				}
			}

			$results = $reader->getRecords($headers);

			foreach ($results as $index => $row) {
				if (array_values($reader->fetchOne()) === array_values($row)) {
					continue;
				}

				$name = $row['name'];
				$domain = $row['domain'];

				if ($name && $domain) {
					$url = new Url($domain);

					if ($this->shopFacade->findShopByDomain($localization, $url->getDomain())) {
						$this->onShopExists('Obchod s doménou ' . $domain . ' nebylo možné vytvořit - již existuje.');
						continue;
					}

					$slug = Strings::webalize($name);
					if ($this->shopFacade->findBySlug($slug, $localization)) {
						$this->onShopExists('Obchod ' . $name . ' nebylo možné vytvořit - již existuje.');
						continue;
					}

					$shop = $this->shopFacade->createShop(
						$localization,
						$name,
						$slug,
						0,
						(new \DateTime())->modify('+ 10 years'),
						1,
						$this->user,
						true
					);

					$shop->setDomain($domain);

					$shop->setPageExtension(
						$this->seoFacade->createPageExtension($shop->getLocalization(), RouterFactory::getTranslation('shop', $shop->getLocalization()) . '/' . $shop->getSlug())
					);

					$shopName = preg_split('/(?=\.[^.]+$)/', $name)[0]; //remove tld
					$keyword = $this->seoFacade->createOrReturnKeyword($shop->getLocalization(), $shopName);

					$this->seoFacade->addKeywordToPageExtension(
						$shop->getPageExtension(),
						$keyword,
						$this->user
					);

					if (isset($row['keywords'])) {
						foreach (explode(',', $row['keywords']) as $keywordString) {
							$keyword = $this->seoFacade->createOrReturnKeyword($shop->getLocalization(), $keywordString);

							$this->seoFacade->addKeywordToPageExtension(
								$shop->getPageExtension(),
								$keyword,
								$this->user
							);
						}
					}

					if (isset($row['metaTitle'])) {
						$shop->getPageExtension()->setMetaTitle($row['metaTitle']);
					}

					if (isset($row['metaDescription'])) {
						$shop->getPageExtension()->setMetaDescription($row['metaDescription']);
					}

					if (isset($row['metaKeywords'])) {
						$shop->getPageExtension()->setMetaKeywords($row['metaKeywords']);
					}

					if (isset($row['shortDescription'])) {
						$shop->setShortDescription($row['shortDescription']);
					}

					if (isset($row['description'])) {
						$shop->setDescription($row['description']);
					}

					if (isset($row['logo']) && !empty($row['logo'])) {
						$logo = $this->imageStorage->saveImageFromUrl($row['logo'], 'shop-logo');
						$shop->setLogo($logo);
					}

					$this->shopFacade->saveShop($shop);
				}
			}

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IShopsImportControlFactory
{
	/**
	 * @return ShopsImportControl
	 */
	public function create(User $user);
}
