<?php

namespace tipli\AdminModule\QuestionsModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Questions\Entities\Group;
use tipli\Model\Questions\QuestionFacade;

class GroupControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var Group|null */
	private $group;

	/** @var QuestionFacade */
	private $questionFacade;

	/**
	 * GroupControl constructor.
	 * @param Group|null $group
	 * @param QuestionFacade $questionFacade
	 */
	public function __construct(?Group $group, QuestionFacade $questionFacade)
	{
				$this->group = $group;
		$this->questionFacade = $questionFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('noteTitle', 'Název skupiny:')
			->setRequired('Doplňte název skupiny.');

		$form->addText('csTitle', 'Nadpis CS:');

		$form->addText('skTitle', 'Nadpis SK:');

		$form->addText('plTitle', 'Nadpis PL:');

		$form->addText('roTitle', 'Nadpis RO:');

		$form->addText('huTitle', 'Nadpis HU:');

		$form->addText('siTitle', 'Nadpis SI:');

		$form->addText('hrTitle', 'Nadpis HR:');

		$form->addText('bgTitle', 'Nadpis BG:');

		$form->addText('priority', 'Priorita:')
			->setDefaultValue(0)
			->setRequired('Zadejte prioritu.')
			->addRule(Form::INTEGER, 'Priorita musí být číslo');

		if ($this->group) {
			$form->setDefaults([
				'noteTitle' => $this->group->getNoteTitle(),
				'csTitle' => $this->group->getLocaleTitle(Localization::LOCALE_CZECH),
				'skTitle' => $this->group->getLocaleTitle(Localization::LOCALE_SLOVAK),
				'plTitle' => $this->group->getLocaleTitle(Localization::LOCALE_POLISH),
				'roTitle' => $this->group->getLocaleTitle(Localization::LOCALE_ROMANIAN),
				'huTitle' => $this->group->getLocaleTitle(Localization::LOCALE_HUNGARIAN),
				'siTitle' => $this->group->getLocaleTitle(Localization::LOCALE_SLOVENIA),
				'hrTitle' => $this->group->getLocaleTitle(Localization::LOCALE_CROATIA),
				'bgTitle' => $this->group->getLocaleTitle(Localization::LOCALE_BULGARIA),
				'priority' => $this->group->getPriority(),
			]);
		}

		$form->addSubmit('submit', 'Uložit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if (!$this->group) {
				$group = $this->questionFacade->createGroup($values->noteTitle);
			} else {
				$group = $this->group;
				$group->setNoteTitle($values->noteTitle);
			}

			$group->setPriority($values->priority);

			$group->setLocaleTitle(Localization::LOCALE_CZECH, $values->csTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_SLOVAK, $values->skTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_POLISH, $values->plTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_ROMANIAN, $values->roTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_HUNGARIAN, $values->huTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_SLOVENIA, $values->siTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_CROATIA, $values->hrTitle ?: null);
			$group->setLocaleTitle(Localization::LOCALE_BULGARIA, $values->bgTitle ?: null);

			$this->questionFacade->saveGroup($group);

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IGroupControlFactory
{
	/**
	 * @param Group|null $group
	 * @return GroupControl
	 */
	public function create(?Group $group = null);
}
