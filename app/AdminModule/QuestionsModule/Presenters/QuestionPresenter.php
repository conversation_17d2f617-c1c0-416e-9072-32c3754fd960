<?php

namespace tipli\AdminModule\QuestionsModule\Presenters;

use tipli\AdminModule\Presenters\BasePresenter;
use tipli\AdminModule\QuestionsModule\Forms\GroupControl;
use tipli\AdminModule\QuestionsModule\Forms\IGroupControlFactory;
use tipli\AdminModule\QuestionsModule\Forms\IQuestionControlFactory;
use tipli\AdminModule\QuestionsModule\Forms\QuestionControl;
use tipli\Model\Questions\QuestionFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;

class QuestionPresenter extends BasePresenter
{
	/** @var IGroupControlFactory @inject */
	public $groupControlFactory;

	/** @var IQuestionControlFactory @inject */
	public $questionControlFactory;

	/** @var QuestionFacade @inject */
	public $questionFacade;

	/**
	 * @param int|null $id Group ID
	 * @throws \Nette\Application\BadRequestException
	 */
	public function actionDefault(?int $id = null)
	{
		if ($id) {
			$group = $this->questionFacade->findGroup($id);

			if (!$group) {
				$this->error('Skupina otázek nebyla nalezena.');
			}

			$this->template->group = $group;
		}

		$this->template->groups = $this->questionFacade->findAllGroups();
	}

	/**
	 * @param int|null $id
	 * @throws \Nette\Application\BadRequestException
	 */
	public function actionGroup(?int $id = null)
	{
		if ($id) {
			$group = $this->questionFacade->findGroup($id);

			if (!$group) {
				$this->error('Skupina otázek nebyla nalezena.');
			}

			$this->template->group = $group;
		}
	}

	/**
	 * @param int $id
	 * @throws \Nette\Application\AbortException
	 * @throws \Nette\Application\BadRequestException
	 */
	public function actionRemoveGroup(int $id)
	{
		$group = $this->questionFacade->findGroup($id);

		if (!$group) {
			$this->error('Skupina otázek nebyla nalezena.');
		}

		$this->questionFacade->removeGroup($group);

		$this->redirect('default');
	}

	/**
	 * @param int|null $id
	 * @throws \Nette\Application\BadRequestException
	 */
	public function actionQuestion(?int $id = null)
	{
		if ($id) {
			$question = $this->questionFacade->findQuestion($id);

			if (!$question) {
				$this->error('Otázka nebyla nalezena.');
			}

			$this->template->question = $question;
		}
	}

	/**
	 * @param int $id
	 * @throws \Nette\Application\AbortException
	 * @throws \Nette\Application\BadRequestException
	 */
	public function actionRemoveQuestion(int $id)
	{
		$question = $this->questionFacade->findQuestion($id);

		if (!$question) {
			$this->error('Otázka nebyla nalezena.');
		}

		$this->questionFacade->removeQuestion($question);

		$this->redirect('default');
	}

	/**
	 * @return GroupControl
	 */
	protected function createComponentGroupControl(): GroupControl
	{
		$control = $this->groupControlFactory->create(
			$this->getParameter('id') ? $this->questionFacade->findGroup($this->getParameter('id')) : null
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Úspěšně uloženo.');
			$this->redirect('default');
		};

		return $control;
	}

	/**
	 * @return QuestionControl
	 */
	protected function createComponentQuestionControl(): QuestionControl
	{
		$control = $this->questionControlFactory->create(
			$this->getParameter('id') ? $this->questionFacade->findQuestion($this->getParameter('id')) : null
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Úspěšně uloženo.');
			$this->redirect('default');
		};

		return $control;
	}

	/**
	 * @param string $name
	 * @throws \Ublaboo\DataGrid\Exception\DataGridException
	 */
	public function createComponentGroupsGrid(string $name)
	{
		$questions = $this->questionFacade->getGroups();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $questions);

		$grid->addColumnLink('noteTitle', 'Název skupiny', 'default');

		$grid->addColumnText('priority', 'Priorita');

		$grid->addAction('group', '', 'group')
			->setIcon('pen')
			->setClass('btn btn-xs btn-primary');

		$grid->addAction('remove', '', 'removeGroup')
			->setConfirmation(new StringConfirmation('Opravdu si přejete odstranit skupinu včetně k ní přiřazených otázek a odpovědí?'))
			->setIcon('remove')
			->setClass('btn btn-xs btn-danger');

		$grid->setPagination(false);
		$grid->setRememberState(false);
		$grid->setDefaultSort(['priority' => 'DESC']);
	}

	/**
	 * @param string $name
	 * @throws \Ublaboo\DataGrid\Exception\DataGridException
	 */
	public function createComponentQuestionsGrid(string $name)
	{
		$group = $this->getParameter('id') ? $this->questionFacade->findGroup($this->getParameter('id')) : null;
		$questions = $this->questionFacade->getQuestions($group)
			->leftJoin('q.group', 'g')
			->orderBy('g.priority', 'desc')
			->addOrderBy('q.priority', 'desc');

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $questions);

		$grid->addColumnText('noteQuestion', 'Otázka');

		$grid->addColumnText('group', 'Skupina')
			->setRenderer(static function ($item) {
				return $item->getGroup()->getNoteTitle();
			});

		$grid->addColumnText('priority', 'Priorita');

		$grid->addAction('question', '', 'question')
			->setIcon('pen')
			->setClass('btn btn-xs btn-primary');

		$grid->addAction('remove', '', 'removeQuestion')
			->setConfirmation(new StringConfirmation('Opravdu si přejete tuto otázku odstranit?'))
			->setIcon('remove')
			->setClass('btn btn-xs btn-danger');

		$grid->setPagination(false);
		$grid->setRememberState(false);
	}
}
