{form form}
	<div class="row">
		<div class="col-md-6">
			<div class="row form-group">
				<div class="col-md-5">
					Localization:
				</div>
				<div class="col-md-7">
					{input localizations, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Refund ID:
				</div>
				<div class="col-md-7">
					{input id, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Type:
				</div>
				<div class="col-md-7">
					{input type, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					User:
				</div>
				<div class="col-md-7">
					{input user, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Shop:
				</div>
				<div class="col-md-7">
					{input shop, class => "form-control"}
				</div>
			</div>
		</div>
		<div class="col-md-6">
			<div class="row form-group">
				<div class="col-md-5">
					State:
				</div>
				<div class="col-md-7">
					{input state, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Assigned to operator:
				</div>
				<div class="col-md-7">
					{input operator, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Resolved by operator:
				</div>
				<div class="col-md-7">
					{input resolvedBy, class => "form-control"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Resolved from-to
				</div>
				<div class="col-md-3">
					{input resolvedAtFrom, class => "form-control datepicker"}
				</div>
				<div class="col-md-1 text-center">
					-
				</div>
				<div class="col-md-3">
					{input resolvedAtTo, class => "form-control datepicker"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Created from-to:
				</div>
				<div class="col-md-3">
					{input createdAtFrom, class => "form-control datepicker"}
				</div>
				<div class="col-md-1 text-center">
					-
				</div>
				<div class="col-md-3">
					{input createdAtTo, class => "form-control datepicker"}
				</div>
			</div>
			<div class="row form-group">
				<div class="col-md-5">
					Sort:
				</div>
				<div class="col-md-7">
					{input sort, class => "form-control"}
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-12 text-center">
			{input submit, class => "btn btn-primary", value => "Apply filter"}
			{input clear, class => "btn btn-danger", value => "Clear filter", onclick => "return confirm('do you really want to clear filter?');"}
		</div>
	</div>
{/form}
