<?php

namespace tipli\AdminModule\RefundsModule\Components\Refund\ExternalFilter;

use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\Template;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Session\SectionFactory;
use tipli\Model\Shops\ShopFacade;

/**
 * @property-read Template|\stdClass $template
 */
class RefundExternalFilterControl extends Control
{
	public array $onSuccess = [];

	public array $onError = [];

	private User $user;

	private Refund $refund;

	public function __construct(
		private LocalizationFacade $localizationFacade,
		private ShopFacade $shopFacade,
		private UserFacade $userFacade,
		private SectionFactory $sectionFactory
	) {
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addText('id')
			->setNullable();

		$form->addSelect('type')
			->setItems(Refund::getTypes())
			->setPrompt('all');

		$form->addSelect('state')
			->setItems(Refund::getStates())
			->setPrompt('all');

		$form->addMultiSelect('localizations', 'Localizations')
			->setItems($this->localizationFacade->findPairs());

		$form->addSelect('operator', 'Operator')
			->setItems(['robot' => 'robot'] + $this->userFacade->usersToList($this->userFacade->findUsersFromTipliSupport()))
			->setPrompt('all');

		$form->addSelect('resolvedBy', 'Resolved by')
			->setItems(['robot' => 'robot'] + $this->userFacade->usersToList($this->userFacade->findUsersFromTipliSupport()))
			->setPrompt('all');

		$form->addText('user')
			->setNullable();

		$form->addText('shop');

		$form->addText('resolvedAtFrom')
			->setNullable();

		$form->addText('resolvedAtTo')
			->setNullable();

		$form->addText('createdAtFrom')
			->setNullable();

		$form->addText('createdAtTo')
			->setNullable();

		$form->addSelect('sort', 'Sort by:', [
			'r.id,desc' => 'Newest',
			'r.id,asc' => 'Oldest',
			'r.resolvedAt,asc' => 'Resolved first',
			'r.resolvedAt,desc' => 'Resolved last',
		]);

		$form->addSubmit('submit');
		$form->addSubmit('clear', 'Clear filter');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values): Form
	{
		if ($form['clear']->isSubmittedBy()) {
			$this->sectionFactory->getRefundFilterSection()->filter = null;
			$this->onSuccess([]);
		}

		$filter = $form->getValues('array');

		$this->sectionFactory->getRefundFilterSection()->filter = $filter;

		$this->onSuccess($filter);

		return $form;
	}

	public function setFilter(array $filter): void
	{
		$this['form']->setDefaults($filter);
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}
