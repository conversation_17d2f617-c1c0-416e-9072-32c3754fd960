<?php

namespace tipli\AdminModule\RefundsModule\Presenters;

use Nette\Utils\Strings;
use tipli\AdminModule\Components\Shop\InternalNotesControl\InternalNotesControl;
use tipli\AdminModule\Components\Shop\InternalNotesControl\InternalNotesControlFactory;
use tipli\AdminModule\Presenters\BasePresenter;
use tipli\AdminModule\RefundsModule\Components\Refund\RefundResolveCheck\RefundResolveCheckControlFactory;
use tipli\AdminModule\RefundsModule\Forms\IRefundSendMessageControlFactory;
use tipli\AdminModule\RefundsModule\Forms\IResolveRefundControlFactory;
use tipli\Model\Account\Entities\UserDuplicity;
use tipli\Model\Account\UserDuplicityCheckerFacade;
use tipli\Model\Admitad\AdmitadClient;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Freshdesk\FreshdeskClient;
use tipli\Model\Freshdesk\FreshdeskFacade;
use tipli\Model\Freshdesk\TicketResponderResolver;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Refunds\RefundResolver;
use tipli\Model\Refunds\RefundResponseProvider;
use tipli\Model\Reports\MetricFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Rewards\ShareRewardFacade;
use tipli\Model\Shops\Entities\Offer;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\Redirection\RedirectionManager;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;
use tipli\Model\Vouchers\VoucherFacade;

class RefundPresenter extends BasePresenter
{
	/** @var RefundFacade @inject */
	public $refundFacade;

	/** @var RefundResolver @inject */
	public $refundResolver;

	/** @var UserDuplicityCheckerFacade @inject */
	public $userDuplicityCheckerFacade;

	/** @var StatisticDataProvider @inject */
	public $statisticDataProvider;

	/** @var PayoutFacade @inject */
	public $payoutFacade;

	/** @var ShareRewardFacade @inject */
	public $shareRewardFacade;

	/** @var TransactionFacade @inject */
	public $transactionFacade;

	/** @var IResolveRefundControlFactory @inject */
	public $resolveRefundControlFactory;

	/** @var IRefundSendMessageControlFactory @inject */
	public $refundSendMessageControlFactory;

	/** @var RefundResolveCheckControlFactory @inject */
	public RefundResolveCheckControlFactory $refundResolveCheckControlFactory;

	/** @var FreshdeskFacade @inject */
	public $freshdeskFacade;

	/** @var RedirectionManager @inject */
	public $redirectionManager;

	/** @var RefundResponseProvider @inject */
	public $refundResponseProvider;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var TicketResponderResolver @inject */
	public $ticketResponderResolver;

	/** @var DealFacade @inject */
	public $dealFacade;

	/** @var VoucherFacade @inject */
	public VoucherFacade $voucherFacade;

	/** @var AdmitadClient @inject */
	public $admitadClient;

	/** @var MetricFacade @inject */
	public $metricFacade;

	/** @var FreshdeskClient @inject */
	public $freshdeskClient;

	/** @var InternalNotesControlFactory @inject */
	public $internalNotesControlFactory;

	public function renderRefund($id): void
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($id);

		if (!$refund) {
			$this->flashMessage('Refund not found.');
			$this->redirect('Refunds:');
		}

		$user = $refund->getUser();
		$this->template->refund = $refund;
		$this->template->refundResponseProvider = $this->refundResponseProvider;
		$this->template->refundResolver = $this->refundResolver;
		$this->template->refundProcess = $refund->getRefundProcess();
		$this->template->userEntity = $user;
		$this->template->getFreshdeskTicketLink = function () use ($refund) {
			return $this->freshdeskFacade->getFreshdeskTicketLink($refund->getFreshdeskTicket());
		};
		$this->template->getOffersList = function () use ($refund) {
			$offers = [];

			/** @var Offer $offer */
			foreach ($this->offerFacade->resolveOffersForShop($refund->getShop()) as $offer) {
				$offers[] = $offer;
			}

			return $offers;
		};

		// Share rewards
		$shareRewardsQuery = $this->shareRewardFacade->createShareRewardsQuery($user)
			->sortByCreatedAt()
			->onlyValid();

		$this->template->shareRewards = $this->shareRewardFacade->fetch($shareRewardsQuery);

		$expiredShareRewardsQuery = $this->shareRewardFacade->createShareRewardsQuery($user)
			->sortByCreatedAt()
			->onlyExpired();

		$this->template->expiredShareRewards = $this->shareRewardFacade->fetch($expiredShareRewardsQuery);

		// Transactions
		$this->template->balance = $this->transactionFacade->getBalance($user);
		$this->template->confirmedBalance = $this->transactionFacade->getConfirmedBalance($user);
		$this->template->registeredBalance = $this->transactionFacade->getRegisteredBalance($user);

		$this->template->registeredCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, false);
		$this->template->confirmedCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, true);

		$this->template->registeredBonusTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS, false);
		$this->template->confirmedBonusTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS, true);

		$this->template->registeredRefundTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS_REFUND);
		$this->template->confirmedRefundTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS_REFUND, true);
		$this->template->countOfRefunds = $this->statisticDataProvider->getCountOfRefundTransactions(null, null, null, null, $user);

		// Income
		$this->template->income = $this->statisticDataProvider->getUserIncome($user);
		$this->template->accountantIncome = $this->statisticDataProvider->getUserIncome($user, true);

		// Payouts
		$this->template->payouts = $this->payoutFacade->getPayoutsByUser($user)->getQuery()->getResult();
		$this->template->sumOfConfirmedPayouts = $this->statisticDataProvider->getSumOfPayouts(null, null, null, true, $user);

		$this->template->resolveUserByAgentId = (function ($agentId) {
			return $this->ticketResponderResolver->getTipliAgentByFreshdeskId($agentId);
		});

		$this->template->getCouponByCode = (function ($code) use ($refund) {
			return $this->dealFacade->findByCode($refund->getLocalization(), $code, $refund->getShop());
		});

		$this->template->operators = $this->userFacade->usersToList(
			$this->userFacade->findUsersFromTipliSupport()
		);

		$this->template->getDuplicityRefundsByOrderId = function ($refund) {
			return $this->refundFacade->findDuplicityRefundsByOrderId($refund);
		};

		$adminLocalizations = $this->localizationFacade->findIn(
			$this->getUserIdentity()->getUserSettings()->getSupportLocalizationIds()
		);

		$this->template->nextRefund = $this->refundFacade->findNextRefund($refund, $adminLocalizations);
		$this->template->previousRefund = $this->refundFacade->findPreviousRefund($refund, $adminLocalizations);

		if ($refund->getShop()) {
			$transactionActiveDaysLast7Days = (int) $this->metricFacade->findShopTransactionActiveDays($refund->getShop()->getId(), new \DateTime('- 7 days'), new \DateTime('- 1 day'));
			$transactionActiveDaysPrevious7Days = (int) $this->metricFacade->findShopTransactionActiveDays($refund->getShop()->getId(), new \DateTime('- 14 days'), new \DateTime('- 8 day'));

			$this->template->transactionActiveDays = [
				'current' => $transactionActiveDaysLast7Days,
				'previous' => $transactionActiveDaysPrevious7Days,
			];
		}

		$this->template->countOfApprovedRefunds = $this->refundFacade->getCountOfApprovedRefunds($user);
		$this->template->countOfApprovedRefundsByAffil = $this->refundFacade->getCountOfRefundsWithRefundSolutionState($user, RefundSolution::STATE_PARTNER_APPROVED);
		$this->template->countOfDeclinedRefundsByAffil = $this->refundFacade->getCountOfRefundsWithRefundSolutionState($user, RefundSolution::STATE_PARTNER_DECLINED);

		if ($user !== null && $refund->getShop() !== null) {
			$this->template->activeVouchers = $this->voucherFacade->findUserVouchersByShop($user, $refund->getShop());
		}
	}

	public function createComponentUserDuplicityGrid($name)
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$user = $refund->getUser();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $this->userDuplicityCheckerFacade->getUserDuplicity($user))
		;

		$grid->addColumnText('relatedUser', 'Duplicate User', 'relatedUser.id')
			->setRenderer(function (UserDuplicity $userDuplicity) {
				return '<a href="' . $this->link(':Admin:Account:User:userCard', $userDuplicity->getRelatedUser()->getId()) . '" target="_blank">' . $userDuplicity->getRelatedUser()->getEmail() . '</a>';
			})
			->setTemplateEscaping(false)
		;

		$grid->addColumnText('type', 'Duplicate')
			->setRenderer(static function (UserDuplicity $userDuplicity) {
				return $userDuplicity->getDuplicityTypeName();
			});

		$grid->addColumnText('note', 'Note')
			->setRenderer(static function (UserDuplicity $userDuplicity) {
				return '<strong>' . $userDuplicity->getValue() . '</strong><br>' . $userDuplicity->getNote();
			})
			->setTemplateEscaping(false)
		;

		$grid->setDefaultPerPage(10);

		return $grid;
	}

	public function createComponentShopRedirectsGrid($name)
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$qb = $this->redirectionManager->getRedirections()
			->andWhere('r.shop = :shop')
			->setParameter('shop', $refund->getShop())
			->andWhere('r.user = :user')
			->setParameter('user', $refund->getUser())
			->andWhere('r.createdAt >= :fromDate')
			->setParameter('fromDate', (clone $refund->getPurchasedAt())->modify('- 48 hours'))
			->andWhere('r.createdAt <= :toDate')
			->setParameter('toDate', (clone $refund->getPurchasedAt())->setTime(23, 59, 59))
			->addOrderBy('r.createdAt', 'desc')
		;

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $qb);

		$grid->addColumnText('adblockUsed', 'Adblock used')
			->setRenderer(static function (Redirection $redirection) {
				return $redirection->getAdblockUsed() ? 'Yes' : (($redirection->getAdblockUsed() === null) ? 'Unable to determine' : 'No');
			});

		$grid->addColumnText('platform', 'Platform')
			->setRenderer(static function (Redirection $redirection) {
				$platform = $redirection->getPlatform();

				if (in_array($platform, [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID])) {
					return 'mobile app - ' . $redirection->getPlatform();
				}

				if ($platform === ClientLayer::PLATFORM_WEB_MOBILE) {
					return 'web - from mobile';
				}

				if ($platform === ClientLayer::PLATFORM_WEB_TABLET) {
					return 'web - from tablet';
				}

				if ($platform === ClientLayer::PLATFORM_WEB) {
					return 'web - desktop';
				}

				return '- unable to determine -';
			});

		$grid->addColumnText('createdAt', 'Redirected at')->setRenderer(function (Redirection $redirection) {
			return $this->dateTimeFilter->__invoke($redirection->getCreatedAt(), $redirection->getUser()->getLocalization());
		});

		return $grid;
	}

	public function handleGetProductsData()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		$productLinks = [];

		/** @var string $productUrl */
		foreach ($refund->getProductUrlsAsArray() as $productUrl) {
			if (Strings::contains($productUrl, 'tipli')) {
				continue;
			}

			$productLinks[] = [
				'url' => $productUrl,
				'isAffiliate' => $this->admitadClient->validateLink($refund->getLocalization(), $productUrl)->isAffiliate(),
			];
		}

		$this->template->productLinks = $productLinks;
		$this->redrawControl('productLinks');
	}

	public function createComponentTransactionsGrid($name)
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$qb = $this->transactionFacade->getTransactions()
			->andWhere('t.shop = :shop')
			->setParameter('shop', $refund->getShop())
			->andWhere('t.user = :user')
			->setParameter('user', $refund->getUser())
			->addOrderBy('t.createdAt', 'desc')
		;

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $qb)
		;

		$grid->addColumnText('type', 'Type')
			->setTemplateEscaping(false)
			->setRenderer(static function (Transaction $transaction) {
				return $transaction->getTypeLabel() . '<br>' . ($transaction->isConfirmed() ? ($transaction->isCancelled() ? 'cancelled' : 'confirmed') : 'registered');
			})
		;

		$grid->addColumnText('amount', 'User reward')
			->setRenderer(function (Transaction $transaction) {
				return $transaction->getAmount() . ' ' . $this->currencyFilter->__invoke($transaction->getCurrency());
			})
		;

		$grid->addColumnText('createdAt', 'Created at')
			->setRenderer(static function (Transaction $transaction) {
				return $transaction->getCreatedAt()->format('d.m.Y H:i:s');
			})
		;

		return $grid;
	}

	public function createComponentOtherRefundsGrid($name)
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$qb = $this->refundFacade->getRefunds()
			->andWhere('r.id != :id')
			->setParameter('id', $refund->getId())
			->andWhere('r.user = :user')
			->setParameter('user', $refund->getUser())
			->addOrderBy('r.createdAt', 'desc')
		;

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $qb)
		;

		$grid->addColumnText('type', 'Type')
			->setTemplateEscaping(false)
			->setRenderer(static function (Refund $refund) {
				$output = $refund->getTypeLabel();

				if ($refund->getShop()) {
					$output .= "<br />" . $refund->getShop()->getName();
				}

				return $output;
			})
		;

		$grid->addColumnText('state', 'State')
			->setRenderer(static function (Refund $refund) {
				return $refund->getStateLabel();
			})
		;

		$grid->addColumnText('createdAt', 'Created at')
			->setRenderer(static function (Refund $refund) {
				return $refund->getCreatedAt()->format('d.m.Y H:i:s');
			})
		;

		$grid->addAction('detail', '>', 'this')
			->setClass('btn btn-xs btn-primary')
		;

		return $grid;
	}

	public function createComponentOtherRefundTransactionsGrid($name)
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		/** @var Refund|null $refund */
		$transactions = $this->transactionFacade->getUserBonusRefundTransactionsWithoutRefund($refund->getUser());

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $transactions)
		;

		$grid->addColumnText('shop.name', 'Shop');

		$grid->addColumnText('bonusAmount', 'Bonus amount')
			->setRenderer(function (Transaction $transaction) {
				return $this->amountFilter->__invoke($transaction->getBonusAmount()) . ' ' . $this->currencyFilter->__invoke($transaction->getCurrency());
			})
		;

		$grid->addColumnText('createdAt', 'Created at')
			->setRenderer(static function (Transaction $transaction) {
				return $transaction->getCreatedAt()->format('d.m.Y H:i:s');
			})
		;

		return $grid;
	}

	public function createComponentResolveRefundControl()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$control = $this->resolveRefundControlFactory->create(
			$refund,
			$this->getUserIdentity()
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Saved.');
			$this->redirect('this');
		};

		return $control;
	}

	public function createComponentRefundSendMessageControl()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$control = $this->refundSendMessageControlFactory->create(
			$refund,
			$this->getUserIdentity()
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Message has been sent.');
			$this->redirect('this');
		};

		return $control;
	}

	public function handleLoadTicketConversation()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		$freshdeskTicket = $refund->getFreshdeskTicket();

		if ($freshdeskTicket?->isConversationRemoved() === true) {
			$importedTicket = $this->freshdeskClient->fetchTicket(
				$refund->getFreshdeskTicket()->getTicketId()
			);

			$this->freshdeskFacade->updateTicketConversation($freshdeskTicket, $importedTicket);
		}

		$this->template->messages = $refund->getFreshdeskTicket()->getMessages();

		$this->redrawControl('ticketConversation');
	}

	public function handleAddComment()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		$comment = $this->getRequest()->getPost('comment');

		if (!$comment) {
			return;
		}

		$this->refundFacade->createRefundComment(
			$refund,
			$this->getUserIdentity(),
			$comment
		);

		if ($this->isAjax()) {
			$this->redrawControl('comments');
		} else {
			$this->redirect('this');
		}
	}

	public function handleAssignOperator(int $id)
	{
		$refund = $this->refundFacade->find($id);
		$operatorId = $this->getRequest()->getPost('operatorId') ?: null;
		$refund->assignOperator($operatorId ? $this->userFacade->find($operatorId) : null);

		$this->refundFacade->saveRefund($refund);
	}

	public function handleReopenRefund(int $id)
	{
		$refund = $this->refundFacade->find($id);

		if ($refund) {
			$this->refundFacade->reopenRefund($refund);
		}

		$this->redirect('this');
	}

	public function handleRenewRefundResolveCheck(int $id)
	{
		$refund = $this->refundFacade->find($id);

		if ($refund) {
			$this->refundFacade->renewRefundResolveCheck($refund);
		}

		if ($this->isAjax()) {
			$this->redrawControl('resolveCheck');
		} else {
			$this->redirect('this');
		}
	}

	public function createComponentRefundResolveCheckControl()
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		if (!$refund) {
			$this->error();
		}

		$control = $this->refundResolveCheckControlFactory->create(
			$refund,
			$this->userFacade->find($this->getUserIdentity()->getId())
		);

		$control->onSuccess[] = function () {
			if ($this->isAjax()) {
				$this->redrawControl('resolveCheck');
			} else {
				$this->redirect('this');
			}
		};

		return $control;
	}

	public function createComponentNotesGrid(): ?InternalNotesControl
	{
		/** @var Refund|null $refund */
		$refund = $this->refundFacade->find($this->getParameter('id'));

		$shop = $refund->getShop();

		if ($shop === null) {
			return null;
		}

		return $this->internalNotesControlFactory->create($this->getUserIdentity(), $shop);
	}
}
