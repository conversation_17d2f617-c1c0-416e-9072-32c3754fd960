<?php

namespace tipli\AdminModule\AccountModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Account\UserFacade;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Transactions\TransactionFacade;
use tipli\Model\Transactions\TransactionTriggerProcessor;

class RecommenderControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	/**
	 * @var UserFacade
	 */
	private $userFacade;

	/**
	 * @var TransactionFacade
	 */
	private $transactionFacade;

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	/** @var TransactionTriggerProcessor */
	private $transactionTriggerProcessor;

	public function __construct(UserFacade $userFacade, TransactionFacade $transactionFacade, LocalizationFacade $localizationFacade, TransactionTriggerProcessor $transactionTriggerProcessor)
	{
				$this->userFacade = $userFacade;
		$this->transactionFacade = $transactionFacade;
		$this->localizationFacade = $localizationFacade;
		$this->transactionTriggerProcessor = $transactionTriggerProcessor;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addSelect('localizationId', 'Localization:', $this->localizationFacade->findPairs(false))
			->setPrompt('-- select localization --')
			->setAttribute('class', 'selectpicker');

		$form->addText('user', 'User:')
			->setAttribute('placeholder', 'ID or e-mail')
			->setRequired('Enter ID or e-mail.');

		$form->addText('parentUser', 'User (Recommender):')
			->setAttribute('placeholder', 'ID or e-mail')
			->setRequired('Enter ID or e-mail.');

		$form->addCheckbox('friendBonus', 'Add friend bonus?');

		$form->addCheckbox('recommendationBonus', 'Add bonus for recommendation?');

		$form->addSubmit('submit', 'Save');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$user = [];
			$parentUser = [];

			/** @var Localization|null $localization */
			$localization = $values->localizationId ? $this->localizationFacade->find($values->localizationId) : null;

			if (filter_var($values->user, FILTER_VALIDATE_INT)) {
				$user = $this->userFacade->find($values->user);
			} elseif (filter_var($values->user, FILTER_VALIDATE_EMAIL)) {
				if ($localization) {
					$user = $this->userFacade->findOneByEmail($values->user, $localization);
				} else {
					$user = $this->userFacade->findUsersByEmail($values->user);
				}
			} else {
				$form->addError('Field "User" has to be user ID or his e-mail.');
			}

			if (filter_var($values->parentUser, FILTER_VALIDATE_INT)) {
				$parentUser = $this->userFacade->find($values->parentUser);
			} elseif (filter_var($values->parentUser, FILTER_VALIDATE_EMAIL)) {
				if ($localization) {
					$parentUser = $this->userFacade->findOneByEmail($values->parentUser, $localization);
				} else {
					$parentUser = $this->userFacade->findUsersByEmail($values->parentUser);
				}
			} else {
				$form->addError('Field "Recommender" has to be user ID or his e-mail.');
			}

			if (!$form->hasErrors()) {
				if (empty($user)) {
					$form->addError('Cannot find Recommended User!');
				} elseif (!$localization && count($user) > 1) {
					$form->addError('Cannot find User`s localization, select localization or enter his ID!');
				}

				if (empty($parentUser)) {
					$form->addError('Cannot find User!');
				} elseif (!$localization && count($parentUser) > 1) {
					$form->addError('Cannot find Recommender`s localization, select localization or enter his ID!');
				}
			}

			if (!$form->hasErrors()) {
				if (is_array($user)) {
					$user = $user[0];
				}
				if (is_array($parentUser)) {
					$parentUser = $parentUser[0];
				}
				if ($parentUser->getId() > $user->getId()) {
					$form->addError('User cannot be older than Recommender');
				}
			}

			if (!$form->hasErrors()) {
				$user->setParentUser($parentUser);
				$this->userFacade->saveUser($user);

				if ($values->recommendationBonus && $user->isActiveUser()) {
					$this->transactionTriggerProcessor->checkRecommendationBonus($parentUser, $user);
				}

				$this->onSuccess($user);
			}
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IRecommenderControlFactory
{
	/**
	 * @return RecommenderControl
	 */
	public function create();
}
