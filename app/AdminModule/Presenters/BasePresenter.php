<?php

namespace tipli\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Autowired\AutowireProperties;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\AdminModule\Components\IDataGridFactory;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Producers\UserAdminVisitProducer;
use tipli\Model\Account\UserFacade;
use tipli\Model\HtmlBuilders\ContentBuilder;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\CustomInputsBinder;

abstract class BasePresenter extends \tipli\Presenters\BasePresenter
{
	use AutowireProperties;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var IDataGridFactory @inject */
	public $dataGridFactory;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var CustomInputsBinder @inject */
	public $customInputsBinder;

	/** @var ContentBuilder @inject */
	public $contentBuilder;

	/** @var UserAdminVisitProducer @inject */
	public $userAdminVisitProducer;

	public function startup()
	{
		parent::startup();

		$this->requireModuleAuthorization();

		// bind custom inputs
		$this->customInputsBinder->bindInputs();

		$this->requireSessionVerification();

		$this->trackVisit();

		if ($this->getUserIdentity()->getEmail() === '<EMAIL>' && $this->getName() !== 'Reports:OfficeDashboard') {
			$this->redirect(':Reports:OfficeDashboard:');
		}
//		Debugger::log($this->getUser()->getIdentity()->getEmail() . ' - ' . $this->getView(), 'adminVisits');

		$this->template->loggedUserEntity = $this->getUser()->getIdentity();
		$this->template->moreContent = $this->getHttpRequest()->getCookie('more-content') == 0 ? false : true;
	}

	public function beforeRender()
	{
		parent::beforeRender();

		$this->template->isPageFavorite = function (): bool {
			/** @var User $user */
			$user = $this->getUserIdentity();

			$url = new Url($this->link('this'));
			if ($url->getQueryParameter('_fid')) {
				$url->setQueryParameter('_fid', null);
			}

			foreach ($user->getUserSettings()->getFavoritePages() as $favoritePage) {
				if (isset($favoritePage['url']) && $favoritePage['url'] === $url->getAbsoluteUrl()) {
					return true;
				}
			}

			return false;
		};

		$this->template->getFavoritePages = function (): array {
			$favoritePages = $this->getUserIdentity()->getUserSettings()->getFavoritePages();

			if ($favoritePages) {
				$favoritePages = array_reverse($favoritePages);
			}

			return $favoritePages;
		};

		$themeMode = $this->getUserIdentity()->getUserSettings()->getDarkMode() ?? 'light';

		if ($themeMode === 'auto') {
			$theme = (new \DateTime())->format('H') >= 18 || (new \DateTime())->format('H') < 6 ? 'dark' : 'light';
		} else {
			$theme = $themeMode;
		}

		$this->template->theme = $theme;
		$this->template->themeMode = $themeMode;
	}

	private function trackVisit()
	{
		if ($this->getParameter('do') !== null && (Strings::contains($this->getParameter('do'), 'filter-submit') || Strings::contains($this->getParameter('do'), 'refreshState'))) {
			return;
		}

		$parameters = $this->getParameters();
		$parameters['action'] = null;

		$this->userAdminVisitProducer->scheduleData($this->getUserIdentity()->getId(), $this->getName() . ':' . $this->getView(), array_filter($parameters), $this->isAjax());
	}

	public function handleValidateContentFromPost()
	{
		$this->template->validateError = null;

		if (!$this->contentBuilder->validate($this->getRequest()->getPost('content'), $errorMessage)) {
			$this->template->validateError = $errorMessage;
		}

		$this->redrawControl('validateError');
	}

	public function handleMarkFavoritePage($title = null)
	{
		if (!$this->isAjax() || $title === null) {
			return;
		}

		$url = new Url($this->link('this'));
		if ($url->getQueryParameter('_fid')) {
			$url->setQueryParameter('_fid', null);
			$url->setQueryParameter('do', null);
			$url->setQueryParameter('title', null);
		}

		$title = str_replace(' | Tipli', '', $title);

		$this->userFacade->setFavoritePage(
			$this->getUserIdentity(),
			$url->getAbsoluteUrl(),
			$title
		);
	}

	public function handleThemeMode(string $m)
	{
		$user = $this->getUserIdentity();
		$user->getUserSettings()->setDarkMode($m);
		$this->userFacade->saveUser($user);

		$this->redirect('this');
	}

	public function handleUnmarkFavoritePage($title = null, $url = null)
	{
		if ($this->isAjax() === false) {
			return;
		}

		$url = new Url($url ? : $this->link('this'));
		$url->setQueryParameter('_fid', null);
		$url->setQueryParameter('do', null);
		$url->setQueryParameter('title', null);

		$this->userFacade->removeFavoritePage(
			$this->getUserIdentity(),
			$url->getAbsoluteUrl()
		);
	}
}
