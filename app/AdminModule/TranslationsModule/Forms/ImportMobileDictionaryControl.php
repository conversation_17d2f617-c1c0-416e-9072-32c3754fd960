<?php

namespace tipli\AdminModule\TranslationsModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Configuration;
use tipli\Model\Github\GithubClient;
use tipli\Model\Translations\DictionaryConverter;
use tipli\Model\Translations\TranslationFacade;

class ImportMobileDictionaryControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var User */
	private $admin;

	/** @var Configuration */
	private $configuration;

	/** @var DictionaryConverter */
	private $dictionaryConvertor;

	/** @var TranslationFacade */
	private $translationFacade;

	/**
	 * DictionaryControl constructor.
	 * @param User $admin
	 * @param Configuration $configuration
	 * @param DictionaryConverter $dictionaryConvertor
	 * @param TranslationFacade $translationFacade
	 */
	public function __construct(User $admin, Configuration $configuration, DictionaryConverter $dictionaryConvertor, TranslationFacade $translationFacade)
	{
				$this->admin = $admin;
		$this->configuration = $configuration;
		$this->dictionaryConvertor = $dictionaryConvertor;
		$this->translationFacade = $translationFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addLocalizationSelect('localization', 'Lokalizace:')
			->setRequired('Zvolte lokalizaci.');

		$form->addSelect('platform', 'Platforma:', DictionaryConverter::getPlatformsSelectList())
			->setRequired('Vyberte platformu')
			->setAttribute('class', 'selectpicker');

		$form->addUpload('sourceFile', 'Zdroj:')
			->setRequired('Nahrajte zdrojový soubor.');

		$form->addSubmit('submit', 'Importovat slovník');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			/** @var Nette\Http\FileUpload $sourceFile */
			$sourceFile = $values->sourceFile;
			$extension = Nette\Utils\Strings::after($sourceFile->getUntrustedName(), '.', -1);

			if (DictionaryConverter::getDictionaryExtension($values->platform) !== $extension) {
				throw new InvalidArgumentException('Chybný typ zdrojového souboru.');
			}

			$data = $this->dictionaryConvertor->convertMobileDictionaryToArray($values->platform, $sourceFile);

			$comment = 'Import ' . DictionaryConverter::getPlatformName($values->platform) . ' slovníku proveden uživatelem: ' . $this->admin->getUserName() . ' (' . $this->admin->getId() . ')';
			if ($this->configuration->getMode() === 'dev') {
				$comment .= ' (localhost)';
			}

			$this->translationFacade->updateDictionary($values->localization, 'mobile_' . $values->platform, $data, GithubClient::DEFAULT_BRANCH, $comment);

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IImportMobileDictionaryControlFactory
{
	/**
	 * @param User $admin
	 * @return ImportMobileDictionaryControl
	 */
	public function create(User $admin);
}
