<?php

namespace tipli\AdminModule\TagsModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;

class AddTagToShopControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	/**
	 * @var Tag
	 */
	private $tag;

	/**
	 * @var TagFacade
	 */
	private $tagFacade;

	/**
	 * @var ShopFacade
	 */
	private $shopFacade;

	public function __construct(Tag $tag = null, TagFacade $tagFacade, ShopFacade $shopFacade)
	{
				$this->tag = $tag;
		$this->tagFacade = $tagFacade;
		$this->shopFacade = $shopFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addGroup('Přiřazení tagu');

		$form->addSelect('shopId', 'Obchod:', $this->getShops())
			->setPrompt('-- vyberte eshop --')
			->setRequired('Zvolte eshop.')
			->setAttribute('data-live-search', 'true')
			->setAttribute('class', 'selectpicker');

		$form->addGroup('submit');

		$form->addSubmit('submit', 'Přiřadit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$tag = $this->tag;

			/** @var Shop $shop */
			$shop = $this->shopFacade->find($values->shopId);

			$shop->addTag($tag, 0);
			$this->shopFacade->saveShop($shop);

			$this->onSuccess($tag);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}

	protected function getShops()
	{
		$shopsQuery = $this->shopFacade->createShopsQuery($this->tag->getLocalization())
			->withTag($this->tag)
			->sortByTagPriority();

		$shopsWithTag = $this->shopFacade->fetch($shopsQuery);

		$shopsIds = [];
		foreach ($shopsWithTag as $shop) {
			$shopsIds[] = $shop->getId();
		}

		$shopsQuery = $this->shopFacade->createShopsQuery($this->tag->getLocalization())
			->sortAlphabetically();

		if (!empty($shopsIds)) {
			$shopsQuery->notIn($shopsIds);
		}

		$shopsWithoutTag = $this->shopFacade->fetch($shopsQuery);

		$shops = [];
		foreach ($shopsWithoutTag as $shop) {
			$shops[$shop->getId()] = $shop->getName();
		}

		return $shops;
	}
}

interface IAddTagToShopControlFactory
{
	/**
	 * @return AddTagToShopControl
	 */
	public function create(Tag $tag = null);
}
