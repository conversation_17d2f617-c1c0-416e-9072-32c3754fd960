{var $freeContent = true}

{block headerContent}
    <a n:href="default" class="btn btn-light btn-sm"> < back</a>
{/block}
{block title}Deal{/block}
{block content}

{snippet errors}
    <div class="row" n:ifset="$error">
        <div class="col-md-12">
            <div class="alert alert-danger">{$error |noescape}</div>
        </div>
        <script>window.location="#"</script>
    </div>
{/snippet}
{form dealControl-form, class => "ajax"}
<div class="row">
    <div class="col-md-7">
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Basic settings</strong>

                <div class="float-right" style="font-size: 13px">
                    {if $deal && $deal->getAuthor()}
                        <span style="margin-left: 10px">Author: {$deal->getAuthor()->getEmail()}</span>
                    {/if}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-12">
                        {snippet dealWithSameUrlExists}
                            <div n:if="isset($showDealWithSameUrlExists) && $showDealWithSameUrlExists" class="alert alert-warning alert-dismissible fade show" role="alert">
                                <strong>Deal with entered url already exists!</strong>
                                <a n:href="this, $dealWithSameUrl->getId()" target="_blank">Open in new window ></a>
                                <button class="close" type="button" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                        {/snippet}

                        <div class="alert alert-warning alert-dismissible fade show hide" role="alert" data-alert="invalid-url">
                            <strong>Invalid URL</strong>
                            URL address is not valid. Please enter a valid URL.
                            <button class="close" type="button" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>

                        <label>URL to the deal:</label>
                        <div class="input-group">
                            {input deepUrl, class => "form-control", placeholder => "https://..", data-input => "url"}
                            <span class="input-group-append">
                                <button class="btn btn-primary" type="button" data-type="load-url">
                                    <span class="spinner-border spinner-border-sm hide" role="status" aria-hidden="true" data-type="spinner"></span>
                                    Load information
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-3">
                        <label>Localization</label>
                        {input localization, class => "form-control", data-input => "localization"}
                        {$form['localization']->getCustomHtml() |noescape}
                    </div>
                    <div class="form-group col-md-5">
                        <label>Primary shop</label>
                        {input primaryShop, class => "form-control", data-input => "primaryShop"}
                        {$form['primaryShop']->getCustomHtml() |noescape}

                        {snippet setDetectedShops}
                            {if isset($detectedShops) && !empty($detectedShops)}
                            <div class="detected-shops">
                                <a href="javascript:void(0);" class="btn btn-sm btn-xs btn-outline-primary" n:foreach="$detectedShops as $detectedShopFromDetectedShops" onclick="javascript:pickShop({$detectedShopFromDetectedShops->getLocalization()->getId()}, {$detectedShopFromDetectedShops->getId()});">
                                    {$detectedShopFromDetectedShops->getLocalization() |flag |noescape}
                                    {$detectedShopFromDetectedShops->getName()}
                                </a>
                            </div>
                            {/if}
                        {/snippet}
                        <small><a href="javascript:void(0);" data-type="edit-alternative-shops">add alternative shops</a></small>
                    </div>
                    <div class="form-group col-md-2">
                        <label>Type</label>
						{input type, class => "form-control", data-input => "type"}
                    </div>
                    <div class="form-group col-md-2">
                        <label>Reward label</label>
						{input rewardLabel, class => "form-control", data-input => "rewardLabel"}
                    </div>
                </div>

                <div class="row hide" data-type="alternative-shops-settings">
                    <div class="form-group col-md-12">
                        <label>Alternative shops</label>

                        {*<div n:snippet="relatedShops">*}
                            {*{if isset($relatedShops) && !empty($relatedShops)}*}
                                {*<a href="javascript:void(0);" onclick="javascript:addAlternativeShopId({$relatedShop->getId()}); $(this).remove();" class="btn btn-sm btn-outline-primary" n:foreach="$relatedShops as $relatedShop">+ {$relatedShop->getName()}</a>*}
                                {*<div class="clearfix" style="margin: 5px;"></div>*}
                            {*{/if}*}
                        {*</div>*}

                        {input alternativeShops, class => "form-control", data-input => "alternativeShops"}
						{$form['alternativeShops']->getCustomHtml() |noescape}
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-md-12">
                        <label>Name</label>
                        {input name, class => "form-control", placeholder => "Zadejte název dealu", data-input => "name"}
                        <small><a href="javascript:void(0);" data-type="edit-slug">edit slug</a></small>
                    </div>
                </div>
                <div class="row hide" data-type="slug-settings">
                    <div class="form-group col-md-12">
                        <label>Slug</label>
                        {input slug, class => "form-control", placeholder => "nazev-dealu", data-input => "slug"}
                    </div>
                </div>
				<div class="row">
					<div class="form-group col-md-12">
						<label>
							{input brandSuffix,  class => "form-control"}
                            Automatically add store name
						</label>
					</div>
				</div>
            </div>
        </div>

        <div class="card card-accent-primary animated fadeIn" data-settings="sale">
            <div class="card-header">
                <strong>Sale settings</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-8">
                        <label>Value</label>
                        {input saleValue, class => "form-control price-input", placeholder => "e.g. 200"}
                    </div>
                    <div class="form-group col-md-4">
                        <label>Unit</label>
                        {input saleUnit, class => "form-control", data-input => "unit"}
                    </div>
					<div class="form-group col-md-12">
						<label>{input unknownSaleValue, class => "form-control"} unknown sale value</label>
					</div>
                </div>
            </div>
        </div>


        <div class="card card-accent-primary animated fadeIn" data-settings="coupon">
            <div class="card-header">
                <strong>Coupon settings</strong>
            </div>
            <div class="card-body">
                <div class="row">
					<div class="form-group col-md-5">
						<label>Code</label>
						{input couponCode, class => "form-control", placeholder => "CODE_123"}
					</div>
					<div class="form-group col-md-4 couponValueData">
						<label>Value</label>
						{input couponValue, class => "form-control price-input", placeholder => "e.g. 250"}
					</div>
					<div class="form-group col-md-3 couponValueData">
						<label>Unit</label>
						{input couponUnit, class => "form-control"}
					</div>
					<div class="form-group col-md-4 customLabelData">
						<label>Reward</label>
						{input customLabelValue, class => "form-control"}
					</div>
                </div>

				<div class="row">
					<div class="form-group col-md-12">
						<label>{input customLabel, class => "form-control"} Other reward</label>
					</div>

					<div class="form-group col-md-12">
						<label>{input exclusive, class => "form-control"} Exclusive</label>
					</div>
				</div>
            </div>
        </div>

        <div class="card card-accent-primary animated fadeIn" data-settings="product">
            <div class="card-header">
                <strong>Product settings</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-4">
                        <label>Current price</label>
                        {input productPrice, class => "form-control price-input", placeholder => "e.g. 100"}
                    </div>
                    <div class="form-group col-md-4">
                        <label>Common price</label>
                        {input productOriginalPrice, class => "form-control price-input", placeholder => "e.g. 160"}
                    </div>
                    <div class="form-group col-md-4">
                        <label>Currency</label>
                        {input productPriceCurrency, class => "form-control"}
                    </div>
                </div>
                <div class="row" data-type="free-shipping-settings">
                    <div class="form-group col-md-12">
                        <label class="checkbox">
                            <input type="checkbox" class="checkbox" n:name="productFreeShipping">
                            Free shipping
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-accent-primary animated fadeIn" data-settings="free_shipping">
            <div class="card-header">
                <strong>Free shipping settings</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-6">
                        <label>Code</label>
                        {input freeShippingCode, class => "form-control", placeholder => "CODE_123"}
                    </div>
                    <div class="form-group col-md-6">
                        <label>Carrier</label>
                        {input freeShippingCarrier, class => "form-control"}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-6">
                        <label>Amount from which shipping is free</label>
                        {input freeShippingMinimalOrder, class => "form-control", placeholder => 0}
                    </div>
                    <div class="form-group col-md-6">
                        <label>Currency</label>
                        {input freeShippingMinimalOrderCurrency, class => "form-control"}
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Deal description</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-12">
                        <label>Deal description or coupon condition</label>
                        {input description, class => "form-control", rows => 3, data-input => "description"}
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Image</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        {snippet setFormPictures}
                            <div class="recommended-pictures" n:if="isset($formData) && isset($formData['pictures']) && !empty($formData['pictures'])">
                                Image suggestions (click to select an image):<br />
                                {foreach $formData['pictures'] as $pictureUrl}
                                    <a href="javascript:void(0);" onclick="pickPicture({$pictureUrl});" class="animated fadeIn">
                                        <img src="{$pictureUrl}" alt="" style="border:1px dashed #000; padding: 3px;">
                                    </a>
                                {/foreach}
                            </div>
                        {/snippet}
                    </div>
                    <div class="col-md-12 text-center">
                        {if $deal}
							{snippet picture}
								<img src="{$deal->getPicture() |image:320,220}" data-type="picture-preview" class="picture-preview" />
								<a n:if="$deal->getPicture()" n:href="removePicture! $deal->getId()" class="ajax btn btn-sm btn-danger" style="position: absolute; left: 0; top: 0; margin: 10px 25px;" onclick="return confirm('Opravdu trvale odstranit obrázek?');"><i class="fa fa-times"></i></a>
                       		{/snippet}
                        {else}
                            <img src="/images/admin-image-placeholder-square.png" data-type="picture-preview" class="picture-preview" />
                        {/if}
                    </div>
                    <div class="col-md-12 text-center">
                        <br />
                        {input pictureUrl, data-input => "pictureUrl"}
						{input picture, data-input => "picture"}
                    </div>

                    <div class="col-md-12">
                        {input imageFlag} Image display (cropping) method
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Other settings</strong>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <label>Tags:</label>
                    </div>
                    <div class="col-md-12">
						{foreach $primaryTags as $localizationId => $tags}
							<ul class="primary-tags" data-localization="{$localizationId}">
								<li n:foreach="$tags as $tagId => $tagName">
									<input type="checkbox" id="tagGroupRadio{$tagId}" value="{$tagId}" name="addTag" class="custom-control-input">
									<label for="tagGroupRadio{$tagId}">
										<a class="btn btn-sm btn-outline-primary" data-value="{$tagId}">{$tagName}</a>
									</label>
								</li>
							</ul>
						{/foreach}

                        {input tagsIds, class => "form-control", data-input => "tagsIds"}
                    </div>
                    {*<div class="col-md-12" data-type="recommended-tag;s">*}
                        {*<div class="clearfix" style="margin: 5px;"></div>*}
                        {*<a href="#" class="btn btn-sm btn-outline-primary">+ móda</a>*}
                        {*<a href="#" class="btn btn-sm btn-outline-primary">+ cestování</a>*}
                        {*<a href="#" class="btn btn-sm btn-outline-primary">+ zdraví</a>*}
                        {*<a href="#" class="btn btn-sm btn-outline-primary">+ pro krásu</a>*}
                        {*<a href="#" class="btn btn-sm btn-outline-primary">+ Aliexpress a ostatní</a>*}
                    {*</div>*}
                </div>
                <hr />
                <div class="row">
                    <div class="form-group col-md-6">
                        <label>Valid since</label>
                        {input validSince, class => "form-control datetimepicker"}
                    </div>
                    <div class="form-group col-md-6">
                        <label>Valid till</label>
                        {input validTill, class => "form-control datetimepicker"}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-6">
                    </div>
                    <div class="form-group col-md-6">
                        <div n:ifset="$form['unknownValidTill']">
                            <label>
                                {input unknownValidTill, data-input => "unknown-valid-till"} Unknown expiry date
                            </label>

                            <br />
                        </div>

                        <label>
                            {input expire} Expire deal
                        </label>
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-md-4">
                        <label>Visibility:</label>
                        {input visibility, class => "form-control"}
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-md-4">
                        <label>Priority:</label>
                        {input priority, class => "form-control"}
                    </div>
                </div>

                <div class="row" style="margin-top: 10px">
                    <div class="col-md-4">
                        <label>Level:</label>

                        {foreach $form[level]->items as $key => $label}
                            <div class="mx-0"><input n:name="level:$key" class="mr-2"><label n:name="level:$key">{$label}</label></div>
                        {/foreach}
                    </div>
                </div>
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-md-12 text-center">
                        {ifset $form['competitionDealId']}
                            {input competitionDealId}
                        {/ifset}

                        {input submit, class => "btn btn-outline-success"}
                        {input submitAndContinue, class => "btn btn-success"}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-5">
        <div class="card card-accent-warning" n:if="isset($rawDeal) && $rawDeal">
            <div class="card-header">
                <strong>Deal from inbox</strong>
                <div class="card-header-actions">
                    <a class="card-header-action btn-close" href="javascript:void(0);" onclick="javascript:$(this).parents('.card').fadeOut();">
                        <i class="fa fa-times"></i>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row form-group">
                    <div class="col-md-3">Source:</div>
                    <div class="col-md-9">{if $rawDeal->getSource()}{$rawDeal::getSources()[$rawDeal->getSource()]}{else}<em>- neznámy -</em>{/if}</div>
                </div>
                <div class="row form-group" n:if="$rawDeal->getDeepUrl()">
                    <div class="col-md-3">URL:</div>
                    <div class="col-md-9"><input class="form-control" value="{$rawDeal->getDeepUrl()}" readonly="readonly" style="background: #fff;" /></div>
                </div>
                <div class="row form-group" n:if="$rawDeal->getName()">
                    <div class="col-md-3">Name:</div>
                    <div class="col-md-9"><input class="form-control" value="{$rawDeal->getName()}" readonly="readonly" style="background: #fff;" /></div>
                </div>
                <div class="row form-group" n:if="$rawDeal->getCode()">
                    <div class="col-md-3">Code:</div>
                    <div class="col-md-9"><input class="form-control" value="{$rawDeal->getCode()}" readonly="readonly" style="background: #fff;" /></div>
                </div>
                <div class="row form-group">
                    <div class="col-md-3">Sent by:</div>
                    <div class="col-md-9">
                        {if $rawDeal->getUser()}
                            <a n:href=":Admin:Account:User:userCard, $rawDeal->getUser()->getId()">{$rawDeal->getUser()->getUserName()}</a>
                        {elseif $rawDeal->isEmailSource()}
                            {$rawDeal->getEmailFrom()}
                        {else}
                            <em>-unknown user -</em>
                        {/if}
                    </div>
                </div>
                {if $rawDeal->isEmailSource()}
                <div class="row form-group">
                    <div class="col-md-3">Email subject:</div>
                    <div class="col-md-9">{$rawDeal->getEmailSubject()}</div>
                </div>
                    <div class="row form-group">
                        <div class="col-md-3">Email content:</div>
                        <div class="col-md-9 text-right"><a n:href="RawDeal:rawDealEmailBody, id => $rawDeal->getId()">view full email content ></a></div>
                        <div class="col-md-12">
                            <iframe src="{plink RawDeal:rawDealEmailBody, id => $rawDeal->getId()}" width="100%" height="900px" frameborder="0" style="border:0"></iframe>
                        </div>
                    </div>
                {/if}
            </div>
        </div>

        {snippet shopBox}
            <div class="card card-accent-primary" n:if="isset($shop) && $shop">
                <div class="card-header">
                    <strong>Available shop deals: {$shop->getName()}</strong>
                    <img src="{$shop->getLogo() |image: 64}" style="float:right;max-width: 64px; max-height:22px;" alt="{$shop->getName()} "/>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <strong>Shop note</strong><br />
                            {$shop->getNote() ? $shop->getNote() : "- bez poznámky -" |breaklines}
                            <br /><br />

							<strong>Preferred version of coupon wording: </strong> <br />
							{if !empty($shop->getDescriptionBlocks('deal')) &&
								$shop->getDescriptionBlocks('deal')[0]->getTitle() !== null}
								{$shop->getDescriptionBlocks('deal')[0]->getTitle()}
							{else}
								{_'front.shops.shop.shop.shopDetail.title', [shop => $shop->getName()]}
							{/if}
							<br /><br />

                            <strong>Active deals</strong>
							{if !$activeDeals->isEmpty()}
								<ul>
									<li n:foreach="$activeDeals as $activeDeal" n:class="$iterator->counter > 5 ? hide">{$activeDeal->getName()}</li>
									<li><a href="javascript:void(0);" onclick="$(this).parents('ul').find('li.hide').removeClass('hide'); $(this).parents('li').remove();">zobrazit další</a></li>
								</ul>
							{else}
								<div class="alert alert-info mt-2">
                                    There are no deals active for the selected shop
								</div>
							{/if}
                        </div>
                    </div>
                </div>;
            </div>
        {/snippet}

        {ifset $competitionDeal}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Competitive coupon detail</strong>
            </div>
            <div class="card-body">
                <div style="margin-bottom: 10px">
                    <a href="{$competitionDeal->getDetailUrl()}" class="btn btn-sm btn-secondary text-white" style="background: #868686" target="_blank">go to coupon detail »</a>
                    <a href="{$competitionDeal->getListingUrl()}" class="btn btn-sm btn-secondary text-white" style="background: #868686" target="_blank">go to the list of coupons »</a>
                </div>

                <table class="table table-bordered">
                    <tr>
                        <td class="font-weight-bold">Name</td>
                        <td>{$competitionDeal->getName()}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Description</td>
                        <td>{$competitionDeal->getDescription()}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Source</td>
                        <td>{$competitionDeal->getCompany()->getName()}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Shop</td>
                        <td>{$competitionDeal->getShop() ? $competitionDeal->getShop()->getName() : '- je potřeba napárovat -'}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Reward</td>
                        <td>{$competitionDeal->getReward()}</td>
                    </tr>

                    <tr>
                        <td class="font-weight-bold">Last seent at</td>
                        <td>{$competitionDeal->getLastSeenAt()->format('d.m.Y H:i')}</td>
                    </tr>
                </table>
            </div>
        </div>
        {/ifset}
    </div>
</div>
{/form}
<div class="clearfix"></div>

<script>
    $(document).ready(function() {
//        algoliaShopSearch("[data-input=primaryShopId]","select[name=localization]");
//        algoliaShopSearch("[data-input=alternativeShopsIds]","select[name=localization]");

		$('#frm-dealControl-form-customLabel').on('change', function(){
			if ($(this).is(':checked')) {
				$('.couponValueData').hide();
				$('.customLabelData').show();
			} else {
				$('.couponValueData').show();
				$('.customLabelData').hide();
			}
		}).trigger('change');

		$('#frm-dealControl-form-unknownSaleValue').on('change', function() {
			var dealValue = $('#frm-dealControl-form-saleValue');
			var dealUnit = $('#frm-dealControl-form-saleUnit');

			if ($(this).is(':checked')) {
				dealValue.prop('disabled', true);
			} else {
				dealValue.prop('disabled', false);
			}
		}).trigger('change');

        $('.price-input').each(function(){
            $(this).on('keyup', function(){
                $(this).val(priceFormatValue($(this).val()));
            });

            $(this).val(priceFormatValue($(this).val()));
        });

        $('input[type=submit]').click(function(e){
            $('.price-input').each(function(){
                $(this).val($(this).val().replace(/\s+/g, ''));
            });
        });

        $('#frm-dealControl-form-type').on('change', function(){
			var selectedType = $(this).val();

			if (selectedType === 'sale' || selectedType === 'coupon'){
				if (selectedType === 'sale') {
					var dealValue = $('#frm-dealControl-form-saleValue');
					var dealUnit = $('#frm-dealControl-form-saleUnit');
				} else if (selectedType === 'coupon') {
					var dealValue = $('#frm-dealControl-form-couponValue');
					var dealUnit = $('#frm-dealControl-form-couponUnit');
				}

				dealValue.add(dealUnit).on('change', function(e){
					if (dealUnit.val() === 'percentage' && parseFloat(dealValue.val()) >= 90) {
						alert("The amount of the discount is suspiciously high. Check the amount of the discount entered.");
					}
				});
			}
		});

        let validSince = $('#frm-dealControl-form-validSince');
        let validTillDefaultValue = $('#frm-dealControl-form-validTill').val();

        // type switch
        {if !$dealId}
            $("[data-input='type']").on("change", function() {
                var type = $(this).val();
                var localization = $('[data-input="localization"]').val();

                let inputDealName = $("[data-input='name']");
                let inputDealDescription = $("[data-input='description']");

                let defaultDealName = {};
                let defaultDealDescription = {};
                let primaryShop;

                let validSinceSplit = validSince.val().split(' ')[0].split('-');
                let ValidSinceDate = new Date(validSinceSplit[0], validSinceSplit[1], validSinceSplit[2]);

                $('#frm-dealControl-form-validTill').val(validTillDefaultValue)

                if (localization) {
                    if (type === 'coupon') {
                        defaultDealName = { 1: "Slevový kód %shopName%", 2: 'Zľavový kód %shopName%', 3: 'Kupon rabatowy %shopName%', 4: 'Voucher %shopName%', 5: 'Coupon', 6: 'Kedvezményes kupon %shopName%'};
                        defaultDealDescription = { 1: 'Kupon využijte v košíku obchodu.', 2: 'Zľavový kupón využijete v košíku obchodu.', 3: 'Kupon wykorzystasz w koszyku sklepu.', 4: 'Cuponul de reducere îl puteți folosi în coșul magazinului.'}

                    } else if (type === 'sale') {
                        let newValidTill = new Date(ValidSinceDate.setMonth(ValidSinceDate.getMonth() + 2))
                        let newValidTillString = newValidTill.toISOString().split('T')[0] + " 23:59:59"

                        defaultDealName = { 1: 'Slevy až '};
                        $('#frm-dealControl-form-validTill').val(newValidTillString)
                    } else if (type === 'free_shipping') {
                        let newValidTill = new Date(ValidSinceDate.setMonth(ValidSinceDate.getMonth()))
                        let newValidTillString = newValidTill.toISOString().split('T')[0] + " 23:59:59"

                        defaultDealName = { 1: 'Doprava zdarma '};

                        defaultDealDescription = { 1: 'V %shopName% využijte dopravu zdarma.' }
                        $('#frm-dealControl-form-validTill').val(newValidTillString)
                    }

					primaryShop = $('[data-input="primaryShop"]').text();

                    if (defaultDealName[localization]) {
                        inputDealName.val(defaultDealName[localization].replace('%shopName%', primaryShop));
                    }

                    if (defaultDealDescription[localization]) {
                        inputDealDescription.val(defaultDealDescription[localization].replace('%shopName%', primaryShop));
                    }
                }

                $("[data-settings]").hide();
                $("[data-settings='" + type + "']").show();
            }).trigger("change");

            $('#frm-dealControl-form-saleValue, [data-input="unit"]').on("change", function() {
                let inputDealDescription = $("[data-input='description']");
                let defaultDealDescription = { 1: 'Ušetřete až %value% v obchodě %shopName%.' }
                let localization = $('[data-input="localization"]').val();
                let primaryShop = $('[data-input="primaryShop"]').text();
                let unit = $("[data-input='unit'] option:selected").text();
                let value;

                if (unit == '%') {
                    value = $('#frm-dealControl-form-saleValue').val() + "%";
                } else {
                    value = $('#frm-dealControl-form-saleValue').val() + " " + unit;
                }

                if (defaultDealDescription[localization]) {
                    inputDealDescription.val(defaultDealDescription[localization].replace('%shopName%', primaryShop).replace('%value%', value));
                }
            });
        {/if}

        // edit slug
        $("[data-type='edit-slug']").unbind("click").bind("click", function() {
            $("[data-type='slug-settings']").removeClass("hide");
            $(this).hide();
        });

        // edit alternative shops
        $("[data-type='edit-alternative-shops']").unbind("click").bind("click", function() {
            $("[data-type='alternative-shops-settings']").removeClass("hide");
            $(this).hide();
	});

        // localization / tags switch
        $('[data-input="localization"]').on("change", function() {
            $("[data-input='type']").trigger('change')
			var localization = $('[data-input="localization"]').val();
			var tagsIdsInput = $('[data-input="tagsIds"]');
			tagsIdsInput.find("option").remove();
			if (localization !== "") {
				var tagsLists = {$tagsLists};
				$.each(tagsLists[localization], function(key,value) {
					tagsIdsInput.append($("<option></option>").attr("value", key).text(value));
				});
			}
			tagsIdsInput.selectpicker('refresh');

			refreshPrimaryTagButtons();
        });

        {ifset $competitionDeal}
            let primaryShopId = $("[data-input='primaryShop']").val();
            $('[data-input="localization"]').trigger("change");
            $("[data-input='primaryShop']").val(primaryShopId)
        {/ifset}

		function refreshPrimaryTagButtons() {
			$('.primary-tags').hide();
			var localization = $('[data-input="localization"]').val();
			var tagsIdsInput = $('[data-input="tagsIds"]');

			if (localization) {
				$('.primary-tags').each(function(){
					if ($(this).data('localization') == localization) {
						$(this).show();

						$(this).find('input').each(function(){
							var button = $(this).parent().find('a');
							var value = $(this).val();

							button.removeClass('btn-primary text-white');
							button.addClass('btn-outline-primary');
							$(this).prop('checked', false);

							if (tagsIdsInput.find('option[value="' + value + '"]').is(':selected')) {
								$(this).prop('checked', true);
								button.removeClass('btn-outline-primary');
								button.addClass('btn-primary text-white');
							}
						})
					}
				});
			}
		} refreshPrimaryTagButtons();

		{if $deal};
			refreshPrimaryTagButtons();
		{/if}

		$('input[name=addTag]').on('change', function(){
			var isChecked = $(this).is(':checked');
			var value = $(this).val();
			var tagsIdsInput = $('[data-input="tagsIds"]');
			var button = $(this).parent().find('a');

			if (isChecked) {
				tagsIdsInput.find('option[value="'+ value +'"]').prop('selected', true);
				button.removeClass('btn-outline-primary');
				button.addClass('btn-primary text-white');
			} else {
				tagsIdsInput.find('option[value="'+ value +'"]').prop('selected', false);
				button.removeClass('btn-primary text-white');
				button.addClass('btn-outline-primary');
			}

			tagsIdsInput.selectpicker('refresh');
		});


        // load data after pick some shop
        $("[data-input='primaryShop']").on("change", function() {
            $.nette.ajax({
                url: {link pickShop!},
                method: "POST",
                data: {
                    shopId: $(this).val()
                },
            });
        });

        {ifset $competitionDeal}
            $("[data-input='primaryShop']").trigger('change');
        {/ifset}

        // Load Url button
        $("[data-type='load-url']").unbind("click").bind("click", function() {
            var url = $("[data-input='url']").val();

            if (url != "" && isUrlValid(url)) {
                loadUrl(url);
            }
        });

        // fist change in url input
        {if !$deal && false}
            var keyPressedInUrl = false;
            $("[data-input='url']").unbind("keyup").bind("keyup", function() {
                var url = $(this).val();

                if (url != "" && isUrlValid(url) && !keyPressedInUrl) {
                   loadUrl(url);

                   keyPressedInUrl = true;
                }
            });
        {/if}
    });

    function pickShop(localization, shopId) {
        $(".detected-shops").remove();

//        $('[data-input="localization"]').selectpicker('val', localization);
//        $('[data-input="localization"]').trigger("change");
    }

    function pickTags(localization, tagsIds) {
        $('[data-input="localization"]').val(localization);
        $('[data-input="localization"]').trigger("change");

        $('[data-input="tagsIds"]').val(tagsIds);
        $('[data-input="tagsIds"]').trigger("change");

//        $('select').selectpicker('refresh');
    }

    function addAlternativeShopId(shopId) {
//        var input = $("[data-input='alternativeShopsIds']");
//
//        var shops = input.selectpicker("val") == null ? [] : input.selectpicker("val");
//        shops.push(shopId);
//
//        input.selectpicker("val", shops);
    }

    function loadUrl(url) {
        var loadButton = $("[data-type='load-url']");
        var spinner = $("[data-type='load-url'] [data-type='spinner']");

        spinner.removeClass("hide");
        loadButton.attr("disabled", "disabled");

        var localization = $('[data-input="localization"]').val();

        if (typeof localization == 'undefined') {
            var localization = null;
        }

        $.nette.ajax({
            url: {link loadUrl!},
            method: "POST",
            data: {
                localization: localization,
                url: url
            },
            success: function() {
                spinner.addClass("hide");
                loadButton.removeAttr("disabled");
            },
        });
    }

    function isUrlValid(url) {
        var pattern = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
        if (pattern.test(url)) {
            return true;
        }

        return false;
    }

    function pickPicture(url) {
        $("[data-type='picture-preview']").attr("src", url);
        $("[data-input='pictureUrl']").val(url);
        resetPictureUpload();
    }

    function resetPictureUpload() {
        $("[data-input='picture']").val(null);
    }

    function priceFormatValue(value) {
        value = value.replace(/[^0-9\.]/g, '');

        if (value != "") {
            valArr = value.split('.');
            valArr[0] = (parseInt(valArr[0], 10)).toLocaleString();
            value = valArr.join('.');
        }

        return value;
    }

    $('#frm-dealControl-form-unknownValidTill').on('change', function() {
        if ($(this).is(':checked')) {
            $('#frm-dealControl-form-validTill').hide()
        } else {
            $('#frm-dealControl-form-validTill').show()
        }
    });

    $('#frm-dealControl-form-unknownValidTill').trigger('change');
</script>

{snippet setDetectedShop}
    <script n:if="isset($detectedShop) && $detectedShop">
        pickShop({$detectedShop->getLocalization()->getId()}, {$detectedShop->getId()});
    </script>
{/snippet}

{snippet setFormData}
    <script n:if="isset($formData) && !empty($formData)">
        {if isset($formData['name']) && (!$rawDeal || !$rawDeal->getName())}
            $("[data-input='name']").val({$formData['name']}).addClass("a");
            $("[data-input='slug']").val(convertToSlug({$formData['name']})).addClass("a");

            setTimeout(function() {
                $("[data-input='name']").removeClass("a");
                $("[data-input='slug']").removeClass("a");
            }, 2000);
        {/if}

        {if isset($formData['description'])}
            $("[data-input='description']").val({$formData['description']}).addClass("a");

            setTimeout(function() {
                $("[data-input='description']").removeClass("a");
            }, 2000);
        {/if}
    </script>
{/snippet}

{if $rawDeal}
    <script>
        loadUrl({$rawDeal->getDeepUrl()});
    </script>
{/if}

<style>
    .btn-xs {
        padding: 1px 3px !important;
    }
    .detected-shops {
        margin-top: 5px;
    }

    .recommended-pictures {
        overflow: scroll;
        width:100%;
        white-space: nowrap;
        margin-bottom:15px;
    }

    .recommended-pictures img {
        max-height: 160px;
        white-space: nowrap;
    }
    .recommended-pictures img:hover {
        opacity:.9;
    }
    .picture-preview {
        max-width:320px;
        max-height:220px;
        border: 3px dashed #eee;
    }

    .a {
        animation-name: color_change;
        animation-duration: 2s;
        animation-iteration-count: 1;
        animation-direction: alternate;
        background:#fff;
    }

    @-webkit-keyframes color_change {
        from { background-color: #ecd526; }
        to { background-color: #fff; }
    }
    @-moz-keyframes color_change {
        from { background-color: #ecd526; }
        to { background-color: #fff; }
    }
    @-ms-keyframes color_change {
        from { background-color: #ecd526; }
        to { background-color: #fff; }
    }
    @-o-keyframes color_change {
        from { background-color: #ecd526; }
        to { background-color: #fff; }
    }
    @keyframes color_change {
        from { background-color: #ecd526; }
        to { background-color: #fff; }
    }

	.primary-tags {
		list-style-type:none;
		margin:4px 0 8px 0;
		padding:0;
	}

	.primary-tags li {
		display: inline-block;
		position:relative;
	}

	.primary-tags a {
		padding: 3px 5px;
	}

	.primary-tags input {
		display:block;
		position:absolute;
		top:0;
		left:0;
		right:0;
		bottom:0;
	}

	.primary-tags input[type="checkbox"] {
		opacity:0.011;
		z-index:100;
	}

	.primary-tags label {
		color: #444444;
		cursor:pointer;
		z-index:90;
		font-size: 13px;
	}
</style>
