<?php

namespace tipli\AdminModule\DealsModule\Presenters;

use DOMDocument;
use DOMXPath;
use Doctrine\ORM\QueryBuilder;
use Nette\Http\Url;
use Nette\Utils\Html;
use Nette\Utils\Strings;
use tipli\AdminModule\DealsModule\Forms\IAssignTagsToDealsControlFactory;
use tipli\AdminModule\DealsModule\Forms\IDealControlFactory;
use tipli\AdminModule\Presenters\BasePresenter;
use tipli\Model\Competition\CompetitionFacade;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\Entities\DealTag;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\RelatedShop;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\Entities\TagGroup;
use tipli\Model\Tags\TagFacade;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;

class DealPresenter extends BasePresenter
{
	/** @var DealFacade @inject */
	public $dealFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var CompetitionFacade @inject */
	public $competitionFacade;

	/** @var IDealControlFactory @inject */
	public $dealControlFactory;

	/** @var IAssignTagsToDealsControlFactory @inject */
	public $assignTagsToDealsControlFactory;

	/** edit multiple deal ids */
	public $dealIds;

	public function startup()
	{
		parent::startup();

		$this->redirectUrl('https://letaky.tipli.cz/admin/deals');
	}

	protected function createComponentDealControl()
	{
		$rawDeal = $this->getParameter('rawDealId') ? $this->dealFacade->findRawDeal($this->getParameter('rawDealId')) : null;
		$deal = $this->getParameter('id') && !$rawDeal ? $this->dealFacade->find($this->getParameter('id')) : null;
		$competitionDeal = $this->getParameter('competitionDealId') && !$rawDeal ? $this->competitionFacade->find($this->getParameter('competitionDealId')) : null;

		$control = $this->dealControlFactory->create($deal, $rawDeal, $competitionDeal, $this->getUserIdentity(), $this->getParameter('shopId') ? (int) $this->getParameter('shopId') : null);

		$control->onSuccess[] = function (Deal $deal, $continue) {
			$this->flashMessage('Nastavení bylo úspěšně uloženo.');

			if ($continue) {
				$this->redirect('deal', $deal->getId());
			} else {
				$this->redirect('default');
			}
		};

		$control->onError[] = function ($error) {
			$this->template->error = $error;
			$this->redrawControl('errors');
		};

		return $control;
	}

	public function handleLoadUrl()
	{
		$url = $this->getHttpRequest()->getPost('url');
		$localizationId = $this->getHttpRequest()->getPost('localizationId');

//        $url = 'https://www.aliexpress.com/item/BIG-SALE-Retail-2014-new-free-shipping-t-shirts-baby-girls-long-sleeve-lace-children-cartoon/1512393029.html?asdf=asdfsfa';

		if ($url) {
			$localization = $localizationId ? $this->localizationFacade->find($localizationId) : null;
			$url = new Url($url);

			$this->processUrl($localization, $url);
		}
	}

	public function handlePickShop()
	{
		$shopId = $this->getHttpRequest()->getPost('shopId');

		/** @var Shop $shop */
		$shop = $shop = $this->shopFacade->find($shopId);

		if ($shopId && $shop) {
			// shop's deals box
			$this->template->shop = $shop;

			$activeDealsQuery = $this->dealFacade->createDealsQuery()
				->withShop($shop)
				->onlyValid()
				->sortTop();

			$this->template->activeDeals = $this->dealFacade->fetch($activeDealsQuery);

			$this->redrawControl('shopBox');

			$this->template->relatedShops = array_map(static function (RelatedShop $relatedShop) {
				return $relatedShop->getRelatedShop();
			}, $this->shopFacade->getRelatedShops($shop));

			$this->redrawControl('relatedShops');
		}
	}

	private function processUrl(Localization $localization = null, Url $url)
	{
		// find same deals
		$dealsWithSameUrl = $this->searchDealsByUrl($localization, $url);
		if (!empty($dealsWithSameUrl)) {
			$this->template->showDealWithSameUrlExists = true;
			$this->template->dealWithSameUrl = reset($dealsWithSameUrl);

			$this->redrawControl('dealWithSameUrlExists');
		}

		// detect shop/s
//        $shops = $this->searchShopsByUrl($localization, $url);
//        if ($shops instanceof Shop) {
//            $this->template->detectedShop = $shops;
//            $this->redrawControl('setDetectedShop');
//        } else if (is_array($shops) and !empty($shops)) {
//            $this->template->detectedShops = $shops;
//            $this->redrawControl('setDetectedShops');
//
//            $this->template->relatedShops = [];
//            $this->redrawControl('relatedShops');
//        } else {
//            $this->template->detectedShops = null;
//            $this->redrawControl('setDetectedShops');
//        }

		// load url - content data
		$data = $this->getDataFromUrl($url);
		if ($data && is_array($data)) {
			$formData = [];

			if (isset($data['og:title'])) {
				$formData['name'] = $data['og:title'];
			}

			if (isset($data['og:description'])) {
				$formData['description'] = $data['og:description'];
			}

			if (isset($data['og:image'])) {
				$formData['pictures'][] = $data['og:image'];
				$this->redrawControl('setFormPictures');
			}

			$this->template->formData = $formData;
			$this->redrawControl('setFormData');
		}
	}

	private function searchDealsByUrl(Localization $localization = null, Url $url)
	{
		// 1 same url
		$deals = $this->dealFacade->searchDealsByDeepUrl($localization, $url->getAbsoluteUrl());

		// 2 url without parameters
		if (empty($deals)) {
			foreach ($url->getQueryParameters() as $queryParameterKey => $value) {
				$url->setQueryParameter($queryParameterKey, null);
			}

			$url = $url->getAbsoluteUrl();

			$deals = $this->dealFacade->searchDealsByDeepUrl($localization, $url);
		}

		return $deals;
	}

	private function searchShopsByUrl(Localization $localization = null, Url $url)
	{
		if ($localization) {
			return $this->shopFacade->findShopByDomain($localization, $url->getDomain());
		} else {
			return $this->shopFacade->findShopsByDomain($url->getDomain());
		}
	}

	private function getDataFromUrl($url)
	{
		$html = @file_get_contents($url);

		if (!$html) {
			return;
		}

		try {
			libxml_use_internal_errors(true);
			$doc = new DOMDocument();
			$doc->loadHTML($html);
			$metas = [];
			foreach ((new DOMXPath($doc))->query('//*/meta[starts-with(@property, \'og:\')]') as $meta) {
				$property = $meta->getAttribute('property');
				$content = $meta->getAttribute('content');
				$metas[$property] = $content;
			}

			return $metas;
		} catch (\Exception $e) {
			return;
		}
	}

	public function renderDeal($id = null, $rawDealId = null, ?int $competitionDealId = null)
	{
		if ($id && $deal = $this->dealFacade->find($id)) {
			if ($deal->isRemoved()) {
				$this->redirect('default');
			}
		}

		if ($competitionDealId) {
			$this->template->competitionDeal = $this->competitionFacade->find($competitionDealId);
		}

		// tags lists
		$tagsLists = [];
		$primaryTags = [];
		foreach ($this->localizationFacade->findLocalizations() as $localization) {
			$tagsLists[$localization->getId()] = [];
			$primaryTags[$localization->getId()] = [];

			/** @var Tag $primaryTag */
			foreach ($this->tagFacade->findTagsByGroup($localization, TagGroup::TYPE_PRIMARY_SALES_TAGS) as $primaryTag) {
				$primaryTags[$localization->getId()][$primaryTag->getId()] = $primaryTag->getName();
			}

			/** @var Tag $tag */
			foreach ($this->tagFacade->getTags($localization, Tag::TYPE_SALE)->getQuery()->getResult() as $tag) {
				$tagsLists[$localization->getId()][$tag->getId()] = $tag->getName();
			}
		}

		$this->template->primaryTags = $primaryTags;
		$this->template->tagsLists = $tagsLists;
		$this->template->dealId = $id;

		// deal
		$deal = $this->getParameter('id') ? $this->dealFacade->find($this->getParameter('id')) : null;
		$this->template->deal = $deal;

		// raw deal
		$rawDeal = $this->getParameter('rawDealId') ? $this->dealFacade->findRawDeal($this->getParameter('rawDealId')) : null;
		$this->template->rawDeal = $rawDeal;
	}

	public function renderDefault()
	{
		// count of validdeals
		$dealsQuery = $this->dealFacade->createDealsQuery()
			->onlyValid();

		$this->template->countOfValidDeals = $this->dealFacade->fetch($dealsQuery)->getTotalCount();

		// count of waiting raw deals
		$this->template->countOfWaitingRawDeals = $this->dealFacade->findCountOfWaitingRawDealsWithoutCompetitive();

		//count of waiting competitive deals
		$this->template->countOfWaitingCompetitiveDeals = $this->dealFacade->findCountOfWaitingCompetitiveDeals();

		// show popup for add multiple tags to selected deals
		$this->template->addTagsToMultipleDeals = $this->getParameter('dealIds') ?: false;

		// count of likes @todo
	}

	public function createComponentDealsGrid($name)
	{
		$grid = $this->dataGridFactory->create()
			->getGrid(
				$this,
				$name,
				$this->dealFacade->getDeals()
					->addSelect('dt, t')
					->leftJoin('d.dealTags', 'dt')
					->leftJoin('dt.tag', 't')
					->andWhere('d.removedAt IS NULL')
					->andWhere('d.type != :type')
					->setParameter('type', Deal::TYPE_CASHBACK)
			);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Deal/grid/default.latte');

		$grid->addColumnText('localization', 'Jazyk')
			->setRenderer(static function (Deal $deal) {
				return $deal->getLocalization()->getName();
			})->setFilterSelect(['' => 'Vše'] + $this->localizationFacade->findPairs());

		$grid->addColumnText('authorEmail', 'Přidal', 'author.email')
			->setRenderer(static function (Deal $deal) {
				return !$deal->isFromFeed() && $deal->getAuthor() ? $deal->getAuthor()->getEmail() : 'Feed';
			})->setFilterText();

		$grid->addColumnText('name', 'Název', 'name')->setRenderer(static function (Deal $deal) {
			$text = $deal->getName();
			$text .= '<p style="color: #504e4e; font-size: 12px">' . Strings::truncate(strip_tags($deal->getDescription()), 145) . '</p>';

			return $text;
		})->setTemplateEscaping(false)->setFilterText();

		$grid->addColumnText('dealTags', 'Tagy', 'dealTags')
			->setRenderer(function (Deal $deal): string {
				$tags = [];
				/** @var DealTag $dealTag */
				foreach ($deal->getDealTags() as $dealTag) {
					$tag = $dealTag->getTag();

					$tags[] = '<a href="' . $this->link(':Admin:Tags:Tag:tag', $tag->getId()) . '">' . $tag->getName() . '</a>';
				}

				return implode(', ', $tags);
			})->setTemplateEscaping(false)
			->setFilterSelect(['' => 'Vše', 'withoutTag' => 'Bez tagu'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == 'withoutTag') {
					$qb->andWhere('dt IS NULL');
				}
			});

		$grid->addColumnText('sourceType', 'Zdroj')
			->setFilterSelect(['' => 'Vše'] + Deal::getSourceTypes());

		$grid->addColumnText('sourceName', 'Název zdroje')
			->setFilterText();


		$grid->addColumnText('type', 'Type');
		$grid->addFilterSelect('type', 'Type', array_merge(['' => '--vše--'], Deal::getTypes()));

		$grid->addColumnText('shop', 'Obchod')->setRenderer(static function (Deal $deal) {
			return $deal->getShop() ? $deal->getShop()->getName() : '- žádný obchod -';
		});

		$grid->addColumnText('valid', 'Platnost')
			->setFilterSelect(['' => 'Vše'] + [true => 'Pouze aktivní', false => 'Pouze neaktivní'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == true) {
					$qb->andWhere('d.validSince <= :datetime AND d.validTill >= :datetime')
						->setParameter('datetime', new \DateTime());
				} elseif ($value == false) {
					$qb->andWhere('d.validSince >= :datetime OR d.validTill <= :datetime')
						->setParameter('datetime', new \DateTime());
				}
			});

		$grid->addColumnText('validity', 'Délka platnosti')->setRenderer(static function (Deal $deal) {
			return $deal->getValidSince()->diff($deal->getValidTill())->days;
		})->setSortable()->setSortableCallback(static function ($qb, $sort) {
			$qb->addSelect('DATE_DIFF (d.validSince, d.validTill) AS HIDDEN validity');
			$qb->addOrderBy('validity', $sort['validity']);
		})->setTemplateEscaping(false);

		$grid->addColumnText('code', 'Kód kuponu')
			->setFilterText();

		$grid->addColumnText('visibility', 'Viditelnost');

		$grid->addColumnText('gaCountOfClicks', 'GA Počet kliknutí')
			->setDefaultHide();

		$grid->addColumnText('gaCountOfViews', 'GA Počet zobrazení')
			->setDefaultHide()
			->setRenderer(function (Deal $deal) {
				return $this->amountFilter->__invoke($deal->getGaCountOfViews());
			});

		$grid->addColumnText('gaCtr', 'GA CTR')
			->setDefaultHide()
			->setRenderer(static function (Deal $deal) {
				return round($deal->getGaCtr(), 2) . ' %';
			});

		$grid->addColumnText('level', 'Level')->setRenderer(static function (Deal $deal) {
			return $deal->getLevelName();
		})->setFilterSelect(['' => 'Vše'] + Deal::getLevels(), 'level');

		$grid->addColumnText('totalCommissionAmount', 'Obrat')
			->setDefaultHide();

		$grid->addColumnText('countOfTransactions', 'Počet transakcí')
			->setDefaultHide();

		$grid->addColumnDateTime('createdAt', 'Vytvořeno')->setRenderer(static function (Deal $deal) {
			return $deal->getCreatedAt()->format('d.m.Y H:i:s');
		})->setSortable();

		$grid->addFilterText('shop', 'Obchod:')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('s.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addColumnText('priority', 'Priorita')->setDefaultHide();
		$grid->setDefaultSort(['priority' => 'DESC']);

		$grid->addAction('remove', '', 'remove!')
			->setClass('btn btn-xs btn-danger')
			->setIcon('times')
			->setConfirmation(
				new StringConfirmation('Určitě?')
			)
		;
		$grid->addAction('deal', '', 'deal')->setClass('btn btn-xs btn-primary')->setIcon('pen');

		$grid->addGroupAction('Nastavit tagy')->onSelect[] = [$this, 'handleAddTagsToMultipleDeals'];
		$grid->addGroupAction('Expirovat vybrané')->onSelect[] = [$this, 'handleExpireDeals'];
		$grid->addGroupAction('Omladit vybrané')->onSelect[] = [$this, 'handleReValidate'];

		$grid->addGroupAction('Nastavit level: Výchozí')->onSelect[] = (function ($dealIds) {
			$this->handleSetLevel($dealIds, 0);
		});

		$grid->addGroupAction('Nastavit level: Důležitý')->onSelect[] = (function ($dealIds) {
			$this->handleSetLevel($dealIds, 1);
		});

		$grid->addGroupAction('Nastavit level: Topovaný')->onSelect[] = (function ($dealIds) {
			$this->handleSetLevel($dealIds, 2);
		});

		$grid->addToolbarButton('#', 'Resetovat filtr')
			->setRenderer(function () {
				return Html::el('a', 'Resetovat filtr')->href($this->presenter->link('dealsGrid-resetFilter!', ['dealsGrid-per_page' => 50]))
					->setAttribute('class', 'btn btn-sm btn-danger');
			});

		$grid->setDefaultPerPage(50);
		$grid->setItemsPerPageList([10, 50, 100, 200], false);

		$grid->setDefaultFilter(['valid' => 1]);
	}

	public function handleRemove($id)
	{
		$deal = $this->dealFacade->find($id);

		$this->dealFacade->removeDeal($deal);

		$this->flashMessage('Deal byl úspěšně smazán.');
		$this->redirect('this');
	}

	public function handleAddTagsToMultipleDeals(array $dealIds)
	{
		$dealsLocalizationLocale = null;

		foreach ($dealIds as $dealId) {
			/** @var Deal $deal */
			$deal = $this->dealFacade->find($dealId);

			if (!$dealsLocalizationLocale) {
				$dealsLocalizationLocale = $deal->getLocalization()->getLocale();
			} elseif ($dealsLocalizationLocale !== $deal->getLocalization()->getLocale()) {
				$this->flashMessage('Pro hromadnou editaci vybírejte dealy pouze ze stejné lokalizace.', 'danger');
				$this->redirect('this');
			}
		}

		$this->redirect('assign-tags-to-deals', ['dealIds' => implode(',', $dealIds)]);
	}

	public function handleExpireDeals(array $dealIds)
	{
		foreach ($dealIds as $dealId) {
			/** @var Deal $deal */
			$deal = $this->dealFacade->find($dealId);
			$deal->setValidTill(new \DateTime());

			$this->dealFacade->saveDeal($deal);
		}

		$this->flashMessage('Hotovo', 'success');
		$this->redirect('this');
	}

	public function actionAssignTagsToDeals($dealIds, $locale)
	{
		$this->dealIds = $dealIds;
	}

	public function createComponentAssignTagsToDealsControlForm()
	{
		$control = $this->assignTagsToDealsControlFactory->create($this->dealIds);

		$control->onSuccess[] = function () {
			$this->flashMessage('Úspěšně uloženo.');
			$this->redirect(':Admin:Deals:Deal:default');
		};

		return $control;
	}

	public function handleRemovePicture($dealId)
	{
		/** @var Deal $deal */
		$deal = $this->dealFacade->find($dealId);
		$deal->setPicture(null);
		$this->dealFacade->saveDeal($deal);

		$this->template->deal = $deal;
		$this->redrawControl('picture');
	}

	public function handleReValidate(array $dealIds)
	{
		$dealsToSave = [];

		/** @var Deal $deal */
		foreach ($this->dealFacade->findByIds($dealIds) as $deal) {
			if ($deal->getValidSince()->diff(new \DateTime())->days < 30) {
				continue;
			}

			$deal->setValidSince(new \DateTime('-30 days'));
			$dealsToSave[] = $deal;
		}

		$this->dealFacade->saveDeals($dealsToSave);

		$this->flashMessage('Hotovo', 'success');
		$this->redirect('this');
	}

	public function handleSetLevel(array $dealIds, int $level)
	{
		$dealsToSave = [];

		/** @var Deal $deal */
		foreach ($this->dealFacade->findByIds($dealIds) as $deal) {
			$deal->setLevel($level);
			$dealsToSave[] = $deal;
		}

		$this->dealFacade->saveDeals($dealsToSave);

		$this->flashMessage('Hotovo', 'success');
		$this->redirect('this');
	}
}
