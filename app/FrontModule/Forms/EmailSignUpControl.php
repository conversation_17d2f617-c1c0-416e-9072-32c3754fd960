<?php

namespace tipli\FrontModule\Forms;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Nette\Localization\Translator;
use Nette;
use Nette\Application\UI\Form;
use Nette\Http\Response;
use Nette\Security\User;
use Nette\Utils\Html;
use tipli\EmailAlreadyRegisteredException;
use tipli\InvalidArgumentException;
use tipli\Model\Account\UserFacade;
use tipli\Model\Configuration;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Layers\EventLayer;
use tipli\Model\Layers\RegistrationSourceLayer;
use tipli\Model\Layers\SuspectedRequestDetector;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Regions\GeoLocationLayer;
use tipli\Model\Rewards\Entities\RewardCampaign;
use tipli\Model\Rewards\EntranceCampaignResolver;
use tipli\Model\Rewards\RewardFacade;
use tipli\Model\Session\SectionFactory;
use Tracy\Debugger;

class EmailSignUpControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	public $onFailure = [];

	/**
	 * @var User
	 */
	private $user;

	/**
	 * @var UserFacade
	 */
	private $userFacade;

	/**
	 * @var Translator
	 */
	private $translator;

	/**
	 * @var EventLayer
	 */
	private $eventLayer;

	/**
	 * @var ClientLayer
	 */
	private $clientLayer;
	/**
	 * @var UtmLayer
	 */
	private $utmLayer;

	/**
	 * @var RewardFacade
	 */
	private $rewardFacade;

	/**
	 * @var EntranceCampaignResolver
	 */
	private $entranceCampaignResolver;

	/**
	 * @var Response
	 */
	private $httpResponse;

	/**
	 * @var SuspectedRequestDetector
	 */
	private $suspectedRequestDetector;

	/**
	 * @var SectionFactory
	 */
	private $sectionFactory;

	/** @var Nette\Caching\Cache */
	private $cache;

	public function __construct(
		User $user,
		UserFacade $userFacade,
		private LocalizationFacade $localizationFacade,
		Translator $translator,
		EventLayer $eventLayer,
		ClientLayer $clientLayer,
		private GeoLocationLayer $geoLocationLayer,
		UtmLayer $utmLayer,
		RewardFacade $rewardFacade,
		EntranceCampaignResolver $entranceCampaignResolver,
		Response $httpResponse,
		SuspectedRequestDetector $suspectedRequestDetector,
		SectionFactory $sectionFactory,
		Nette\Caching\Storage $storage,
		private Configuration $configuration,
		private RegistrationSourceLayer $registrationSourceLayer,
	) {
		$this->user = $user;
		$this->userFacade = $userFacade;
		$this->translator = $translator;
		$this->eventLayer = $eventLayer;
		$this->clientLayer = $clientLayer;
		$this->utmLayer = $utmLayer;
		$this->rewardFacade = $rewardFacade;
		$this->entranceCampaignResolver = $entranceCampaignResolver;
		$this->httpResponse = $httpResponse;
		$this->suspectedRequestDetector = $suspectedRequestDetector;
		$this->sectionFactory = $sectionFactory;
		$this->cache = new Nette\Caching\Cache($storage, self::class);
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addSubmit('facebook', 'front.homepage.header.form.facebook');

		$form->addSubmit('submit', 'front.form.label.send');

		$form->addText('email', 'front.form.label.yourEmail')
			->addRule(Form::EMAIL, 'front.form.validator.emailNotValid')
			->addConditionOn($form['submit'], Form::SUBMITTED, true)
			->setRequired('front.form.validator.emailRequired')
		;

		$utm = $this->utmLayer->getUtm();
		if ($utm && $utm->getUtmSource() == 'erabat') {
			$honeypot = $form->addHoneypot('name');
			$honeypot->onError[] = function () {
				Debugger::log($this->clientLayer->getIp(), 'honeypot-email-sign-up');
			};
		}

		$form->addCheckbox('iHaveCode')
			->setHtmlId('iHaveCode-' . uniqid());

		$form->addText('code')
			->setHtmlId('code-' . uniqid())
			->addConditionOn($form['iHaveCode'], Form::EQUAL, true)
			->setRequired('front.sign.up.form.validator.codeRequired');

		$rewardCampaigns = $this->rewardFacade->findValidCampaigns($utm, true);

		/** @var RewardCampaign|null $rewardCampaign */
		$rewardCampaign = count($rewardCampaigns) > 0 ? $rewardCampaigns[0] : null;

		if ($rewardCampaign && $rewardCampaign->getCode()) {
			$form['iHaveCode']->setDefaultValue(true);
		}

		if ($this->isRecaptchaNeeded()) {
			$form->addReCaptcha('recaptcha')
				->setRequired('front.form.validator.recaptchaRequired');
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function isRecaptchaNeeded(): bool
	{
		if (in_array($this->configuration->getMode(), ['test', 'dev']) === true) {
			return false;
		}

		$countryCode = $this->geoLocationLayer->getCountry();

		if (
			$countryCode !== null &&
			in_array($countryCode, GeoLocationLayer::getWhiteLabeledCountries()) === false &&
			$this->localizationFacade->getCurrentLocalization()->getLocale() !== $countryCode
		) {
			//Debugger::log($this->clientLayer->getIp() . ' - ' . $this->localizationFacade->getCurrentLocalization()->getLocale() . ' | ' . $countryCode, 'recaptcha-sign-up');
			return true;
		}

		if ($this->suspectedRequestDetector->isRequestSuspected(SuspectedRequestDetector::REQUEST_SIGN_UP, 50)) {
			//Debugger::log('SUSPECTED ' . $this->clientLayer->getIp(), 'recaptcha-sign-up');
			return true;
		}

		$countOfOrganicUsers = $this->cache->load('countOfOrganicUsers');

		if ($countOfOrganicUsers === null) {
			$countOfOrganicUsers = $this->userFacade->findCountOfOrganicUsers((new \DateTime())->modify('- 30 minutes'));

			$this->cache->save(
				'countOfOrganicUsers',
				$countOfOrganicUsers,
				[
					Nette\Caching\Cache::EXPIRE => '150 seconds',
				]
			);
		}

		if ($countOfOrganicUsers > 250) {
			//Debugger::log('ORGANIC ' . $this->clientLayer->getIp(), 'recaptcha-sign-up');
		}

		return $countOfOrganicUsers > 250;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->suspectedRequestDetector->isRequestSuspected(SuspectedRequestDetector::REQUEST_SIGN_UP, 5, false)) {
//				throw new InvalidArgumentException('front.sign.up.badAccount');
			}

			$this->suspectedRequestDetector->trackRequest(SuspectedRequestDetector::REQUEST_SIGN_UP);

			// resolve entrance campaign
			$code = $values->code;
			$campaign = $this->entranceCampaignResolver->resolveEntranceCampaign($code);

			if ($code && !$campaign) {
				throw new InvalidArgumentException($this->translator->translate('front.rewardCodeForm.errors.badCode'));
			}

			if ($form['facebook']->isSubmittedBy()) {
				if ($values->code) {
					$this->httpResponse->setCookie('rewardCode', $values->code, new \DateTime('+ 1 hour'));
				}

				$this->getPresenter()->redirect(':Front:Sign:fbLogin');
			}

			$user = $this->userFacade->createUser($values->email);
			$this->user->login($user);

			$location = $this->registrationSourceLayer->getRegistrationSource();

			$this->eventLayer->trackGoogleAnalyticsRegistration($user, 'Email', $location);

			$this->eventLayer->trackFacebookCompleteRegistration();
			$this->eventLayer->trackGoogleAdwordsRegistration();

			if ($user->getLocalization()->isCzech()) {
				$this->eventLayer->trackSklikRegistration();
			}

			// applying entrance campaign
			if ($campaign) {
				$this->rewardFacade->applyRewardCampaignToUser($campaign, $user);
			}

			$this->onSuccess();
		} catch (EmailAlreadyRegisteredException $e) {
			if ($this->configuration->hasNewDesign($this->localizationFacade->getCurrentLocalization())) {
				$link = Html::el('a')
					->addAttributes(['href' => $this->getPresenter()->link(':NewFront:Sign:in', ['email' => $values->email]), 'class' => 'underline'])
					->setText($this->translator->translate('front.sign.up.signInLink'));
			} else {
				$link = Html::el('a')
					->addAttributes(['href' => $this->getPresenter()->link(':Front:Sign:in', ['email' => $values->email]), 'class' => 'underline'])
					->setText($this->translator->translate('front.sign.up.signInLink'));
			}

			$message = Html::el('span')->setText($e->getMessage() . ' ');
			$form->addError(Html::el()->addHtml($message)->addHtml($link)->toHtml());
			$this->onFailure($this);
		} catch (InvalidArgumentException $e) {
			if ($e->getMessage()) {
				$form->addError($e->getMessage());
			}

			$this->onFailure($this);
		} catch (UniqueConstraintViolationException $e) {
			$this->onSuccess();
		}
	}
}

interface IEmailSignUpControlFactory
{
	/**
	 * @return EmailSignUpControl
	 */
	public function create();
}
