<div class="aq-popup__top aq-popup__bg-white">
	{if $shop && $shop->isCashbackActive()}
		<div class="aq-popup__logo-wrapper aq-popup__logo-wrapper--relative">
			<img src="{$shop->getLogo() |image: 170}" alt="{$shop->getName()}" class="aq-popup__logo">
		</div>

		<h2 class="aq-popup__title aq-popup__title--medium mx-auto" style="line-height: 1.25;">{_'front.aqPopup.exitWithShop.title', [reward => ($shop |reward:pure), shop => $shop->getName()]|noescape}</h2>
		<p class="aq-popup__text" style="max-width: 510px;">{_'front.aqPopup.exitWithShop.text'|noescape}</p>
	{elseif $shop && !$shop->isCashbackActive()}
		<div class="aq-popup__logo-wrapper aq-popup__logo-wrapper--relative">
			<img src="{$shop->getLogo() |image: 170}" alt="{$shop->getName()}" class="aq-popup__logo">
		</div>

		<h2 class="aq-popup__title aq-popup__title--medium mx-auto" style="line-height: 1.25;">{_'front.aqPopup.redirectionWithoutCashback.title', [shop => $shop->getName()]|noescape}</h2>
		<p class="aq-popup__text" style="max-width: 510px;">{_'front.aqPopup.redirectionWithoutCashback.text'|noescape}</p>
	{else}
		<h2 class="aq-popup__title mx-auto" style="max-width: 480px; line-height: 1.25;">{_'front.aqPopup.exitWithoutShop.title', [reward => "15 %"]|noescape}</h2>
		<p class="aq-popup__text" style="max-width: 510px;">{_'front.aqPopup.exitWithoutShop.text'|noescape}</p>
	{/if}

	<p class="aq-popup__c-message mt-5" n:if="$bonusAmount">
		{_'front.aqPopup.intencia', [bonusAmount => $bonusAmount]|noescape}
	</p>

</div>

<div class="aq-popup__bottom aq-popup__bg-grey js-show-section-1">
	{control socialLoginButtons "my-signin4"}

	<span class="aq-popup__or">- {_'front.aqPopup.form.or'} -</span>

	<div class="aq-popup-form" n:snippet="form">
		{form emailSignUpControl-form, class => "ajax"}
			<ul class="form-error" n:if="$form->hasErrors()">
				<li n:foreach="$form->errors as $error">{$error |noescape}</li>
			</ul>

			<label for="" class="aq-popup-form__label">{_'front.aqPopup.form.label'}</label>
			<input type="text" class="aq-popup-form__input" name="email" placeholder="{_'front.aqPopup.form.input'}" n:name="email">
			<div class="form-group" n:ifset="$form[recaptcha]">
				<div n:name="recaptcha"></div>
			</div>
			<input type="submit" class="aq-popup-form__submit" value="{_'front.aqPopup.form.submit'}" n:name="submit" data-hit="event" data-category="signUp" data-action="click" data-label="newExitPopup">
			<span class="aq-popup-form__condition">{_'front.aqPopup.form.acceptAllConditions', [condition => $presenter->link(':Front:Static:conditions'), privacy => $presenter->link(':Front:Static:privacyPolicy')]|noescape}</span>
		{/form}
	</div>

	<span class="aq-popup-form__small">{_'front.sign.up.accountExists'} <a href="{plink :Front:Sign:in, backLink => $presenter->link('//this')}" data-hit="event" data-category="aqPopup" data-action="click" data-label="login">{_'front.sign.up.signInLink'}</a></span>
</div>
