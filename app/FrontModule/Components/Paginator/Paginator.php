<?php

namespace tipli\FrontModule\Components;

use Nette\Application\UI\Control;
use Nette\Utils\Paginator as NettePaginator;

interface IPaginatorFactory
{
	/** @return Paginator */
	public function create();
}

final class Paginator extends Control
{
	public $onChange = [];

	/** @var NettePaginator */
	protected $paginator;

	/** @var bool */
	protected $pagesDisabled = false;

	/** @var bool */
	protected $nextDisabled = false;

	/** @var bool */
	protected $stepPagingEnabled = false;

	public function __construct()
	{
		$this->paginator = new NettePaginator();
		if ($this->getParameter('page') !== null) {
			$this->paginator->setPage($this->getParameter('page'));
		}
	}

	public function setItemsPerPage($itemsPerPage)
	{
		$this->paginator->setItemsPerPage($itemsPerPage);

		return $this;
	}

	public function setItemsCount($itemsCount)
	{
		$this->paginator->setItemCount($itemsCount);

		return $this;
	}

	/**
	 * @crossOrigin
	 */
	public function handlePage($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}

		if ($this->presenter->isAjax()) {
			$this->onChange();
			$this->redrawControl();
		}
	}

	/**
	 * @crossOrigin
	 */
	public function handlePrev($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}
		$this->onChange();
		$this->redrawControl();
	}

	/**
	 * @crossOrigin
	 */
	public function handleNext($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}
		$this->onChange();
		$this->redrawControl();
	}

	public function render()
	{
		if ($this->getParameter('page') !== null) {
			$this->paginator->setPage($this->getParameter('page'));
		}

		$this->template->paginator = $this->paginator;
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->pagesDisabled = $this->pagesDisabled;
		$this->template->nextDisabled = $this->nextDisabled;
		$this->template->stepPagingEnabled = $this->stepPagingEnabled;
		$this->template->render();
	}

	public function getPaginator()
	{
		return $this->paginator;
	}

	public function enableStepPaging()
	{
		$this->stepPagingEnabled = true;

		return $this;
	}

	public function disablePages()
	{
		$this->pagesDisabled = true;

		return $this;
	}

	public function disableNext()
	{
		$this->nextDisabled = true;

		return $this;
	}

	public function enableNext()
	{
		$this->nextDisabled = false;

		return $this;
	}
}
