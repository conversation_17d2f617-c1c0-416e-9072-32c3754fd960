<?php

namespace tipli\FrontModule\Components;

use Nette\Application\UI\Control;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Images\SvgFilter;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\RewardFilter;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;

interface IMainNavbarFactory
{
	/** @return MainNavbar */
	public function create(Localization $localization);
}

/**
 * @property-read \Nette\Bridges\ApplicationLatte\Template|\stdClass $template
 */
class MainNavbar extends Control
{
	/** @var Localization */
	protected $localization;

	/** @var TagFacade */
	protected $tagFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var SvgFilter */
	private $svgFilter;

	/** @var RewardFilter */
	private $rewardFilter;

	/** @var UtmLayer */
	private $utmLayer;

	public function __construct(Localization $localization, TagFacade $tagFacade, ShopFacade $shopFacade, ImageFilter $imageFilter, SvgFilter $svgFilter, RewardFilter $rewardFilter, UtmLayer $utmLayer)
	{
		$this->localization = $localization;
		$this->tagFacade = $tagFacade;
		$this->shopFacade = $shopFacade;
		$this->imageFilter = $imageFilter;
		$this->svgFilter = $svgFilter;
		$this->rewardFilter = $rewardFilter;
		$this->utmLayer = $utmLayer;
	}

	/**
	 * Renders control.
	 */
	public function render()
	{
		$this->template->addFilter('image', $this->imageFilter);
		$this->template->addFilter('reward', $this->rewardFilter);
		$this->template->addFilter('svg', $this->svgFilter);

		$this->template->setFile(__DIR__ . '/mainNavbar.latte');


		$this->template->getTags = (function () {
			return $this->tagFacade->getNavigationTagsTree($this->localization);
		});

		$this->template->utm = $this->utmLayer->getUtm();

		$this->template->getShopsByTag = (function (Tag $tag, $maxResults = 3) {
			return $this->shopFacade->findPublishedShopsByTag($tag, true, $maxResults);
		});

		$this->template->getCountOfShopsByTag = (function (Tag $tag) {
			return $this->shopFacade->findCountOfPublishedShopsByTag($tag, false);
		});

		$this->template->getTopShops = (function () {
			return $this->shopFacade->findTopShops(16, $this->localization, false, null, true);
		});

		$this->template->getCountOfTotalShops = (function () {
			return $this->shopFacade->findTotalCountOfShops($this->localization);
		});

		$this->template->render();
	}
}
