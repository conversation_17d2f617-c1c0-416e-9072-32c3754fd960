{var $cacheKey = $user->isLoggedIn() ? 'shops-navigation-user-' . $user->getIdentity()->getId() : 'shops-navigation-utm-' . $utm->getId()}
{var $cacheTags = $user->isLoggedIn() ? ['user/' . $user->getIdentity()->getId()] : []}
{cache $cacheKey, expire => '60 minutes', tags => $cacheTags}
{var $tags = $getTags()}
<div class="navbar-secondary">
    <div class="navbar-secondary__container">
        <span class="navbar__item navbar__mobile-back-link"><svg>{('angle-double-left-solid'|svg)|noescape}</svg>{_'front.navbar.backLink'}</span>
        <div class="navbar-secondary__wrapper">

            <span class="navbar-secondary__item">
                <a href="{plink :Front:Shops:Shop:shops}" class="navbar-secondary__link" data-category-content="0">
                    <span class="navbar-secondary__item-inner" data-hit="event" data-category="navbarCategory" data-action="click" data-label="{_'front.navbar.category.sidebar.allShops'}">{_'front.navbar.category.sidebar.allShops'}</span>
                </a>
                <span class="navbar-secondary__plus">+</span>
            </span>

            {foreach $tags as $tag}
                {var $tag = $tag['entity']}
                <span class="navbar-secondary__item">
                    <a href="{plink :Front:Shops:Shop:shops, $tag}" class="navbar-secondary__link" data-category-content="{$tag->getId()}">
                        <span class="navbar-secondary__item-inner" data-hit="event" data-category="navbarCategory" data-action="click" data-label="{$tag->getName()}">{$tag->getName()}</span>
                    </a>
                    <span class="navbar-secondary__plus">+</span>
                </span>
            {/foreach}
        </div>
    </div>
</div>

<div class="navbar-dropdown">
    <span class="navbar__item navbar__mobile-back-link navbar__mobile-back-link--category"><svg>{('angle-double-left-solid'|svg)|noescape}</svg>{_'front.navbar.backLinkToCategory'}</span>

    <div class="navbar-dropdown__container">

        <div data-category-id="0" class="navbar-dropdown__inner">
            <div class="navbar-dropdown__category navbar-dropdown__category--full">
                <h4 class="navbar-dropdown__title">{_'front.navbar.category.sidebar.allShops'}</h4>
                <div class="navbar-dropdown__wrapper">
                    {var $topShops = $getTopShops()}
                    {foreach $topShops as $shop}
                        {include "../../Presenters/templates/snippets/shopCard.latte", shop => $shop, unveil => "nav"}
                    {/foreach}
                </div>
                {var $countOfTotalShops = $getCountOfTotalShops()}
                <a href="{plink :Front:Shops:Shop:shops}" class="navbar-dropdown__link">{_'front.navbar.category.moreShops', [count => $countOfTotalShops]} »</a>
            </div>
        </div>

        {foreach $tags as $tag}
            {var $parentTag = $tag['entity']}
            <div data-category-id="{$parentTag->getId()}" class="navbar-dropdown__inner">
				{var $shops = $getShopsByTag($parentTag, 16)}
				{include dropdownCategory, tag => $parentTag, shops => $shops, noChildren => true}

				{*
				{var $shops = $getShopsByTag($parentTag, 16)}
				{include "../../Presenters/templates/snippets/shopCard.latte", shop => $shops, unveil => "nav"}

                {foreach $tag['children'] as $childrenTag}
                    {var $childrenTag = $childrenTag['entity']}
                    {var $shops = $getShopsByTag($childrenTag)}

                    {include dropdownCategory, tag => $childrenTag, shops => $shops, noChildren => false}

                    {breakIf $iterator->counter >= 4}
                {/foreach}

                {if empty($tag['children'])}
                    {var $shops = $getShopsByTag($parentTag, 16)}
                    {include dropdownCategory, tag => $parentTag, shops => $shops, noChildren => true}
                {/if}
				*}
            </div>
        {/foreach}
    </div>
</div>

{define dropdownCategory}
    <div class="navbar-dropdown__category {if $noChildren}navbar-dropdown__category--full{/if}" {if ($bgImage = $tag->getBackgroundImage())}style="background-image: url('{$bgImage|image|noescape}');"{/if}>
        <h4 class="navbar-dropdown__title">{$tag->getName()}</h4>
        <div class="navbar-dropdown__wrapper">
            {foreach $shops as $shop}
                {include "../../Presenters/templates/snippets/shopCard.latte", shop => $shop, unveil => "nav"}
            {/foreach}
        </div>

        {var $countOfTotalShops = ($getCountOfShopsByTag($tag) - count($shops))}
        <a n:if="$countOfTotalShops > 0" href="{plink :Front:Shops:Shop:shops, $tag}" class="navbar-dropdown__link">{_'front.navbar.category.moreShops', [count => $countOfTotalShops]} »</a>
    </div>
{/define}

{/cache}
