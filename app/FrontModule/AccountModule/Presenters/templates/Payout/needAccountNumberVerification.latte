{block title}{_'front.account.payout.default.title'}{/block}

{block innerContent}

<div class="payout">
	<div class="payout__header">
		<div class="payout__header-content">
			<h1 class="payout__header-title">{_'front.account.payout.request.forPayout', [count => ($confirmedBalance |amount)]|noescape}</h1>
			<h2 class="payout__header-subtitle" n:if="$registeredBalance > 0">{_'front.account.payout.request.waiting', [count => ($registeredBalance |amount)]|noescape}</h2>
		</div>
	</div>

	<div class="payout__content payout__content--padding payout__content--tablet-column">
		<div class="payout__content-column">
			<h3 class="payout__content-red-title">
				<i class="fa fa-exclamation-triangle"></i>
				{_'front.account.payout.request.noFullProfile.accountNumberVerificationTitle'}
			</h3>

			<p class="payout__content-text">
				{_'front.account.payout.request.noFullProfile.accountNumberVerificationText'}
			</p>
		</div>

		<div class="payout__content-column payout__content-column--right">
			<a n:href="accountNumberVerification!" class="payout__content-button payout__content-button--phone">
				{_'front.account.payout.request.noFullProfile.accountNumberVerificationButton'}
			</a>
		</div>
	</div>
</div>


{include payoutsGrid}
