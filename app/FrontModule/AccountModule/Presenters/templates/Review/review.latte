{block title}{_'front.account.transaction.default.title'}{/block}

{block innerContent}

<div class="review-form">
    <h2 n:if="$shop">{_'front.account.reviews.review.titleShop', [shop => $shop->getName()]}</h2>
    <h2 n:if="!$shop">{_'front.account.reviews.review.title'}</h2>

    <form class="form-horizontal form" n:name="addReviewControl-form">
        <div class="alert alert-danger" n:foreach="$form->getErrors() as $error">
            {$error}
        </div>
        <input n:name="shopId" value="{$shop ? $shop->getId()}">

        {if $shop}
            <img src="{$shop->getLogo() |image:121,0}" class="mx-auto d-block mb-5">
        {else}
            <img src="{$basePath}/images/logo-1.png" alt="Tipli" class="mx-auto d-block mb-5" style="width: 70px;">
        {/if}


        <div class="radio-w">
            {foreach $form[rate]->items as $key => $label}
                <div class="radio-b">
                    <input n:name="rate:$key">
                    <label n:name="rate:$key" class="radiolabel">{$label}</label>
                </div>
            {/foreach}
        </div>

        <span class="label">{_'front.account.reviews.review.opinion'}</span>
        <div class="score-star">
            <i class="fa fa-star active" aria-hidden="true"></i>
            <i class="fa fa-star active" aria-hidden="true"></i>
            <i class="fa fa-star active" aria-hidden="true"></i>
            <i class="fa fa-star active" aria-hidden="true"></i>
            <i class="fa fa-star active" aria-hidden="true"></i>
        </div>

        <div class="wrapper">
            <div class="line">
                <input n:name="userName" class="form-control" placeholder="{_'front.account.reviews.review.userName'} ">
                <span class="label">{_'front.account.reviews.review.userNameWillBePublished'}</span>
            </div>
            <textarea n:name="text" class="form-control" placeholder="{_'front.account.reviews.review.text'}"></textarea>

{*            <div class="checkbox checkbox--left">*}
{*              <label n:name="allowUserPicture">*}
{*                <input type="checkbox" n:name="allowUserPicture">*}
{*                {_'front.account.reviews.review.allowUserPicture'}*}
{*              </label>*}
{*            </div>*}

            <input type="submit" n:name="submit" class="btn btn-green" value="{_'front.account.reviews.review.submit'}">
        </div>

    </form>

    <div class="tips">
        {if $shop}
        <h3>{_'front.account.reviews.review.tips.shop.title'}</h3>
        <ul>
            <li>{_'front.account.reviews.review.tips.shop.text1'}</li>
            <li>{_'front.account.reviews.review.tips.shop.text2'}</li>
            <li>{_'front.account.reviews.review.tips.shop.text3'}</li>
        </ul>
        {else}
        <h3>{_'front.account.reviews.review.tips.tipli.title'}</h3>
        <ul>
            <li>{_'front.account.reviews.review.tips.tipli.text1'}</li>
            <li>{_'front.account.reviews.review.tips.tipli.text2'}</li>
            <li>{_'front.account.reviews.review.tips.tipli.text3'}</li>
            <li>{_'front.account.reviews.review.tips.tipli.text4'}</li>
        </ul>
        {/if}

        <h3>{_'front.account.reviews.review.globalTips.title'}</h3>
        <ul>
            <li>{_'front.account.reviews.review.globalTips.text1'}</li>
            <li>{_'front.account.reviews.review.globalTips.text2'}</li>
            <li>{_'front.account.reviews.review.globalTips.text3'}</li>
            <li>{_'front.account.reviews.review.globalTips.text4'}</li>
            <li>{_'front.account.reviews.review.globalTips.text5'}</li>
        </ul>
    </div>
</div>
