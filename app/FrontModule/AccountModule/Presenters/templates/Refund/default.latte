{block title}{_'front.account.user.guarantee.title'}{/block}
{block keywords}{_'front.account.user.guarantee.keywords'}{/block}
{block description}{_'front.account.user.guarantee.description'}{/block}

{block #scripts}
	<link rel="stylesheet" href="{$basePath}/libs/jquery-ui/jquery-ui.css">
    <script src="{$basePath}/libs/jquery-ui/jquery-ui.min.js" defer></script>
	<script src="{$basePath}/js/account/account.refund.js?v=0.8" defer></script>

	<script>
        window.addEventListener('DOMContentLoaded', function() {
            (function($) {
                $(document).ready(function() {
					$( ".datepicker" ).datepicker(
						$.extend({
                        	changeMonth: true,
                        	changeYear: true,
                        	dateFormat: 'dd.mm.yy',
							maxDate: new Date(),
                        	yearRange: '1920:{date('Y')}'
                    	},
                        $.datepicker.regional['cs']
                    ));

                    $.datepicker.regional.lang = {
                        closeText: "{_front.form.datepicker.close|noescape}",
                        prevText: "{_front.form.datepicker.prev|noescape}",
                        nextText: "{_front.form.datepicker.next|noescape}",
                        currentText: "{_front.form.datepicker.now|noescape}",
                        monthNames: [ {_front.form.datepicker.month|noescape} ],
                        monthNamesShort: [ {_front.form.datepicker.monthshort|noescape} ],
                        dayNames: [ {_front.form.datepicker.day|noescape} ],
                        dayNamesShort: [ {_front.form.datepicker.dayshort|noescape} ],
                        dayNamesMin: [ {_front.form.datepicker.daymin|noescape} ],
                        weekHeader: "{_front.form.datepicker.week|noescape}",
                        dateFormat: "dd.mm.yy",
                        firstDay: 1,
                        isRTL: false,
                        showMonthAfterYear: false,
                        yearSuffix: "" };
                    $.datepicker.setDefaults( $.datepicker.regional.lang );
                });
            })(jQuery);
        });
    </script>
{/block}

{block innerContent}
<section class="static-page-content static-page-content--first">
	{if $userRefundsCount > 0}
		<div class="alert alert-warning">{_'front.refund.existingRefund.inProgress', [count => $userRefundsCount, refundLink => $presenter->link(':Front:Account:Refunds:')]|noescape}</div>
	{elseif $userApprovedRefundsCount > 0}
		<div class="alert alert-warning">{_'front.refund.existingRefund.allClose', [count => $userApprovedRefundsCount, refundLink => $presenter->link(':Front:Account:Refunds:')]|noescape}</div>
	{/if}

    <p class="static-page-content__text">
		{_'front.refund.text'}
	</p>

	<div class="refund--step1">
		<h2>{_'front.refund.step1.title'}</h2>

		<div class="refund-wrapper">
			<button class="refund-wrapper__item refund--js-step2 refund-wrapper__item--wide">
				<svg class="">{('coins-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step1.choice1.title'}</strong>
					<small class="d-block mw-540">{_'front.refund.step1.choice1.text'}</small>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form" data-js-class="refund-payout">
				<svg class="">{('money-bill-alt-regular'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step1.choice2.title'}</strong>
					<small>{_'front.refund.step1.choice2.text'}</small>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form" data-js-class="refund-other">
				<svg class="">{('question-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step1.choice3.title'}</strong>
					<small>{_'front.refund.step1.choice3.text'}</small>
				</span>
			</button>
		</div>
	</div>

	<div class="refund--step2 hide">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.step2.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="1">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		<p class="refund-form__title-text">{_'front.refund.step2.text'}</p>

		<div class="refund-wrapper">
			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" data-js-class="refund-missing-commission">
				<svg class="">{('coins-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice1.title'}</strong>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" data-js-class="refund-missing-bonus">
				<svg class="">{('coins-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice2.title'}</strong>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" id="refund-incorrect-amount" data-js-class="refund-incorrect-amount">
				<svg class="">{('times-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice3.title'}</strong>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" data-js-class="refund-unconfirmed-transaction">
				<svg class="">{('times-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice4.title'}</strong>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" data-js-class="refund-canceled-commission">
				<svg class="">{('times-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice5.title'}</strong>
				</span>
			</button>

			<button class="refund-wrapper__item refund--js-form refund-wrapper__item--smaller" data-js-class="refund-other">
				<svg class="">{('question-solid'|svg)|noescape}</svg>
				<span>
					<strong>{_'front.refund.step2.choice6.title'}</strong>
					<small class="d-block mw-540">{_'front.refund.step2.choice6.text'}</small>
				</span>
			</button>
		</div>
	</div>

	<div class="refund-form hide refund-missing-commission">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.missingCommission.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="2">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		{control refundMissingCommissionControl}
	</div>

	<div class="refund-form hide refund-missing-bonus">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.missingBonus.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="2">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		{control refundMissingBonusControl}
	</div>

	<div class="refund-form hide refund-incorrect-amount">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.incorrectAmount.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="2">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		{control refundIncorrectAmountControl}
	</div>

	<div class="refund-form hide refund-unconfirmed-transaction">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.unconfirmedTransaction.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="2">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		{control refundUnconfirmedTransactionControl}
	</div>

	<div class="refund-form hide refund-canceled-commission">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.canceledCommission.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="2">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		{control refundCanceledCommissionControl}
	</div>

	<div class="refund-form hide refund-payout">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.payout.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="1">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		<p class="refund-form__title-text">{_'front.refund.payout.text'}</p>

		{control refundPayoutControl}
	</div>

	<div class="refund-form hide refund-other">
		<h2 class="refund-form__title-wrapper">
			{_'front.refund.other.title'}
			<a href="#" class="refund-form__link js-refund-back" data-js-step="1">
				<svg class="">{('redo-alt-solid'|svg)|noescape}</svg>
				{_'front.refund.back'}
			</a>
		</h2>

		<p class="refund-form__title-text">{_'front.refund.other.text'}</p>

		{control refundOtherControl}
	</div>
</section>
