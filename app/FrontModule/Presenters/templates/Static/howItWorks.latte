
{block title}{_'front.static.howItWorks.title'}{/block}
{block keywords}{_'front.static.howItWorks.metaKeywords'}{/block}
{block description}{_'front.static.howItWorks.metaDescription'}{/block}

{block #styles}
  {control cssBuilderControl ['css/staticPage/howitworks/main.howitworks.css']}
{/block}

{block #scripts}
    <script type="text/javascript" src="{$basePath}/js/faq.js" defer></script>
{/block}

{block content}

<div class="howitworks-header-n">
	<div class="container">
		<div class="howitworks-header-n__wrapper {if $localization->isBulgarian() || $localization->isCroatian() || $localization->isSlovenian()}howitworks-header-n__wrapper--noSidebar{/if}">
			<div class="howitworks-header-n__content">
				<h1 class="fz-xxl fw-bold mt-0 mb-4">{_'front.static.howItWorks.mainTitle'}</h1>
				<p class="fz-l fw-regular lh-m mb-0">
					{_'front.static.howItWorks.text1', [link => $presenter->link(':Front:Homepage:default')] |noescape}
				</p>
				</p>
			</div>

			<div class="howitworks-header-n__sidebar">
				<div class="howitworks-header-n__iframe">
					<iframe n:if="$localization->isCzech()" width="500" height="312" src="https://www.youtube.com/embed/s1_JU-_bO08" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
					<iframe n:if="$localization->isSlovak()" width="500" height="312" src="https://www.youtube.com/embed/k_gK9_13mBo" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
					<iframe n:if="$localization->isPolish()" width="500" height="312" src="https://www.youtube.com/embed/gRKTZKudWMw" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
					<iframe n:if="$localization->isRomanian()" width="500" height="312" src="https://www.youtube.com/embed/d9vSnYZOzAE" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
					<iframe n:if="$localization->isHungarian()" width="500" height="312" src="https://www.youtube.com/embed/Q6Unq_o2mI8" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="container mb-6">
	<div class="static-page-content__wrapper static-page-content__wrapper--align-items-start howitworks-header__steps">
		<div class="static-page-content__column">
			<div class="howitworks-header__image-wrapper">
				{if $localization->isSlovak()}
					<img src="{$basePath}/images/howItWorks/step1-sk.png" alt="shop" class="howitworks-header__image">
				{elseif $localization->isPolish()}
					<img src="{$basePath}/images/howItWorks/step1-pl.png" alt="shop" class="howitworks-header__image">
				{elseif $localization->isRomanian()}
					<img src="{$basePath}/images/howItWorks/step1-ro.png" alt="shop" class="howitworks-header__image">
				{elseif $localization->isHungarian()}
					<img src="{$basePath}/images/howItWorks/step1-hu.png" alt="shop" class="howitworks-header__image">
				{else}
					<img src="{$basePath}/images/howItWorks/step1-cz.png" alt="shop" class="howitworks-header__image">
				{/if}
			</div>
			<div class="howitworks-header__content">
				<h2 class="howitworks-header__title">{_'front.static.howItWorks.steps.choose.title' |noescape}</h2>
				<p class="howitworks-header__text">{_'front.static.howItWorks.steps.choose.text', [link => $presenter->link(':Front:Shops:Shop:shops'), activeShops => $activeShops] |noescape}</p>
			</div>
		</div>

		<div class="static-page-content__column">
			<div class="howitworks-header__image-wrapper">
				{if $localization->isSlovak()}
					<img src="{$basePath}/images/howItWorks/step2-sk.png" alt="buy" class="howitworks-header__image">
				{elseif $localization->isPolish()}
					<img src="{$basePath}/images/howItWorks/step2-pl.png" alt="buy" class="howitworks-header__image">
				{elseif $localization->isRomanian()}
					<img src="{$basePath}/images/howItWorks/step2-ro.png" alt="buy" class="howitworks-header__image">
				{elseif $localization->isHungarian()}
					<img src="{$basePath}/images/howItWorks/step2-hu.png" alt="buy" class="howitworks-header__image">
				{else}
					<img src="{$basePath}/images/howItWorks/step2-cz.png" alt="buy" class="howitworks-header__image">
				{/if}
			</div>
			<div class="howitworks-header__content">
				<h2 class="howitworks-header__title">{_'front.static.howItWorks.steps.transaction.title' |noescape}</h2>
				<p class="howitworks-header__text">{_'front.static.howItWorks.steps.transaction.text' |noescape}</p>
			</div>
		</div>

		<div class="static-page-content__column">
			<div class="howitworks-header__image-wrapper"><img src="{$basePath}/images/howItWorks/step3.jpg" alt="cashback" class="howitworks-header__image"></div>
			<div class="howitworks-header__content">
				<h2 class="howitworks-header__title">{_'front.static.howItWorks.steps.cashback.title' |noescape}</h2>
				<p class="howitworks-header__text">{_'front.static.howItWorks.steps.cashback.text' |noescape}</p>
			</div>
		</div>
	</div>
</div>

<section class="bg-grey py-5">
    <div class="container">
        <h2 class="static-page-content__title ta-center">{_'front.static.howItWorks.practise.title' |noescape}</h2>
        <p class="static-page-content__text mw-700 mx-auto mb-0 ta-center">{_'front.static.howItWorks.practise.text' |noescape}</p>

        <div class="howitworks-content howitworks-content howitworks-content-n">
            <div class="howitworks-content__item">
                <div class="howitworks-content__image-wrapper">
                    {if $localization->isSlovak()}
                        <img src="{$basePath}/images/howItWorks/choice1-sk.png" alt="shop" class="howitworks-content__image">
                    {elseif $localization->isPolish()}
                        <img src="{$basePath}/images/howItWorks/choice1-pl.png" alt="shop" class="howitworks-content__image">
					{elseif $localization->isRomanian()}
                        <img src="{$basePath}/images/howItWorks/choice1-ro.png" alt="shop" class="howitworks-content__image">
					{elseif $localization->isHungarian()}
                        <img src="{$basePath}/images/howItWorks/choice1-hu.png" alt="shop" class="howitworks-content__image">
                    {else}
                        <img src="{$basePath}/images/howItWorks/choice1-cz.png" alt="shop" class="howitworks-content__image">
                    {/if}

                    <span class="howitworks-content__image-value"><small>{_'front.static.howItWorks.practise.steps.choose.subtitle' |noescape}</small> {_'front.static.howItWorks.practise.steps.choose.value' |noescape}</span>
                </div>
                <div class="howitworks-content__content">
                    <h2 class="howitworks-content__title">{_'front.static.howItWorks.practise.steps.choose.title' |noescape}</h2>
                    <p class="howitworks-content__text">{_'front.static.howItWorks.practise.steps.choose.text' |noescape} </p>
                </div>
            </div>

            <div class="howitworks-content__item">
                <div class="howitworks-content__image-wrapper">
                    {if $localization->isSlovak()}
                        <img src="{$basePath}/images/howItWorks/choice2-sk.png" alt="buy" class="howitworks-content__image">
                    {elseif $localization->isPolish()}
                        <img src="{$basePath}/images/howItWorks/choice2-pl.png" alt="buy" class="howitworks-content__image">
					{elseif $localization->isRomanian()}
                        <img src="{$basePath}/images/howItWorks/choice2-ro.png" alt="buy" class="howitworks-content__image">
					{elseif $localization->isHungarian()}
                        <img src="{$basePath}/images/howItWorks/choice2-hu.png" alt="buy" class="howitworks-content__image">
                    {else}
                        <img src="{$basePath}/images/howItWorks/choice2-cz.png" alt="buy" class="howitworks-content__image">
                    {/if}

                    <span class="howitworks-content__image-value"><small>{_'front.static.howItWorks.practise.steps.transaction.subtitle' |noescape}</small> {_'front.static.howItWorks.practise.steps.transaction.value' |noescape}</span>
                </div>
                <div class="howitworks-content__content">
                    <h2 class="howitworks-content__title">{_'front.static.howItWorks.practise.steps.transaction.title' |noescape}</h2>
                    <p class="howitworks-content__text">{_'front.static.howItWorks.practise.steps.transaction.text' |noescape}</p>
                </div>
            </div>

            <div class="howitworks-content__item">
                <div class="howitworks-content__image-wrapper">
                    <img src="{$basePath}/images/howItWorks/choice3.png" alt="cashback" class="howitworks-content__image">
                    <span class="howitworks-content__image-value"><small n:class="$localization->isPolish() ? howitworks-content__image-value--small">{_'front.static.howItWorks.practise.steps.cashback.subtitle' |noescape}</small> {_'front.static.howItWorks.practise.steps.cashback.value' |noescape}</span>
                </div>
                <div class="howitworks-content__content">
                    <h2 class="howitworks-content__title">{_'front.static.howItWorks.practise.steps.cashback.title' |noescape}</h2>
                    <p class="howitworks-content__text">{_'front.static.howItWorks.practise.steps.cashback.text' |noescape}</p>
                    <small class="howitworks-content__small">{_'front.static.howItWorks.practise.steps.cashback.small' |noescape}</small>
                </div>
            </div>
        </div>
    </div>
</section>

{var $reviews = $reviews()}
<section class="static-page-content static-page-content--center static-page-content--pt-pb" n:if="!$reviews->isEmpty()">
    <div class="container">
        <h2 class="static-page-content__title">{_'front.homepage.review.title'}</h2>
        {cache 'hiw-reviews' . $localization->getLocale(), expire => '15 minutes'}
            <div class="review-bubble review-bubble--grey review-bubble--mt">
                <div class="review-bubble__msg" n:foreach="$reviews as $review" title="{$review->getText()}">
                    {$review->getText()}
                    <img src="{$basePath}/images/shop-card-bg.png" data-src="{$review->getUserPicture() |image:116}" data-src-retina="{$review->getUserPicture() |image:232}" alt="{$review->getShortUsername()}" class="unveil review-bubble__msg-image">
                    <span class="review-bubble__msg-name">{$review->getShortUsername()}</span>
                </div>
            </div>
        {/cache}
    </div>
</section>

<section class="static-page-content static-page-content--center static-page-content--bg-grey static-page-content--pt-pb">
    <div class="container static-page-content__container--medium">
        <h2 class="static-page-content__title">{_'front.static.whereNow.title'}</h2>
        <p class="static-page-content__text">{_'front.static.whereNow.text'}</p>
        <div class="static-page-content__button-wrapper">
            <a n:href=":Front:Static:faq" class="static-page-content__button">{_'front.static.faq.title'}</a>
            <a n:href=":Front:Static:contact" class="static-page-content__button">{_'front.static.contact.title'}</a>
        </div>
    </div>
</section>

<section class="static-page-content static-page-content--center static-page-content--bg-green static-page-content--pt-pb static-page-content--flex-center howitworks--bg">
    <div class="container static-page-content__container--medium">
        <h2 class="static-page-content__title static-page-content__title--white">{_'front.static.howItWorks.start.title'}</h2>
        <div class="static-page-content__button-wrapper">
            <a n:href=":Front:Sign:up" class="static-page-content__button static-page-content__button--transparent-white static-page-content__button--medium" data-hit="event" data-category="button" data-action="click" data-label="howItWorksSignUpFooter">{_'front.static.howItWorks.start.sign' |noescape}</a>
        </div>
    </div>
</section>




