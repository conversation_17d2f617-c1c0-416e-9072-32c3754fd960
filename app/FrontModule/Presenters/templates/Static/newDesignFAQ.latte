<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
  <meta name="skype_toolbar" content="skype_toolbar_parser_compatible">

  <!-- Metadata info -->
  <title></title>
  <meta name="description" content="">
  <meta name="keywords" content="">
  <meta name="robots" content="">
  <meta name="googlebot" content="">
  <meta name="author" content="">

  <!-- Favicon images -->

  <!-- Viewport for mobile devices -->
  <meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">

  <!-- Stylesheet -->
  <link rel="stylesheet" href="{$basePath}/css2/output.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght,HEXP@160..700,0..100&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
</head>
<body>
  <header class="w-full">
    <div class="border-b w-full border-b-light-4">
      <div class="relative flex justify-between container h-[64px] items-center">
        <div class="flex items-center">
          <a href="/new/vitejte">
            <svg width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
            </svg>
          </a>

          <form action="" class="flex items-center w-[365px] h-[47px] bg-white rounded-[14px] border border-zinc-200 pl-4 pr-[3px] ml-9">
            <input type="text" placeholder="Hľadať zľavy, vouchery a obchody..." class="text-sm w-3/4 outline-none">
            <button class="w-[41px] h-[41px] ml-auto" style="background-image: url('{$basePath}/new-design/search.svg')"></button>
          </form>
        </div>

        <div class="flex gap-4">
          <div class="w-px h-[63px] bg-zinc-200"></div>
          <div class="flex items-center">
            <div class="flex items-center justify-center min-w-[69px] h-[27px] rounded-md border border-slate-700 text-slate-700 text-sm font-medium">
              2 345 €</div>
            <div class="relative flex justify-center items-center w-[41px] h-[41px] bg-zinc-200 rounded-xl mr-4">
              <div class=" text-zinc-950 text-sm font-medium">OG</div>
              <div class="absolute flex items-center justify-center right-[-3px] top-[-3px] text-white text-[9px] font-bold leading-[17.50px] w-3.5 h-3.5 bg-red-500 rounded-[300px]">
                3
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="11" viewBox="0 0 16 11" fill="none" class="absolute right-[-3px] bottom-[-3px]">
                <path d="M7.58872 1.59488C7.78752 1.30734 8.21248 1.30734 8.41128 1.59489L10.8115 5.06668C10.9744 5.30225 11.3015 5.35316 11.5282 5.17823L13.9308 3.32484C14.2982 3.04139 14.82 3.36788 14.7258 3.82227L13.5271 9.60154C13.479 9.83361 13.2745 10 13.0375 10H2.96249C2.72549 10 2.52104 9.83361 2.47291 9.60154L1.27425 3.82227C1.18 3.36788 1.7018 3.04139 2.06923 3.32484L4.47178 5.17823C4.69854 5.35316 5.0256 5.30225 5.18847 5.06668L7.58872 1.59488Z" fill="white" stroke="#BBA24D"></path>
              </svg>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none">
              <path d="M1 6.99991L7.00018 1L13 7" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </div>
          <div class="w-px h-[63px] bg-zinc-200"></div>
        </div>
      </div>
    </div>

    <div class="border-b border-b-light-4">
      <div class="flex py-3 container">
        <a class="flex items-center cursor-pointer gap-2 js-show-shops" href="/new/obchody">
          <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>

          <div class="hidden font-medium text-sm leading-[24.5px] md:block">
            Obchod s odmenami
          </div>

          <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.00015 6L5.46983 6.53034C5.76273 6.82323 6.2376 6.82322 6.53048 6.53032L6.00015 6ZM1.53032 0.469734C1.23742 0.176847 0.762545 0.176858 0.469658 0.469758C0.176771 0.762657 0.176782 1.23753 0.469682 1.53042L1.53032 0.469734ZM11.5303 1.53032C11.8232 1.23742 11.8232 0.762551 11.5303 0.469662C11.2374 0.176773 10.7626 0.17678 10.4697 0.469678L11.5303 1.53032ZM6.53046 5.46966L1.53032 0.469734L0.469682 1.53042L5.46983 6.53034L6.53046 5.46966ZM6.53048 6.53032L11.5303 1.53032L10.4697 0.469678L5.46981 5.46968L6.53048 6.53032Z" fill="#080B10"></path>
          </svg>
        </a>

        <a class="flex items-center cursor-pointer gap-2 ml-9" href="/new/slevy">
          <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
            <path d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>

          <div class="hidden font-medium text-sm leading-[24.5px] md:block">
            Zľavové kupóny
          </div>
        </a>
      </div>
    </div>
  </header>

  <div class="container px-5 md:p-0">
    <div class="text-dark-1 text-[33px] font-bold leading-[49.5px] mt-[63px]">Často kladené otázky - FAQ</div>

    <div class="flex flex-wrap mx-[-12px] mb-[61.5px]">
      <button onclick="scrollToSection('faq')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Najčastejšie otázky našich používateľov
        <small class="text-orange-500 font-normal text-base">(5)</small>
      </button>
      <button onclick="scrollToSection('rewards')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Odmeny
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
      <button onclick="scrollToSection('8-tips')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        8 tipov pre úspešné zaregistrovanie odmeny
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
      <button onclick="scrollToSection('pay-rewards')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Výplaty odmien
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
      <button onclick="scrollToSection('tipli-app')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Tipli aplikácia
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
      <button onclick="scrollToSection('tipli-and-safety')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Tipli a bezpečnosť
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
      <button onclick="scrollToSection('tipli-to-browser')" class="bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] hover:bg-zinc-950 hover:text-white">
        Tipli do prehliadača
        <small class="text-orange-500 font-normal text-base">(0)</small>
      </button>
    </div>
  </div>

  <div class="bg-img">
    <div class="container">
      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px]">Najčastejšie otázky našich používateľov</div>

      <div id="faq" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section1')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section1" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section1" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section1" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section1')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section1" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section1" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section1" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">Odmeny</div>

      <div id="rewards" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section2')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section2" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section2" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section2" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section2')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section2" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section2" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section2" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">8 tipov pre úspešné zaregistrovanie odmeny</div>

      <div id="8-tips" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section3')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section3" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section3" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section3" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section3')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section3" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section3" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section3" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">Výplaty odmien</div>

      <div id="pay-rewards" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section4')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section4" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section4" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section4" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section4')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section4" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section4" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section4" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">Tipli aplikácia</div>

      <div id="tipli-app" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section5')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section5" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section5" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section5" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section5')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section5" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section5" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section5" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">Tipli a bezpečnosť</div>

      <div id="tipli-and-safety" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section6')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section6" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section6" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section6" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section6')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section6" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section6" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section6" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>

      <div class="text-dark-1 text-center text-[26px] leading-[39px] font-medium mt-[77px] mt-[100px]">Tipli do prehliadača</div>

      <div id="tipli-to-browser" class="bg-white rounded-xl mt-10 pt-[21px] pb-[14px] px-10 w-[895px] max-w-full m-auto">
        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq1', 'section7')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq1-section7" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq1-section7" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq1-section7" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>

        <div class="w-full h-px bg-light-5 border-b"></div>

        <div>
          <div class="flex items-center justify-between py-5 cursor-pointer" onclick="toggleFAQ('faq2', 'section7')">
            Ako môžem odporučiť kamaráta?
            <div class="flex">
              <svg id="faq-icon-open-faq2-section7" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="transition-transform duration-300">
                <path d="M7.5001 1L7.5001 14M14 7.5H1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg id="faq-icon-close-faq2-section7" xmlns="http://www.w3.org/2000/svg" width="15" height="2" viewBox="0 0 15 2" fill="none" class="hidden">
                <path d="M14 1L1 1" stroke="#80899C" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div id="faq-content-faq2-section7" class="text-sm text-dark-1 leading-[24.5px] md:pr-[258px] hidden">
            Za odporučenie získáte odmenu 5 €. Na váš účet pripíšeme bonusovú odmenu 5 € hneď, ako váš kamarát vykoná cez Tipli prvý nákup.
          </div>
        </div>
      </div>
    </div>
  </div>

  <div n:syntax="off">
    <script>
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function toggleFAQ(id, section) {
            const content = document.getElementById(`faq-content-${id}-${section}`);
            const iconOpen = document.getElementById(`faq-icon-open-${id}-${section}`);
            const iconClose = document.getElementById(`faq-icon-close-${id}-${section}`);

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                content.classList.add('block', 'mb-5');
                iconOpen.classList.add('hidden');
                iconClose.classList.remove('hidden');
            } else {
                content.classList.remove('block', 'mb-5');
                content.classList.add('hidden');
                iconOpen.classList.remove('hidden');
                iconClose.classList.add('hidden');
            }
        }
    </script>
  </div>
</body>
</html>

<style>
    .bg-img {
        left: 0;
        z-index: 10;
        position: absolute;
        width: 100%;
        height: auto;
        min-height: 482px;
        background-image: url(/new-design/faq-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
    }
</style>