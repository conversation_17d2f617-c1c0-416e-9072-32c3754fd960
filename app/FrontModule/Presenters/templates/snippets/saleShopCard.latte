<a href="{plink :Front:Shops:Sale:sales $shop}" title="{$shop->getName()}" class="shop-card">
  <span class="shop-card__image">
    <span class="shop-card__table-cell">
      <img src="{$basePath}/images/pixel.png" data-src="{$shop->getLogo() |image:100,0}" alt="{$shop->getName()}" n:if="$shop->getLogo()" class="unveil">
    </span>
  </span>

  <span class="shop-card__content">
    <p class="shop-card__title">{$shop->getName()}</p>

    <span class="cashback-value cashback-value--sale">
      <span class="_value">{count($shop->getValidSales())}</span>
      {_'front.shopCard.sale', count($shop->getValidSales())}
    </span>

    {*<span class="cashback-value">*}
      {*{if $sale->isGift()}dárek*}
      {*{elseif $sale->isFreeShipping()}doprava zdarma*}
      {*{else}{$sale->getBenefitLabel()}{/if}*}
    {*</span>*}
  </span>
</a>
