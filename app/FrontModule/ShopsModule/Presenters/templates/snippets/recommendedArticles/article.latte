{define article, $article}
<div class="article">
    <a href="{plink :Front:Articles:Article:article $article}" class="image">
        <img src="{$article->getPreviewImage() |image:262,150}" alt="{$article->getName()}" n:if="$article->getPreviewImage()">
        <img n:if="!$article->getPreviewImage()" src="http:/placehold.it/262x150" alt="{$article->getName()}">
    </a>

    <div class="content">
        <h2 class="title"><a href="{plink :Front:Articles:Article:article $article}" class="">{$article->getName()}</a></h2>
        <span class="row">
            <span class="date"><i class="fa fa-calendar-o" aria-hidden="true"></i> {$article->getPublishedAt() |date:'j.n.Y'}</span>
            <a href="{plink :Front:Articles:Article:article $article}" class="btn">{_'front.articles.article.articlesList.entity.btnLabel'}</a>
        </span>
    </div>
</div>
{/define}
