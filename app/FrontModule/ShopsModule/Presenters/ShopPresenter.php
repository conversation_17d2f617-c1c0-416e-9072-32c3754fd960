<?php

namespace tipli\FrontModule\ShopsModule\Presenters;

use tipli\Model\Doctrine\QueryObject\ResultSet;
use Nette\Application\UI\Multiplier;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\FrontModule\Components\AqPopup;
use tipli\FrontModule\Components\IBannerCarouselControlFactory;
use tipli\FrontModule\Components\IPaginatorFactory;
use tipli\FrontModule\Components\ISorterFactory;
use tipli\FrontModule\DealsModule\Components\IDealControlFactory;
use tipli\FrontModule\DealsModule\Components\IDealDetailControlFactory;
use tipli\FrontModule\Presenters\BasePresenter;
use tipli\FrontModule\ShopsModule\Forms\AliexpressCashbackCheckControl;
use tipli\FrontModule\ShopsModule\Forms\IAliexpressCashbackCheckControlFactory;
use tipli\InvalidStateException;
use tipli\Model\Account\Entities\FavoriteShop;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\FavoriteShopFacade;
use tipli\Model\Algolia\AlgoliaFacade;
use tipli\Model\Articles\ArticlesRecommender;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\ParentReferencingLayer;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Marketing\BannerFacade;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Products\ProductFacade;
use tipli\Model\Products2\ProductFacade as Product2Facade;
use tipli\Model\Reviews\ReviewFacade;
use tipli\Model\Rewards\ShareRewardFacade;
use tipli\Model\Seo\SeoFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\NewShopsRecommender;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\ShopConditionsProvider;
use tipli\Model\Shops\ShopsRecommender;
use tipli\Model\Shops\ShopSteveDataFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\Entities\TagGroup;
use tipli\Model\Tags\TagFacade;

class ShopPresenter extends BasePresenter
{
	public const SORT_NEWEST = 'newest';
	public const SORT_ALPHABETICALLY = 'alphabetically';

	/** @var DealFacade @autowire */
	public $dealFacade;

	/** @var ReviewFacade @autowire */
	public $reviewFacade;

	/** @var ShopsRecommender @autowire */
	public $shopsRecommender;

	/** @var NewShopsRecommender @autowire */
	public $newShopsRecommender;

	/** @var ArticlesRecommender @autowire */
	public $articlesRecommender;

	/** @var  AlgoliaFacade @autowire */
	public $algoliaFacade;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var ISorterFactory @inject */
	public $sorterFactory;

	/** @var ParentReferencingLayer @inject */
	public $parentReferencingLayer;

	/** @var BannerFacade @autowire */
	public $bannerFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var LeafletFacade @autowire */
	public $leafletFacade;

	/** @var ProductFacade @autowire */
	public $productFacade;

	/** @var Product2Facade @inject */
	public $product2Facade;

	/** @var IDealControlFactory @autowire */
	public $dealControlFactory;

	/** @var IDealDetailControlFactory @autowire */
	public $dealDetailControlFactory;

	/** @var ShareRewardFacade @autowire */
	public $shareRewardFacade;

	/** @var FavoriteShopFacade @autowire */
	public $favoriteShopFacade;

	/** @var ShopSteveDataFacade @inject */
	public ShopSteveDataFacade $shopSteveDataFacade;

	/** @var IBannerCarouselControlFactory @autowire */
	public $bannerControlFactory;

	/** @var IAliexpressCashbackCheckControlFactory @autowire */
	public $aliexpressCashbackCheckControlFactory;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopConditionsProvider @inject */
	public $shopConditionsProvider;

	/** @var Shop */
	private $shop;

	public function startup()
	{
		parent::startup();

		if ($this->getParameter('do') == 'shopsList-sort') {
			$this->redirect('this', ['do' => null, 'shopsList-sort' => null]);
		}

		if ($this->getParameter('do') == 'shopsList-next') {
			$this->redirect('this', ['do' => null, 'shopsList-page' => null, 'shopsList-offsetPage=1' => null, 'shopsList-next' => null]);
		}

		if ($this->getParameter('do') == 'shopsList-page') {
			$this->redirect('this', ['do' => null, 'shopsList-page' => null, 'shopsList-sort' => null]);
		}

		if ($this->getParameter('do') !== null && (Strings::startsWith($this->getParameter('do'), 'reviewsPaginator') || Strings::startsWith($this->getParameter('do'), 'reviewsList'))) {
			$this->redirect('this', ['do' => null, 'reviewsList-page' => null]);
		}
	}

	public function actionSortedShops()
	{
		$query = $this->shopFacade->createShopsQuery($this->getLocalization())
			->onlyPublished()
			->optimized()
			->sortByFirstDescriptionAt()
			->sortByCreatedAt();

		// NON CASHBACK
		$nonCashbackShopsQuery = clone ($query);

		$nonCashbackShopsQuery->notIn([1998]);
		$nonCashbackShopsQuery->onlyWithCashbackDisabled();

		$this->template->nonCashbackShops = $this->shopFacade->fetch($nonCashbackShopsQuery)->applyPaging(0, 56);

		// CASHBACK
		$cashbackShopsQuery = clone ($query);

		$cashbackShopsQuery
			->onlyWithCashbackAllowed()
			->exceptPaused();

		$this->template->shops = $this->shopFacade->fetch($cashbackShopsQuery)->applyPaging(0, 56);
	}

	public function renderShops(Tag $tag = null, $searchQuery = null)
	{
		if ($searchQuery !== null && $searchQuery !== Strings::webalize($searchQuery)) {
			$this->redirect('this', ['searchQuery' => Strings::webalize($searchQuery)]);
		}

		$this->template->getSteveShopData = (function (Shop $shop) {
			$shopSteveData = $this->shopSteveDataFacade->findByShop($shop);

			if ($shopSteveData === null) {
				return null;
			}

			return $shopSteveData;
		});

		$this->template->getCouponsCount = (function (Shop $shop) {
			$couponsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops([5816, 7746])
				->onlyValid()
				->visibleOnWeb()
				->notRemoved()
				->withType(Deal::TYPE_COUPON)
				->withShop($shop);

			return $this->dealFacade->fetch($couponsQuery)->count();
		});

		$this->template->getDealsCount = (function (Shop $shop) {
			$dealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops([5816, 7746])
				->onlyValid()
				->visibleOnWeb()
				->notRemoved()
				->withoutTypes([Deal::TYPE_CASHBACK, Deal::TYPE_COUPON])
				->withShop($shop);

			return $this->dealFacade->fetch($dealsQuery)->count();
		});

		$query = $this->shopFacade->createShopsWithTagQuery($this->getLocalization(), $tag)
			->onlyPublished()
			->optimized();

		$nonCashbackShopsQuery = clone ($query);

		if ($tag) {
			$query->sortByTagPriority();
		} else {
			$query->sortTop();
		}

		if ($searchQuery) {
			$searchQuery = SeoFacade::cleanUrl($searchQuery);
			$searchedShopIds = $this->algoliaFacade->searchShops($searchQuery);
			$query->in($searchedShopIds);
			$nonCashbackShopsQuery->in($searchedShopIds);
		} else {
			$nonCashbackShopsQuery
				->notIn([1998]);
		}

		$sortedNonCashBackShopsByPriorityQuery = clone ($nonCashbackShopsQuery);
		$sortedNonCashBackShopsByPriorityQuery = $sortedNonCashBackShopsByPriorityQuery
			->onlyWithCashbackDisabled()
			->sortTop();

		$sortedNonCashBackShopsByPriority = $this->shopFacade->fetch($sortedNonCashBackShopsByPriorityQuery)->applyPaging(0, 16);

		$nonCashbackShopsQuery
			->onlyWithCashbackDisabled()
			->sortByIds(array_reverse(array_keys($sortedNonCashBackShopsByPriority->toArray())))
			->sortBySitemapUpdatedAt();

		$nonCashbackShops = $this->shopFacade->fetch($nonCashbackShopsQuery)->applyPaging(0, 56);

		$cashbackShops = clone ($query);

		$cashbackShops
			->onlyWithCashbackAllowed()
			->exceptPaused();

		if ($tag) {
			$shops = $this->shopFacade->fetch($cashbackShops)->applyPaging(0, 56);
		} else {
			$shops = $this->shopFacade->fetch($cashbackShops);
			$this['paginator']->setItemsCount($shops->getTotalCount());
			$shops->applyPaginator($this['paginator']->getPaginator());
		}

		$this->template->shops = $shops;
		$this->template->tag = $tag;
		$this->template->tagType = Tag::TYPE_SHOP;
		$this->template->searchQuery = $searchQuery;
		$this->template->recommendedArticles = $this->articlesRecommender->getRecommendedArticles($this->getLocalization(), $tag, 4);

		if ($searchQuery) {
			$this->template->robotsNoIndexFollow = true;
		}

		if ($searchQuery) {
			$allShops = array_merge($shops->toArray(), $nonCashbackShops->toArray());

			if (count($allShops) === 1) {
				$this->redirect(':Front:Shops:Shop:shop', array_values($allShops)[0]);
			}

			/** @var Shop $shop */
			foreach ($allShops as $shop) {
				if (Strings::webalize($shop->getName()) === Strings::webalize($searchQuery)) {
					$this->redirect(':Front:Shops:Shop:shop', $shop);
				}
			}
		}
		$this->template->paginator = $this->getComponent('paginator')->getPaginator();
		$this->template->paginatorNonCashbackShops = $this->getComponent('paginatorNonCashbackShops')->getPaginator();

		$this->template->subTags = !empty($tag) && $tag->isParent() ? $this->tagFacade->findShopChildrenWithGroup($tag, TagGroup::TYPE_MAIN_NAVIGATION) : [];
		$this->template->nonCashbackShops = $nonCashbackShops;

		$this->template->tags = (function () {
			$tagsQuery = $this->tagFacade->createTagsQuery($this->getLocalization())
				->withType(Tag::TYPE_SHOP)
				->onlyParentTags()
				->withGroup(TagGroup::TYPE_MAIN_NAVIGATION)
				->onlyVisible()
				->sortTop();

			return $this->tagFacade->fetch($tagsQuery);
		});

		/** @var User $user */
		$user = $this->getUser()->isLoggedIn() ? $this->getUserIdentity() : null;
		$this->template->banners = $this->bannerFacade->findValidBanners($this->getLocalization(), 3, Banner::FORMAT_HOMEPAGE, $user ? $user->getSegment() : null, null, null, $user);

		// zasilkovna
		if (in_array($this->getParameter('zasilkovnaThankYou'), ['activation', 'extended']) && $user && $user->hasZasilkovnaPacket()) {
			$this->template->zasilkovnaThankYou = $this->getParameter('zasilkovnaThankYou');

			$shareRewardCampaign = $this->shareRewardFacade->findCampaign($this->configuration->getZasilkovnaShareRewardCampaignId($user->getLocalization()));
			if ($shareRewardCampaign) {
				$shareReward = $this->shareRewardFacade->findShareRewardByCampaignAndUser($shareRewardCampaign, null, $user);

				if ($shareReward) {
					$this->template->zasilkovnaShareReward = $shareReward;
				}
			}
		}

		$this->template->currentCashbackShopsPage = $this['paginator']->getPaginator()->getPage();
		$this->template->currentNonCashbackShopsPage = $this['paginatorNonCashbackShops']->getPaginator()->getPage();

		$cashbackCacheKeyPostfix = 'l' . $this->getLocalization()->getId() . '_t' . ($tag ? $tag->getId() : 0) . '_';
		$cashbackCacheKey = $this->getCashbackCacheKey();

		$this->template->cashbackCacheDisabled = $cashbackCacheKey === null;
		$this->template->cashbackCacheKeyPostfix = $cashbackCacheKeyPostfix . $cashbackCacheKey;
	}

	public function renderShop(Shop $shop, $deepUrl = null, $shortcut = null, $fromAddon = null)
	{
//		Debugger::log($this->clientLayer->getIp(), 'shop-detail-visit');
//		Debugger::log($this->clientLayer->getIp() . ' @@ ' . $this->clientLayer->getUserAgent(), 'shop-detail-visit-user-agent');

		// redirection checks
		if ($shop->isOnlyLeafletShop()) {
			$this->redirectPermanent(':Front:Leaflets:Leaflet:leaflets', ['shop' => $shop]);
		}

		/** @var User $user */
		$user = $this->getUserIdentity();

//		if ($this->getAction() !== 'shop' && !$this->isDeveloperCookieAllowed() && (!$user || $user->isAdmin())) {
//			$this->redirect('shops');
//		}

		if ((!$user || !$user->isAdmin()) && (!$shop->isPublished() || !$shop->isActive())) {
			$this->redirectPermanent('shops');
		}

		if ($user && $shortcut) {
			$this->redirectPermanent(':Front:Shops:Redirection:shop', [
				'shop' => $shop,
				'deepUrl' => $deepUrl,
				'utm_source' => $this->getParameter('utm_source'),
				'utm_medium' => $this->getParameter('utm_medium'),
				'utm_campaign' => $this->getParameter('utm_campaign'),
			]);
		} elseif ($fromAddon && !$user) {
			bdump("from addon");
//            $this->redirect(':Front:Shops:Redirection:shopSpinner', [
//                'shop' => $shop,
//                'userId' => $this->configuration->getUnLoggedRedirectionUser($this->getLocalization()),
//                'deepUrl' => $deepUrl
//            ]);
		}

		$isAdmin = $user ? $user->isAdmin() : null;

		$this->shop = $shop;
		$this->template->shop = $shop;
		$this->template->deepUrl = $deepUrl;
		$this->template->isAdmin = $isAdmin;

		$this->eventLayer->trackFacebookShopDetail($this->getUser()->isLoggedIn());

		$this->template->getLeaflets = (function () use ($shop) {
			$leafletsQuery = $this->leafletFacade->createLeafletsQuery()
				->withShop($shop)
				->onlyValidOrScheduled()
				->onlyVisible()
				->onlyConfirmed()
				->newestFirst();

			return $this->leafletFacade->fetch($leafletsQuery)->applyPaging(0, 4);
		});

		$this->template->getProducts = (function () use ($shop) {
			$itemsPerPage = 8;
			$maxCountOfPages = 4;
			$maxCountOfItems = $maxCountOfPages * $itemsPerPage;

			$productsQuery = $this->product2Facade->createProductsQuery($shop->getLocalization())
				->withShop($shop)
				->onlyValid()
				->sortPosition();

			$products = $this->product2Facade->fetchProduct($productsQuery)->applyPaging(0, $itemsPerPage);

			if ($products->isEmpty() === true) {
				$productsQuery = $this->productFacade->createProductsQuery($shop->getLocalization())
					->withShop($shop)
					->withPrimaryTag()
					->onlyValid()
					->sortTop();

				$products = $this->productFacade->fetch($productsQuery)->applyPaging(0, $itemsPerPage);
			}

			$page = $this['paginatorProducts']->getPaginator()->getPage();
			$this['paginatorProducts']->setItemsCount($products->getTotalCount() > $maxCountOfItems ? $maxCountOfItems : $products->getTotalCount());
			$this['paginatorProducts']->disablePages();

			if ($page >= $maxCountOfPages) {
				$this['paginatorProducts']->disableNext();
			} else {
				$products->applyPaginator($this['paginatorProducts']->getPaginator(), $itemsPerPage);
			}

			return $products;
		});

		$this->template->getExpiredProducts = (function () use ($shop) {
			$expiredProductsQuery = $this->product2Facade->createProductsQuery()
				->withShop($shop)
				->withSale()
				->onlyExpired()
				->sortOldest();

			return $this->product2Facade->fetchProduct($expiredProductsQuery)->applyPaging(0, 20);
		});

		$this->template->paginatorProductsPage = $this['paginatorProducts']->getPaginator()->getPage();

		$this->template->getProductsTags = (function () use ($shop) {
			$tagsQuery = $this->tagFacade->createTagsQuery($shop->getLocalization())
				->withType(Tag::TYPE_PRODUCT)
				->withShopProducts($shop)
				->onlyVisible()
				->onlyLevel(1)
				->sortTopParents()
				->sortTop();

			$limit = 10;
			$tags = $this->tagFacade->fetch($tagsQuery)->applyPaging(0, $limit)->toArray();

			if (count($tags) < $limit) {
				$tagsQuery = $this->tagFacade->createTagsQuery($shop->getLocalization())
					->withType(Tag::TYPE_PRODUCT)
					->withShopProducts($shop)
					->onlyVisible()
					->exceptTags($tags)
					->sortById();

				$tags = array_merge($tags, $this->tagFacade->fetch($tagsQuery)->applyPaging(0, $limit - count($tags))->toArray());
			}

			return $tags;
		});

		$this->template->unLoggedRedirectionUserId = $this->configuration->getUnLoggedRedirectionUser($this->getLocalization());

		$this->template->navigationTag = (function () use ($shop) {
			$parentTags = array_map(static function ($map) {
				return $map['entity'];
			}, $this->tagFacade->getNavigationTagsTree($shop->getLocalization()));

			foreach ($parentTags as $parentTag) {
				if ($shop->hasTag($parentTag)) {
					return $parentTag;
				}
			}

			return null;
		});

		$this->template->recommendedShops = (function () use ($shop) {
			return $this->shopsRecommender->getRecommendedShops($this->getLocalization(), $shop, 6, true);
		});

		$this->template->recommendedOrganicShops = (function () use ($shop) {
			return $this->newShopsRecommender->getRecommendedOrganicShopsForShop($shop);
		});

		$this->template->alternativeNames = (function () {
			return $this->shop->getAlternativeNamesArray();
		});

		$this->template->newestShops = (function () use ($shop) {
			return $this->shopFacade->findNewestShops(3, $shop->getLocalization(), false, true);
		});

		$this->template->recommendedShopsWithCoupons = (function () use ($shop) {
			$shopsQuery = $this->shopFacade->createShopsQuery($shop->getLocalization())
				->withTags($shop->getTags())
				->onlyWithSomeCoupons()
				->exceptShop($shop)
				->sortBoosted()
				->sortTop()
				->onlyPublished();

			return $this->shopFacade->fetch($shopsQuery)
				->applyPaging(0, 6);
		});

		$this->template->sidebarRecommendedShopsWithCoupons = (function () use ($shop) {
			$shopsQuery = $this->shopFacade->createShopsQuery($shop->getLocalization())
				->onlyWithSomeCoupons()
				->exceptShop($shop)
				->sortBoosted()
				->sortTop()
				->onlyPublished();

			return $this->shopFacade->fetch($shopsQuery)
				->applyPaging(0, 10);
		});

		$this->template->getForeignShops = (function () {
			return $this->shopFacade->findForeignShops($this->shop);
		});

		$this->template->recommendedShopsWithCashbackAllowed = (function (int $limit = 3, $onlyWithSomeCoupons = true) use ($shop) {
			$shopsQuery = $this->shopFacade->createShopsQuery($shop->getLocalization())
				->withTags($shop->getTags())
				->exceptShop($shop)
				->onlyWithCashbackAllowed()
				->sortBoosted()
				->sortTop()
				->onlyPublished();

			if ($onlyWithSomeCoupons) {
				$shopsQuery->onlyWithSomeCoupons();
			}

			return $this->shopFacade->fetch($shopsQuery)
				->applyPaging(0, $limit);
		});

		$this->template->recommendedArticles = (function () use ($shop) {
			return $this->articlesRecommender->getRecommendedArticles($this->getLocalization(), $shop, 3);
		});

		$this->template->descriptionBlock = (static function ($type) use ($shop) {
			$data = $shop->getDescriptionBlocks($type);

			if ($type === 'long_description' && isset($data[0])) {
				$data[0]->setDescription(ContentFilter::replaceIframe($data[0]->getDescription()));
			}

			return $data;
		});

		$this->template->banners = $this->bannerFacade->findValidBanners($this->localizationFacade->getCurrentLocalization(), 6, Banner::FORMAT_HOMEPAGE2, null, Banner::SIZE_FULL, null, $this->getUserIdentity(), $shop);

		$this->template->getDeals = function () use ($shop, $user) {

			if ($shop->isShowDeals() === false || $shop->getId() === 5816 || $shop->getId() === 7746) {
				return null;
			}

			if ($shop->getCountOfDeals() > 0) {
				$dealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops([5816, 7746])
					->withShop($shop)
					->onlyValid()
					->visibleOnWeb()
					->sortByExclusive()
					->sortByDealTypePriority($user);

				// vyjimka pro HU a neprihlasene uzivatele
				if ($this->getLocalization()->getLocale() === 'hu' && $this->getUser()->isLoggedIn() === false) {
					$dealsQuery
						->withoutTypes([Deal::TYPE_CASHBACK, Deal::TYPE_PRODUCT]);
				} else {
					$dealsQuery
						->withoutType(Deal::TYPE_PRODUCT);
				}

				$dealsQuery
					//->sortHighest()
					->sortTop();
			} elseif (!$shop->isCashbackAllowed()) {
				/** @var ResultSet $recommendedShops */
				$recommendedShops = $this->shopsRecommender->getRecommendedShops($this->getLocalization(), $shop, 6);

				$alternativeDealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops([5816, 7746])
					->withShops($recommendedShops->toArray())
					->onlyValid()
					->visibleOnWeb()
					->withType(Deal::TYPE_COUPON)
					->sortByCouponFirst()
					->sortByShopPriority();

				$dealIds = [];
				/** @var Deal $deal */
				foreach ($this->dealFacade->fetch($alternativeDealsQuery)->applyPaging(0, 15) as $deal) {
					$shopId = $deal->getShop()->getId();

					if (count($dealIds) >= 3) {
						break;
					}

					if (isset($dealIds[$shopId])) {
						continue;
					}

					$dealIds[$shopId] = $deal->getId();
				}

				$dealsQuery = $this->dealFacade->createDealsQuery()
					->withoutShops([5816, 7746])
					->in($dealIds)
					->sortByExclusive()
					->sortByCouponFirst()
					->sortByShopPriority();
			}

			if (!isset($dealsQuery)) {
				return null;
			}

			$deals = $this->dealFacade->fetch($dealsQuery);

			$this['paginatorDeals']->setItemsCount($deals->getTotalCount());
			$deals->applyPaginator($this['paginatorDeals']->getPaginator());

			return $deals;
		};

		$this->template->topDeal = (function () use ($shop) {

			// fashion RO and Alza hu
			if ($shop->getId() === 5816 || $shop->getId() === 7746) {
				return null;
			}

			return $this->dealFacade->findTopPriorityDealByShop($shop);
		});

		$coefficients = [
			Deal::UNIT_CZK => 20,
			Deal::UNIT_EUR => 0.8,
			Deal::UNIT_USD => 0.8,
			Deal::UNIT_PLN => 3,
			Deal::UNIT_HUF => 320,
			Deal::UNIT_RON => 4,
			Deal::UNIT_BGN => 1.5,
		];

		$topOfferRelative = $this->dealFacade->findTopValueDealByShop($shop, [Deal::TYPE_COUPON, Deal::TYPE_SALE], [Deal::UNIT_PERCENTAGE]);
		$topOfferAbsolute = $this->dealFacade->findTopValueDealByShop($shop, [Deal::TYPE_COUPON, Deal::TYPE_SALE], [Deal::UNIT_CZK, Deal::UNIT_EUR, Deal::UNIT_USD, Deal::UNIT_PLN, Deal::UNIT_HUF, Deal::UNIT_RON, Deal::UNIT_BGN]);

		if ($shop->getId() === 5816 || $shop->getId() === 7746) {
			$topOfferRelative = null;
			$topOfferAbsolute = null;
		}

		$coefficient = 1;

		if ($topOfferAbsolute !== null && isset($coefficients[$topOfferAbsolute->getUnit()])) {
			$coefficient = $coefficients[$topOfferAbsolute->getUnit()];
		}

		$this->template->topOfferRelative = $topOfferRelative;
		$this->template->topOfferAbsolute = $topOfferAbsolute;
		$this->template->coefficient = $coefficient;

		$this->template->topBoostedDeal = null;

		if ($shop->isBoostCoupons()) {
			$this->template->topBoostedDeal = $this->dealFacade->findTopBoostedDealByShop($shop);
		}

		$this->template->dealVerticalLayout = false;

		$this->template->tipliReviews = (function () {
			return $this->reviewFacade->findTipliPrioritizedReviews($this->getLocalization())->applyPaging(0, 2);
		});

		$reviews = $this->reviewFacade->findReviewsWithShop($shop, 4);

		$this['paginatorReviews']->setItemsCount($reviews->getTotalCount());
		$paginator = $this['paginatorReviews']->getPaginator();
		$reviews->applyPaginator($paginator);

		$this->template->reviews = $reviews;


		$this->template->getReviews = (function () use ($shop) {
			return $this->reviewFacade->findApprovedShopReviews($shop, 10, 4, false);
		});

		// reviews
		$this->template->getAverageShopReview = (function () use ($shop) {
			return $this->reviewFacade->findAverageShopReview($shop);
		});

		$this->template->getCountOfTotalReviews = (function () use ($shop) {
			return $this->reviewFacade->findApprovedShopReviewsCount($shop);
		});

		$this->template->shopOffers = (function () use ($shop) {
			return $this->offerFacade->resolveOffersForShop($shop);
		});

		$this->template->hasShopAllowedDeals = $shop->getId() !== 3085;

		$this->template->getExpiredCoupons = (function ($limit = 10) use ($shop) {
			$dealsQuery = $this->dealFacade->createDealsQuery()
				->withoutShops([5816, 7746])
				->withShop($shop)
				->withTypes([Deal::TYPE_COUPON, Deal::TYPE_SALE, Deal::TYPE_TIP, Deal::TYPE_FREE_SHIPPING])
				->onlyExpired()
				->sortNewest();

			return $this->dealFacade->fetch($dealsQuery)->applyPaging(0, $limit);
		});

		if ($this->getUserIdentity()) {
			$this->template->isShopFavorite = $this->favoriteShopFacade->isShopFavorite($this->getUserIdentity(), $shop);

			if ($shop->isCashbackActive()) {
				$referralLink = new Url($this->link('//:Front:Shops:Redirection:shop', ['shop' => $shop]));
				$referralLink->setQueryParameter('userId', $this->getUserIdentity()->getId());
				$referralLink->setQueryParameter('ref', 'friend');
				$this->template->userReferralLink = $referralLink->getAbsoluteUrl();
			}
		}

		// constants
		$cashbackCacheKeyPostfix = 's' . $shop->getId() . '_';
		$cashbackCacheKey = $this->getCashbackCacheKey();

		$this->template->cashbackCacheDisabled = $cashbackCacheKey === null;
		$this->template->cashbackCacheKeyPostfix = $cashbackCacheKeyPostfix . $cashbackCacheKey;

		$this->template->currentDealsPageNumber = $this['paginatorDeals']->getPaginator()->getPage();
		$this->template->canUserAddReview = $user ? $this->reviewFacade->canUserAddReview($this->getUserIdentity(), $shop) : false;

		$this->template->isSafariMobile = $this->clientLayer->isSafari() && $this->clientLayer->isMobile();

		if ($user) {
			$this->template->userStatus = 'logged-' . ($user->isActiveUser() ? 'active' : 'inactive');
		} else {
			$this->template->userStatus = 'unlogged-' . ($this->utmLayer->isUtmSourcePaid() || $this->parentReferencingLayer->getParentUser() ? 'paid' : 'default');
		}

		if ($deepUrl && ($aqPopupControl = $this->getComponent('aqPopup'))) {
			$aqPopupControl->setDeepUrl($deepUrl);
		}

		$this->template->variant = '';

		$this->template->getShopConditions = function () use ($shop) {
			return $this->shopConditionsProvider->getConditionsForFrontend($shop);
		};

		$this->listenOpenDeal('dealDetailControl');

		$this->template->popup = $this->resolvePopup($shop, $user);

		if ($this->localizationFacade->getCurrentLocalization()->isSlovak()) {
			$this->template->promoRegistrationVariant = $this->googleExperimentVariantResolver->getExperimentVariant('sk-002', 2)->getVariant();
		}

//		if ($this->localizationFacade->getCurrentLocalization()->isCzech()) {
//			$this->template->popupPromoRegistrationVariant = $this->googleExperimentVariantResolver->getExperimentVariant('cz-004', 2)->getVariant();
//		}
	}

	public function renderSearch($id)
	{
		$this->redirect('shops', ['searchQuery' => $id]);
	}

	public function handleAddToFavourites(Shop $shop)
	{
		if ($user = $this->getUserIdentity()) {
			if (!$this->favoriteShopFacade->isShopFavorite($user, $shop)) {
				$this->favoriteShopFacade->markShopAsFavorite($user, $shop, FavoriteShop::SOURCE_USER, FavoriteShop::REASON_FAVORITE);
			}

			$this->redrawControl('favouriteShop');
		}
	}

	public function handleRemoveFromFavourites(Shop $shop)
	{
		if ($user = $this->getUserIdentity()) {
			if ($this->favoriteShopFacade->isShopFavorite($user, $shop)) {
				$this->favoriteShopFacade->unMarkShopAsFavorite($user, $shop, FavoriteShop::SOURCE_USER, FavoriteShop::REASON_FAVORITE);
			}

			$this->redrawControl('favouriteShop');
		}
	}

	public function createComponentPaginator() // @todo trait
	{
		$control = $this->paginatorFactory->create();

		$control->setItemsPerPage(32);

		$control->onChange[] = function () {
			$this->redrawControl('shops');
		};

		return $control;
	}

	public function createComponentPaginatorDeals()
	{
		$control = $this->paginatorFactory->create()
			->enableNext();

		$control->setItemsPerPage(10);

		$control->onChange[] = function () {
			$this->redrawControl('deals');
		};

		return $control;
	}

	public function createComponentPaginatorNonCashbackShops()
	{
		$control = $this->paginatorFactory->create()
			->enableNext();

		$control->setItemsPerPage(32);

		$control->onChange[] = function () {
			$this->redrawControl('nonCashbackShops');
		};

		return $control;
	}

	public function createComponentPaginatorReviews()
	{
		$control = $this->paginatorFactory->create();

		$control->setItemsPerPage(3);
		$control->disablePages();
		$control->onChange[] = function () {
			$this->redrawControl('reviews');
		};

		return $control;
	}

	public function createComponentAliexpressCashbackCheckControl(): AliexpressCashbackCheckControl
	{
		/** @var Shop $shop */
		$shop = $this->getParameter('shop');

		if (!$shop->isAliexpress()) {
			throw new InvalidStateException('Trying to create aliexpress cashback-check form on non-aliexpress shop!');
		}

		$control = $this->aliexpressCashbackCheckControlFactory->create($shop, $this->getUserIdentity());

		$control->onSuccess[] = function () {
			$this->template->aliexpressCashbackCheckResult = true;
		};

		$control->onFailure[] = function () {
			$this->template->aliexpressCashbackCheckResult = false;
		};

		return $control;
	}

	public function createComponentSorter() // @todo trait
	{
		$control = $this->sorterFactory->create();

		$control->setDefaultSort(null);
		$control->setSorts([
			null => $this->translator->translate('front.shops.shop.shopsList.sort.favourites'),
			self::SORT_NEWEST => $this->translator->translate('front.shops.shop.shopsList.sort.newest'),
		]);

		$control->setNewTemplate();

		$control->onSort[] = function () {
			$this->redrawControl('shopSorter');
		};

		return $control;
	}

	public function createComponentNonCashbackShopSorter()
	{
		$control = $this->sorterFactory->create();

		$control->setDefaultSort(null);
		$control->setSorts([
			null => $this->translator->translate('front.shops.shop.shopsList.sort.favourites'),
			self::SORT_NEWEST => $this->translator->translate('front.shops.shop.shopsList.sort.newest'),
		]);

		$control->setNewTemplate();

		$control->onSort[] = function () {
			$this->redrawControl('nonCashbackShopSorter');
		};

		return $control;
	}

	protected function createComponentSideSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();
//		$control->setSignInUrl($this->link(':Front:Sign:in'));

		$control->onSuccess[] = function ($user) {
//			$this->flashMessage($this->translator->translate('front.emailSignUp.successMessage'));

//            $this->redirect('this');
			$this->afterSignUp();
		};

		return $control;
	}

	protected function createComponentDealControl()
	{
		return new Multiplier(function ($dealId) {
			$deal = $this->dealFacade->find($dealId);

			return $this->dealControlFactory->create($deal);
		});
	}

	protected function createComponentDealDetailControl()
	{
		return $this->dealDetailControlFactory->create();
	}

	public function createComponentBannerControl()
	{
		return $this->bannerControlFactory->create(
			6,
			Banner::FORMAT_HOMEPAGE2,
			null,
			Banner::SIZE_FULL,
			null,
			$this->getUserIdentity(),
			$this->shop,
			null,
			null
		);
	}

	private function resolvePopup(Shop $shop, ?User $user = null): ?string
	{
		if ($this->isWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if ($this->isMobileWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if ($this->isWarningPopupRequired($shop)) {
			return AqPopup::TYPE_SHOP_WARNING;
		}

		if (!$user && !$this->getHttpRequest()->getCookie(AqPopup::COOKIE_SHOP_UNLOGGED_USER_POPUP_PREFIX . '-' . $shop->getId())) {
			return AqPopup::TYPE_SHOP_REDIRECTION;
		}

		if (!$this->isPromoPopupAllowed()) {
			return null;
		}

		if ($this->isAddonPopupAllowed($user)) {
			return AqPopup::TYPE_ADDON;
		}

		if ($user && !$user->isActiveUser()) {
			return AqPopup::TYPE_EDUCATION;
		}

		return null;
	}

	private function isWarningPopupRequired(Shop $shop): bool
	{
		if ($this->clientLayer->isMobile()) {
			return false;
		}

		$httpRequest = $this->getHttpRequest();
		$warningPopupCookieName = AqPopup::COOKIE_SHOP_WARNING_POPUP_PREFIX . '-' . $shop->getId();

		return $shop->getWarningMessage() && (!$httpRequest->getCookie($warningPopupCookieName) || $httpRequest->getCookie($warningPopupCookieName) !== $shop->getWarningMessageHash());
	}

	private function isMobileWarningPopupRequired(Shop $shop): bool
	{
		if (!$this->clientLayer->isMobile()) {
			return false;
		}

		$httpRequest = $this->getHttpRequest();
		$warningPopupCookieName = AqPopup::COOKIE_SHOP_WARNING_POPUP_PREFIX . '-' . $shop->getId();

		return $shop->getMobileWarningMessage() && (!$httpRequest->getCookie($warningPopupCookieName) || $httpRequest->getCookie($warningPopupCookieName) !== $shop->getMobileWarningMessageHash());
	}

	private function isPromoPopupAllowed(): bool
	{
		$httpRequest = $this->getHttpRequest();

		return !$httpRequest->getCookie(AqPopup::COOKIE_SHOP_PROMO_POPUP_OPENED);
	}

	private function isAddonPopupAllowed(?User $user = null): bool
	{
		if ($this->configuration->isThereAddon($this->getLocalization()) === false) {
			return false;
		}

		return $user && !$user->hasInstalledAddon()
			&& $user->isOlderThanThreeDays()
			&& !$this->clientLayer->isMobile()
			&& ($this->clientLayer->isChrome() || $this->clientLayer->isFirefox())
		;
	}

	public function createComponentPaginatorProducts()
	{
		$control = $this->paginatorFactory->create()
			->enableNext();

		$control->setItemsPerPage(8);

		$control->onChange[] = function () {
			$this->redrawControl('productsArea');
			$this->redrawControl('products-paginator');
			$this->redrawControl('products');
		};

		return $control;
	}

	protected function createComponentEmailSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function () {
			$this->afterSignUp();
		};

		$control->onFailure[] = function ($form) {
			$this->redrawControl('registrationForm');
		};

		return $control;
	}
}
