{var $dataLabel = "shopDetail"}

{if $showLogo == true}
    {var $dataLabel = "productPage"}
{/if}

<div class="deal-item deal-item--product">
{*    <a n:if="$isAdmin" href="https://www.tipli.cz/admin/products.products/product/{$product->getId()}" target="_blank" class="deal-item__edit"><svg class="deal-item__edit-icon">{('edit-solid'|svg)|noescape}</svg></a>*}

    <div class="deal-item__aside pb-0">
        <a n:href=":Front:Shops:Redirection:product2 $product" rel="nofollow" target="_blank" class="deal-item__image-wrapper" data-hit="event" data-category="product" data-action="click" data-label="{$dataLabel}">
            <img src="{$basePath}/images/pixel.png" data-src="{$product->getImageUrl() |image:400,400, fit, false, null, false, true}" alt="{$product->getName()}"
                 class="deal-item__image unveil" data-hit="event" data-category="product" data-action="click" data-label="{$dataLabel}">
        </a>

        <p class="product-tag__wrapper">
            <span n:if="$product->getPercentageDiscount()" class="product-tag__item danger">{_'front.products.products.labels.discount'} -{$product->getPercentageDiscount()}%</span>
            <span n:if="$product->isInStock()" class="product-tag__item success">{_'front.products.products.labels.inStock'}</span>
        </p>

        <p class="product-tag__wrapper">
            <span n:if="$product->getPercentageDiscount()" class="product-tag__item danger">{_'front.products.products.labels.discount'} -{$product->getPercentageDiscount()}%</span>
        </p>
    </div>

    <div class="deal-item__content">
        <a n:if="$showLogo == true" n:href=":Front:Shops:Shop:shop $product->getShop()" class="deal-item__top-logo mb-0" data-type="banner">
            <img src="{$basePath}/images/pixel.png" data-src="{$product->getShop()->getLogo() |image:226,184, fit, false, null, false, true}"
                 data-src-retina="{$product->getShop()->getLogo() |image:452,368, fit, false, null, false, true}" alt="{$product->getShop()->getName()}" class="deal-item__logo-image unveil">
        </a>

        <div class="deal-item__row deal-item__row--direction-row justify-content-start w100 mt-2 mb-2 deal-item__row--vertical deal-item__row--product">
            <p class="deal-item__reward deal-item--horizontal-no-hover mb-0">
                <strong class="deal-item__reward--medium">{$product->getPrice() |amount} {$product->getCurrency() |currency}</strong>
                <small class="deal-item__reward--small" n:if="$product->getOldPrice() !== null">{$product->getOldPrice() |amount} {$product->getCurrency() |currency}</small>
            </p>
        </div>

        <div class="deal-item__row deal-item__row--direction-column deal-item__text-content">
            <p class="deal-item__title">
                <a n:href=":Front:Shops:Redirection:product2 $product" rel="nofollow" target="_blank" class="deal-item__title-link fw-regular" data-hit="event" data-category="product" data-action="click" data-label="{$dataLabel}">
                    {$product->getName()}
                </a>
            </p>

            <p n:if="false" class="deal-item__text">
                <span class="deal-item__show-more--short">
					{$product->getDescription() |noescape}
                </span>
            </p>
        </div>

        <div class="deal-item__row mt-auto deal-item__row--bottom">
            <a n:href=":Front:Shops:Redirection:product2 $product" rel="nofollow" target="_blank" class="deal-item__button" data-hit="event" data-category="product" data-action="click" data-label="{$dataLabel}">
                {_'front.products.product.button'}
            </a>
        </div>
    </div>
</div>
