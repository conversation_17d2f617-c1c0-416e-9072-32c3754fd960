{layout ../../../Presenters/templates/@layout.latte}

{block #styles}

{/block}

{define leafletSnippet}
	<a n:href=":NewFront:Leaflets:Leaflet:leaflet, $leaflet" class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
		<span itemscope itemtype="http://schema.org/SaleEvent">
            <meta itemprop="name" content="{$leaflet->getTitle()}">
            <meta itemprop="url" content="{_'front.links.homepage'}{link :NewFront:Leaflets:Leaflet:leaflet, $leaflet}">
            <meta itemprop="image" content="{($leaflet->getFirstLeafletPage()) ? $leaflet->getFirstLeafletPage()->getDownloadUrl() |image:198,198,'exactTop',null}">
            <meta itemprop="startDate" content="{$leaflet->getValidSince()->format('Y-m-d')}">
            <meta itemprop="endDate" content="{$leaflet->getValidTill()->format('Y-m-d')}">
            <span itemprop="location" itemscope itemtype="http://schema.org/ShoppingCenter">
                <meta itemprop="name" content="{$leaflet->getShop()->getName()}">
                <meta itemprop="url" content="{_'front.links.homepage'}{link :NewFront:Shops:Shop:default $leaflet->getShop()}">
                <meta itemprop="image" content="{$leaflet->getShop()->getCurrentLogo() |image:60}">
                <meta itemprop="address" content="{$leaflet->getShop()->getName()} {_'front.leaflets.country.' . $leaflet->getLocalization()->getLocale()}">
            </span>
        </span>

        <span n:if="($leaflet->isScheduled())" class="hidden"><span>{_'front.leaflet.validity.scheduled'}</span></span>
        <span n:if="($leaflet->isExpired())" class="hidden"><span>{_'front.leaflet.validity.expired'}</span></span>
        <span n:if="($leaflet->isValid())" class="hidden"><span>{_'front.leaflet.validity.valid'}</span></span>

		<div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
			<div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
				<div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
				<div class="w-full relative ">
					<img class="rounded-lg w-full max-h-[297.66px]" src="{($leaflet->getFirstLeafletPage()) ? $leaflet->getFirstLeafletPage()->getDownloadUrl() |image:268,268,'exactTop',null}" alt="{$leaflet->getTitle()}" loading="lazy">
					{*<img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/q0Ywt8ntDbXK5DlEepchPh0c4KXk1u55-Uc2Su3HGTI/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjE3LzIxNzAwMi8wY2EwZGJkMGQ5ZGYwZTYwLnJqNGhycmtqdXN5dC5qcGc.jpg" alt="">*}
				</div>
				<div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
					{*<button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-orange py-1 px-2.5">Nový leták</button>*}

					<button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
						{_'front.leaflet.button'}
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
							<path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>

				</div>
			</div>
			<div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
				<img class="max-w-[36px] max-h-[36px] w-full h-auto rounded-md" src="{$leaflet->getShop()->getCurrentLogo() |image:60}" alt="{$leaflet->getShop()->getName()}" loading="lazy">
				{*<img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/ye2TIprPYFU-sT0I5j0gkvI_VrbqeI6S4qjHIQ4OEAw/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.png" alt="">*}
				<div class="leading-[21px]">
					<div class="text-xs md:text-lg font-medium line-clamp-1">{$leaflet->getTitle()}</div>
					<div class="text-xs font-light">{$leaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} - {$leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}</div>
				</div>
			</div>
		</div>
	</a>
{/define}

{define shopSnippet}
    <a n:href=":NewFront:Leaflets:Leaflet:leaflets, shop => $shop" class="leaflet-list-compact__item">
        <span class="flex justify-center items-center flex-col">
            <span class="flex justify-center max-h-12 mb-3">
				<img src="{$shop->getCurrentLogo() |image:100}" loading="lazy" class="max-h-10 max-w-[100px] w-auto" alt="{$shop->getName()}">
			</span>
            <span class="text-base">{$shop->getName()}</span>
        </span>
    </a>
{/define}
