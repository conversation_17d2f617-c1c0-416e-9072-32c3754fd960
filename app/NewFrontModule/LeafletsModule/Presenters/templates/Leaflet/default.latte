{block title}
  {if isset($pageExtension) && $pageExtension->getHeading()}
    {$pageExtension->getHeading()}
  {else}
    {_'front.leaflet.list.pageTitle'}
  {/if}
{/block}
{block keywords}{/block}
{block description}{/block}

{block #scripts}
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
            crossorigin="anonymous"></script>

    <script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>
{/block}

{block content}

<div class="leaflet leaflet-layout">
    <div class="container">

        <div class="leaflet__wrapper">
            <div class="leaflet__content">
                <div class="leaflet__wrapper leaflet__wrapper-inner">
                    <div class="leaflet__content">

                        {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                            <!-- Letáky - Vypis - Obsah - Responsive 4 -->
							<div class="relative pt-5 min-h-[300px]">
                            	<ins class="adsbygoogle leaflet-adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="8588593468" data-ad-format="auto" data-full-width-responsive="true"></ins>
							</div>
                        {/if}

                        <div class="page-header">
                            {if isset($pageExtension) && $pageExtension->getHeading()}
                                <h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{$pageExtension->getHeading()}</h1>
                            {else}
                                <h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.pageTitle'}</h1>
                            {/if}

                            {if isset($pageExtension) && $pageExtension->getTopDescription()}
                                <div class="mb-5 md:mb-10">{$pageExtension->getTopDescription() |content |noescape}</div>
                            {/if}
                        </div>

                        <div id="leaflet-result" class="leaflet-result"></div>

                            {if isset($pageExtension) && $pageExtension->getMiddleDescription()}
                                <div class="page-extension content-block">
                                    {$pageExtension->getMiddleDescription() |noescape}
                                </div>
						    {/if}

                        {cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'newestLeaflets-' . $localization->getLocale(), expire => '1 hour', tags => ['leaflets/' . $localization->getLocale()]}
							<div class="leaflet__title-wrapper">
								<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]"><a n:href="allValidLeaflets">{_'front.leaflet.list.title.actual'}</a></h2>
							</div>

							<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
								{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
									<div class="leaflet-list__item">
										<!-- Letáky - Vypis - Obsah - Responsive 2 -->
										<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5136132291" data-ad-format="rectangle" data-full-width-responsive="true"></ins>
									</div>
								{/if}

								{var $topLeafletShops = $topLeafletShops()}
								{if $topLeafletShops}
									{foreach $topLeafletShops as $shop}
										{var $leaflet = $getNewestLeafletByShop($shop)}
										{if $leaflet !== null}
											{include leafletSnippet, leaflet => $leaflet}
										{/if}
									{/foreach}
								{/if}
							</div>

							<div class="text-center mt-[32px] mb-10">
							  <a n:href="allValidLeaflets" class="text-sm leading-[24.5px] underline md:hover:no-underline">{_'front.leaflet.list.button.actual'}</a>
							</div>
                        {/cache}

                        {cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletShops-' . $localization->getLocale(), expire => '1 hour'}
                            {var $leafletShops = $leafletShops()}
                            {if !$leafletShops->isEmpty()}
                                <div class="leaflet__title-wrapper leaflet__title-wrapper--no-top">
                                    <h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]"><a n:href="shops">{_'front.leaflet.list.title.favorite'}</a></h2>
                                </div>

                                <div class="">
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                                        {foreach $leafletShops as $leafletShop}
                                            {include shopSnippet, shop => $leafletShop}
                                        {/foreach}
                                    </div>
                                </div>

                                <!-- Letáky - Vypis - Obsah - Responsive -->
                                <ins class="adsbygoogle leaflet-adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="4488855910" data-ad-format="auto" data-full-width-responsive="true"></ins>

                                <div class="text-center mt-[32px] mb-10">
                                    <a n:href="shops" class="text-sm leading-[24.5px] underline md:hover:no-underline">{_'front.leaflet.list.moreLink'}</a>
                                </div>
                            {/if}
                        {/cache}

                        {cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'scheduledLeaflets-' . $localization->getLocale(), expire => '1 hour'}
                            {var $scheduledLeaflets = $scheduledLeaflets()}
                            {if !$scheduledLeaflets->isEmpty()}
                                <div class="leaflet__title-wrapper">
                                    <h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]"><a n:href="allScheduledLeaflets">{_'front.leaflet.list.title.future'}</a></h2>
                                </div>

                                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                                    {foreach $scheduledLeaflets as $leaflet}
                                        {include leafletSnippet, leaflet => $leaflet}
                                    {/foreach}
                                </div>

                                <div class="text-center mt-[32px] mb-10">
                                  <a n:href="allScheduledLeaflets" class="text-sm leading-[24.5px] underline md:hover:no-underline">{_'front.leaflet.list.button.future'}</a>
                                </div>
                            {/if}
                        {/cache}

                        {cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletTags-' . $localization->getLocale(), expire => '1 hour'}
                            {var $leafletTags = $leafletTags()}
                            {if count($leafletTags) > 0}
                                {foreach $leafletTags as $leafletTag}
                                    {var $tagLeaflets = $getLeafletsByTag($leafletTag)}
                                    {continueIf $tagLeaflets->isEmpty()}
                                    <div class="leaflet__title-wrapper">
                                        <h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]"><a n:href="leaflets, tag => $leafletTag">{$leafletTag->getName()}</a></h2>
                                    </div>

                                    {if $iterator->isEven()}
                                        <!-- Letáky - Vypis - Obsah - Responsive 3 -->
                                        <ins class="adsbygoogle leaflet-adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="9629501950" data-ad-format="auto" data-full-width-responsive="true"></ins>
                                    {/if}

                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                                        {foreach $tagLeaflets as $leaflet}
                                            {include leafletSnippet, leaflet => $leaflet}
                                        {/foreach}
                                    </div>
                                {/foreach}
                            {/if}
                        {/cache}
                    </div>

                    <div class="leaflet-layout__aside">
                        {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                            <!-- Letáky - výpisy - reponsive -->
                            <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="7808500197" data-ad-format="auto" data-full-width-responsive="true"></ins>
                        {/if}
                    </div>
                </div>
            </div>

            <div class="leaflet__sidebar">
                {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                    <!-- Tipli - general - letaky - responsive 4 -->
                    <ins class="adsbygoogle mrec-xs mrec-sm mrec-md mrec-lg" data-ad-client="ca-pub-3515231881192503" data-ad-slot="7023869273" data-ad-format="rectangle" data-full-width-responsive="true"></ins>

                    <div class="float-wrapper">
                        <!-- Tipli - general - letaky - responsive 5 -->
                        <ins class="adsbygoogle hide-xs halfpage-sm halfpage-md halfpage-lg" data-ad-client="ca-pub-3515231881192503" data-ad-slot="2893052570" data-ad-format="auto" data-full-width-responsive="true"></ins>
                    </div>
                {/if}
            </div>
        </div>

        <div class="float-form__stop"></div>
    </div>
</div>

<div class="page-extension content-block" n:if="isset($pageExtension) && $pageExtension->getBottomDescription()">
  <div class="container">
      {if isset($pageExtension) && $pageExtension->getTopDescription()}
          <div class="mb-5 md:mb-10">{$pageExtension->getTopDescription() |content |noescape}</div>
      {else}
          {*<div class="mb-5 md:mb-10">{_'front.leaflet.list.pagePerex'}</div>*}
      {/if}

      <div class="page-extension" n:if="isset($pageExtension) && $pageExtension->getMiddleDescription()">
          {$pageExtension->getMiddleDescription() |noescape}
      </div>

    {$pageExtension->getBottomDescription() |noescape}
  </div>
</div>

