
{*{block title}{if isset($pageExtension) && $pageExtension->getHeading()}{$pageExtension->getHeading()}{else}{$leaflet->getTitle()}{/if}{/block}*}
{*{block keywords}{_'front.leaflet.branch.keywords', [name => $leaflet->getShop()->getName(), title => $leaflet->getTitle(), validSince => $leaflet->getValidSince()->format('d.m.Y')]}{/block}*}
{*{block description}{_'front.leaflet.branch.description', [title => $leaflet->getTitle(), validSince => $leaflet->getValidSince()->format('d.m.'), validTo => $leaflet->getValidSince()->format('d.m.Y')]}{/block}*}

{block #scripts}
    {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
        <script src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" defer></script>
        <script type="text/javascript" src="{$basePath}/js/leaflet/leaflet-float-form.js" defer></script>

        <script>
            (adsbygoogle = window.adsbygoogle || []).push({});
            (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    {/if}
{/block}

{block content}

<div class="leaflet leaflet--bg">
    <div class="container">

        {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
		<div class="relative pt-5 min-h-[300px]">
			<!-- Letáky - Vypis - Obsah - Responsive 4 -->
			<ins class="adsbygoogle leaflet-adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="8588593468" data-ad-format="auto" data-full-width-responsive="true"></ins>
			<script>
				(adsbygoogle = window.adsbygoogle || []).push({});
			</script>
		</div>
        {/if}

        <div class="page-header">
            <h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
                {if $pageExtension} {$pageExtension->getHeading()}
                {else} {_'front.leaflet.shops.title'} {/if}
            </h1>

            <p class="mb-5 md:mb-10">
                {if $pageExtension}
                  {$pageExtension->getTopDescription() |content |noescape}
                {else}
                  {_'front.leaflet.shops.description'}
                {/if}
            </p>
        </div>

        <div class="leaflet__wrapper">
            <div class="leaflet__content">
                <div class="">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {cache 'leafletShops-' . $localization->getLocale(), expire => '1 hour'}
                            {foreach $leafletShops() as $leafletShop}
                                {include shopSnippet, shop => $leafletShop}

                                {if (($iterator->getCounter() % 18) == 0)}
                    </div>

                    {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                    <div class="leaflet__content-ads leaflet__content-ads--inner-margin">
                        <!-- Letáky - obchody - mezi obchodami - non responsive -->
                        <ins class="adsbygoogle"
                             style="display:inline-block;width:320px;height:100px"
                             data-ad-client="ca-pub-3515231881192503"
                             data-ad-slot="7635100258"></ins>
                        <!-- Letáky - obchody - mezi obchodami - non responsive -->
                        <ins class="adsbygoogle"
                             style="display:inline-block;width:320px;height:100px"
                             data-ad-client="ca-pub-3515231881192503"
                             data-ad-slot="7635100258"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                    </div>
                    {/if}


                                {/if}
                            {/foreach}
                        {/cache}
                    </div>
                </div>
            </div>

            <div class="leaflet__sidebar">
                {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                <!-- Boční banner letáky vypis čtverec -->
                <ins class="adsbygoogle" style="display:inline-block;width:300px;height:250px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="8713086702"></ins>

                <div class="float-wrapper">
                    <!-- Letáky výpis -->
                    <ins class="adsbygoogle" style="display:inline-block;width:300px;height:600px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="6061461884"></ins>
                </div>
                {/if}
            </div>
        </div>

        <div class="float-form__stop"></div>
    </div>
</div>

<div class="page-extension content-block" n:if="isset($pageExtension) && $pageExtension->getBottomDescription()">
    <div class="container">
        {$pageExtension->getBottomDescription() |noescape}
    </div>
</div>

