{block title}
  {if isset($pageExtension) && $pageExtension->getHeading()}
    {$pageExtension->getHeading()}
  {else}
    {_'front.leaflet.list.pageTitle'}
  {/if}
{/block}
{block keywords}{/block}
{block description}{/block}

{block #scripts}
    {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
        <script src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" defer></script>
        <script type="text/javascript" src="{$basePath}/js/leaflet.js" defer></script>
        <script type="text/javascript" src="{$basePath}/js/leaflet.default.js" defer></script>

		<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			(adsbygoogle = window.adsbygoogle || []).push({});
			(adsbygoogle = window.adsbygoogle || []).push({});
		</script>
    {/if}
{/block}

{block content}

<div class="leaflet leaflet-layout">
    <div class="container">
        <div class="page-header">
            <h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
                {if $pageExtension} {$pageExtension->getHeading()}
                {else} {_'front.leaflet.' . $presenter->getAction() . '.title'} {/if}
            </h1>

            <p class="mb-5 md:mb-10">
                {if $pageExtension}
                    {$pageExtension->getTopDescription() |content |noescape}
                {else}
                    {_'front.leaflet.' . $presenter->getAction() . '.description'}
                {/if}
            </p>
        </div>

        <div class="leaflet__wrapper">
            <div class="leaflet__content">
                <div class="leaflet__wrapper leaflet__wrapper-inner">
                    <div class="leaflet__content">

                        {if !$leaflets->isEmpty()}
                            <div class="datalist datalist-data">
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-5" n:snippet="leaflets" data-ajax-append="true">
                                    {foreach $leaflets as $leaflet}
                                        {include leafletSnippet, leaflet => $leaflet}
                                    {/foreach}
                                </div>
                                {snippet paginator}
                                    {control paginator}
                                {/snippet}
                            </div>
                        {else}
                            <div class="alert alert-info">{_'front.leaflet.noLeafletsMessage'}</div>
                        {/if}

                    </div>
                </div>
            </div>

            <div class="leaflet__sidebar">
                {if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
                    <!-- Boční banner letáky vypis čtverec -->
                    <ins class="adsbygoogle" style="display:inline-block;width:300px;height:250px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="8713086702"></ins>

                    <div class="float-wrapper">
                        <!-- Letáky výpis -->
                        <ins class="adsbygoogle" style="display:inline-block;width:300px;height:600px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="6061461884"></ins>
                    </div>
                {/if}
            </div>

        </div>

        <div class="float-form__stop"></div>
    </div>
</div>

<div class="page-extension content-block" n:if="isset($pageExtension) && $pageExtension->getBottomDescription()">
    <div class="container">
        <div class="page-extension" n:if="isset($pageExtension) && $pageExtension->getMiddleDescription()">
            {$pageExtension->getMiddleDescription() |noescape}
        </div>

        {$pageExtension->getBottomDescription() |noescape}
    </div>
</div>

