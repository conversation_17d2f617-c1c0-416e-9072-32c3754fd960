<?php

namespace tipli\NewFrontModule\LeafletsModule\Presenters;

use Nette\Http\IResponse;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\Model\Articles\ArticleFacade;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Regions\CoordinatesResolver;
use tipli\Model\Regions\Entities\Region;
use tipli\Model\Regions\RegionFacade;
use tipli\Model\Reviews\ReviewFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopLeafletTag;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Tags\Entities\Tag;
use tipli\Model\Tags\TagFacade;
use tipli\NewFrontModule\Presenters\BasePresenter;
use Tracy\Debugger;

class LeafletPresenter extends BasePresenter
{
	private const LIMIT_LEAFLETS_RELATED = 8;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ArticleFacade @inject */
	public $articleFacade;

	/** @var ReviewFacade @inject */
	public $reviewFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var RegionFacade @inject */
	public $regionFacade;

	/** @var CoordinatesResolver @inject */
	public $coordinatesResolver;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var DatadogProducer @inject */
	public $datadogProducer;

	public function startup()
	{
		parent::startup();

		// ini_set('memory_limit', '1024M');
	}

	public function actionDefault(): void
	{
		if ($this->configuration->isThereLeaflets($this->getLocalization()) === false) {
			$this->redirect(':NewFront:Homepage:default');
		}
	}

	public function renderDefault(): void
	{
		$topLeafletShopsQuery = $this->shopFacade->createShopsQuery()
			->onlyWithShowedLeaflets()
			->withLocalization($this->getLocalization())
			->onlyWithSomeActiveLeaflets()
			->onlyIndexable()
			->sortByLeafletsPriority();

		$newestLeafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
			->onlyAllowed()
			->onlyVisible()
			->onlyConfirmed()
			->onlyValid()
			->onlyWithShop()
			->onlyPrimary()
			->onlyIndexable()
			->withoutNewsletters()
			->withoutScreenshots()
			->sortByTopShops()
			->notOld()
			->onlyOnePerShop();

		$scheduledLeafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
			->onlyAllowed()
			->onlyVisible()
			->onlyConfirmed()
			->onlyScheduled()
			->onlyWithShop()
			->onlyIndexable()
			->withoutNewsletters()
			->withoutScreenshots()
			->sortByTopShops()
			->expireFirst()
			->notOld()
			->onlyOnePerShop();

		$leafletShopsQuery = $this->shopFacade->createShopsQuery($this->getLocalization())
			->onlyWithShowedLeaflets()
			->onlyWithLeafletTag()
			->onlyPublished()
			->onlyWithSomeLeaflets()
			->optimized()
			->onlyIndexable()
			->sortByLeafletsPriority();

		$this->template->scheduledLeaflets = function () use ($scheduledLeafletsQuery) {
			return $this->leafletFacade->fetch($scheduledLeafletsQuery)->applyPaging(0, 8);
		};

		$this->template->newestLeaflets = function () use ($newestLeafletsQuery) {
			return $this->leafletFacade->fetch($newestLeafletsQuery)->applyPaging(0, 8);
		};

		$this->template->topLeafletShops = function () use ($topLeafletShopsQuery) {
			return $this->shopFacade->fetch($topLeafletShopsQuery)->applyPaging(0, 8);
		};

		$this->template->getNewestLeafletByShop = function ($shop) {
			$leaflet = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->onlyAllowed()
				->onlyVisible()
				->onlyConfirmed()
				->onlyValid()
				->onlyPrimary()
				->onlyIndexable()
				->withoutNewsletters()
				->withoutScreenshots()
				->lastConfirmedFirst()
				->notOld()
				->withShop($shop);

			$leaflet = array_values($this->leafletFacade->fetch($leaflet)->applyPaging(0, 1)->toArray());

			if (count($leaflet) > 0) {
				return $leaflet[0];
			}

			return null;
		};

		$this->template->leafletShops =  function () use ($leafletShopsQuery) {
			return $this->shopFacade->fetch($leafletShopsQuery)->applyPaging(0, 12);
		};

		$this->template->leafletTags = function () {
			return $this->tagFacade->getTags($this->getLocalization(), Tag::TYPE_LEAFLET)->getQuery()->getResult();
		};

		$this->template->getLeafletsByTag = (function (Tag $tag) {
			$tagLeafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->onlyAllowed()
				->onlyVisible()
				->onlyValid()
				->onlyConfirmed()
				->onlyWithShop()
				->onlyIndexable()
				->withoutNewsletters()
				->withoutScreenshots()
				->withTag($tag)
				->expireFirst();

			return $this->leafletFacade->fetch($tagLeafletsQuery)->applyPaging(0, 4);
		});
	}

	public function renderLeaflets(Tag $tag = null, Shop $shop = null, Region $region = null)
	{
		if ($shop !== null && ($this->leafletFacade->isShopIndexable($shop) === false || $shop->isShowLeaflets() === false)) {
			$this->error('', IResponse::S410_GONE);
		}

		if ($tag !== null) {
			$this->template->robotsNoIndex = true;
		}

		$createBasicLeafletsQuery = function () {
			return $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->onlyIndexable()
				->onlyVisible()
				->onlyConfirmed()
				->newestFirst();
		};

		$leafletsQuery = $createBasicLeafletsQuery();

		if ($tag) {
			$leafletsQuery->withTag($tag);
			$this->template->tag = $tag;
		}

		if ($shop) {
			$leafletsQuery->withShop($shop);
			$this->template->shop = $shop;

			$leafletRelatedShopsQuery = $this->shopFacade->createShopsQuery($this->getLocalization())
				->onlyIndexable()
				->onlyWithLeafletTag()
				->withTags($shop->getTags())
				->onlyPublished()
				->onlyWithSomeLeaflets()
				->optimized()
				->sortByLeafletsPriority();

			$this->template->leafletRelatedShops =  function () use ($leafletRelatedShopsQuery) {
				return $this->shopFacade->fetch($leafletRelatedShopsQuery)->applyPaging(0, 12);
			};

			$this->template->reviews = (function () use ($shop) {
				return $this->reviewFacade->findApprovedShopReviews($shop, 4, 4, true);
			});

			if ($this->getLocalization()->getLocale() == 'cs') {
				$tag = $this->tagFacade->findBySlug('vernostni-programy', $this->getLocalization());
				if ($tag) {
					$this->template->shopArticle = $this->articleFacade->findArticleByShopAndTag($shop, $tag);
				}
			}
		}

		$expiredLeafletsQuery = clone $leafletsQuery;
		$expiredLeafletsQuery->onlyExpired()
			->notRemoved();

		$leafletsQuery->notOld();

		$leafletToMetaTitleQuery = $leafletsQuery->onlyValidOrScheduled();
		$leafletToMetaTitle = $this->leafletFacade->fetch($leafletToMetaTitleQuery)->applyPaging(0, 1);

		if (!$leafletToMetaTitle->isEmpty()) {
			$this->template->leafletToMetaTitle = current($leafletToMetaTitle->toArray());
		} else {
			$this->template->leafletToMetaTitle = null;
		}

		$newestLeafletsQuery = clone $leafletsQuery;
		$newestLeafletsQuery->onlyValidOrScheduled()
			->notRemoved();

		$this->template->newestLeaflets = function () use ($newestLeafletsQuery) {
			return $this->leafletFacade->fetch($newestLeafletsQuery);
		};

		$this->template->expiredLeaflets = function () use ($expiredLeafletsQuery) {
			return $this->leafletFacade->fetch($expiredLeafletsQuery)->applyPaging(0, 9);
		};

		$this->template->relatedLeafletsByTag = function (ShopLeafletTag $shopLeafletTag = null) use ($createBasicLeafletsQuery) {
			$relatedLeafletsQuery = $createBasicLeafletsQuery();
			$relatedLeafletsQuery->onlyValid();

			if ($shopLeafletTag) {
				$relatedLeafletsQuery->withTag($shopLeafletTag->getTag());
			}

			return $this->leafletFacade->fetch($relatedLeafletsQuery)->applyPaging(0, self::LIMIT_LEAFLETS_RELATED);
		};

		$this->template->tag = $tag;
		$this->template->shop = $shop;
	}

	public function renderAllValidLeaflets()
	{
		$leaflets = $this->leafletFacade->fetch($this->getAllLeafletsQuery(true));

		$this->template->leaflets = $leaflets;
		$this->setView('allLeaflets');

		$this['paginator']->setItemsCount($leaflets->getTotalCount());
		$leaflets->applyPaginator($this['paginator']->getPaginator());
	}

	public function renderAllScheduledLeaflets()
	{
		$leaflets = $this->leafletFacade->fetch(
			$this->getAllLeafletsQuery(false, false, true)
		);

		$this->template->leaflets = $leaflets;
		$this->setView('allLeaflets');

		$this['paginator']->setItemsCount($leaflets->getTotalCount());
		$leaflets->applyPaginator($this['paginator']->getPaginator());
	}

	private function getAllLeafletsQuery($onlyValid = false, $onlyExpired = false, $onlyScheduled = false)
	{
		$leafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
			->onlyAllowed()
			->onlyIndexable()
			->onlyVisible()
			->onlyConfirmed()
			->onlyWithShop()
			->sortByTopShops();

		if ($onlyValid) {
			$leafletsQuery->onlyValid();
		}

		if ($onlyExpired) {
			$leafletsQuery->onlyExpired();
		}

		if ($onlyScheduled) {
			$leafletsQuery->onlyScheduled();
		}

		return $leafletsQuery;
	}

	public function handleResolveLocation(string $lat = null, string $lng = null)
	{
		if (!$lat || !$lng) {
			$ip = $this->getHttpRequest()->getRemoteAddress();

			if ($this->configuration->getMode() == 'dev') {
				$ip = '*************';
			}

			$coordinates = $this->coordinatesResolver->resolveCoordinatesByIp($ip);

			$lat = $coordinates['lat'];
			$lng = $coordinates['lng'];

			if ($lat && $lng) {
				$this->redirect('Leaflet:leaflets', ['region' => $this->regionFacade->resolveNearestRegionByCoordinates($lat, $lng)]);
			}
		}

		$this->redirect('Leaflet:leaflets');
	}

	public function renderShops()
	{
		$leafletShopsQuery = $this->shopFacade->createShopsQuery($this->getLocalization())
			->onlyIndexable()
			->onlyWithShowedLeaflets()
			->withTypes([Shop::PROFILE_BUSINESS_LEAFLET, Shop::PROFILE_LEAFLET])
			->onlyWithLeafletTag()
			->onlyPublished()
			->onlyWithSomeLeaflets()
			->optimized()
			->sortByLeafletsPriority();

		$this->template->leafletShops = function () use ($leafletShopsQuery) {
			return $this->shopFacade->fetch($leafletShopsQuery);
		};
	}

	public function renderLeaflet(Leaflet $leaflet)
	{
		/** @var Shop $shop */
		$shop = $leaflet->getShop();

		if ($this->leafletFacade->isShopIndexable($leaflet->getShop()) === false || $shop->isActive() === false || $shop->isShowLeaflets() === false) {
			$this->error('', IResponse::S410_GONE);
		}

		if ($leaflet->isArchived() || $leaflet->isRemoved()) {
			$this->error('', IResponse::S410_GONE);
		}

		$this->template->leaflet = $leaflet;

		// expired leaflets (right panel)
		$this->template->expiredLeaflets = function ($limit = 8) use ($leaflet) {
			$this->datadogProducer->scheduleSendEvent('frontend.leaflet.generateData');
			Debugger::log('get expired leaflets', '___leaflet_callback');
			$expiredLeafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->withShop($leaflet->getShop())
				->onlyIndexable()
				->notRemoved()
				->onlyVisible()
				->onlyConfirmed()
				->onlyExpired()
				->newestFirst()
			;

			return $this->leafletFacade->fetch($expiredLeafletsQuery)->applyPaging(0, $limit);
		};

		// top leafles (left panel)
		$this->template->topLeaflets = function ($limit = 8) use ($leaflet) {
			$this->datadogProducer->scheduleSendEvent('frontend.leaflet.generateData');
			Debugger::log('get top leaflets', '___leaflet_callback');
			$topLeafletsQuery = $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->excludeLeaflet($leaflet)
				->onlyIndexable()
				->notRemoved()
				->onlyVisible()
				->onlyConfirmed()
//					->sortByTopShops()
				->newestFirst()
			;

			return $this->leafletFacade->fetch($topLeafletsQuery)->applyPaging(0, $limit);
		};

		$this->template->getCountOfPages = static function () use ($leaflet) {
			return $leaflet->getCountOfPages() ? : $leaflet->getLeafletPages()->count();
		};

		// leaflet pages paginator
		$this['leafletPagesPaginator']->setItemsCount($leaflet->getCountOfPages() ? : $leaflet->getLeafletPages()->count());

		$currentLeafletPageNumber = $this['leafletPagesPaginator']->getPaginator()->getPage();
		$this->template->currentLeafletPageNumber = $currentLeafletPageNumber;

		$this->template->isAdmin = $this->getUserIdentity() && $this->getUserIdentity()->isAdmin();

		if ($currentLeafletPageNumber > 1) {
			$this->template->robotsNoIndex = true;
		}

		// leaflet pages
		$this->template->getLeafletPages = function () use ($leaflet) {
			$leafletPagesQuery = $this->leafletFacade->createLeafletPagesQuery($leaflet);
			$leafletPages = $this->leafletFacade->fetchLeafletPages($leafletPagesQuery);

			$paginator = $this['leafletPagesPaginator']->getPaginator();
			$leafletPages->applyPaginator($paginator);

			return $leafletPages;
		};

		// basic query
		$createBasicLeafletsQuery = function () {
			return $this->leafletFacade->createLeafletsQuery($this->getLocalization())
				->onlyVisible()
				->onlyIndexable()
				->onlyConfirmed()
				->newestFirst();
		};

		// next leaflets based on current leaflet first tag
		$this->template->getTagLeaflets = function () use ($leaflet, $createBasicLeafletsQuery) {
			if (count($leaflet->getTags()) > 0) {
				$leafletsQuery = $createBasicLeafletsQuery();

				$leafletTag = $leaflet->getTags()[0];
				$leafletsQuery->withTag($leafletTag);
				$tagLeafletsQuery = clone $leafletsQuery;
				$tagLeafletsQuery->onlyValid()
					->onlyOnePerShop()
					->excludeLeaflet($leaflet);

				return $this->leafletFacade->fetch($tagLeafletsQuery)->applyPaging(0, 4);
			}

			return null;
		};

		// leaflet first tag
		$this->template->getLeafletFirstTag = static function () use ($leaflet) {
			return $leaflet->getTags()[0];
		};

		// next shop-leaflets based
		$this->template->getShopLeaflets = function () use ($leaflet, $createBasicLeafletsQuery) {
			$this->datadogProducer->scheduleSendEvent('frontend.leaflet.generateData');
			Debugger::log('get shop leaflets', '___leaflet_callback');
			$shopLeafletsQuery = $createBasicLeafletsQuery();

			$shopLeafletsQuery
				->withShop($leaflet->getShop());
			$shopLeafletsQuery->onlyValidOrScheduled()
				->excludeLeaflet($leaflet);

			return $this->leafletFacade->fetch($shopLeafletsQuery)->applyPaging(0, 4);
		};

		// similar shops
		$this->template->getLeafletShops = function () use ($leaflet) {
			$this->datadogProducer->scheduleSendEvent('frontend.leaflet.generateData');
			Debugger::log('get leaflet shops', '___leaflet_callback');
			$leafletShopsQuery = $this->shopFacade->createShopsQuery($this->getLocalization())
				->onlyIndexable()
				->onlyWithLeafletTag()
				->withTags($leaflet->getShop()->getTags())
				->onlyPublished()
				->onlyWithSomeLeaflets()
				->optimized()
				->sortByLeafletsPriority();

			return $this->shopFacade->fetch($leafletShopsQuery)->applyPaging(0, 12);
		};

		// top leaflet-shops
		$this->template->getTopLeafletShops = function () use ($leaflet) {
			$this->datadogProducer->scheduleSendEvent('frontend.leaflet.generateData');
			Debugger::log('get top leaflet shops', '___leaflet_callback');
			$topLeafletShopsQuery = $this->shopFacade->createShopsQuery($this->getLocalization())
				->onlyIndexable()
				->onlyWithLeafletTag()
				->onlyPublished()
				->onlyWithSomeLeaflets()
				->exceptShop($leaflet->getShop())
				->optimized()
				->sortByLeafletsPriority();

			return $this->shopFacade->fetch($topLeafletShopsQuery)->applyPaging(0, 5);
		};
	}

	public function createComponentLeafletPagesPaginator()
	{
		$control = $this->paginatorFactory->create();

		$control->disableNext();
		$control->enableStepPaging();

		$control->setItemsPerPage(1);

		$control->onChange[] = function () {
			$this->redrawControl('leafletPages');
		};

		return $control;
	}

	public function createComponentPaginator()
	{
		$control = $this->paginatorFactory->create()
			->setItemsPerPage(16);

		$control->onChange[] = function () {
			$this->redrawControl('paginator');
			$this->redrawControl('leaflets');
		};

		return $control;
	}
}
