<div id="voucher-detail-popup" class="fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
	{if $voucherDetail !== -1}
		{var $voucherCampaign = $voucherDetail->getVoucherCampaign()}
		{var $shop = $voucherCampaign->getShop()}
		{var $status = $voucherDetail->getStatus()}
		{var $userVoucher = $voucherDetail->getUserVoucher()}
	<div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
		<div class="pt-[26px] rounded-t-2xl border bg-white relative {if $status === 'active'}border-b-secondary-green{/if}">
			<div class="voucher-detail-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
				<img src="{$basePath}/new-design/close-btn.svg" alt="close">
			</div>

			<div class="flex items-center justify-center w-[169px] h-[96px] bg-white mx-auto mb-5">
				{if $shop}
					<img class="m-auto max-w-[100px] max-h-[60px] w-full" src="{$shop->getCurrentLogo() |image: 200,0,'fit',false,$shop->getName()}" alt="{$shop->getName()}">
				{else}
					{if $voucherCampaign->getLocalization()->isHungarian()}
						<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" n:syntax="off" xmlns="http://www.w3.org/2000/svg" width="124.3808mm" height="44.5442mm" viewBox="0 0 352.5755 126.2671"><defs><style>.a,.b{fill:#54565a;}.a,.d{fill-rule:evenodd;}.c,.d{fill:#ec7700;}</style></defs><path class="a" d="M81.0992,86.1627h-.065c-1.1919,0-2.2192.0005-2.9482.0015-1.2681.0009-1.2681.0009-1.2681.1108-.0459,4.7451-.4853,7.7617-1.3833,9.4937A13.5829,13.5829,0,0,1,72.38,99.7452a13.3421,13.3421,0,0,1-4.0742,2.4492,12.28,12.28,0,0,1-4.4331.8726,13.9287,13.9287,0,0,1-4.5107-.8008,12.2524,12.2524,0,0,1-4.2159-2.4477,12.9341,12.9341,0,0,1-3.0537-4.0474A12.2632,12.2632,0,0,1,50.93,90.3443V78.3077H77.9683v-17.92H50.93V38.6349H33.5577V90.2706a31.59,31.59,0,0,0,2.7968,13.7578,28.76,28.76,0,0,0,7.2124,9.5664,30.3169,30.3169,0,0,0,9.7124,5.5918,31.4524,31.4524,0,0,0,10.3,1.8389,32.0807,32.0807,0,0,0,10.3731-1.8389c8.29-2.8393,14.0864-8.0976,17.2261-15.6289,2.5478-6.1123,2.87-12.4472,2.8662-17.2832v-.1079L89.1,86.1651C86.4859,86.1642,83.4737,86.1627,81.0992,86.1627Z" transform="translate(-33.5577 -37.9274)"/><rect class="b" x="67.6538" y="22.7128" width="17.543" height="61.1641"/><path class="a" d="M182.8726,110.7472a31.1094,31.1094,0,0,0,5.6406-9.9326,32.0941,32.0941,0,0,0,1.8389-10.3731,31.4277,31.4277,0,0,0-1.84-10.3,30.2984,30.2984,0,0,0-5.5918-9.7124,28.7459,28.7459,0,0,0-9.5664-7.2129A31.5851,31.5851,0,0,0,159.5972,60.42H129.2818v81.8931h17.2256V120.9034h13.1855a28.9468,28.9468,0,0,0,13.6377-2.7241A29.6076,29.6076,0,0,0,182.8726,110.7472ZM171.52,95.1681a13.3312,13.3312,0,0,1-2.4483,4.0737,13.7244,13.7244,0,0,1-3.9775,3.0547,11.7164,11.7164,0,0,1-5.4239,1.2344h-13.163V77.7921H159.67a12.3392,12.3392,0,0,1,5.4258,1.1616,13.0554,13.0554,0,0,1,4.0489,3.0547,12.2275,12.2275,0,0,1,2.4462,4.2158,13.8713,13.8713,0,0,1,.8008,4.5108A12.27,12.27,0,0,1,171.52,95.1681Z" transform="translate(-33.5577 -37.9274)"/><rect class="b" x="192.3911" y="22.7128" width="17.5449" height="61.1641"/><rect class="b" x="164.1274" width="17.5449" height="83.877"/><rect class="b" x="68.1865" y="0.3623" width="16.4775" height="16.4775"/><rect class="c" x="192.4809" y="0.1212" width="17.3645" height="17.3645"/><path class="a" d="M384.271,80.6008a30.6656,30.6656,0,0,0-5.6594-9.83,29.0945,29.0945,0,0,0-9.682-7.3,31.6336,31.6336,0,0,0-13.3759-2.8183l-.0038-.0053h-.227c-.1067-.0007-.2094-.0071-.3165-.0071l-.2153.0038v.0043A29.1935,29.1935,0,0,0,341.17,63.4044a29.9657,29.9657,0,0,0-9.6574,7.522,31.4827,31.4827,0,0,0-5.7088,10.0527,32.48,32.48,0,0,0-1.8611,10.4984,31.8069,31.8069,0,0,0,1.8621,10.4244,30.6675,30.6675,0,0,0,5.6594,9.83,29.0939,29.0939,0,0,0,9.6821,7.3,31.9659,31.9659,0,0,0,13.9231,2.8307h.949l.0024-.0123a28.7494,28.7494,0,0,0,12.8855-2.7519,29.9671,29.9671,0,0,0,9.6574-7.522,31.4848,31.4848,0,0,0,5.7088-10.0527,32.4807,32.4807,0,0,0,1.8611-10.4985A31.8068,31.8068,0,0,0,384.271,80.6008Zm-17.1976,15.208a13.4921,13.4921,0,0,1-2.4778,4.123,13.8916,13.8916,0,0,1-4.0257,3.0916,11.8565,11.8565,0,0,1-5.4894,1.2493h-.23a12.4372,12.4372,0,0,1-5.3472-1.1685,13.2139,13.2139,0,0,1-4.0978-3.0916,12.3763,12.3763,0,0,1-2.4758-4.2668,14.04,14.04,0,0,1-.81-4.5653,12.418,12.418,0,0,1,.8826-4.4867,13.4911,13.4911,0,0,1,2.4779-4.1229,13.89,13.89,0,0,1,4.0256-3.0917,11.8577,11.8577,0,0,1,5.4894-1.2493h.23a12.4376,12.4376,0,0,1,5.3471,1.1685A13.2142,13.2142,0,0,1,364.67,82.49a12.3761,12.3761,0,0,1,2.4759,4.2667,14.0429,14.0429,0,0,1,.81,4.5653A12.4176,12.4176,0,0,1,367.0734,95.8088Z" transform="translate(-33.5577 -37.9274)"/><path class="d" d="M264.89,127.3312c-13.9385,15.0014-27.8018,22.6352-41.2032,22.69h-.1445c-13.4746,0-27.53-7.6479-41.7763-22.7319l-10.3047,9.7314c17.0312,18.0327,34.55,27.1738,52.0781,27.1738.0674,0,.1377,0,.2051,0,17.4785-.0713,34.8144-9.2276,51.5283-27.2154Z" transform="translate(-33.5577 -37.9274)"/><path class="b" d="M314.987,103.983V90.7975A28.9471,28.9471,0,0,0,312.2629,77.16a29.6092,29.6092,0,0,0-7.4322-9.542,31.1073,31.1073,0,0,0-9.9326-5.6406c-3.29-1.1271-8.503-1.2219-9.4049-1.3087H271.5768v-.0039H254.1441v61.1h17.4327V78.1012h13.5207a14.8481,14.8481,0,0,1,3.3152.449,7.88,7.88,0,0,1,.8585.2673,14.3021,14.3021,0,0,1,7.1089,6.5786,11.7163,11.7163,0,0,1,1.2343,5.4239v13.163h-.0022v17.758h17.3794V103.983Z" transform="translate(-33.5577 -37.9274)"/></svg>
					{else}
						<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path fill-rule="evenodd" clip-rule="evenodd"
								  d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z"
								  fill="#646C7C" />
							<path fill-rule="evenodd" clip-rule="evenodd"
								  d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z"
								  fill="#EF7F1A" />
						</svg>
					{/if}
				{/if}
			</div>

			<div class="text-center md:text-xl leading-[24.5px] md:leading-[35px] text-dark-1 md:px-[59px] pb-6">
				{$voucherCampaign->getName()}
			</div>
		</div>

		<div class="flex items-center justify-center w-full">
			<p n:if="$status === 'active'" class="relative flex w-auto items-center gap-2 px-3 h-7 text-secondary-green bg-white border border-secondary-green rounded-2xl text-center text-xs font-bold -mt-3.5">
				<svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12" fill="none">
				<path d="M14 1.5332L5.75 10.2415L2 6.2832" stroke="#66B940" stroke-width="2.25" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				{_newFront.vouchers.voucher.extraActivated}
			</p>
		</div>

		<div class="hidden flex items-center justify-center gap-[7px] text-sm leading-[24.5px] text-dark-4 mt-2.5">
			{_newFront.popups.dealDetail.conditions}
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="11" height="7" viewBox="0 0 11 7" fill="none">
					<path d="M1 1L4.91903 5.35448C5.22952 5.69947 5.77048 5.69947 6.08097 5.35448L10 1" stroke="#ADB3BF"/>
				</svg>
			</div>
		</div>

		{if $userVoucher}
			<div class="flex justify-center mt-8 mb-3">
				{if $voucherCampaign->isRewardTypeAddon()}
					<a href="{plink :NewFront:Static:addon}" class="flex items-center justify-center gap-2.5 border border-secondary-green bg-secondary-green text-white font-bold leading-7 h-[56px] px-10 rounded-xl xl:hover:bg-pastel-green-light xl:hover:text-secondary-green cursor-pointer">
						{_newFront.vouchers.voucher.addon}
						<svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
						<path d="M1 11.0328L10.898 1.13477M10.8964 9.52242L10.8964 1.13508L2.50901 1.13507" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</a>
				{elseif $voucherCampaign->getShop() !== null}
					<a href="{plink :NewFront:Shops:Redirection:shop $voucherCampaign->getShop()}" class="flex items-center justify-center gap-2.5 border border-secondary-green bg-secondary-green text-white font-bold leading-7 h-[56px] px-10 rounded-xl xl:hover:bg-pastel-green-light xl:hover:text-secondary-green cursor-pointer"
					   target="_blank"
					   data-redirect-popup="{plink aqPopup-open!, aqPopup-type => redirect, aqPopup-shopId => $voucherCampaign->getShop()->getId()}"
					   data-shop-id="{$voucherCampaign->getShop()->getId()}" data-toggle="tooltip" data-placement="top"
					   title="{_'front.shops.shop.shop.btnGetCashback.tooltip'}" data-hit="event" data-category="shopDetail"
					   data-action="click" data-label="cashbackBtnLogIn">
						{_newFront.vouchers.voucher.redirect}
						<svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
						<path d="M1 11.0328L10.898 1.13477M10.8964 9.52242L10.8964 1.13508L2.50901 1.13507" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</a>
				{else}
					<a class="voucher-detail-popup-close2 flex items-center justify-center gap-2.5 border border-secondary-green bg-secondary-green text-white font-bold leading-7 h-[56px] px-10 rounded-xl xl:hover:bg-pastel-green-light xl:hover:text-secondary-green cursor-pointer">
						{_newFront.vouchers.voucher.activated}
						<svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
						<path d="M1 11.0328L10.898 1.13477M10.8964 9.52242L10.8964 1.13508L2.50901 1.13507" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</a>
				{/if}
			</div>
		{else}
			{if in_array($status, [tipli\Model\Vouchers\VoucherObject::STATUS_ASSIGNED, tipli\Model\Vouchers\VoucherObject::STATUS_AVAILABLE])}
				<div class="py-4">
					<a href="{plink :NewFront:Vouchers:Vouchers:activateVoucher, id => $voucherCampaign->getId()}" class="flex mx-auto items-center gap-2.5 justify-center h-[56px] lg:h-[61px] w-full lg:w-[260px] bg-primary-blue-dark text-white font-bold leading-7 rounded-xl mt-2 lg:mt-0">
						{_newFront.vouchers.voucher.activate}
						<img src="{$basePath}/new-design/rocket.png" alt="rocket">
					</a>
				</div>
			{/if}
		{/if}

		{if $userVoucher !== null}
			{var $entity = $userVoucher}
		{else}
			{var $entity = $voucherCampaign}
		{/if}

		<div class="flex justify-center mt-5 mb-3">
			{if $entity->getValidTillDays() === 0}
				<div class="text-secondary-red border-secondary-red text-dark-2 border flex gap-1.5 items-center justify-between text-[10px] font-medium leading-[17.5px] rounded-full py-1 px-2 flex-shrink-0">
					<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
						<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="red" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					{_'newFront.vouchers.voucher.validTillToday'} {$entity->getValidTillDiff()->format('%hH:%iM:%sS')}
				</div>
			{elseif $entity->getValidTillDays() < 3}
				<div class="text-primary-orange border-[#FDBB47] border flex gap-1.5 items-center justify-between text-[10px] font-medium leading-[17.5px] rounded-full py-1 px-2 flex-shrink-0">
					<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
						<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#FDBB47" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					{_'newFront.vouchers.voucher.validTillDays', ['count' => $entity->getValidTillDays() + 1]}
				</div>
			{elseif $entity->getValidTillDays() <= 6}
				<div class="text-dark-2 border-light-2 border flex gap-1.5 items-center justify-between text-[10px] font-medium leading-[17.5px] rounded-full py-1 px-2 flex-shrink-0">
					<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
						<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					{_'newFront.vouchers.voucher.validTillDays', ['count' => $entity->getValidTillDays() + 1]}
				</div>
			{else}
				<div class="text-dark-2 border-light-2 border flex gap-1.5 items-center justify-between text-[10px] font-medium leading-[17.5px] rounded-full py-1 px-2 flex-shrink-0">
					<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
						<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
					{_'newFront.vouchers.voucher.validTill', ['date' => ($entity->getValidTill()|localDate:'d.m.Y')]}
				</div>
			{/if}
		</div>

		<div class="p-5 pt-2.5">
			<div class="bg-light-6 text-center text-xs text-black p-5 rounded-lg">
				{$voucherCampaign->getDescription()}
			</div>
		</div>
	</div>
	{else}
	<div class="bg-pastel-orange-light m-auto w-[464px] max-w-full rounded-2xl">
		<div class="relative p-[30px]">
			<div class="voucher-detail-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
				<img src="{$basePath}/new-design/close-btn.svg" alt="close">
			</div>

			<div class="flex flex-col items-center justify-center">
				<svg xmlns="http://www.w3.org/2000/svg" width="46" height="46" viewBox="0 0 46 46" fill="none" class="mb-5">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M23 3.2093C12.0699 3.2093 3.2093 12.0699 3.2093 23C3.2093 33.93 12.0699 42.7907 23 42.7907C33.93 42.7907 42.7907 33.93 42.7907 23C42.7907 12.0699 33.93 3.2093 23 3.2093ZM0 23C0 10.2975 10.2975 0 23 0C35.7026 0 46 10.2975 46 23C46 35.7026 35.7026 46 23 46C10.2975 46 0 35.7026 0 23ZM15.6259 32.4086C17.7064 30.8664 20.2507 29.9535 23 29.9535C25.7493 29.9535 28.2936 30.8664 30.3741 32.4086C31.0862 32.9362 31.2355 33.9412 30.7077 34.6532C30.1801 35.3652 29.1751 35.5146 28.4631 34.9867C26.9047 33.8318 25.0238 33.1628 23 33.1628C20.9762 33.1628 19.0954 33.8318 17.5369 34.9867C16.825 35.5146 15.82 35.3652 15.2923 34.6532C14.7645 33.9412 14.9139 32.9362 15.6259 32.4086Z" fill="#EF7F1A"/>
				<path d="M31.5603 19.7908C31.5603 21.5632 30.6024 23.0001 29.4208 23.0001C28.2391 23.0001 27.2812 21.5632 27.2812 19.7908C27.2812 18.0184 28.2391 16.5815 29.4208 16.5815C30.6024 16.5815 31.5603 18.0184 31.5603 19.7908Z" fill="#EF7F1A"/>
				<path d="M18.7244 19.7908C18.7244 21.5632 17.7665 23.0001 16.5848 23.0001C15.4032 23.0001 14.4453 21.5632 14.4453 19.7908C14.4453 18.0184 15.4032 16.5815 16.5848 16.5815C17.7665 16.5815 18.7244 18.0184 18.7244 19.7908Z" fill="#EF7F1A"/>
				</svg>

				<div class="text-center text-xl font-normal mb-[30px]">{_newFront.vouchers.voucher.expired}</div>

				{* Doplnit odkaz *}
				<a href="{plink Vouchers:default}" class="min-w-[294px] px-8 py-4 relative rounded-xl border border-primary-orange text-primary-orange text-base font-medium cursor-pointer xl:hover:bg-white">
					{_newFront.vouchers.voucher.showVouchers}
				</a>
			</div>
		</div>
	</div>
	{/if}
</div>

<script>
	let copyCouponPopup = document.getElementById("voucher-detail-popup");
	let closeCopyCouponPopup = document.querySelector(".voucher-detail-popup-close");
	let closeCopyCouponPopup2 = document.querySelector(".voucher-detail-popup-close2");

	closeCopyCouponPopup.onclick = function() {
		copyCouponPopup.style.display = "none";
	}

	closeCopyCouponPopup2.onclick = function() {
		copyCouponPopup.style.display = "none";
	}
</script>
