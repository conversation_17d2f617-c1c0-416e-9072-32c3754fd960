<?php

namespace tipli\NewFrontModule\Components\ContentSections\ContentSectionItem;

use Nette\Application\UI\Control;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Images\ImageStorage;
use tipli\Model\Marketing\ContentSectionBlockFacade;
use tipli\Model\Marketing\ContentSectionFacade;
use tipli\Model\Marketing\Entities\ContentSection;
use tipli\Model\Marketing\Entities\ContentSectionBlock;
use tipli\NewFrontModule\Components\Banners\BannerItem\BannerItem;
use tipli\NewFrontModule\Components\Banners\BannerItem\BannerItemFactory;
use tipli\NewFrontModule\DealsModule\Components\DealItem\DealItem;
use tipli\NewFrontModule\DealsModule\Components\DealItem\DealItemFactory;
use tipli\NewFrontModule\ShopsModule\Components\ShopItem\ShopItem;
use tipli\NewFrontModule\ShopsModule\Components\ShopItem\ShopItemFactory;

/* @property-read Template $template */
class ContentSectionItem extends Control
{
	public function __construct(
		private ContentSectionFacade $contentSectionFacade,
		private ContentSectionBlockFacade $contentSectionBlockFacade,
		private ShopItemFactory $shopItemFactory,
		private DealItemFactory $dealItemFactory,
		private BannerItemFactory $bannerItemFactory,
		private ImageStorage $imageStorage,
		private ImageFilter $imageFilter,
		private ?ContentSection $contentSection = null,
		private ?string $source = null
	) {
	}

	public function setContentSection(ContentSection $contentSection = null)
	{
		$this->contentSection = $contentSection;
	}

	public function setSource(string $source): void
	{
		$this->source = $source;
	}

	public function render(ContentSectionBlock $contentSectionBlock, bool $isLast = false): void
	{
		$this->template->block = $contentSectionBlock;
		$this->template->isLast = $isLast;
		$this->template->source = $this->source;

		$this->template->addFilter('image', $this->imageFilter);

		$this->template->getBlockBanners = function (ContentSectionBlock $contentSectionBlock) {
			return $this->contentSectionBlockFacade->findBanners($contentSectionBlock);
		};

		$this->template->getBlockShops = function (ContentSectionBlock $contentSectionBlock) {
			return $this->contentSectionBlockFacade->findShops($contentSectionBlock);
		};

		$this->template->getBlockDeals = function (ContentSectionBlock $contentSectionBlock) {
			return $this->contentSectionBlockFacade->findDeals($contentSectionBlock);
		};

		$this->template->setFile(__DIR__ . '/component.latte');
		$this->template->render();
	}

	protected function createComponentBannerItem(): BannerItem
	{
		return $this->bannerItemFactory->create();
	}

	protected function createComponentShopItem(): ShopItem
	{
		$shopItem = $this->shopItemFactory->create();

		$shopItem->setRedirectToProfile(false);

		return $shopItem;
	}

	protected function createComponentDealItem(): DealItem
	{
		return $this->dealItemFactory->create();
	}
}
