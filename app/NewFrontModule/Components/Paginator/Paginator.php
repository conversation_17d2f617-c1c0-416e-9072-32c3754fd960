<?php

namespace tipli\NewFrontModule\Components;

use Nette\Application\UI\Control;
use Nette\Utils\Paginator as NettePaginator;

interface IPaginatorFactory
{
	public function create(): Paginator;
}

final class Paginator extends Control
{
	public array $onChange = [];
	public array $onSetPage = [];

	protected NettePaginator $paginator;

	protected bool $pagesDisabled = false;

	protected bool $nextDisabled = false;

	protected bool $stepPagingEnabled = false;

	protected bool $ajax = false;

	public string $theme = 'dark';

	public string $showMoreTranslation = 'newFront.datalist.nextItems';

	public function __construct()
	{
		$this->paginator = new NettePaginator();
		if ($this->getParameter('page') !== null) {
			$this->paginator->setPage($this->getParameter('page'));
		}
	}

	public function setItemsPerPage($itemsPerPage)
	{
		$this->paginator->setItemsPerPage($itemsPerPage);

		return $this;
	}

	public function setItemsCount($itemsCount)
	{
		$this->paginator->setItemCount($itemsCount);

		return $this;
	}

	public function setTheme(string $theme): static
	{
		$this->theme = $theme;

		return $this;
	}

	/**
	 * @crossOrigin
	 */
	public function handlePage($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}

		if ($this->presenter->isAjax()) {
			$this->onChange();
			$this->redrawControl();
		}
	}

	/**
	 * @crossOrigin
	 */
	public function handlePrev($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}
		$this->onChange();
		$this->redrawControl();
	}

	/**
	 * @crossOrigin
	 */
	public function handleNext($page)
	{
		if ($page !== null) {
			$this->paginator->setPage($page);
		}
		$this->onChange();
		$this->redrawControl();
	}

	public function enableAjax(): static
	{
		$this->ajax = true;

		return $this;
	}

	public function render()
	{
		if ($this->getParameter('page') !== null) {
			$this->paginator->setPage($this->getParameter('page'));
		}

		$this->template->paginator = $this->paginator;
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->pagesDisabled = $this->pagesDisabled;
		$this->template->nextDisabled = $this->nextDisabled;
		$this->template->stepPagingEnabled = $this->stepPagingEnabled;
		$this->template->showMoreTranslation = $this->showMoreTranslation;
		$this->template->ajax = $this->ajax;
		$this->template->theme = $this->theme;
		$this->template->render();
	}

	public function getPaginator()
	{
		return $this->paginator;
	}

	public function enableStepPaging()
	{
		$this->stepPagingEnabled = true;

		return $this;
	}

	public function disablePages()
	{
		$this->pagesDisabled = true;

		return $this;
	}

	public function disableNext()
	{
		$this->nextDisabled = true;

		return $this;
	}

	public function enableNext()
	{
		$this->nextDisabled = false;

		return $this;
	}

	public function setShowMoreTranslation(string $translation): void
	{
		$this->showMoreTranslation = $translation;
	}
}
