{var $campaignValidTill = ($campaignSubscription && $campaignSubscription->getValidTill()) ? $campaignSubscription->getValidTill(): (new \DateTime)->modify('+ 7 days')}
{capture $campaignAvailableBonus |trim}
    {$campaignAvailableBonus |amount}
{/capture}
<div class="flex items-center justify-between c-progress">
    <div class="flex items-center gap-[11px]">
        <div class="text-lg font-bold leading-[31.5px] py-[5px] px-[11px] bg-secondary-green text-white rounded-[10px]">2×</div>
        <div>
            <div class="font-bold leading-7 text-secondary-green">
                {_newFront.campaign300.bonus, ['amount' => $campaignAllocatedBonus]}
            </div>
            <div class="text-xs leading-[21px] text-dark-1 -mt-1">
                {if !$campaignSubscription || $campaignPercentageState < 100}
                    {_newFront.campaign300.progressBar.title, ['amount' => $campaignAllocatedBonus]}
                {elseif !$campaignSubscription->isFinished()}
                    {_'newFront.campaign300.progressBar.finish.title', [campaignAllocatedBonus => $campaignAllocatedBonus] |noescape}
                {else}
                    {_'newFront.campaign300.progressBar.complete.title', [campaignAllocatedBonus => $campaignAllocatedBonus] |noescape}
                {/if}
            </div>
        </div>
    </div>
    <div class="text-end">
        <div class="inline-flex items-center gap-[5px] bg-secondary-green rounded-[20px] text-xs text-white leading-[21px] font-medium pl-[7px] pr-[4px] mb-1">
            {_newFront.campaign300.progressBar.active}
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                <rect opacity="0.5" width="10" height="10" rx="5" fill="white"/>
                <rect x="2" y="2" width="6" height="6" rx="3" fill="white"/>
            </svg>
        </div>
        <div class="text-xs leading-[21px] text-dark-3">{_newFront.campaign300.progressBar.validTill, ['date' => ($campaignValidTill|localDate)]}</div>
    </div>
</div>

<div class="h-1.5 w-full bg-light-6 rounded-[300px] relative mt-[11px]">
    <div class="relative h-1.5 bg-secondary-green rounded-[300px] mt-[3px] ml-[3px]" style="width: {$campaignPercentageState |noescape}%">
		<img src="{$basePath}/images/rocket.png" width="55px" height="55px" class="absolute w-14 h-14 max-w-none right-0 bottom-0 mb-[-23px] mr-[-23px]">
	</div>
</div>

<div class="flex justify-between items-center text-xs text-dark-3 mt-[5px] leading-[21px]">
    <div class="c-progress__status-start-inner">0 {_'newFront.campaign300.progressBar.currency'}</div>
    <div class="hidden md:block">
        {if !$campaignSubscription || $campaignPercentageState < 100}
            {_newFront.campaign300.progressBar.obtained, ['obtained' => $campaignAmountProgress, 'total' => $campaignAllocatedBonus] |noescape}
        {elseif !$campaignSubscription->isFinished()}
            {_'newFront.campaign300.progressBar.finish.text' |noescape}
        {else}
            {_'newFront.campaign300.progressBar.complete.text', [link => $presenter->link("Account:Transaction:default")] |noescape}
        {/if}
    </div>
    <div class="c-progress__status-end-inner">{$campaignAllocatedBonus} {_'newFront.campaign300.progressBar.currency'}</div>
</div>
