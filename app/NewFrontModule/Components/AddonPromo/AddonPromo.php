<?php

namespace tipli\NewFrontModule\Components\AddonPromo;

use Nette\Application\UI\Control;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Localization\Translator;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\ShopFacade;

/* @property-read Template $template */
class AddonPromo extends Control
{
	private Cache $cache;

	public function __construct(
		private Localization $localization,
		private Translator $translator,
		private ClientLayer $clientLayer,
		private ShopFacade $shopFacade,
		private Storage $storage,
	) {
		$this->cache = new Cache($storage, self::class);
	}

	public function render(): void
	{
		if ($this->clientLayer->isChrome()) {
			$this->template->addonLink = $this->localization->getChromeAddonUrl();
		} elseif ($this->clientLayer->isFirefox()) {
			$this->template->addonLink = $this->localization->getFirefoxAddonUrl();
		} elseif ($this->clientLayer->isSafari()) {
			$this->template->addonLink = $this->localization->getSafariAddonUrl();
		} else {
			$this->template->addonLink = null;
		}

		$this->template->localization = $this->localization;

		$localization = $this->localization;

		$this->template->countOfCashbackShops = $this->cache->load('countOfCashbackShops-' . $this->localization->getId(), function (&$dependencies) use ($localization) {
			$shopsQuery = $this->shopFacade->createShopsQuery($localization)
				->onlyPublished()
				->onlyWithCashbackAllowed();

			$dependencies = [
				Cache::EXPIRATION => '1 hour',
			];

			return $this->shopFacade->fetch($shopsQuery)->getTotalCount();
		});

		$this->template->translator = $this->translator;
		$this->template->setFile(__DIR__ . '/component.latte');
		$this->template->render();
	}
}
