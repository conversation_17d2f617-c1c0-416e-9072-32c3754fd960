<div class="aq-popup__bottom aq-popup__bg-white px-2" data-survey-link="{link surveyResponse!, $survey->getId()}">
	<div class="aq-popup__logo-wrapper aq-popup__logo-wrapper--relative mb-4">
		<img src="{$survey->getShop()->getCurrentLogo() |image: 170}" alt="{$survey->getShop()->getName()}" class="aq-popup__logo">
	</div>

	<div class="js-survey-section" data-section="1">
		<div class="mb-5">
			<small class="d-block ta-center fz-m lh-m mw-300 color-grey-light mb-5 mx-auto">{_'front.aqPopup.survey.section1.small'|noescape}</small>
			<h2 class="aq-popup__title aq-popup__title--medium mw-500 fw-bold mx-auto">{_'front.aqPopup.survey.section1.text', [shop => $survey->getShop()->getName()]|noescape}</h2>
		</div>

		<div class="row">
			<div class="col-sm-6 mb-3 mb-sm-0">
				<button class="btn btn-green-inverse aq-popup__button js-survey-button" data-section="2" data-answer-number="1" data-answer-option="yes" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yes">{_'front.aqPopup.survey.section1.answer.1'}</button>
			</div>

			<div class="col-sm-6">
				<button class="btn btn-red-inverse aq-popup__button js-survey-close-and-save" data-answer-number="1" data-answer-option="no" data-hit="event" data-category="surveyPopup" data-action="click" data-label="no">{_'front.aqPopup.survey.section1.answer.2'}</button>
			</div>
		</div>
	</div>

	<div class="js-survey-section hide" data-section="2">
		<div class="mb-5">
			<h2 class="aq-popup__title aq-popup__title--medium mw-500 mx-auto">{_'front.aqPopup.survey.section2.text', [shop => $survey->getShop()->getName(), reward => ($survey->getShop() |reward:pure)]|noescape}</h2>
		</div>

		<div class="row">
			<div class="col-sm-6 mb-3 mb-sm-0">
				<button class="btn btn-green-inverse aq-popup__button js-survey-button" data-section="3" data-answer-number="2" data-answer-option="yes" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesYes">{_'front.aqPopup.survey.section2.answer.1'}</button>
			</div>

			<div class="col-sm-6">
				<button class="btn btn-red-inverse aq-popup__button js-survey-button" data-section="4" data-answer-number="2" data-answer-option="i_dont_know" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesNo">{_'front.aqPopup.survey.section2.answer.2'}</button>
			</div>
		</div>
	</div>

	<div class="js-survey-section hide" data-section="3">
		<div class="mb-3">
			<h2 class="d-block fz-xl lh-m ta-center mw-500 mx-auto">{_'front.aqPopup.survey.section3.text', [shop => $survey->getShop()->getName()]|noescape}</h2>
		</div>

		<form>
			<textarea name="" id="" class="aq-popup-survey__textarea js-survey-form-textarea" placeholder="{_'front.aqPopup.survey.section3.textarea'}"></textarea>
			<button class="btn btn-orange-inverse aq-popup__button js-survey-button" data-section="5" data-answer-number="3" data-answer-option="form" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesYesForm">{_'front.aqPopup.survey.section3.submit'}</button>
		</form>
	</div>

	<div class="js-survey-section hide" data-section="4">
		<div class="mb-5">
			<h2 class="d-block fz-l lh-m ta-center mw-500 mx-auto">{_'front.aqPopup.survey.section4.text', [shop => $survey->getShop()->getName(), reward => ($survey->getShop() |reward:pure)]|noescape}</h2>

			{if $showAddonPromo}
				<p class="d-block fz-xl lh-s fw-bold ta-center mw-500 mx-auto mt-5 mb-4">{_'front.aqPopup.survey.section4.or'}</p>
				<p class="d-block fz-l lh-m fw-regular ta-center color-grey mw-500 mx-auto mb-4">{_'front.aqPopup.survey.section4.addon'}</p>

				<p class="aq-popup__c-message">
					{_'front.aqPopup.survey.section4.addonReward', ['amount' => $addonRewardAmount] |noescape}
				</p>
			{/if}
		</div>

		<div>
			{if $showAddonPromo}
				<a href="{plink :NewFront:Static:addon}" class="btn btn-orange-inverse aq-popup__button mw-320" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesNoAddon">{_'front.aqPopup.survey.section4.addonCta'}</a>
			{else}
				<button class="btn btn-orange-inverse aq-popup__button mw-320 js-survey-close" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesNoClose">{_'front.aqPopup.survey.section4.button'}</button>
			{/if}
		</div>
	</div>

	<div class="js-survey-section hide" data-section="5">
		<div class="mb-5">
			<h2 class="aq-popup__title aq-popup__title--medium mw-500 mx-auto">{_'front.aqPopup.survey.section5.text'|noescape}</h2>
		</div>

		<div>
			<button class="btn btn-orange-inverse aq-popup__button mw-320 js-survey-close" data-hit="event" data-category="surveyPopup" data-action="click" data-label="yesYesFormClose">{_'front.aqPopup.survey.close'}</button>
		</div>
	</div>
</div>
