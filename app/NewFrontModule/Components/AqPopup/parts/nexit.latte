<div id="redirect-not-logged-second-popup" class="fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5" n:if="$shop !== null">
    <div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
        <div class="pt-[26px] rounded-t-2xl border border-b-secondary-green bg-light-6 relative">
            <div class="redirect-not-logged-second-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
                <img src="{$basePath}/new-design/close-btn.svg" alt="close">
            </div>
            <div class="flex justify-center">
                <div class="bg-white flex items-center justify-center border border-light-5 rounded-2xl mb-5 p-5">
                    <img class="m-auto w-full max-w-[100px] max-h-[40px]" src="{$shop->getCurrentLogo() |image:200,0,'fit',false,$shop->getName(), false, true}" alt="">
                </div>
            </div>

            <div class="flex absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 whitespace-nowrap">
                <div class="flex items-center text-sm bg-secondary-green rounded-full text-white font-bold leading-[24.5px] uppercase py-1 pl-1 pr-[15px]">
                    <div class="bg-white text-secondary-green rounded-full px-3 leading-[28px] mr-1.5">{_newFront.popups.exit.bonusAmount, ['amount' => $bonusAmount]}</div>
                    {_newFront.popups.exit.bonus}
                </div>
            </div>

            <div class="text-center md:text-xl leading-[24.5px] md:leading-[35px] text-dark-1 px-5 md:px-[59px] pb-[45px]">
                {if $isCashbackAllowed}
                    {_newFront.popups.exit.title, ['shop' => $shop->getName(), 'reward' => ($shop |reward,false)] |noescape}
                {else}
                    {_newFront.popups.exit.titleNonCashback, ['shop' => $shop->getName(), 'reward' => ($shop |reward,false)] |noescape}
                {/if}
            </div>
        </div>

        <div class="mt-[49px] mb-[37px] px-5 md:px-[79px] relative">
            {snippet form}
                {form emailSignUpControl-form, class => "ajax"}
                    <div class="bg-secondary-red text-white mt-3 rounded p-2" n:foreach="$form->errors as $error">
                        {$error |noescape}
                    </div>

                    <img class="absolute right-[-123px] bottom-[-42px]" src="{$basePath}/new-design/hp-register-donkey.png" loading="lazy" alt="donkey">
                    <input n:name="email" placeholder="{_'newFront.popups.exit.email'}" class="w-full bg-light-6 border border-solid border-light-4 pl-5 py-4 leading-[28px] rounded-xl">
                    <input n:name="password" placeholder="{_'newFront.popups.exit.password'}" class="w-full mt-2 bg-light-6 border border-solid border-light-4 pl-5 py-4 leading-[28px] rounded-xl">
                    <div class="form-group" n:ifset="$form[recaptcha]">
                        <div n:name="recaptcha"></div>
                    </div>
                    <button n:name="submit" class="w-full py-4 rounded-xl bg-orange-gradient text-white font-medium mt-2 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover">{_'newFront.popups.exit.submit'}</button>
                {/form}
            {/snippet}
            <div class="absolute top-[31px] left-[-130px]">
                <div class="hidden relative md:inline-block">
                    <svg  xmlns="http://www.w3.org/2000/svg" width="180" height="139" viewBox="0 0 180 139" fill="none">
                        <g filter="url(#filter0_b_572_4757)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.68629 134.314C0 129.627 0 122.085 0 107V32C0 16.9151 0 9.37259 4.68629 4.68629C9.37258 0 16.9151 0 32 0H133C148.085 0 155.627 0 160.314 4.68629C165 9.37259 165 16.9151 165 32V41.2417C165 45.4852 166.686 49.5549 169.686 52.5554L174.74 57.6091C180.988 63.8575 180.988 73.9882 174.74 80.2366L169.686 85.2903C166.686 88.2909 165 92.3605 165 96.604V107C165 122.085 165 129.627 160.314 134.314C155.627 139 148.085 139 133 139H32C16.9151 139 9.37258 139 4.68629 134.314Z" fill="#182B4A" fill-opacity="0.85"/>
                        </g>
                        <defs>
                            <filter id="filter0_b_572_4757" x="-35.3" y="-35.3" width="250.026" height="209.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feGaussianBlur in="BackgroundImageFix" stdDeviation="17.65"/>
                                <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_572_4757"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_572_4757" result="shape"/>
                            </filter>
                        </defs>
                    </svg>
                    <div class="w-[128px] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm leading-[24.5px] text-white">
                        {if $isCashbackAllowed}
                            {_newFront.popups.exit.text |noescape}
                        {else}
                            {_newFront.popups.exit.textNonCashback |noescape}
                        {/if}
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-center py-5">
                <div class="flex-grow border-t border-light-4"></div>
                <div class="px-2 text-dark-2 text-sm leading-[24.5px]">{_'newFront.popups.exit.or'}</div>
                <div class="flex-grow border-t border-light-4"></div>
            </div>

            <div class="text-sm text-dark-1">
                {control socialLoginButtons "my-signin4"}
            </div>

            <div class="text-center mt-5 text-xs text-dark-3 font-normal leading-[21px] px-[22px]">
                <span class="aq-popup-form__condition">{_'front.aqPopup.form.acceptAllConditions', [condition => $presenter->link(':NewFront:Static:conditions'), privacy => $presenter->link(':NewFront:Static:privacyPolicy')]|noescape}</span>
            </div>

            <div class="text-center text-xs pb-2.5 mt-2.5 text-dark-2 leading-[21px]">
                {_'newFront.popups.exit.footer'} <a href="{plink :NewFront:Sign:in}" class="text-primary-orange cursor-pointer hover:underline">
                    {_'newFront.popups.exit.signIn'}
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // REDIRECT NOT LOGGED POPUP 2 STEP
    var redirectNotLoggedSecondPopup = document.getElementById("redirect-not-logged-second-popup");
    var redirectNotLoggedSecondPopupBtn = document.getElementById("open-redirect-not-logged-second-popup");
    var closeRedirectNotLoggedSecondPopupBtn = document.querySelector(".redirect-not-logged-second-popup-close");

    if (redirectNotLoggedSecondPopupBtn) {
        redirectNotLoggedSecondPopupBtn.onclick = function() {
            redirectNotLoggedSecondPopup.style.display = "flex";
        }
    }

	if (closeRedirectNotLoggedSecondPopupBtn) {
		closeRedirectNotLoggedSecondPopupBtn.onclick = function() {
			redirectNotLoggedSecondPopup.style.display = "none";
		}
	}
</script>
