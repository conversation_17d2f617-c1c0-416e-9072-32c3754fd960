<div id="redirect-logged-popup-1" class="fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
	<div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
		<div class="py-[35px] rounded-t-2xl bg-light-6 relative">
			<div class="redirect-logged-popup-1-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
				<img src="{$basePath}/new-design/close-btn.svg" alt="close">
			</div>
			<div class="text-center md:text-xl leading-[24.5px] md:leading-[35px] text-dark-1 px-5 md:px-[84px]">
				{_'newFront.popups.redirect.title', [shop => $shop->getName()]|noescape}
			</div>
		</div>

		<div>
			<div class="flex flex-col pt-[30px] pb-[35px] pl-5 md:px-[73px]">
				<div class="flex items-center">
					<div class="relative">
						<div class="absolute flex justify-center items-center w-[88px] h-[88px] top-[7px] right-[7px]">
							<img src="{$shop->getCurrentLogo() |image:170,0}" alt="{$shop->getName()}" class="max-w-16 h-auto">
						</div>

						<svg width="116" height="97" viewBox="0 0 116 97" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path opacity="0.15" d="M11.7518 76.7746V28.9437L14.9027 34.5211L2.55474 40.5211L0 29.3662L16.691 21H24.3552V76.7746H11.7518ZM1.36253 81V69.5916H35V81H1.36253Z" fill="#66B940"/>
						<g filter="url(#filter0_b_614_4300)">
						<rect x="20" y="5" width="92" height="92" rx="12" fill="white" fill-opacity="0.5"/>
						<rect x="20.5" y="5.5" width="91" height="91" rx="11.5" stroke="#66B940"/>
						</g>
						<circle cx="106" cy="10" r="10" fill="#66B940"/>
						<path d="M102.666 10.5061L104.793 12.5002L109.333 8.3335" stroke="white" stroke-width="2" stroke-linecap="round"/>
						<defs>
						<filter id="filter0_b_614_4300" x="14" y="-1" width="104" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
						<feFlood flood-opacity="0" result="BackgroundImageFix"/>
						<feGaussianBlur in="BackgroundImageFix" stdDeviation="3"/>
						<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_614_4300"/>
						<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_614_4300" result="shape"/>
						</filter>
						</defs>
						</svg>
					</div>
					<div class="flex flex-col ml-4">
						<div class="text-dark-1 leading-7 ">
							{_newFront.popups.redirect.steps.step1, ['shop' => $shop->getName()] |noescape}
						</div>
					</div>
				</div>

				<svg class="my-[9px] ml-[62px]" xmlns="http://www.w3.org/2000/svg" width="8" height="32" viewBox="0 0 8 32" fill="none">
					<path d="M5.45394 1.20962C5.56971 0.958918 5.46032 0.661833 5.20962 0.546062C4.95892 0.430292 4.66183 0.539677 4.54606 0.79038L5.45394 1.20962ZM4.82218 31.4673C5.08027 31.5655 5.3691 31.4359 5.46731 31.1778L7.06768 26.972C7.16588 26.7139 7.03627 26.4251 6.77818 26.3269C6.52009 26.2287 6.23126 26.3583 6.13305 26.6164L4.7105 30.3549L0.972008 28.9323C0.713918 28.8341 0.425083 28.9637 0.326877 29.2218C0.228671 29.4799 0.358282 29.7687 0.616371 29.8669L4.82218 31.4673ZM4.54606 0.79038C1.89412 6.53322 0.514371 11.1069 0.500112 15.7807C0.485857 20.4534 1.83661 25.1718 4.54382 31.2047L5.45617 30.7953C2.77443 24.8191 1.48648 20.2516 1.50011 15.7837C1.51374 11.3169 2.82847 6.89514 5.45394 1.20962L4.54606 0.79038Z" fill="#CCD0D7"></path>
				</svg>

				<div class="flex items-center">
					<div>
						<svg xmlns="http://www.w3.org/2000/svg" width="112" height="92" viewBox="0 0 112 92" fill="none">
							<path opacity="0.15" d="M3.20766 76L0.260081 69.0256L18.0323 49.783C19.0726 48.7746 20.1418 47.5982 21.2399 46.2538C22.3958 44.9093 23.494 43.5368 24.5343 42.1364C25.5746 40.6798 26.4126 39.2794 27.0484 37.9349C27.6841 36.5904 28.002 35.358 28.002 34.2376C28.002 32.8932 27.713 31.7448 27.1351 30.7924C26.6149 29.7841 25.8058 29.0278 24.7077 28.5237C23.6095 27.9635 22.2513 27.6834 20.6331 27.6834C19.1882 27.6834 17.8011 28.0755 16.4718 28.8598C15.1425 29.588 13.8999 30.6804 12.744 32.1369C11.588 33.5934 10.5766 35.386 9.70968 37.5148L0 32.1369C1.15591 28.8878 2.80309 26.0588 4.94153 23.65C7.07997 21.1851 9.62298 19.2805 12.5706 17.936C15.5181 16.5916 18.668 15.9473 22.0202 16.0034C25.5457 16.0034 28.6956 16.7316 31.4698 18.1881C34.3018 19.5886 36.5269 21.5493 38.1452 24.0701C39.7634 26.591 40.5726 29.504 40.5726 32.8091C40.5726 34.3777 40.3414 36.0022 39.879 37.6828C39.4745 39.3074 38.7809 41.044 37.7984 42.8926C36.8159 44.6852 35.5444 46.6179 33.9839 48.6906C32.4234 50.7633 30.545 52.9761 28.3488 55.3289L14.7379 69.6978L13.004 64.6561H43V76H3.20766Z" fill="url(#paint0_linear_572_5666)"/>
							<g filter="url(#filter0_b_572_5666)">
								<rect x="20" width="92" height="92" rx="12" fill="white" fill-opacity="0.5"/>
								<rect x="20.5" y="0.5" width="91" height="91" rx="11.5" stroke="#E1E4E8"/>
							</g>
							<path d="M47.6016 27.5996H50.8772C52.9104 27.5996 54.5105 29.3108 54.3411 31.2796L52.7786 49.606C52.515 52.6052 54.9435 55.1812 58.0309 55.1812H78.0801C80.791 55.1812 83.163 53.01 83.3701 50.3788L84.3867 36.5788C84.6126 33.5244 82.2406 31.0404 79.0967 31.0404H54.7929" stroke="url(#paint1_linear_572_5666)" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M74.4274 64.3998C75.727 64.3998 76.7806 63.3701 76.7806 62.0998C76.7806 60.8295 75.727 59.7998 74.4274 59.7998C73.1278 59.7998 72.0742 60.8295 72.0742 62.0998C72.0742 63.3701 73.1278 64.3998 74.4274 64.3998Z" stroke="url(#paint2_linear_572_5666)" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M59.3688 64.3998C60.6685 64.3998 61.722 63.3701 61.722 62.0998C61.722 60.8295 60.6685 59.7998 59.3688 59.7998C58.0692 59.7998 57.0156 60.8295 57.0156 62.0998C57.0156 63.3701 58.0692 64.3998 59.3688 64.3998Z" stroke="url(#paint3_linear_572_5666)" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M60.7812 38.6396H83.3719" stroke="url(#paint4_linear_572_5666)" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<defs>
								<filter id="filter0_b_572_5666" x="14" y="-6" width="104" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
									<feFlood flood-opacity="0" result="BackgroundImageFix"/>
									<feGaussianBlur in="BackgroundImageFix" stdDeviation="3"/>
									<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_572_5666"/>
									<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_572_5666" result="shape"/>
								</filter>
								<linearGradient id="paint0_linear_572_5666" x1="1.38718e-06" y1="76" x2="48.3475" y2="47.8848" gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A"/>
									<stop offset="1" stop-color="#FFA439"/>
								</linearGradient>
								<linearGradient id="paint1_linear_572_5666" x1="47.6016" y1="55.1812" x2="73.0927" y2="27.5838" gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A"/>
									<stop offset="1" stop-color="#FFA439"/>
								</linearGradient>
								<linearGradient id="paint2_linear_572_5666" x1="72.0742" y1="64.3998" x2="76.2662" y2="60.9197" gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A"/>
									<stop offset="1" stop-color="#FFA439"/>
								</linearGradient>
								<linearGradient id="paint3_linear_572_5666" x1="57.0156" y1="64.3998" x2="61.2076" y2="60.9197" gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A"/>
									<stop offset="1" stop-color="#FFA439"/>
								</linearGradient>
								<linearGradient id="paint4_linear_572_5666" x1="60.7813" y1="39.6396" x2="60.8821" y2="37.7909" gradientUnits="userSpaceOnUse">
									<stop stop-color="#EF7F1A"/>
									<stop offset="1" stop-color="#FFA439"/>
								</linearGradient>
							</defs>
						</svg>
					</div>

					<div class="flex flex-col ml-4">
						<div class="text-dark-1 leading-7 ">
							{_newFront.popups.redirect.steps.step2, ['shop' => $shop->getName()] |noescape}
						</div>
					</div>
				</div>

				<svg class="my-[9px] ml-[62px]" xmlns="http://www.w3.org/2000/svg" width="8" height="32" viewBox="0 0 8 32" fill="none">
					<path d="M2.54606 1.20962C2.43029 0.958917 2.53968 0.661832 2.79038 0.546062C3.04108 0.430292 3.33817 0.539677 3.45394 0.79038L2.54606 1.20962ZM3.17782 31.4673C2.91973 31.5655 2.63089 31.4359 2.53269 31.1778L0.932322 26.972C0.834116 26.7139 0.963726 26.4251 1.22182 26.3269C1.47991 26.2287 1.76874 26.3583 1.86695 26.6164L3.28949 30.3549L7.02799 28.9323C7.28608 28.8341 7.57491 28.9637 7.67312 29.2218C7.77133 29.4799 7.64172 29.7687 7.38363 29.8669L3.17782 31.4673ZM3.45394 0.79038C6.10588 6.53323 7.48563 11.1069 7.49989 15.7807C7.51414 20.4534 6.16338 25.1718 3.45617 31.2047L2.54382 30.7953C5.22557 24.8191 6.51352 20.2516 6.49989 15.7837C6.48626 11.3169 5.17153 6.89514 2.54606 1.20962L3.45394 0.79038Z" fill="#CCD0D7"></path>
				</svg>

				<div class="flex items-center">
					<div class="flex flex-shrink-0">
						<img class="m-auto w-[112px]" src="{$basePath}/new-design/hp-third-step.svg" alt="third-step">
					</div>

					<div class="flex flex-col ml-4">
						<div class="text-dark-1 leading-7">
							{_newFront.popups.redirect.steps.step3, ['shop' => $shop->getName()] |noescape}
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="pb-2.5 px-2.5 relative">
			<div class="text-sm font-medium leading-[24.5px] text-primary-orange py-[15px] px-[50px] bg-pastel-orange-light rounded-lg ">
				{_newFront.popups.redirect.footer |noescape}
			</div>
			<img class="hidden md:block absolute bottom-[-7px] right-[-230px] z-10" src="{$basePath}/new-design/lying-donkey.png" alt="donkey">
		</div>
	</div>
</div>

<script>
	// REDIRECT LOGGED POPUP 1 STEP
	var redirectLoggedPopup1 = document.getElementById("redirect-logged-popup-1");
	var redirectLoggedPopup1Btn = document.getElementById("open-redirect-logged-popup-1");
	var closeRedirectLoggedPopup1Btn = document.querySelector(".redirect-logged-popup-1-close");

	if (redirectLoggedPopup1Btn) {
		redirectLoggedPopup1Btn.onclick = function() {
			redirectLoggedPopup1.style.display = "flex";
		}
	}

	closeRedirectLoggedPopup1Btn.onclick = function() {
		redirectLoggedPopup1.style.display = "none";
	}
</script>
