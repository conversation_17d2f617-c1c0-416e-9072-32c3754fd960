<?php

namespace tipli\NewFrontModule\ShopsModule\Presenters;

use DateTime;
use Nette\Application\AbortException;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;
use Nette\Application\UI\Form;
use Nette\Security\IIdentity;
use Nette\Utils\Strings;
use tipli\FrontModule\Forms\EmailSignUpControl;
use tipli\Model\Account\Entities\FavoriteShop;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\FavoriteShopFacade;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Layers\RedirectionLayer;
use tipli\Model\Layers\SuspectedRequestDetector;
use tipli\Model\Queues\QueueFacade;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Shops\AsyncRedirectionPlan;
use tipli\Model\Shops\Entities\Redirection;
use tipli\Model\Shops\Events\HomecreditUserRedirectedToShopEvent;
use tipli\Model\Shops\Events\RondoUserRedirectedToShopEvent;
use tipli\Model\Shops\Events\TwistoUserRedirectedToShopEvent;
use tipli\Model\Shops\Events\UserRedirectedToBannerEvent;
use tipli\Model\Shops\Events\UserRedirectedToDealEvent;
use tipli\Model\Shops\Events\UserRedirectedToShopEvent;
use tipli\Model\Account\UserFacade;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\HomeCredit\HomeCreditFacade;
use tipli\Model\Layers\ClientLayer;
use tipli\Model\Marketing\BannerFacade;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Products\Entities\Product;
use tipli\Model\Products2\Entities\Product as Product2;
use tipli\Model\Products\ProductObject;
use tipli\Model\Rondo\RondoFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\Producers\RedirectionsProducer;
use tipli\Model\Shops\Redirection\RedirectionManager;
use tipli\Model\Shops\RedirectionPlan;
use tipli\NewFrontModule\Presenters\BasePresenter;
use Tracy\Debugger;

class RedirectionPresenter extends BasePresenter
{
	public const PARTNER_TWISTO = 'twisto';
	public const PARTNER_RONDO = 'rondo';
	public const PARTNER_HOMECREDIT = 'homecredit';
	public const PARTNER_HOMECREDIT_TEST = 'homecredit_test';

	public const SOURCE_RONDO = 'rondo';
	public const SOURCE_HOME_CREDIT = 'homecredit';

	/** @var RedirectionManager @inject */
	public $redirectionManager;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var BannerFacade @inject */
	public $bannerFacade;

	/** @var RondoFacade @inject */
	public $rondoFacade;

	/** @var HomeCreditFacade @inject */
	public $homeCreditFacade;

	/** @var FavoriteShopFacade @inject */
	public FavoriteShopFacade $favoriteShopFacade;

	/** @var Shop @persistent */
	public $shop;

	/** @var string @persistent */
	public $deepUrl;

	/** @var string|null @persistent */
	public ?string $source = null;

	/** @var ClientLayer @inject */
	public $clientLayer;

	/** @var EventDispatcher @inject */
	public $eventDispatcher;

	/** @var DatadogProducer @inject */
	public $datadogProducer;

	/** @var QueueFacade @inject */
	public $queueFacade;

	/** @var SuspectedRequestDetector @inject */
	public $suspectedRequestDetector;

	/** @var RedirectionLayer @inject */
	public $redirectionLayer;

	/** @var RedirectionsProducer @inject */
	public $redirectionsProducer;

	/** @var SqlQueryScheduler @inject */
	public $sqlQueryScheduler;

	/** @var OfferFacade @inject */
	public OfferFacade $offerFacade;

	public function startup()
	{
		parent::startup();

		$platform = $this->getParameter('_platform');
		if ($platform && in_array($platform, ClientLayer::getPlatforms(), true)) {
			$this->clientLayer->setManualPlatform($platform);
		}

		if ($this->getParameter('debug')) {
			Debugger::log(json_encode($this->getHttpRequest()->getHeaders()), 'redirect-headers');
			Debugger::log(json_encode($this->getUser()->isLoggedIn() ? $this->getUser()->getId() : 0), 'redirect-user');
		}

//		Debugger::log($this->clientLayer->getIp() . ' ## ' . $this->clientLayer->getUserAgent(), 'shop-detail-redirections');
	}

	public function renderExit(Redirection $redirection, $secureHash)
	{
		$validated = true;

		if ($redirection->getSecureHash() !== $secureHash) {
			$validated = false;

			Debugger::log('invalid hash ' . $redirection->getId() . ', IP: ' . $this->clientLayer->getIp(), 'exitRedirection-logs');
			$this->datadogProducer->scheduleSendEvent('exitRedirection.invalid.hash');
		}

		if ((new DateTime())->modify('- 24 hours') > $redirection->getCreatedAt()) {
			$validated = false;

			Debugger::log('expired ' . $redirection->getId() . ', IP: ' . $this->clientLayer->getIp(), 'exitRedirection-logs');
			$this->datadogProducer->scheduleSendEvent('exitRedirection.invalid.expired');
		}

		if (!$validated) {
			$this->error();
		}

		$this->template->redirection = $redirection;
	}

	public function renderSyncExit(int $syncId, string $secureHash)
	{
		/** @var AsyncRedirectionPlan|null $asyncRedirection */
		$asyncRedirectionPlan = $this->redirectionLayer->loadRedirectionAsyncPlan($syncId, $secureHash);

		if ($asyncRedirectionPlan === null) {
			bdump('asyncRedirectionPlan IS NULL');
			Debugger::log(' asyncRedirectionPlan is null, ' . $syncId, 'redirection-sync-errors');
			$this->redirect(':NewFront:Homepage:default');
		}

		if ($asyncRedirectionPlan->getRedirectionSecureHash() !== $secureHash) {
			Debugger::log(' asyncRedirectionPlan have invalid secure hash, ' . $syncId, 'redirection-sync-errors');
			bdump('invalid secure hash');
			$this->redirect(':NewFront:Homepage:default');
		}

		$this->template->redirectionLink = $asyncRedirectionPlan->getRedirectionLink();
	}

	/**
	 * @param Shop $shop
	 * @param string|null $deepUrl
	 * @param string|null $userId
	 */
	public function actionShop(Shop $shop, $deepUrl = null, $userId = null, $source = null, bool $forceRedirect = false)
	{
		$user = $this->getUserIdentity();

		if ((!$user || !$user->isAdmin()) && (!$shop->isPublished() || !$shop->isActive())) {
			$this->redirectPermanent(':NewFront:Shops:Shops:default');
		}

		if (!$this->getUser()->isLoggedIn() && !$userId) {
			$this->redirectPermanent(':NewFront:Shops:Shop:default', [
				'shop' => $shop,
				'aqPopup-type' => 'shopRedirection',
				'aqPopup-shopId' => $shop->getId(),
				'do' => 'aqPopup-open',
			]);
		}

		if ($shop->getShopSettings()->isAddonIgnoreDeepUrl() === true && $this->utmLayer->getUtm()->getUtmSource() === 'addon') {
			$deepUrl = null;
		}

		if ($this->getParameter('ref') === 'friend') {
			Debugger::log('friend-referral', 'shop-redirection');
		}

		$this->template->forceRedirect = $forceRedirect;

		$showNonProfitMessage = $shop->isNonProfitShop();

		$isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID], true);
		$isMobileWeb = $this->clientLayer->isMobile();

		if (
			$this->getUser()->isLoggedIn() === false
			&& $isMobileApp === false
			&& $isMobileWeb === false
		) {
			if ($this->suspectedRequestDetector->isRequestSuspected(SuspectedRequestDetector::REQUEST_SHOP_REDIRECTION, 30, false)) {
				$recaptchaBackLink = $this->link('//this');
				$recaptchaToken = Strings::substring(sha1('osel' . $recaptchaBackLink), 0, 16);

				Debugger::log($this->clientLayer->getIp(), 'redirection-recaptcha-activated');
				$this->redirect('recaptcha', ['backLink' => $recaptchaBackLink, 'token' => $recaptchaToken, 's' => $shop->getSlug()]);
			} else {
				$this->suspectedRequestDetector->trackRequest(SuspectedRequestDetector::REQUEST_SHOP_REDIRECTION);
			}
		}

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($shop, $this->getUserFromRequest(), null, null, $deepUrl);

		if ($user !== null || ($userId !== null && $this->getUserFromRequest() !== null)) {
			$this->favoriteShopFacade->scheduleFavoriteShop($this->getUserFromRequest(), $shop, FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());

			$this->sqlQueryScheduler->scheduleUpdateLastShopRedirectionAt($this->getUserFromRequest()->getId(), new DateTime());
		}

		if (
			(!$shop->isCashbackAllowed() && !$showNonProfitMessage)
			|| (!$this->getUser()->isLoggedIn() && !$isMobileApp && !$userId)
		) {
			$this->redirectUrl($redirectionPlan->getRedirectionLink());
		}

		$this->eventLayer->trackGoogleAnalyticsRedirection();
		$this->eventLayer->trackFacebookRedirection();

		$this->template->isMobileApp = $isMobileApp;
		$this->template->isMobileWeb = $isMobileWeb;
		$this->template->shop = $shop;
		$this->template->offers = $this->offerFacade->resolveOffersForShop($shop);
		$this->template->redirectionId = $redirectionPlan instanceof RedirectionPlan ? $redirectionPlan->getRedirectionId() : $redirectionPlan->getRedirectionSyncId();
		$this->template->redirectionSecureHash = $redirectionPlan->getRedirectionSecureHash();

		// Invia upozorneni pro uzivatele z addonu
		$this->template->showNonProfitMessage = $showNonProfitMessage;

		if ($redirectionPlan instanceof RedirectionPlan) {
			$this->template->redirectionLink = $this->link('exit', [
				'redirection' => $this->redirectionManager->find($redirectionPlan->getRedirectionId()),
				'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				'sessionActivated' => 1,
			]);

			$this->eventDispatcher->dispatch(
				new UserRedirectedToShopEvent($user, $redirectionPlan->getRedirectionId())
			);
		} else {
			$this->redirectionLayer->saveRedirection($redirectionPlan);

			$this->template->redirectionLink = $this->link('syncExit', [
				'syncId' => $redirectionPlan->getRedirectionSyncId(),
				'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
			]);

			$this->eventDispatcher->dispatch(
				new UserRedirectedToShopEvent($user, null, $redirectionPlan->getRedirectionSyncId())
			);
		}

		$this->template->usingExitPage = true;
	}

	public function actionAdBlock(Shop $shop): void
	{
		$this->template->shop = $shop;
	}

	public function actionRecaptcha(string $backLink, string $token): void
	{
		if ($token !== Strings::substring(sha1('osel' . $backLink), 0, 16)) {
			$this->error();
		}
	}

	public function actionShopDirect(Shop $shop, $userId, $deepUrl, $spinner = false)
	{
		if (!$shop->isActive()) {
			$this->redirect('Shop:shop', $shop);
		}

		$user = $this->getUserFromRequest(true);

		$this->eventDispatcher->dispatch(
			new UserRedirectedToShopEvent()
		);

		if ($user->getPartnerOrganization() && $user->getPartnerOrganization()->getSlug() == 'twisto') {
			if ($user->isBanned() || $user->isInactiveUser() || $user->isRemoved()) {
				if ($user->isCzech()) {
					$this->redirectUrl('https://www.twisto.cz/kontakt/');
				} elseif ($user->isPolish()) {
					$this->redirectUrl('https://www.twisto.pl/poznaj/kontakt/');
				} else {
					$this->redirectUrl('https://www.twisto.' . $user->getLocalization()->getDomain());
				}
			}

			// todo: dát pryč updateShopSettings210220
			if (in_array($shop->getId(), [757, 745, 3372, 973, 4730])) {
				$this->redirectUrl('https://www.twisto.pl/');
			}

			if (!$spinner) {
				if (in_array($shop->getId(), [2000, 3771])) {
					$this->redirectUrl('https://www.twisto.cz/');
				}
			}

			if ($shop->getShopSettings()->isTwistoDisabled()) {
				$this->redirectUrl('https://www.twisto.' . $user->getLocalization()->getDomain());
			}

			$this->eventDispatcher->dispatch(
				new TwistoUserRedirectedToShopEvent()
			);

			Debugger::log('twisto ### ' . $shop->getSlug() . ' ### ' . $user->getId() . ' ### ' . 0, '___old-partner-redirection');
		}

		if ($user !== null) {
			$this->favoriteShopFacade->scheduleFavoriteShop($this->getUserFromRequest(), $shop, FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());

			$this->sqlQueryScheduler->scheduleUpdateLastShopRedirectionAt($user, new DateTime());
		}

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($shop, $user, null, null, null, null);

		if ($spinner) {
			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
				$this->redirectionLayer->saveRedirection($redirectionPlan);

				$this->redirect('syncExit', [
					'syncId' => $redirectionPlan->getRedirectionSyncId(),
					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				]);
			}

			$this->getHttpResponse()->setHeader('refresh', '2;url=' . $redirectionPlan->getRedirectionLink());
		} else {
			$this->redirectUrl($redirectionPlan->getRedirectionLink());
		}
	}

	public function actionShopRondoDirect(Shop $shop, $userId, $deepUrl, $subId)
	{
		if (!$shop->isActive()) {
			$this->redirect('Shop:shop', $shop);
		}

		$user = $this->getUserFromRequest(true);
		if ($user->isBanned() || $user->isInactiveUser() || $user->isRemoved()) {
			$this->redirectUrl('https://www.rondo.cz');
		}

		if ($shop->getShopSettings()->isRondoDisabled()) {
			$this->redirectUrl('https://www.rondo.cz');
		}

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($shop, $user, null, null, null, $subId);

		$this->eventDispatcher->dispatch(
			new RondoUserRedirectedToShopEvent()
		);

		Debugger::log('rondo ### ' . $shop->getSlug() . ' ### ' . $user->getId() . ' ### ' . $subId, '___old-partner-redirection');

		$this->redirectUrl($redirectionPlan->getRedirectionLink());
	}

	public function actionShopPartner($partner, Shop $shop, $userId, $deepUrl, $subId = null)
	{
		/** @var User|null $user */
		$user = $this->getUserFromRequest(true);

		if (
			!$shop->isActive()
			|| !$user
			|| !in_array($partner, [self::PARTNER_TWISTO, self::PARTNER_HOMECREDIT, self::PARTNER_HOMECREDIT_TEST, self::PARTNER_RONDO])
		) {
			$this->redirect('Shop:shop', $shop);
		}

		$fallbackUrl = $this->translator->translate('api.partners.fallbackUrl.' . $partner);

		if (
			$user->isBanned()
			|| $user->isInactiveUser()
			|| $user->isRemoved()
			|| !$user->getPartnerOrganization()
			|| $user->getPartnerOrganization()->getSlug() !== $partner
		) {
			$this->redirectUrl($fallbackUrl);
		}
		if (in_array($shop->getId(), [757, 745, 3372, 973, 4730])) { // manually disabled shops
			$this->redirectUrl($fallbackUrl);
		}

		if ($partner === self::PARTNER_HOMECREDIT || $partner === self::PARTNER_HOMECREDIT_TEST) {
			$this->eventDispatcher->dispatch(
				new HomecreditUserRedirectedToShopEvent()
			);
		} elseif ($partner === self::PARTNER_RONDO) {
			$this->eventDispatcher->dispatch(
				new RondoUserRedirectedToShopEvent()
			);
		} elseif ($partner === self::PARTNER_TWISTO) {
			$this->eventDispatcher->dispatch(
				new TwistoUserRedirectedToShopEvent()
			);
		}

		/** @var RedirectionPlan $redirectionPlan */
		$redirectionPlan = $this->redirectionManager->getRedirectionPlan(
			$shop,
			$this->getUserFromRequest(true),
			null,
			null,
			null,
			$subId
		);

		if ($user !== null) {
			$this->favoriteShopFacade->scheduleFavoriteShop($this->getUserFromRequest(), $shop, FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());
		}

		$this->template->redirectionLink = $redirectionPlan->getRedirectionLink();
		$this->template->redirectionSecureHash = $redirectionPlan->getRedirectionSecureHash();

		$this->getHttpResponse()->setHeader('refresh', '2;url=' . $redirectionPlan->getRedirectionLink());

		Debugger::log($partner . ' ### ' . $shop->getSlug() . ' ### ' . $user->getId() . ' ### ' . $subId, '___new-partner-redirection');
	}

	/**
	 * @param Shop $shop
	 * @param string|null $deepUrl
	 */
	private function actionShopUnLogged(Shop $shop, $deepUrl = null)
	{
		$this['emailSignUpControl']['form']->setAction($this->link('//shop', ['shop' => $shop, 'deepUrl' => $deepUrl]));

		$this->template->shop = $shop;
		$this->template->deepUrl = $deepUrl;
		$this->template->unLoggedRedirectionUserId = $this->configuration->getUnLoggedRedirectionUser($this->getLocalization());
	}

	/**
	 * @param Deal $deal
	 */
	public function actionDeal(Deal $deal, $userId = null, $subId = null, $direct = true)
	{
		if (!$direct) {
			$this->setView('shop');
			$this->actionShop($deal->getShop(), $deal->getDeepUrl(), $userId);
			return;
		}

		if ($userId === null && !$this->getUser()->isLoggedIn()) {
			$user = $this->userFacade->find($this->configuration->getUnLoggedRedirectionUser($this->getLocalization()));
		} else {
			$user = $this->getUserFromRequest();
		}

		if ($user !== null) {
			$this->favoriteShopFacade->scheduleFavoriteShop($user, $deal->getShop(), FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());

			$this->sqlQueryScheduler->scheduleUpdateLastShopRedirectionAt($user->getId(), new DateTime());
		}

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($deal->getShop() ? : null, $user, $deal, null, $deal->getDeepUrl(), $subId);

		$this->eventDispatcher->dispatch(new UserRedirectedToDealEvent());

		if ($userId === null && !$this->getUser()->isLoggedIn()) {
			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
				$this->redirectionLayer->saveRedirection($redirectionPlan);

				$this->redirect('syncExit', [
					'syncId' => $redirectionPlan->getRedirectionSyncId(),
					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				]);
			} else {
				$this->template->isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID]);
				$this->template->redirectionLink = $redirectionPlan->getRedirectionLink();
				$this->template->redirectionId = $redirectionPlan->getRedirectionId();
				$this->setView('dealSpinner');
			}
		} else {
			$this->redirectUrl($redirectionPlan->getRedirectionLink());
		}
	}

	public function actionDealOld(Deal $deal, $userId = null, $subId = null)
	{
		$this->redirectPermanent('deal', $this->getParameters());
	}

	/**
	 * @param Product|ProductObject $product
	 */
	public function actionProduct($product)
	{
		$user = $this->isUserLoggedIn() ? $this->getUserIdentity() : $this->userFacade->find($this->configuration->getUnLoggedRedirectionUser($this->getLocalization()));

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($product->getShop(), $user, null, null, $product->getDeepUrl(), null);

		if (!$this->getUser()->isLoggedIn()) {
			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
				$this->redirectionLayer->saveRedirection($redirectionPlan);

				$this->redirect('syncExit', [
					'syncId' => $redirectionPlan->getRedirectionSyncId(),
					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				]);
			} else {
				$this->template->isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID]);
				$this->template->redirectionLink = $redirectionPlan->getRedirectionLink();
				$this->template->redirectionId = $redirectionPlan->getRedirectionId();
				$this->setView('dealSpinner');
			}
		} else {
			if ($user !== null) {
				$this->favoriteShopFacade->scheduleFavoriteShop($this->getUserFromRequest(), $product->getShop(), FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());
			}

			$this->redirectUrl($redirectionPlan->getRedirectionLink());
		}
	}

	/**
	 * @param Product2 $product2
	 */
	public function actionProduct2(Product2 $product2)
	{
		$user = $this->isUserLoggedIn() ? $this->getUserIdentity() : $this->userFacade->find($this->configuration->getUnLoggedRedirectionUser($this->getLocalization()));

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($product2->getShop(), $user, null, null, $product2->getUrl(), null);

		if (!$this->getUser()->isLoggedIn()) {
			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
				$this->redirectionLayer->saveRedirection($redirectionPlan);

				$this->redirect('syncExit', [
					'syncId' => $redirectionPlan->getRedirectionSyncId(),
					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				]);
			} else {
				$this->template->isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID]);
				$this->template->redirectionLink = $redirectionPlan->getRedirectionLink();
				$this->template->redirectionId = $redirectionPlan->getRedirectionId();
				$this->setView('dealSpinner');
			}
		} else {
			if ($user !== null) {
				$this->favoriteShopFacade->scheduleFavoriteShop($this->getUserFromRequest(), $product2->getShop(), FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());
			}

			$this->redirectUrl($redirectionPlan->getRedirectionLink());
		}
	}

	/**
	 * @param Banner $banner
	 * @param int|null $userId
	 * @param int|null $subId
	 * @param bool $direct
	 * @return void
	 * @throws AbortException
	 */
	public function actionBanner(Banner $banner, int $userId = null, int $subId = null, bool $direct = true): void
	{
		if (!$direct) {
			$this->setView('shop');
			$this->actionShop($banner->getShop(), $banner->getRedirectionLink(), $userId);
			return;
		}

		if ($userId === null && !$this->getUser()->isLoggedIn()) {
			$user = $this->userFacade->find($this->configuration->getUnLoggedRedirectionUser($this->getLocalization()));
		} else {
			$user = $this->getUserFromRequest();
			$platform = $this->clientLayer->getPlatform();
			$this->bannerFacade->trackClick($banner, $this->getUserFromRequest(), $this->source, $platform);
		}

		if ($user !== null && $banner->getShop() !== null) {
			$this->favoriteShopFacade->scheduleFavoriteShop($user, $banner->getShop(), FavoriteShop::SOURCE_SYSTEM, FavoriteShop::REASON_REDIRECTION, new DateTime());

			$this->sqlQueryScheduler->scheduleUpdateLastShopRedirectionAt($user->getId(), new DateTime());
		}

		if (!empty($banner->getLink())) {
			$this->redirectUrl($banner->getLink());
		}

		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($banner->getShop() ? : null, $user, null, $banner, $banner->getRedirectionLink(), $subId);

		$this->eventDispatcher->dispatch(new UserRedirectedToBannerEvent());

		$this->template->shop = $banner->getShop();

		if ($banner->getShop()) {
			$this->template->offers = $this->offerFacade->resolveOffersForShop($banner->getShop());
		}

		$this->template->showNonProfitMessage = $banner->getShop()->isNonProfitShop();
		$this->template->redirectionId = $redirectionPlan instanceof RedirectionPlan ? $redirectionPlan->getRedirectionId() : $redirectionPlan->getRedirectionSyncId();
		$this->template->isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID]);
		$this->template->isMobileWeb = $this->clientLayer->isMobile();

		if ($redirectionPlan instanceof RedirectionPlan) {
			$this->template->redirectionLink = $this->link('exit', [
				'redirection' => $this->redirectionManager->find($redirectionPlan->getRedirectionId()),
				'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
				'sessionActivated' => 1,
			]);

			$this->eventDispatcher->dispatch(
				new UserRedirectedToShopEvent($user, $redirectionPlan->getRedirectionId())
			);
		} else {
			$this->redirectionLayer->saveRedirection($redirectionPlan);

			$this->template->redirectionLink = $this->link('syncExit', [
				'syncId' => $redirectionPlan->getRedirectionSyncId(),
				'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
			]);

			$this->eventDispatcher->dispatch(
				new UserRedirectedToShopEvent($user, null, $redirectionPlan->getRedirectionSyncId())
			);
		}

//		if ($userId === null && !$this->getUser()->isLoggedIn()) {
//			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
//				$this->redirectionLayer->saveRedirection($redirectionPlan);
//
//				$this->redirect('syncExit', [
//					'syncId' => $redirectionPlan->getRedirectionSyncId(),
//					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
//				]);
//			} else {
//				$this->template->isMobileApp = in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID]);
//				$this->template->redirectionLink = $redirectionPlan->getRedirectionLink();
//				$this->template->redirectionId = $redirectionPlan->getRedirectionId();
//				$this->setView('dealSpinner');
//			}
//		} else {
//			if ($redirectionPlan instanceof AsyncRedirectionPlan) {
//				$this->redirectionLayer->saveRedirection($redirectionPlan);
//
//				$this->template->redirectionLink = $this->link('syncExit', [
//					'syncId' => $redirectionPlan->getRedirectionSyncId(),
//					'secureHash' => $redirectionPlan->getRedirectionSecureHash(),
//				]);
//
//				$this->eventDispatcher->dispatch(
//					new UserRedirectedToShopEvent($user, null, $redirectionPlan->getRedirectionSyncId())
//				);
//			} else {
//				$this->redirectUrl($redirectionPlan->getRedirectionLink());
//			}
//		}
	}

	/**
	 * @param bool $ignoreLoggedEntity
	 * @return User|IIdentity|null
	 */
	private function getUserFromRequest($ignoreLoggedEntity = false)
	{
		// logged users
		if (!$ignoreLoggedEntity && $this->getUser()->isLoggedIn()) {
			return $this->getUserIdentity();
		}

		// user by subId parameter (rondo, etc)
		if (($this->getParameter('source') === self::SOURCE_RONDO || $this->getParameter('partner') === self::PARTNER_RONDO) && $this->getParameter('subId')) {
			return $this->rondoFacade->getUserBySubId($this->getLocalization(), $this->getParameter('subId'));
		}

		if (
			($this->getParameter('source') === self::SOURCE_HOME_CREDIT
				|| $this->getParameter('partner') === self::PARTNER_HOMECREDIT
				|| $this->getParameter('partner') === self::PARTNER_HOMECREDIT_TEST
			) && $this->getParameter('subId')
		) {
			return $this->homeCreditFacade->getUserBySubId($this->getLocalization(), $this->getParameter('subId'), $this->getParameter('partner') === self::PARTNER_HOMECREDIT_TEST);
		}

		// user by userId parameter
		$userId = $this->getParameter('userId');
		if (empty($userId)) {
			$this->error(!'Chybí označující identifikátor uživatele.');
		}

		$user = $this->userFacade->find($userId);
		if ($user && $this->localizationFacade->getCurrentLocalization()->getId() != $user->getLocalization()->getId()) {
			$this->error('Uživatel má nekompatibilní lokalizaci.');
		}

		if (empty($user)) {
			$this->error('Uživatel bohužel nebyl nalezen.');
		}

		return $user;
	}

	/**
	 * @return EmailSignUpControl
	 */
	protected function createComponentEmailSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function () {
			$this->afterSignUp();
		};

		return $control;
	}

	public function actionSetAdblockUsed()
	{
		if (in_array($this->clientLayer->getPlatform(), [ClientLayer::PLATFORM_IOS, ClientLayer::PLATFORM_ANDROID], true)) {
			return;
		}

		$redirectionId = $this->getRequest()->getPost('redirectionId');

		if ($redirectionId === null) {
			return;
		}

		$adblockUsed = $this->getRequest()->getPost('adblockUsed') ? true : false;

		if ($redirectionId < 70000000) {
			$redirection = $this->redirectionManager->find($redirectionId);

			if ($redirection && $redirection->getCreatedAt()->diff(new DateTime())->s < 10) {
				$this->queueFacade->scheduleCreateSqlQuery(
					'UPDATE tipli_shops_redirection SET adblock_used = ? WHERE id = ?',
					[
						$adblockUsed ? 1 : 0,
						$redirectionId,
					]
				);
//				$this->redirectionManager->setAdblockUsed($redirectionId, $adblockUsed);
			}
		} else {
			$this->redirectionsProducer->scheduleUpdateRedirection(
				$redirectionId,
				$adblockUsed
			);
		}

		$this->terminate();
	}

	public function createComponentRecaptchaControl()
	{
		$backLink = $this->getParameter('backLink');

		$form = new Form();

		$form->addReCaptcha('recaptcha')
				->setRequired('front.form.validator.recaptchaRequired');

		$form->addSubmit('submit', 'front.sign.in.form.submit');

		$form->onSuccess[] = function (Form $form) use ($backLink): void {
			$this->suspectedRequestDetector->releaseLimitForRequest(SuspectedRequestDetector::REQUEST_SHOP_REDIRECTION);

			$this->presenter->redirectUrl($backLink);
		};

		return $form;
	}

	public function actionShopSpinner(Shop $shop, $userId)
	{
		Debugger::log('shop spinner', 'redirectionPresenter');
		$this->actionShopDirect($shop, $userId, null, true);
	}

//	public function actionShopHomeCreditDirect(Shop $shop, $userId, $deepUrl, $subId)
//	{
//		if (!$shop->isActive()) {
//			$this->redirect('Shop:shop', $shop);
//		}
//
//		$user = $this->getUserFromRequest(true);
//		if ($user->isBanned() || $user->isInactiveUser() || $user->isRemoved()) {
//			$this->redirectUrl('https://www.homecredit.cz');
//		}
//
//		if ($shop->getShopSettings()->isHomecreditDisabled()) {
//			$this->redirectUrl('https://www.homecredit.cz');
//		}
//
//		$redirectionPlan = $this->redirectionManager->getRedirectionPlan($shop, $this->getUserFromRequest(true), null, null, $subId);
//
//		$this->eventDispatcher->dispatch(
//			new HomecreditUserRedirectedToShopEvent()
//		);
//
//		Debugger::log('homecredit ### ' . $shop->getSlug() . ' ### ' . $user->getId() . ' ### ' . $subId, '___old-partner-redirection');
//
//		$this->redirectUrl($redirectionPlan->getRedirectionLink());
//	}
}
