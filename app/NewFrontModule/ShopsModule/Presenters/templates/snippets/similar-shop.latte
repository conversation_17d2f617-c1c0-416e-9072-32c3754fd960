{cache 'similar-shops-'. $currentDealsPageNumber . '-' . $cashbackCacheKeyPostfix, 'expire' => ($cashbackCacheDisabled ? '0 seconds' : '15 minutes'), tags => ['shop/' . $shop->getId()]}
    {var $similarShops = $recommendedShops()}
    {include similarShops, $similarShops}

	{define similarShops, $similarShops}
	  {if !empty($similarShops)}
		<p class="shop-detail__section-title">
			{if $shop->isCashbackActive()}
				{_'front.shops.shop.shop.similiarShopsTitleForCashbackShop'}
			{else}
				{_'front.shops.shop.shop.similiarShopsTitleForNonCashbackShop'}
			{/if}
		</p>
		<ul class="shop-list shop-card--6">
			{foreach $similarShops as $similarShop}
			  <a href="{plink :NewFront:Shops:Shop:default $similarShop}" title="{$similarShop->getName()}" class="shop-card">
			  <span class="shop-card__image">
				<span class="shop-card__table-cell">
				  <img src="{$basePath}/images/pixel.png" data-src="{$similarShop->getCurrentLogo() |image:100,0}" alt="{$similarShop->getName()}" class="unveil" n:if="$similarShop->getCurrentLogo()">
				</span>
			  </span>

				<span class="shop-card__content">
				<p class="shop-card__title">{$similarShop->getName()}</p>
				<span class="cashback-value">{$similarShop |reward:true,'extended' |noescape}</span>
			  </span>
			  </a>
			{/foreach}
		</ul>
	  {/if}
	{/define}
{/cache}
