{layout '@layoutUnlogged.latte'}

{var $variant = $googleExperimentVariantResolver->getExperimentVariant('googleoptimize', 2)->getVariant()}

{block #styles}
  {control cssBuilderControl ['css/redirect/redirect-new.css']}
{/block}

{block #scripts}
    <!-- JS file -->
    {control jsBuilderControl [
        'js/jquery-1.12.0.min.js',
        'js/nette.ajax.js',
		'js/redirect/redirect.js'
    ]}
{/block}

{block content}

<div class="redirect-new aq-popup-bg js-visible">
	<div class="aq-popup aq-popup--medium ">

		<div class="aq-popup__content">
			<img src="{$shop->getCurrentLogo() |image:170}" class="aq-popup__logo" title="{$shop->getName()}" alt="{$shop->getName()}">

			<div class="aq-popup__top aq-popup__bg-white">
				{if $shop->isCashbackAllowed()}
					<h3 class="aq-popup__title">{_'front.aqPopup.redirectionWithCashback.title', [reward => ($shop |reward:pure), shop => $shop->getName()]|noescape}</h3>

					<div class="js-show-section-1 {if $localization->isCzech() && $variant == 1}hide{/if}">
						<p class="aq-popup__text">{_'front.aqPopup.redirectionWithCashback.text'|noescape}</p>
					</div>
				{else}
					<h3 class="aq-popup__title">{_'front.aqPopup.redirectionWithoutCashback.title', [shop => $shop->getName()]|noescape}</h3>

					<div class="js-show-section-1 {if $localization->isCzech() && $variant == 1}hide{/if}">
						<p class="aq-popup__text">{_'front.aqPopup.redirectionWithoutCashback.text'|noescape}</p>
					</div>
				{/if}
			</div>

			{if $localization->isCzech() && $variant == 1}
				{if $shop->isCashbackAllowed()}
					<div class="aq-popup__bottom aq-popup__bg-grey d-flex px-4 js-hide-section-1">
						<a href="{plink :NewFront:Shops:Redirection:shop, shop => $shop, userId => $unLoggedRedirectionUserId}" class="btn btn-grey aq-popup__button mx-2 js-no-reward-button" data-hit="event" data-category="redirectPage" data-action="click" data-label="noReward">{_'front.aqPopup.button.noReward'}</a>
						<a href="" class="btn btn-orange aq-popup__button mx-2 js-show-section" data-section="section-1" data-hit="event" data-category="redirectPage" data-action="click" data-label="getReward">{_'front.aqPopup.button.getReward'}</a>
					</div>
				{else}
					<div class="aq-popup__bottom aq-popup__bg-grey d-flex px-4 js-hide-section-1">
						<a href="{plink :NewFront:Shops:Redirection:shop, shop => $shop, userId => $unLoggedRedirectionUserId}" class="btn btn-grey aq-popup__button mx-2 js-no-reward-button" data-hit="event" data-category="redirectPage" data-action="click" data-label="noReward">{_'front.aqPopup.button.noDealsInfo'}</a>
						<a href="" class="btn btn-orange aq-popup__button mx-2 js-show-section" data-section="section-1" data-hit="event" data-category="redirectPage" data-action="click" data-label="getReward">{_'front.aqPopup.button.getDealsInfo'}</a>
					</div>
				{/if}
			{/if}

			<div class="aq-popup__bottom aq-popup__bg-grey js-show-section-1 {if $localization->isCzech() && $variant == 1}hide{/if}">
				<div class="sign__social-login ">
    				{control socialLoginButtons "my-signin2"}
				</div>

				<span class="aq-popup__or">- {_'front.sign.in.or'} -</span>

				<div class="aq-popup-form">
					{form emailSignUpControl-form}
						<ul class="form-error" n:if="$form->hasErrors()">
							<li n:foreach="$form->errors as $error">{$error |noescape}</li>
						</ul>

						<label for="" class="aq-popup-form__label">{_'front.aqPopup.form.label'}</label>
						<input type="text" class="aq-popup-form__input" name="email" placeholder="{_'front.aqPopup.form.input'}" n:name="email">
						<input type="submit" class="aq-popup-form__submit" value="{_'front.aqPopup.form.submit'}" n:name="submit" data-hit="event" data-category="signUp" data-action="click" data-label="redirectionFormSubmit">
						<span class="aq-popup-form__condition">{_'front.aqPopup.form.acceptAllConditions', [condition => $presenter->link(':NewFront:Static:conditions'), privacy => $presenter->link(':NewFront:Static:privacyPolicy')]|noescape}</span>
					{/form}
				</div>
			</div>
		</div>

		<div class="redirect-new__bottom">
			<p class="redirect-new__text">{_'front.shops.redirectionUnLogged.alreadyRegistered.title'} {_'front.shops.redirectionUnLogged.alreadyRegistered.text', [link => $presenter->link(':NewFront:Sign:in', ['backLink' => $presenter->link('this')])] |noescape}</p>

			<a n:href="shop, shop => $shop, userId => $unLoggedRedirectionUserId" class="redirect-new__no-reward" rel="nofollow" data-hit="event" data-category="redirectionForm" data-action="click" data-label="noReward">{_'front.shops.redirectionUnLogged.continue' |noescape} <i class="fa fa-angle-right" aria-hidden="true"></i></a>
		</div>

	</div>
</div>

