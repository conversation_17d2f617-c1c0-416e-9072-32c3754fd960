{*{layout '../@layout.latte'}*}

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">

    <title>{ifset #title}{include title|striptags} | {/ifset}Tipli</title>
    <meta name="keywords" content="{ifset keywords}{include keywords|striptags}{/ifset}">
    <meta name="description" content="{ifset description}{include description|striptags}{/ifset}">
	<meta name="robots" content="noindex,nofollow" />
    <meta name="author" content="Tipli">

    <link rel="stylesheet" href="{$basePath}/css2/output.css">

    <link rel="shortcut icon" href="{$basePath}/favicon.ico?v1">

    <link rel="canonical" href="{link //this}" />

    <link rel="search" type="application/opensearchdescription+xml" title="Tipli" href="/opensearch.xml">

    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600" rel="stylesheet">

    <script src="{$basePath}/js/jquery-1.12.0.min.js"></script>
    <script src="{$basePath}/js/netteForms.min.js"></script>
    <script src="{$basePath}/js/nette.ajax.js"></script>

    {block #styles}{/block}

    <!-- Viewport for mobile devices -->
    <meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">

    {if $configuration->isTrackingAllowed()}
        {include './../../../../Presenters/templates/pixels/gtm.latte'}
    {/if}

    {if $configuration->isTrackingAllowed()}
        {include './../../../../Presenters/templates/pixels/fbPixel.latte'}
    {/if}

    {if $configuration->isTrackingAllowed() && $localization->isCzech()}
        {include './../../../../Presenters/templates/pixels/sklikRetargeting.latte'}
    {/if}
</head>

<body class="{$locale}" data-basePath="{$basePath}">
    {if $configuration->isTrackingAllowed()}
        {include './../../../../Presenters/templates/pixels/gtmNoScript.latte'}
    {/if}

    {include content}

    {include './../../../../Presenters/templates/pixels/heureka.latte'}
</body>

<style>
    .arrow {
        animation: moveArrow 1.2s ease-in-out infinite;
        animation-fill-mode: both;
    }

    .arrow:nth-child(1) { animation-delay: 0s; }
    .arrow:nth-child(2) { animation-delay: 0.2s; }
    .arrow:nth-child(3) { animation-delay: 0.4s; }
    .arrow:nth-child(4) { animation-delay: 0.6s; }
    .arrow:nth-child(5) { animation-delay: 0.8s; }

    @keyframes moveArrow {
        0% {
            transform: translateX(0);
            opacity: 0;
        }
        20% {
            opacity: 1;
        }
        100% {
            transform: translateX(10px);
            opacity: 0;
        }
    }

    .arrow-mb {
        animation: moveArrowMb 1.2s ease-in-out infinite;
        animation-fill-mode: both;
    }

    .arrow-mb:nth-child(1) { animation-delay: 0s; }
    .arrow-mb:nth-child(2) { animation-delay: 0.2s; }
    .arrow-mb:nth-child(3) { animation-delay: 0.4s; }

    @keyframes moveArrowMb {
        0% {
            transform: translateY(0);
            opacity: 0;
        }
        20% {
            opacity: 1;
        }
        100% {
            transform: translateY(10px);
            opacity: 0;
        }
    }
</style>

{*<script src="{$basePath}/js/jquery-1.12.0.min.js"></script>*}

<script src="{$basePath}/js/blockadblock/bundle.umd.js"></script>

{block scripts}{/block}

<script n:ifset="$redirectionId">
    $(document).ready( function(e) {

		{if isset($shop) && isset($offers) && empty($offers) === false && $shop->isCashbackActive()}
			var redirectionTime = 5000;
		{else}
			var redirectionTime = {(isset($usingExitPage) && $usingExitPage) ? 1500 : 2000};
		{/if}

        {if $isMobileApp}
            redirectionTime = redirectionTime / 2;
        {/if}

		{if !isset($showNonProfitMessage) || $showNonProfitMessage === false}
        setTimeout(function() {
           window.location = $("#redirection").data("redirect");
        }, redirectionTime);
		{/if}

		justDetectAdblock.detectAnyAdblocker().then(function(detected) {
			var adblockUsed = detected ? 1 : 0;
			console.log("adblock: " + adblockUsed);
			$.ajax({
				url: {link setAdblockUsed},
				data: { redirectionId: {$redirectionId}, adblockUsed: adblockUsed },
				method: "post"
			});
		});
    })
</script>
