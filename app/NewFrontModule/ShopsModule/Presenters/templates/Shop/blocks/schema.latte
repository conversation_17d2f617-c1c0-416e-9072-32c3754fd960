{capture $metaTitleFormatted|spaceless}
	{$metaTitle}
{/capture}

{capture $metaDescriptionFormatted|spaceless}
	{$metaDescription}
{/capture}

{var $breadcrumbs = []}

{var $breadcrumbs[] = [
'name' => $translator->translate('front.shops.shop.shop.shopDetail.nav.shop'),
'item' => $presenter->link('//:NewFront:Shops:Shops:default')
]}

{if $navigationTag !== null}
	{var $breadcrumbs[] = [
	'name' => $navigationTag->getName(),
	'item' => $presenter->link('//:NewFront:Shops:Shops:default', [$navigationTag])
	]}
{/if}

{var $breadcrumbs[] = [
'name' => $shop->getName(),
'item' => $presenter->link('//this')
]}

<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@graph": [
			{
				"@type": "Organization",
				"@id": {$localization->getBaseUrl() . '/#organization'},
				"name": {$localization->getDomainName()},
				"url": {$localization->getBaseUrl()},
				"logo": {
					"@type": "ImageObject",
					"url": "https://www.tipli.cz/new-design/footer/tipli.svg"
				},
				"sameAs": [
					"https://www.facebook.com/tipli{$localization->getLocale() === 'cs' ? 'cz' : $localization->getLocale()|noescape}/",
					"https://www.instagram.com/tipli{$localization->getLocale() === 'cs' ? 'cz' : $localization->getLocale()|noescape}/"
				]
			},
			{
				"@type": "WebSite",
				"@id": {$localization->getBaseUrl() . '/#website'},
				"url": {$localization->getBaseUrl()},
				"name": {$localization->getDomainName()},
				"publisher": {
					"@id": {$localization->getBaseUrl() . '/#organization'}
				},
				"inLanguage": {$localization->getLocaleCode('cs')}
			},
			{
				"@type": "BreadcrumbList",
				"@id": {plink //this#breadcrumb},
				"itemListElement": [
					{foreach $breadcrumbs as $breadcrumb}
					{
						"@type": "ListItem",
						"position": {$iterator->counter},
						"name": {$breadcrumb[name]},
						"item": {$breadcrumb[item]}
					}{if !$iterator->isLast()},{/if}
					{/foreach}
				]
			},
			{
				"@type": "CollectionPage",
				"@id": {plink //this#webpage},
				"url": {plink //this},
				"name": {$metaTitleFormatted},
				"description": {$metaDescriptionFormatted},
				"isPartOf": {
					"@id": {$localization->getBaseUrl() . '/#website'}
				},
				"publisher": {
					"@id": {$localization->getBaseUrl() . '/#organization'}
				},
				"breadcrumb": {
					"@id": {plink //this#breadcrumb}
				},
				"mainEntity": {
					"@type": "ItemList",
					"itemListElement": [
						{foreach $deals as $deal}
						{
							"@type": "ListItem",
							"position": {$iterator->counter},
							"item": {
								"@type": "Offer",
								"@id": {plink '//:NewFront:Shops:Shop:default#deal-' . $deal->getId(), 'shop' => $deal->getShop(), 'openDeal' => $deal->getFullSlug()},
								"url": {plink '//:NewFront:Shops:Shop:default#deal-' . $deal->getId(), 'shop' => $deal->getShop(), 'openDeal' => $deal->getFullSlug()},
								"name": {$deal->getName()},
								"description": {$deal->getDescription()},
								"validFrom": {$deal->getValidSince()->format('Y-m-d\TH:i:s\Z')},
								"validThrough": {$deal->getValidTill()->format('Y-m-d\TH:i:s\Z')},
								"availability": "https://schema.org/OnlineOnly",
								{if $deal->isFreeShippingType()}
								"category": "FreeShipping",
								{else}
								"category": "Cashback",
								{/if}
								"offeredBy": {
									"@type": "Store",
									"name": {$deal->getShop()->getName()},
									"url": {$deal->getShop()->getDomain()}
								}
							}
						}{if !$iterator->isLast()},{/if}
						{/foreach}
					]
				}
			}
		]
	}
</script>
