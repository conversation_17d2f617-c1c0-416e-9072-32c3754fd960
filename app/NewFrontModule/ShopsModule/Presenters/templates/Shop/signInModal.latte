{var $isCashbackAllowed = $shop->isCashbackAllowed()}

<div id="redirect-not-logged-second-popup" class="fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5" n:if="$shop !== null">
    <div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
        <div class="pt-[26px] rounded-t-2xl border border-b-secondary-green bg-light-6 relative">
            <div class="redirect-not-logged-second-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
                <img src="{$basePath}/new-design/close-btn.svg" alt="close">
            </div>
            <div class="flex justify-center">
                <div class="bg-white flex items-center justify-center border border-light-5 rounded-2xl mb-5 p-5">
                    <img class="m-auto w-full max-w-[100px] max-h-[40px]" src="{$shop->getCurrentLogo() |image:200,0,'fit',false,$shop->getName(), false, true}" alt="">
                </div>
            </div>

            <div class="text-center md:text-xl leading-[24.5px] md:leading-[35px] text-dark-1 pb-5 px-5">
                {if $isCashbackAllowed}
                    {_newFront.popups.signIn.title, ['shop' => $shop->getName(), 'reward' => ($shop |reward,false)] |noescape}
                {else}
                    {_newFront.popups.exit.titleNonCashback, ['shop' => $shop->getName(), 'reward' => ($shop |reward,false)] |noescape}
                {/if}
            </div>
        </div>

        <div class="py-5 px-10 relative">
            {snippet form}
                <form n:name="signInControl-form">
                    <div class="form-group" n:foreach="$form->errors as $error">
                        <div class="bg-secondary-red text-white my-3 rounded p-3">{$error |noescape}</div>
                    </div>

                    <div class="w-full relative text-gray-600 focus-within:text-gray-400 mb-2.5 lg:mb-5">
                        <label n:name="email" class="hidden lg:block text-xs font-medium text-gray-700 mb-1">{_'newFront.sign.in.form.email'} <span class="text-red-600">* </span></label>
                        <span class="lg:hidden absolute left-[11px] flex items-center pl-2 top-[19px]">
                           <svg xmlns="http://www.w3.org/2000/svg" width="19" height="13" viewBox="0 0 19 13" fill="none">
                              <path d="M17.7249 1.40337L11.1329 5.99697C10.6648 6.3232 10.0908 6.50008 9.50013 6.50008C8.9095 6.50008 8.33544 6.3232 7.86731 5.99697L1.27455 1.40337M1 2.1L1.00012 10.9C1.00013 11.5075 1.54378 12 2.21441 12H16.7857C17.4563 12 18 11.5075 18 10.9V2.1C18 1.80827 17.8721 1.52847 17.6444 1.32218C17.4167 1.11589 17.1077 1 16.7857 1H2.21429C1.89224 1 1.58338 1.11589 1.35566 1.32218C1.12793 1.52847 1 1.80827 1 2.1Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        <input n:name="email" class="text-sm text-black bg-white rounded-md pl-[47px] lg:pl-3 focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange block w-full border border-light-4 leading-[24.5px] pb-[11px] pt-3">
                    </div>

                    <div class="w-full mb-2 md:mb-5 relative text-gray-600 focus-within:text-gray-400">
                        <label n:name="password" class="hidden lg:block text-xs font-medium text-gray-700 mb-1">{_'newFront.sign.in.form.password'} <span class="text-red-600">* </span></label>
                        <span class="lg:hidden absolute left-[11px] flex items-center pl-2 top-[15px]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                              <path d="M3.125 8.4375H11.625M3.125 8.4375C1 8.4375 1 10.5625 1 10.5625V15.875C1 18 3.125 18 3.125 18H11.625C13.75 18 13.75 15.875 13.75 15.875V10.5625C13.75 8.4375 11.625 8.4375 11.625 8.4375M3.125 8.4375V5.25C3.125 4.12283 3.57277 3.04183 4.3698 2.2448C5.16684 1.44777 6.2479 1 7.375 1C8.5021 1 9.58316 1.44777 10.3802 2.2448C11.1772 3.04183 11.625 4.12283 11.625 5.25V8.4375" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        <input  n:name="password" class="text-sm text-black bg-white rounded-md pl-[47px] lg:pl-3 focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange block w-full border border-light-4 leading-[24.5px] pb-[11px] pt-3">
                    </div>

                    <div class="form-group lg:mb-5" n:ifset="$form[recaptcha]">
                        <div n:name="recaptcha" style="background-color: rgb(244 244 246 / var(--tw-bg-opacity));"></div>
                    </div>

                    <div class="text-end">
                        <input n:name="submit" value="{_'newFront.sign.in.form.submit'}" class="w-full leading-7 font-bold text-white text-sm sm:text-base bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
                    </div>
                </form>
            {/snippet}

            <div class="flex items-center justify-center py-5">
                <div class="flex-grow border-t border-light-4"></div>
                <div class="px-2 text-dark-2 text-sm leading-[24.5px]">{_'newFront.popups.exit.or'}</div>
                <div class="flex-grow border-t border-light-4"></div>
            </div>

            <div class="text-sm text-dark-1">
                {control socialLoginButtons "my-signin4"}
            </div>

            <div class="text-center text-xs pb-2.5 mt-2.5 text-dark-2 leading-[21px]">
                {_'newFront.popups.signIn.footer'} <a href="{plink :NewFront:Sign:up, backLink => $backLink}" class="text-primary-orange cursor-pointer hover:underline">
                    {_'newFront.popups.signIn.signUp'}
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // REDIRECT NOT LOGGED POPUP 2 STEP
    var redirectNotLoggedSecondPopup = document.getElementById("redirect-not-logged-second-popup");
    var redirectNotLoggedSecondPopupBtn = document.getElementById("open-redirect-not-logged-second-popup");
    var closeRedirectNotLoggedSecondPopupBtn = document.querySelector(".redirect-not-logged-second-popup-close");

    if (redirectNotLoggedSecondPopupBtn) {
        redirectNotLoggedSecondPopupBtn.onclick = function() {
			const eventParams = {
				popup_name: 'registration',
				page_location: window.location.href,
				trigger_method: 'button'
			};

			gtag('event', 'popup_view', eventParams);
            redirectNotLoggedSecondPopup.style.display = "flex";
        }
    }

	if (closeRedirectNotLoggedSecondPopupBtn) {
		closeRedirectNotLoggedSecondPopupBtn.onclick = function() {
			const eventParams = {
				popup_name: 'registration',
				page_location: window.location.href,
				trigger_method: 'button'
			};

			gtag('event', 'popup_close', eventParams);
			redirectNotLoggedSecondPopup.style.display = "none";
		}
	}
</script>
