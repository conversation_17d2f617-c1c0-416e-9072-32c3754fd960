{capture $type}
	{if $deal->getType() == $deal::TYPE_FREE_SHIPPING}
		{_'newFront.deals.deal.freeShipping'}
	{else}
		{_'newFront.deals.deal.' . $deal->getType()}
	{/if}
{/capture}

{var $dealShop = $deal->getShop()}
{var $id = $deal->getId()}
{var $isDognet = $deal->getShop()->getPartnerSystem() !== null ? $deal->getShop()->getPartnerSystem()->isDognet() : false}
{var $isDetailable = !$user->isLoggedIn() && $deal->getCode() && !empty($deal->getCode())}
{capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}{/capture}


<div class="deal-item hidden xl:flex mb-5 relative ">
	<div class="absolute right-[-17px] -top-[20px]">
		<a n:if="$user->isLoggedIn() && $user->getIdentity()->isAdmin() && $deal->getSteveId()" href="https://letaky.tipli.cz/admin/deals/deal/{$deal->getSteveId()}" target="_blank" class="flex justify-center items-center py-2 relative z-20 rounded-xl bg-secondary-green text-white font-bold py-3 px-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
			<svg class="shop-profile__edit-icon w-[19px] h-[19px]">{('edit-solid'|svg)|noescape}</svg>
		</a>
	</div>

	<div n:if="$deal->isExclusive()" class="absolute left-0" title="{_'front.deals.deal.exclusive'}">
		<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
			<path fill-rule="evenodd" clip-rule="evenodd"
						d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9" />
			<path fill-rule="evenodd" clip-rule="evenodd"
						d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z"
						fill="#EF7F1A" />
		</svg>
	</div>

	<div class="h-[151px] flex flex-col mt-auto mb-auto bg-white p-2.5 pr-0 rounded-l-2xl shadow-sm">
		{if $deal->isCashbackType()}
			<div class="flex flex-col mt-auto mb-auto">
				<div class="h-[96px] flex items-center justify-center flex-col">
					{if $localization->isHungarian()}
						<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" width="175" height="56" class="max-w-[245px] h-[29px]">+
					{else}
					<svg xmlns="http://www.w3.org/2000/svg" width="169" height="56" viewBox="0 0 169 56" fill="none">
						<rect width="169" height="56" rx="8" transform="matrix(1 0 0 -1 0 56)" fill="white" />
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M71.3196 11.5534H75.7964V27.6055H71.3196V11.5534ZM103.73 27.4201V11.734H107.839V27.4205L103.73 27.4201ZM96.4264 27.4201V6H100.54V27.4205L96.4264 27.4201ZM89.7114 19.5391C89.7114 19.1589 89.6409 18.7649 89.5042 18.356C89.3628 17.9474 89.1556 17.5767 88.8634 17.2493C88.5697 16.9145 88.2114 16.6408 87.8123 16.4467C87.3928 16.2425 86.9218 16.1425 86.394 16.1425H82.9684V22.9022H86.394C86.9218 22.9022 87.3928 22.7928 87.8123 22.5744C88.5785 22.1786 89.1742 21.5127 89.4849 20.7032C89.6358 20.3089 89.711 19.924 89.711 19.5394L89.7114 19.5391ZM78.5719 11.6722H86.3751C87.7418 11.6722 88.9197 11.9147 89.9185 12.3992C90.9173 12.8837 91.7373 13.5059 92.3828 14.271C93.6023 15.722 94.2809 17.5611 94.2965 19.4638C94.2794 21.3909 93.5965 23.2548 92.3687 24.7312C91.6955 25.5399 90.8566 26.1981 89.9138 26.6597C88.9277 27.1304 87.8479 27.3761 86.7564 27.3761C86.6394 27.3761 86.519 27.3736 86.4027 27.3681L82.9684 27.3678V32.9452H78.5719V11.6722ZM61.646 22.7787C61.2691 22.7787 60.878 22.7074 60.4727 22.5697C59.6415 22.2789 58.9589 21.665 58.5787 20.8646C58.3763 20.4467 58.2771 19.9669 58.2771 19.4395V16.2279H65.2178V11.71H58.2767V6H53.8428V19.4206C53.8428 20.7934 54.083 21.9856 54.5639 22.988C55.0444 23.9952 55.6619 24.8217 56.4205 25.4727C57.179 26.1236 58.0132 26.6034 58.9178 26.9214C59.8272 27.2398 60.7134 27.4012 61.5708 27.4012C62.4282 27.4012 63.319 27.2398 64.2426 26.9214C69.1665 25.2208 69.4162 20.6604 69.4162 18.38H65.0201C65.0106 19.3588 64.9354 20.3278 64.6573 20.8646C64.4454 21.2779 64.1815 21.6294 63.8657 21.9093C63.2507 22.4534 62.4638 22.7619 61.646 22.7787ZM71.3196 6H75.7964V10.5186H71.3196V6Z"
							fill="#646C7C" />
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M103.729 6H107.838V10.1475H103.729V6ZM113.583 26.9738L116.254 29.5155C112.36 33.6266 108.441 36.45 102.782 36.6034C97.0471 36.353 93.1362 33.3562 89.4463 29.5249L92.0992 26.9596C95.0538 30.0374 98.3105 32.6816 102.81 32.8834C107.335 32.7489 110.45 30.2751 113.583 26.9738Z"
							fill="#EF7F1A" />
						<path
							d="M87.2936 46.0511V48.7955H75.9463V46.0511H87.2936ZM83.0639 41.3682V53.747H80.1873V41.3682H83.0639Z"
							fill="#646C7C" />
					</svg>
					{/if}
					<img src="{$dealShop->getCurrentLogo() |image:160,0}" alt="{$dealShop->getName()}" class="max-w-20 max-h-10 h-auto mt-1.5" loading="lazy">
				</div>
			</div>
		{else}
			<div class="h-[96px] flex items-center justify-center">
				<img src="{$deal->getShop()->getCurrentLogo() |image:200,0,'fit',false,$deal->getShop()->getName()}" loading="lazy"
					alt="{$deal->getShop()->getName()}"
					class="max-h-[60px] max-w-[100px] w-full">
			</div>
		{/if}

		{if $deal->isCouponType()}
		<div
			class="flex w-[175px] items-center justify-center gap-2 bg-pastel-orange-light text-primary-orange text-sm leading-[24.5px] font-medium py-[5px] rounded-md">
			<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
				<path
					d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
					stroke="url(#paint0_linear_470_5539)" stroke-linecap="round" stroke-linejoin="round" />
				<path
					d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
					stroke="url(#paint1_linear_470_5539)" stroke-linecap="round" stroke-linejoin="round" />
				<defs>
					<linearGradient id="paint0_linear_470_5539" x1="1" y1="18" x2="16.4231" y2="5.48525"
						gradientUnits="userSpaceOnUse">
						<stop stop-color="#EF7F1A" />
						<stop offset="1" stop-color="#FFA439" />
					</linearGradient>
					<linearGradient id="paint1_linear_470_5539" x1="1" y1="18" x2="16.4231" y2="5.48525"
						gradientUnits="userSpaceOnUse">
						<stop stop-color="#EF7F1A" />
						<stop offset="1" stop-color="#FFA439" />
					</linearGradient>
				</defs>
			</svg>
			{$type}
		</div>
		{elseif $deal->isCashbackType() === false}
			<div
				class="flex w-[175px] items-center justify-center gap-2 bg-pastel-green-light text-secondary-green text-sm leading-[24.5px] font-medium py-[5px] rounded-md">
				<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
					<path
						d="M12.3411 14.7839C12.3411 15.4361 12.6005 16.0615 13.0623 16.5227C13.5241 16.9838 14.1503 17.2428 14.8034 17.2428C15.4564 17.2428 16.0827 16.9838 16.5445 16.5227C17.0062 16.0615 17.2656 15.4361 17.2656 14.7839C17.2656 14.1318 17.0062 13.5064 16.5445 13.0452C16.0827 12.5841 15.4564 12.325 14.8034 12.325C14.1503 12.325 13.5241 12.5841 13.0623 13.0452C12.6005 13.5064 12.3411 14.1318 12.3411 14.7839Z"
						fill="#F0F8EC" />
					<path
						d="M1.73441 4.19173C1.73441 4.84387 1.99382 5.4693 2.45559 5.93044C2.91736 6.39157 3.54364 6.65064 4.19668 6.65064C4.84971 6.65064 5.47599 6.39157 5.93776 5.93044C6.39953 5.4693 6.65894 4.84387 6.65894 4.19173C6.65894 3.53959 6.39953 2.91416 5.93776 2.45302C5.47599 1.99189 4.84971 1.73282 4.19668 1.73282C3.54364 1.73282 2.91736 1.99189 2.45559 2.45302C1.99382 2.91416 1.73441 3.53959 1.73441 4.19173Z"
						fill="#F0F8EC" />
					<path
						d="M2.91628 17.9995C2.84006 17.9965 2.76523 17.9783 2.69625 17.9457C2.62726 17.9133 2.56553 17.8672 2.51474 17.8104L1.16618 16.4636C1.05977 16.3573 1 16.2131 1 16.0627C1 15.9123 1.05977 15.768 1.16618 15.6617L15.6822 1.16546C15.7887 1.0592 15.9331 0.999512 16.0837 0.999512C16.2343 0.999512 16.3788 1.0592 16.4853 1.16546L17.8339 2.51218C17.9402 2.61858 18 2.7628 18 2.91318C18 3.06355 17.9402 3.20777 17.8339 3.31417L3.31782 17.8104C3.26703 17.8672 3.2053 17.9133 3.13632 17.9457C3.06733 17.9783 2.9925 17.9965 2.91628 17.9995Z"
						fill="#F0F8EC" />
					<path
						d="M12.3411 14.7839C12.3411 15.4361 12.6005 16.0615 13.0623 16.5227C13.5241 16.9838 14.1503 17.2428 14.8034 17.2428C15.4564 17.2428 16.0827 16.9838 16.5445 16.5227C17.0062 16.0615 17.2656 15.4361 17.2656 14.7839C17.2656 14.1318 17.0062 13.5064 16.5445 13.0452C16.0827 12.5841 15.4564 12.325 14.8034 12.325C14.1503 12.325 13.5241 12.5841 13.0623 13.0452C12.6005 13.5064 12.3411 14.1318 12.3411 14.7839Z"
						stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M1.73441 4.19173C1.73441 4.84387 1.99382 5.4693 2.45559 5.93044C2.91736 6.39157 3.54364 6.65064 4.19668 6.65064C4.84971 6.65064 5.47599 6.39157 5.93776 5.93044C6.39953 5.4693 6.65894 4.84387 6.65894 4.19173C6.65894 3.53959 6.39953 2.91416 5.93776 2.45302C5.47599 1.99189 4.84971 1.73282 4.19668 1.73282C3.54364 1.73282 2.91736 1.99189 2.45559 2.45302C1.99382 2.91416 1.73441 3.53959 1.73441 4.19173Z"
						stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M2.91628 17.9995C2.84006 17.9965 2.76523 17.9783 2.69625 17.9457C2.62726 17.9133 2.56553 17.8672 2.51474 17.8104L1.16618 16.4636C1.05977 16.3573 1 16.2131 1 16.0627C1 15.9123 1.05977 15.768 1.16618 15.6617L15.6822 1.16546C15.7887 1.0592 15.9331 0.999512 16.0837 0.999512C16.2343 0.999512 16.3788 1.0592 16.4853 1.16546L17.8339 2.51218C17.9402 2.61858 18 2.7628 18 2.91318C18 3.06355 17.9402 3.20777 17.8339 3.31417L3.31782 17.8104C3.26703 17.8672 3.2053 17.9133 3.13632 17.9457C3.06733 17.9783 2.9925 17.9965 2.91628 17.9995Z"
						stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{$type}
			</div>
		{/if}
	</div>

	<svg width="39" height="151" viewBox="0 0 39 151" fill="none" xmlns="http://www.w3.org/2000/svg" class="hidden">
		<path fill-rule="evenodd" clip-rule="evenodd" d="M0 150.996V1.94717e-06C1.34918 0.00929027 2.11238 0.0634205 2.67722 0.378877C3.35906 0.759677 4.05318 1.88646 5.44143 4.14003C8.34592 8.85493 13.5559 11.9981 19.5 11.9981C25.4441 11.9981 30.6541 8.85493 33.5586 4.14003C34.9468 1.88646 35.6409 0.759675 36.3228 0.378876C36.8876 0.0634162 37.6508 0.00928774 39 0V150.996C37.6508 150.987 36.8876 150.933 36.3228 150.617C35.6409 150.236 34.9468 149.11 33.5586 146.856C30.6541 142.141 25.4441 138.998 19.5 138.998C13.5559 138.998 8.34592 142.141 5.44144 146.856C4.05319 149.11 3.35906 150.236 2.67723 150.617C2.11239 150.933 1.34919 150.987 0 150.996Z" fill="white"/>
	</svg>

	<div>
		<svg width="39" height="151" viewBox="0 0 39 151" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M0 150.996V1.94717e-06C1.34918 0.00929027 2.11238 0.0634205 2.67722 0.378877C3.35906 0.759677 4.05318 1.88646 5.44143 4.14003C8.34592 8.85493 13.5559 11.9981 19.5 11.9981C25.4441 11.9981 30.6541 8.85493 33.5586 4.14003C34.9468 1.88646 35.6409 0.759675 36.3228 0.378876C36.8876 0.0634162 37.6508 0.00928774 39 0V150.996C37.6508 150.987 36.8876 150.933 36.3228 150.617C35.6409 150.236 34.9468 149.11 33.5586 146.856C30.6541 142.141 25.4441 138.998 19.5 138.998C13.5559 138.998 8.34592 142.141 5.44144 146.856C4.05319 149.11 3.35906 150.236 2.67723 150.617C2.11239 150.933 1.34919 150.987 0 150.996Z" fill="white"/>
			<path d="M19 13L19 138" stroke="#ADB3BF" stroke-linejoin="round" stroke-dasharray="4 4"/>
		</svg>
	</div>

	<div class="flex flex-col justify-between flex-grow pt-[11px] pb-[14px] bg-white pl-1">
		<div class="text-lg text-dark-1 leading-[31.5px]">

			<span n:if="$deal->isExclusive()" class="text-primary-orange">{_'front.deals.deal.exclusive'}: </span>

			{$deal |dealName:true|noescape}

			{if $deal->isCashbackType()}
				<div
					class="inline-flex rounded-md items-center font-medium text-sm text-secondary-green gap-[8px] bg-pastel-green-light py-[5px] px-5 mt-[5px]">
					<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
						<path
							d="M12.6396 14.7844C12.6396 15.4366 12.9058 16.062 13.3797 16.5231C13.8536 16.9843 14.4964 17.2433 15.1666 17.2433C15.8369 17.2433 16.4796 16.9843 16.9535 16.5231C17.4274 16.062 17.6937 15.4366 17.6937 14.7844C17.6937 14.1323 17.4274 13.5069 16.9535 13.0457C16.4796 12.5846 15.8369 12.3255 15.1666 12.3255C14.4964 12.3255 13.8536 12.5846 13.3797 13.0457C12.9058 13.5069 12.6396 14.1323 12.6396 14.7844Z"
							fill="#F0F8EC" />
						<path
							d="M1.75373 4.19222C1.75373 4.84436 2.01998 5.46979 2.49389 5.93093C2.96781 6.39206 3.61058 6.65113 4.2808 6.65113C4.95101 6.65113 5.59378 6.39206 6.06771 5.93093C6.54162 5.46979 6.80786 4.84436 6.80786 4.19222C6.80786 3.54008 6.54162 2.91465 6.06771 2.45351C5.59378 1.99238 4.95101 1.73331 4.2808 1.73331C3.61058 1.73331 2.96781 1.99238 2.49389 2.45351C2.01998 2.91465 1.75373 3.54008 1.75373 4.19222Z"
							fill="#F0F8EC" />
						<path
							d="M2.96671 18C2.88849 17.997 2.81169 17.9787 2.74088 17.9462C2.67009 17.9137 2.60673 17.8677 2.5546 17.8109L1.17055 16.4641C1.06134 16.3578 1 16.2135 1 16.0631C1 15.9128 1.06134 15.7685 1.17055 15.6621L16.0686 1.16595C16.1779 1.05969 16.3261 1 16.4807 1C16.6352 1 16.7835 1.05969 16.8928 1.16595L18.2768 2.51267C18.386 2.61907 18.4474 2.76329 18.4474 2.91366C18.4474 3.06403 18.386 3.20826 18.2768 3.31465L3.37882 17.8109C3.32669 17.8677 3.26333 17.9137 3.19254 17.9462C3.12173 17.9787 3.04493 17.997 2.96671 18Z"
							fill="#F0F8EC" />
						<path
							d="M12.6396 14.7844C12.6396 15.4366 12.9058 16.062 13.3797 16.5231C13.8536 16.9843 14.4964 17.2433 15.1666 17.2433C15.8369 17.2433 16.4796 16.9843 16.9535 16.5231C17.4274 16.062 17.6937 15.4366 17.6937 14.7844C17.6937 14.1323 17.4274 13.5069 16.9535 13.0457C16.4796 12.5846 15.8369 12.3255 15.1666 12.3255C14.4964 12.3255 13.8536 12.5846 13.3797 13.0457C12.9058 13.5069 12.6396 14.1323 12.6396 14.7844Z"
							stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M1.75373 4.19222C1.75373 4.84436 2.01998 5.46979 2.49389 5.93093C2.96781 6.39206 3.61058 6.65113 4.2808 6.65113C4.95101 6.65113 5.59378 6.39206 6.06771 5.93093C6.54162 5.46979 6.80786 4.84436 6.80786 4.19222C6.80786 3.54008 6.54162 2.91465 6.06771 2.45351C5.59378 1.99238 4.95101 1.73331 4.2808 1.73331C3.61058 1.73331 2.96781 1.99238 2.49389 2.45351C2.01998 2.91465 1.75373 3.54008 1.75373 4.19222Z"
							stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M2.96671 18C2.88849 17.997 2.81169 17.9787 2.74088 17.9462C2.67009 17.9137 2.60673 17.8677 2.5546 17.8109L1.17055 16.4641C1.06134 16.3578 1 16.2135 1 16.0631C1 15.9128 1.06134 15.7685 1.17055 15.6621L16.0686 1.16595C16.1779 1.05969 16.3261 1 16.4807 1C16.6352 1 16.7835 1.05969 16.8928 1.16595L18.2768 2.51267C18.386 2.61907 18.4474 2.76329 18.4474 2.91366C18.4474 3.06403 18.386 3.20826 18.2768 3.31465L3.37882 17.8109C3.32669 17.8677 3.26333 17.9137 3.19254 17.9462C3.12173 17.9787 3.04493 17.997 2.96671 18Z"
							stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
					{_newFront.deals.deal.cashbackDealPromoText}
				</div>
			{/if}
		</div>

		<div class="flex items-center">
			<div n:if="$deal->isCashbackType() === false"
				class="text-xs text-secondary-green font-bold leading-[21px] py-[3px] px-2.5 border border-light-4 rounded-md mr-3">
				+ {$dealShop |reward:false,extended |noescape}
			</div>

			<div n:if="$deal->isCashbackType() === false" class="flex items-center">
				<div class="flex items-center text-xs leading-[21px] gap-[5px] text-dark-4">
					<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
						<path
							d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048"
							stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
					{if $deal->getValidTillDays() === 0}
						{_'newFront.deals.deal.validTillToday'}
					{elseif $deal->getValidTillDays() <= 3}
						<span n:class="$deal->isCouponType() ? 'text-red-400'">
							{_'newFront.deals.deal.validTillDays', ['count' => $deal->getValidTillDays()]}
						</span>
					{else}
						{_'newFront.deals.deal.validTill', ['date' => ($deal->getValidTillForUser()|localDate:'d.m.Y')]}
					{/if}
				</div>

				<div n:if="$deal->isCashbackType() === false" class="px-2.5 text-dark-4">|</div>

				{*<a href="{plink "this!#deal-$id", openDeal=> $deal->getFullSlug()}" class="ajax">popup</a>*}

				<button n:if="$deal->isCashbackType() === false" href="javascript:void(0);"
					class="ajax flex items-center text-xs leading-[21px] text-dark-4 gap-[5px] hover:cursor-pointer hover:underline"
					onclick="toggleDetails('deal-details-{$id}')">

					{_newFront.deals.deal.conditions}
					<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
						<path
							d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656"
							stroke="#ADB3BF" />
					</svg>
				</button>
			</div>
		</div>
	</div>

	<div class="flex flex-col gap-[10px] pl-5 bg-white py-2.5 pr-2.5 rounded-r-2xl">
		{if $deal->isCouponType()}
			{if ($user->isLoggedIn() || $isDognet) && $deal->isCouponType()}
				<button
					class="h-[60px] flex py-2 items-center justify-between relative w-full md:py-3 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4  js-copy-code-button" data-coupon-code="{$deal->getCode()}" data-coupon-copied="{_'newFront.deals.deal.copied'}">
					<span class="js-copy-change-text max-w-[130px] whitespace-nowrap overflow-hidden m-auto" n:if="$deal->getCode()">{$deal->getCode()}</span>
					<span class="max-w-[130px] whitespace-nowrap overflow-hidden m-auto" n:if="$deal->getCode() === null">{_'newFront.deals.deal.couponWithoutCode'}</span>

					<img class="absolute right-[15px] js-copy-icon" n:if="$deal->getCode()" src="{$basePath}/new-design/copy-icon.svg" alt="copy">
					<img class="absolute right-[15px] hidden js-copy-icon-copied" n:if="$deal->getCode()" src="{$basePath}/new-design/copy-icon-copied.svg" alt="copy">
				</button>

				<a n:href="//:NewFront:Shops:Redirection:deal $deal" target="_blank"
																	 class="w-[210px] h-[60px] flex justify-center items-center py-2 relative z-20 rounded-xl bg-orange-gradient text-white font-bold md:py-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
					{if $deal->getCode()}
						{if $deal->getShop()->getShopData()->getCouponLabel()}
							{$deal->getShop()->getShopData()->getCouponLabel()}
						{else}
							{_newFront.deals.deal.useCode}
						{/if}
					{else}
						{if $deal->getShop()->getShopData()->getDealLabel()}
							{$deal->getShop()->getShopData()->getDealLabel()}
						{else}
							{_newFront.deals.deal.getSale}
						{/if}
					{/if}
				</a>
			{else}
				<a n:href="//:NewFront:Shops:Redirection:deal $deal"  {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} class="block relative rounded-2xl h-[59px] w-[210px] border-t border-b border-r border-dashed border-dark-4 overflow-hidden h-[60px] overflow-visible cursor-pointer hover:border-primary-orange hover:border-solid mt-auto">
					<div class="relative z-20 h-[61px] top-[-1px]">
						<svg class="w-[180px] h-full" viewBox="0 0 180 61" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M167 0L24 0C12.6863 0 7.02943 0 3.51472 3.51472C0 7.02944 0 12.6863 0 24V36C0 47.3137 0 52.9706 3.51472 56.4853C7.02943 60 12.6863 60 24 60H180L167 0Z" fill="url(#paint0_linear_827_3009)"/>
						<defs>
							<linearGradient id="paint0_linear_827_3009" x1="180" y1="60" x2="140.896" y2="-35.1907" gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A"/>
								<stop offset="1" stop-color="#FFA439"/>
							</linearGradient>
						</defs>
						</svg>
					</div>
					<div class="absolute inset-0 flex items-center pl-[32px] z-30">
					<span class="font-bold text-white leading-7">
						{if $deal->getShop()->getShopData()->getCouponLabel()}
							{$deal->getShop()->getShopData()->getCouponLabel()}
						{else}
							{_'newFront.deals.deal.getCode'}
						{/if}
					</span>
					</div>
					<svg class="absolute top-0 right-[31px] z-40" width="30" height="56" viewBox="0 0 30 56" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M1.89346 26.3026L18 0L30 55.5L4.06843 36.9919C0.64999 34.5521 -0.29979 29.8842 1.89346 26.3026Z" fill="#CE6D14"/>
					</svg>
					<div class="absolute right-[14px] top-[16px]">
						{if $deal->getCode() !== null && Nette\Utils\Strings::length($deal->getCode()) > 3}{mb_substr($deal->getCode(), -3, 3)}
						{else}
							{$deal->getCode()}
						{/if}
					</div>
				</a>
			{/if}
		{else}
			{if $deal->isCashbackType()}
				<a n:href="aqPopup-open!, aqPopup-type => virtualCoupon, aqPopup-shopId => $dealShop->getId()"
						class="ajax w-[210px] h-[60px] flex justify-center items-center py-2 relative z-20 rounded-xl bg-orange-gradient text-white font-bold md:py-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
					{if $deal->getShop()->getShopData()->getDealLabel()}
						{$deal->getShop()->getShopData()->getDealLabel()}
					{else}
						{_newFront.deals.deal.getSale}
					{/if}
				</a>
			{else}
        <a n:href="//:NewFront:Shops:Redirection:deal $deal" target="_blank"
          class="w-[210px] h-[60px] flex justify-center items-center py-2 relative z-20 rounded-xl bg-orange-gradient text-white font-bold md:py-3 leading-[28px] mt-auto cursor-pointer xl:hover:bg-orange-gradient-hover">
			{if $deal->getShop()->getShopData()->getDealLabel()}
				{$deal->getShop()->getShopData()->getDealLabel()}
			{else}
				{_newFront.deals.deal.getSale}
			{/if}
        </a>
			{/if}
		{/if}
	</div>
</div>

<div id="deal-details-{$id}" class="hidden pt-10 pb-5 px-5 rounded-b-2xl bg-[#FDFDFD] -mt-10 mb-5">
	<div class="flex justify-between">
		<p n:foreach="$deal->getDescriptionRows() as $row" class="text-sm leading-[24.5px] text-dark-2 w-full max-w-[594px]">
			{$row}
		</p>
		<div class="flex items-center self-baseline gap-[6px] text-sm underline text-primary-orange leading-[24.5px] cursor-pointer hover:no-underline"
			onclick="toggleDetails('deal-details-{$id}')">
			{_newFront.deals.deal.closeConditions}
			<svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none">
				<path d="M1 5L4.44732 1.55268C4.75256 1.24744 5.24744 1.24744 5.55268 1.55268L9 5" stroke="#EF7F1A" />
			</svg>
		</div>
	</div>
</div>
