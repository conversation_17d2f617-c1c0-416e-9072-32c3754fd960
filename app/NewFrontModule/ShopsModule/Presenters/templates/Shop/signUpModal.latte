<div id="register-popup" class="hidden fixed z-30 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center">
    <div class="fixed bottom-0 bg-white m-auto w-full">
        <div class="flex justify-between w-[1105px] max-w-full m-auto relative pl-5 xl:pl-0">
            <div class="hidden lg:block pt-[70px] pb-[83px] max-w-[690px]">
                <div class="flex items-center gap-[18px] mb-[32px]">
					{if $localization->isHungarian()}
						<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[93px] h-[47px]">
					{else}
						<svg xmlns="http://www.w3.org/2000/svg" width="97" height="48" viewBox="0 0 97 48" fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M27.1624 8.63103H34.1202V33.579H27.1624V8.63103ZM77.5341 33.291V8.91177H83.9202V33.2915L77.5341 33.291ZM66.1831 33.291V0H72.576V33.2915L66.1831 33.291ZM55.7466 21.0423C55.7466 20.4515 55.637 19.8391 55.4246 19.2036C55.2049 18.5686 54.8829 17.9925 54.4287 17.4835C53.9723 16.9632 53.4153 16.5379 52.7951 16.2362C52.1432 15.9187 51.4111 15.7634 50.5909 15.7634H45.2667V26.2693H50.5909C51.4111 26.2693 52.1432 26.0993 52.7951 25.7598C53.9859 25.1446 54.9117 24.1097 55.3947 22.8517C55.6292 22.2387 55.7461 21.6405 55.7461 21.0429L55.7466 21.0423ZM38.4338 8.81573H50.5615C52.6855 8.81573 54.5163 9.19251 56.0686 9.94552C57.621 10.6985 58.8954 11.6656 59.8986 12.8547C61.7938 15.1098 62.8485 17.9682 62.8728 20.9254C62.8462 23.9205 61.7848 26.8172 59.8766 29.1118C58.8304 30.3687 57.5266 31.3917 56.0613 32.1091C54.5287 32.8407 52.8504 33.2225 51.154 33.2225C50.9721 33.2225 50.7852 33.2186 50.6044 33.2101L45.2667 33.2096V41.8779H38.4338V8.81573ZM12.1277 26.0772C11.5419 26.0772 10.9341 25.9665 10.3042 25.7524C9.01231 25.3005 7.95145 24.3464 7.36057 23.1025C7.04592 22.4529 6.89172 21.7072 6.89172 20.8875V15.8961H17.6789V8.87448H6.89114V0H0V20.8581C0 22.9917 0.373387 24.8446 1.12074 26.4026C1.86753 27.9679 2.82729 29.2525 4.00623 30.2642C5.18516 31.276 6.4816 32.0216 7.88762 32.5159C9.30099 33.0107 10.6782 33.2616 12.0108 33.2616C13.3434 33.2616 14.7279 33.0107 16.1633 32.5159C23.816 29.8727 24.204 22.785 24.204 19.2409H17.3716C17.357 20.7621 17.24 22.2681 16.8079 23.1025C16.4785 23.7448 16.0684 24.291 15.5775 24.726C14.6217 25.5717 13.3987 26.0513 12.1277 26.0772ZM27.1624 0H34.1202V7.02274H27.1624V0Z" fill="#646C7C"/>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M77.5346 0H83.9207V6.44598H77.5346V0ZM92.8484 32.5972L97.0009 36.5475C90.9486 42.9371 84.8568 47.3252 76.0619 47.5635C67.149 47.1743 61.0707 42.5168 55.3359 36.5622L59.4591 32.5752C64.0511 37.3587 69.1126 41.4683 76.1054 41.7819C83.1389 41.5728 87.979 37.7282 92.8484 32.5972Z" fill="#EF7F1A"/>
						</svg>
					{/if}
					<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
						<path d="M19.2051 8.06348V12.4238H0.6875V8.06348H19.2051ZM12.3027 0.623047V20.291H7.6084V0.623047H12.3027Z" fill="#646C7C"/>
					</svg>
                    <img src="{$shop->getCurrentLogo() |image:200,0}" class="max-w-48 max-h-20 w-full" alt="{$shop->getName()}">
                </div>

                <div class="text-dark-1 text-[32px] font-bold leading-[58px]">
                    {_newFront.shops.shop.signUpModal.title, ['shop' => $shop->getName(), 'bonus' => $getCampaignBonusAmount()] |noescape}
                </div>
            </div>
            <div class="flex w-full justify-center xl:justify-end absolute right-0 bottom-0">
                <div class="bg-white border border-secondary-green max-w-[415px] px-5 rounded-t-2xl pb-[37px] pt-[41px] shadow-custom relative md:px-[55px]">
                    <img class="hidden xl:block absolute right-[-103px] bottom-0" src="{$basePath}/new-design/hp-register-donkey.png" loading="lazy" alt="donkey">
                    <div class="flex absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 whitespace-nowrap">
                        <div class="flex items-center text-sm bg-secondary-green rounded-full text-white font-bold leading-[24.5px] uppercase py-1 pl-1 pr-[15px]">
                            <div class="bg-white text-secondary-green rounded-full px-3 leading-[28px] mr-1.5">+{_newFront.shops.shop.signUpModal.bonusReward, ['bonus' => $getCampaignBonusAmount()]}</div>
                             {_newFront.shops.shop.signUpModal.bonusText}
                        </div>
                    </div>
                    <div class="text-xl font-medium leading-[35px]">
                        {_newFront.shops.shop.signUpModal.registrationTitle}
                    </div>

                    {form emailSignUpControl-form, class => "ajax"}
						{snippet errors}
							{var $form = $control['emailSignUpControl']['form']}
							<div class="bg-secondary-red text-white mt-3 rounded p-2" n:foreach="$form->errors as $error">
								{$error |noescape}
							</div>
						{/snippet}
                        <div class="mt-[22px]">
                            <input
                                    n:name="email"
                                    type="text"
                                    placeholder="{_'front.aqPopup.form.input'}"
                                    class="w-full bg-light-6 border border-solid border-light-4 pl-5 py-4 leading-[28px] rounded-xl"
                            >
                        </div>
                        <div class="form-group" n:ifset="$form[recaptcha]">
                            <div n:name="recaptcha"></div>
                        </div>

                        <button n:name="submit" class="w-full py-4 rounded-xl bg-orange-gradient text-white font-medium mt-2 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover">
                            {_newFront.shops.shop.signUpModal.registrationCta}
                        </button>
                    {/form}

                    <div class="flex items-center justify-center py-5">
                        <div class="flex-grow border-t border-light-4"></div>
                        <div class="px-2 text-dark-2 text-sm leading-[24.5px]">{_newFront.shops.shop.signUpModal.or}</div>
                        <div class="flex-grow border-t border-light-4"></div>
                    </div>

                    <div class="text-sm text-dark-1">
                        {control socialLoginButtons "my-signin2"}
                    </div>

                    <div class="mt-5 text-center text-xs text-dark-3 font-normal leading-[21px] px-[22px]">
                        <span class="aq-popup-form__condition">{_'front.aqPopup.form.acceptAllConditions', [condition => $presenter->link(':NewFront:Static:conditions'), privacy => $presenter->link(':NewFront:Static:privacyPolicy')]|noescape}</span>
                    </div>

                    <div class="text-xs text-center mt-2.5 text-dark-2 leading-[21px]">
                        {_'newFront.popups.exit.footer'} <a href="{plink :NewFront:Sign:in}" class="text-primary-orange cursor-pointer hover:underline">
                            {_'newFront.popups.exit.signIn'}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
	var registerPopup = document.getElementById("register-popup");
	var buttons = document.querySelectorAll(".open-register-popup");

	if (buttons) {
		buttons.forEach(function(btn) {
			btn.addEventListener('click', function() {
				const eventParams = {
					popup_name: 'registration',
					page_location: window.location.href,
					trigger_method: 'button'
				};

				gtag('event', 'popup_view', eventParams);
				registerPopup.style.display = "block";
			});
		});
	}

	window.onclick = function (event) {
		if (event.target == registerPopup) {
			const eventParams = {
				popup_name: 'registration',
				page_location: window.location.href,
				trigger_method: 'button'
			};

			gtag('event', 'popup_close', eventParams);
			registerPopup.style.display = "none";
		}
	}
</script>

