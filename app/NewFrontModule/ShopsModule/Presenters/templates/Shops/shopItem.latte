<a n:href=":NewFront:Shops:Shop:, $shop"
	class="{isset($class) ? $class} shop-item flex flex-col items-center justify-center bg-white rounded-xl cursor-pointer shadow-hover p-2 shop-item">
	<div class="h-[92px] px-4 py-4 flex items-center justify-center">
		<img class="max-w-[100px] max-h-[60px] w-full" loading="lazy" alt="{$shop->getName()}"
			src="{$shop->getCurrentLogo() |image:282,0,'fit',false,$shop->getName()}" />
	</div>
	<img class="m-auto" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">

	{if $shop->isCashbackAllowed() || $shop->isCashbackActive()}
	<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
		{$shop |reward:true,'extended'|noescape}
	</div>
	{else}
	<div class="text-center text-zinc-950 text-base font-normal leading-[25px] mt-3 mb-3">
		{$shop->getName()}
	</div>
	<div class="flex w-full items-center justify-center py-2 bg-green-50 rounded-md gap-2 ">
		<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
			<path
				d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
				stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
			<path
				d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
				stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
		</svg>
		<div class="text-lime-500 text-sm font-medium leading-normal">
			{_newFront.shops.nonCashbackShops.shopLabel}
		</div>
	</div>
	{/if}
</a>
