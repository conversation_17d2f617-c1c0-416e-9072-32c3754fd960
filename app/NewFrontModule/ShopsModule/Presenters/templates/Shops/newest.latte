{block content}
<section class="container relative">
	<div class="relative z-20">
		<h1 class="text-[26px] leading-[39px] md:text-[33px] md:leading-[49.5px] text-dark-1 font-bold mt-9 mb-[30px]">
			{if isset($pageExtension) && $pageExtension->getHeading()}
				{$pageExtension->getHeading()}
			{elseif $tag}
				{_'newFront.shops.shop.sortedShops.title', 2, ['category' => $tag->getName()]}
			{else}
				{_'newFront.shops.shop.sortedShops.title', 1}
			{/if}
		</h1>

		<div class="flex mx-[-12px] mb-5 overflow-x-scroll md:flex-wrap md:overflow-x-auto md:mb-10">
			{var $tags = $tags()}
			<a n:href="newest"
				n:class="$tag === null ? 'bg-zinc-950 text-white', 'bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] flex-shrink-0 xl:hover:bg-light-4 xl:hover:text-dark-1'">
					{_newFront.shops.allShops}
					<small class="text-orange-500 font-normal text-base">({$countOfCashbackShops})</small>
			</a>
			{foreach $tags as $item}
				<a n:href="newest tag: $item"
				n:class="$tag === $item ? 'bg-zinc-950 text-white', 'bg-white rounded-xl border px-[17px] py-[10px] mx-[6px] my-[6px] flex-shrink-0 xl:hover:bg-light-4 xl:hover:text-dark-1'">
					{$item->getName(s)}
					<small class="text-orange-500 font-normal text-base">({$item->getCountOfShops()})</small>
				</a>
			{/foreach}
		</div>
	</div>
</section>

<div class="relative h-8 z-10 overflow-y-hidden md:pb-20">
	<img class="w-screen" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
</div>

<div class="relative bg-light-6 pb-10 md:pb-36">
	<section class="container relative z-20">
		{if $shops->isEmpty() === false}
			{snippet shopSorter}
				<div id="non-cashback-shops-list" class="datalist" n:if="!$shops->isEmpty()">
					<div class="datalist-data grid grid-cols-2 pb-5 gap-[15px] sm:grid-cols-4 md:gap-5 md:grid-cols-5 lg:grid-cols-6 mb-" data-ajax-append="true" n:snippet="shops">
						{foreach $shops as $shop}
							<a n:href=":NewFront:Shops:Shop:, $shop" class="flex flex-col items-center justify-center bg-white rounded-xl cursor-pointer shadow-hover">
								<div class="h-[96px] flex items-center justify-center">
									<img class="max-h-[60px] max-w-[100px] w-full" alt="{$shop->getName()}"
										 src="{$shop->getCurrentLogo() |image:200,0,'fit',false,$shop->getName()}" />
								</div>
								<img class="m-auto" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">
								<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
									{$shop |reward:true,'extended'|noescape}
								</div>
							</a>
						{/foreach}
					</div>

					{control paginator}
				</div>
			{/snippet}
		{/if}
	</section>
</div>
