{layout '../@defaultLayout.latte'}

{block title}
    {_'front.articles.article.allArticles.title'|noescape}
{/block}

{block content}
<div class="bg-img">
    <div class="container">
        <div class="flex justify-between">
            <div>
                <h1 class="text-white text-[33px] leading-[49.5px] font-bold pt-[63px] mb-2.5">
                    {_newFront.articles.title}
                </h1>
                <div class="text-white leading-7 w-full max-w-[584px]">
                    {_newFront.articles.text}
                </div>
            </div>
            <div class="hidden md:block pt-[17px]">
                <img src="{$basePath}/new-design/blog-donkey.png" alt="oslik">
            </div>
        </div>
    </div>
</div>

<div class="container mt-[73px] pb-10 md:pb-36">
    <div class="flex flex-col md:flex-row items-center gap-[44px]"  n:if="$currentTag === null && $firstArticle">
        <div class="w-full">
			<a n:href=":NewFront:Articles:Article:default, $firstArticle">
				<img n:if="$firstArticle->getPreviewImage()" src="{$firstArticle->getPreviewImage() |image:614,344,'exact'}" loading="lazy">
			</a>
        </div>
        <div>
            <div class="inline-flex text-dark-2 text-sm leading-[24.5px] py-[3px] px-3 rounded-lg bg-light-6 mb-5" n:if="$firstArticle->getFirstTag()">
				<a n:href=":NewFront:Articles:Articles:default, 'tag' => $firstArticle->getFirstTag()">
                	{$firstArticle->getFirstTag()?->getName()}
				</a>
            </div>
            <h2 class="text-dark-1 text-[33px] leading-[49.5px] font-bold mb-2.5">
				<a n:href=":NewFront:Articles:Article:default, $firstArticle">
                	{$firstArticle->getName()}
				</a>
            </h2>
            <div class="text-dark-1 leading-7 mb-5">
                {$firstArticle->getDescription()|stripHtml|truncate:100,'...'}
            </div>
            <div class="flex items-center gap-[11px]">
                <a class="underline leading-7 text-dark-1 hover:no-underline" n:href=":NewFront:Articles:Article:default, $firstArticle">
                    {_newFront.articles.article.readMore}
                </a>
                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
                    <path d="M4.19576 15.0328L14.0938 5.13477" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
        </div>
    </div>

    <div class="lg:block w-full h-px bg-light-5 my-[50px] md:mt-[100px] md:mb-[87px]" n:if="$currentTag === null && $firstArticle"></div>

    <h2 class="text-dark-1 text-center text-[33px] font-bold leading-[49.5px] mb-[66px]">
		{if $currentTag !== null}
			{$currentTag->getName()}
		{else}
        	{_newFront.articles.otherArticles}
    	{/if}
	</h2>

    {snippet}
        <div class="datalist pb-10">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-x-[26px] gap-y-10 datalist-data" data-ajax-append="true" n:snippet="articles">
                {foreach $getArticles() as $article}
                    <a class="hover:cursor-pointer hover:underline" n:href=":NewFront:Articles:Article:default, $article">
                        <img n:if="$article->getPreviewImage()" src="{$article->getPreviewImage() |image:614,344,'exact'}" loading="lazy" class="rounded-2xl mb-5">
                        <div class="inline-flex text-dark-2 text-sm leading-[24.5px] py-[3px] px-3 rounded-lg bg-light-6 mb-[18px]" n:if="$firstArticle->getFirstTag()">
                            {$article->getFirstTag()?->getName()}
                        </div>
                        <h3 class="text-dark-1 text-[20px] font-medium leading-[35px] mb-[5px]">
                            {$article->getName()}
                        </h3>
                        <div class="text-dark-1 text-sm font-light leading-[24.5px] line-clamp-3">
                            {$article->getDescription()|stripHtml|truncate:100,'...'}
                        </div>
                    </a>
                {/foreach}
            </div>

            <div class="relative mt-[60px] text-center">
                {control paginator}
            </div>
        </div>
    {/snippet}

	<div n:foreach="$tags as $tag" class="inline-flex text-dark-2 text-sm leading-[24.5px] py-[3px] px-3 rounded-lg bg-light-6 mb-5">
		<a n:href=":NewFront:Articles:Articles:default, 'tag' => $tag">
			{$tag->getName()}
		</a>
	</div>
</div>


<style>
    .bg-img {
        left: 0;
        z-index: 10;
        width: 100%;
        height: 264px;
        background-image: url(/new-design/blog-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
    }
</style>
