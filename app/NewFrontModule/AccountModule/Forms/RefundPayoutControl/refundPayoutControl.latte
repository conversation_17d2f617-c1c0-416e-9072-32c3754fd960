<div class="flex items-center justify-between mt-10 mb-2.5">
	<div class="text-lg font-medium leading-[31.5px] text-dark-1 pl-5 md:pl-10">{_'front.refund.payout.title'}</div>
	<a class="flex items-center gap-2 text-sm leading-[24.5px] underline text-dark-1 xl:hover:no-underline" onclick="showSection('help-with')" style="cursor: pointer;">
		<svg xmlns="http://www.w3.org/2000/svg" width="13" height="11" viewBox="0 0 13 11" fill="none">
			<path d="M12.2009 5.6038L0.994538 5.6038M0.994538 5.6038L5.74254 0.855893M0.994538 5.6038L5.74254 10.3519" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
		</svg>
		{_'newFront.refund.back'}
	</a>
</div>
<div class="text-sm leading-[24.5px] text-dark-1 pl-5 md:pl-10 mb-5">
	{_'newFront.refund.payout.text'}
</div>

<form n:name="form" class="mb-5 px-5 pt-5 md:px-10 md:pt-10 pb-5 bg-white rounded-2xl">
	<div class="bg-secondary-red text-white mt-3 rounded p-2 md:ml-5 md:mr-5" n:foreach="$form->errors as $error">
		{$error |noescape}
	</div>

	<div class="text-xs leading-[21px] font-medium text-dark-2">{_'front.refund.form.chooseProblemPayout'}</div>

	<div data-payout-id="" class="flex w-full text-sm justify-between items-center border border-light-4 px-3 pb-[11px] pt-3 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-orange mb-[15px]">
		<div class="flex items-center text-sm">
			<input type="radio" id="problem" value="problem" class="leading-[24.5px] checked:bg-primary-orange checked:hover:bg-primary-orange checked:active:bg-primary-orange checked:focus:bg-primary-orange focus:bg-primary-orange focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange" name="radio">
			<label class="ml-2" for="problem">{_'front.refund.form.otherProblemPayout'}</label>
		</div>
	</div>


	<div class="relative max-h-[300px] overflow-hidden refund-payout-list__wrapper">
	{foreach $payouts as $payout}
		<div data-payout-id="{$payout->getId()}" class="flex w-full text-sm justify-between items-center border border-light-4 pl-3 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-orange js-refund-payout-list">
			<div class="flex items-center text-sm">
				<input type="radio" id="IBAN" value="IBAN" class="leading-[24.5px] checked:bg-primary-orange checked:hover:bg-primary-orange checked:active:bg-primary-orange checked:focus:bg-primary-orange focus:bg-primary-orange focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange" name="radio">
				<label class="ml-2" for="IBAN">{$payout->getAccountNumber()}</label>
			</div>
			<div class="flex items-center gap-5">
				<div class="text-xs leading-[21px] text-dark-2">{$payout->getCreatedAt() |localDate}</div>
				<div class="text-sm font-bold text-dark-1 leading-[24.5px] bg-light-6 px-[28px] pt-[11px] pb-[10px] text-nowrap">
					{$payout->getAmount() |amount} {$payout->getUser()->getLocalization() |currency}
				</div>
			</div>
		</div>
	{/foreach}
	</div>

	{if count($payouts) > 5}
		<div class="flex justify-center flex-wrap items-center">
			<div class="datalist-next-items">
				<span class="js-refund-payout-list-show-more cursor-pointer min-w-[294px] h-[56px] flex items-center justify-center font-medium text-dark-1 text-sm px-5 py-2.5 border border-light-2 rounded-xl xl:hover:bg-light-4">
					{_'front.refund.form.showMorePayouts'}
				</span>
			</div>
		</div>
	{/if}

	<div class="js-refund-payout-id">
		{input payoutId}
	</div>

	<div class="w-full mt-[25px]">
		<label n:name="message" class="block text-xs font-medium text-gray-700">{_'newFront.refund.form.message'}</label>
		<textarea n:name="message" rows="4" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange"></textarea>
	</div>

	{input submit, class => "mt-5 leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover"}
</form>

<script>
	if (document.querySelector('.js-refund-payout-list-show-more')) {
		document.querySelector('.js-refund-payout-list-show-more').addEventListener('click', function(e) {
			console.log('click');
			this.style.display = 'none';
			document.querySelector('.refund-payout-list__wrapper').style.maxHeight = '100%';
		});
	}

	document.querySelectorAll('.js-refund-payout-list').forEach(function(element) {
		element.addEventListener('click', function(e) {
			document.querySelector('.js-refund-payout-id input').value = this.getAttribute('data-payout-id');
		});
	});
</script>
