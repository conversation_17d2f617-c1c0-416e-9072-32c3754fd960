<?php

namespace tipli\NewFrontModule\AccountModule\Forms;

use Nette\Localization\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Configuration;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\Files\FileStorage;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use Tracy\Debugger;

class RefundMissingCommissionControl extends Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var User */
	private $user;

	/** @var RefundFacade */
	private $refundFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var Localization */
	private $localization;

	/** @var FileStorage */
	private $fileStorage;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var Translator */
	private $translator;

	/** @var Configuration */
	private $configuration;

	private array $defaultData = [];

	private bool $showRedirectionFeedbackMessage = false;

	public function setDefaultData($defaultData): void
	{
		$this->defaultData = $defaultData;
	}

	public function showRedirectionFeedbackMessage(): void
	{
		$this->showRedirectionFeedbackMessage = true;
	}

	public function __construct(User $user, RefundFacade $refundFacade, ShopFacade $shopFacade, FileStorage $fileStorage, ImageFilter $imageFilter, Translator $translator, Configuration $configuration)
	{
		$this->user = $user;
		$this->refundFacade = $refundFacade;
		$this->localization = $user->getLocalization();
		$this->shopFacade = $shopFacade;
		$this->fileStorage = $fileStorage;
		$this->imageFilter = $imageFilter;
		$this->translator = $translator;
		$this->configuration = $configuration;
	}

	public function render()
	{
		$this->template->addFilter('image', $this->imageFilter);
		$this->template->aliexpressId = Shop::ALIEXPRESS_ID[$this->localization->getLocale()];
		$this->template->setFile(__DIR__ . '/refundMissingCommissionControl.latte');
		$this->template->showRedirectionFeedbackMessage = $this->showRedirectionFeedbackMessage;
		$this->template->render();
	}

	public function createComponentForm()
	{
		$form = new Form();

		$form->addHidden('shopName')
			->setNullable()
			->setDefaultValue(null);

		$form->setHtmlAttribute('autocomplete', 'off');

		$form->setTranslator($this->translator);

		$form->addProtection($this->translator->translate('front.refund.form.errors.csrf'));

		$form->addText('purchasedAt')
			->setHtmlAttribute('type', 'date')
			->setHtmlAttribute('autocomplete', 'off')
			->setRequired($this->translator->translate('front.refund.form.errors.commission.orderDateMissing'))
		;

		$form->addText('orderAmount')
			->setRequired($this->translator->translate('front.refund.form.errors.commission.orderAmountMissing'))
		;

		$currencyList = [];
		foreach (Currency::getCurrencies() as $currency) {
			$currencyList[$currency] = Currency::getCurrencySymbol($currency);
		}

		$form->addSelect('orderCurrency', null, $currencyList)
			->setDefaultValue($this->user->getLocalization()->getCurrency())
			->setRequired($this->translator->translate('front.refund.form.errors.commission.orderCurrencyMissing'))
		;

		$devices = [
			'desktop' => $this->translator->translate('front.refund.form.platform.desktop'),
			'mobile' => $this->translator->translate('front.refund.form.platform.mobile'),
			'tablet' => $this->translator->translate('front.refund.form.platform.tablet'),
		];

		$form->addRadioList('platform', null, $devices);

		$form->addCheckbox('addonUsed');

		$form->addCheckbox('mobileAppUsed');

		$form->addHidden('shopId')
			->setRequired($this->translator->translate('front.refund.form.errors.commission.missingShop'));

		$form->addCheckbox('couponUsed');

		$form->addText('couponCode')
			->setRequired(false)
			->addRule($form::MAX_LENGTH, $this->translator->translate('front.refund.form.errors.commission.couponCodeLength'), 200)
		;

		$form->addText('expectedAmount');

		$form->addText('orderId')
			->setRequired($this->translator->translate('front.refund.form.errors.commission.orderIdMissing'));

		$form->addUpload('invoice');

		if ($this->configuration->getMode() !== 'test') {
			$form['invoice']->setRequired($this->translator->translate('front.refund.form.errors.commission.invoiceMissing'));
		}

		$form->addUpload('secondInvoice');

//		if ($this->localization->isPolish()) {
//			$form->addText('purchasedAtTime')
//				->setHtmlAttribute('type', 'time')
//				->setRequired($this->translator->translate('front.refund.form.errors.commission.orderTimeMissing'));
//		}

		$form->addTextArea('productUrls');

		$form->addTextArea('message');

		if (empty($this->defaultData) === false) {
			$form->setDefaults($this->defaultData);
		}

		$form->onAnchor[] = static function (Form $form): void {
			$presenter = $form->getPresenter();
			$form->setAction($presenter->link('this#missing-reward'));
		};
		$form->addSubmit('submit', 'front.form.label.send');
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$shop = $this->shopFacade->find($values->shopId);

			if (!$shop || ($shop instanceof Shop && (!$shop->isCashbackActive() && !$shop->getShopData()->hasLastTransactionIn()))) {
				throw new InvalidArgumentException();
			}

			$invoice = null;
			if ($values->invoice->hasFile()) {
				$invoice = $this->fileStorage->saveFile($values->invoice, File::NAMESPACE_REFUNDS);
			}

			$secondInvoice = null;
			if ($values->secondInvoice->hasFile()) {
				$secondInvoice = $this->fileStorage->saveFile($values->secondInvoice, File::NAMESPACE_REFUNDS);
			}

			if ($values->orderAmount <= 0 || !$orderAmount = $this->resolveAmount($values->orderAmount)) {
				Debugger::log($values->orderAmount, 'missingCommisionControl-incorrectAmount');
				throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.invalidOrderAmount'));
			}

			$expectedAmount = null;
//            $expectedAmount = $this->resolveAmount($values->expectedAmount);
//            if ($values->expectedAmount && !$expectedAmount) {
//                throw new InvalidArgumentException('neplátná očekávaná výše nákupu');
//            }

			$purchasedAt = new \DateTime($values->purchasedAt);

			$purchasedAtClone = clone $purchasedAt;
			$purchasedAtClone->setTime(0, 0, 0);

			$now = new \DateTime();
			$limitHours = $shop->getRefundHoursLimit();
			$interval = $purchasedAtClone->diff($now);
			$totalHours = ($interval->days * 24) + $interval->h + ($interval->i / 60);

			if ($totalHours < $limitHours) {
				throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.orderDateInterval', ['hours' => $limitHours]));
			}

			if ($purchasedAt < new \DateTime('- 2 years') || $purchasedAt > new \DateTime()) {
				throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.invalidOrderDateFormat'));
			}

			$userCreatedAt = clone $this->user->getCreatedAt()->setTime(0, 0, 0);
			if ($purchasedAt < $userCreatedAt) {
				throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.dateBeforeUserRegistration'));
			}

//			if ($this->localization->isPolish()) {
//				$purchasedAtTime = $values->purchasedAtTime;
//
//				if (!Strings::contains($purchasedAtTime, ':')) {
//					throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.invalidOrderTimeFormat'));
//				}
//
//				[$hours, $minutes] = explode(':', $purchasedAtTime);
//
//				if (!is_numeric($hours) || !is_numeric($minutes) || $hours > 23 || $hours < 0 || $minutes > 59 || $minutes < 0) {
//					throw new InvalidArgumentException($this->translator->translate('front.refund.form.errors.commission.invalidOrderTimeFormat'));
//				}
//
//				$purchasedAt->setTime((int) $hours, (int) $minutes);
//			}

			if ($purchasedAt->format('Ymd') === date('Ymd') || $purchasedAt->format('Ymd') === date('Ymd', strtotime('- 1 day'))) {
				throw new InvalidArgumentException($this->translator->translate('front.refund.form.dateAlert'));
			}

			$refund = $this->refundFacade->createMissingCommissionRefund(
				$this->user,
				$shop,
				$purchasedAt,
				$orderAmount,
				$values->orderCurrency,
				$values->orderId,
				$values->couponUsed,
				$values->couponCode,
				$expectedAmount,
				$invoice,
				$secondInvoice,
				$values->productUrls ?? null,
				$values->platform,
				$values->addonUsed,
				$values->mobileAppUsed,
				$values->message
			);

			$this->onSuccess($refund);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function resolveAmount(string $amount): ?float
	{
		$amount = Strings::trim($amount);
		$amount = str_replace([' ', ' ', ',-'], '', $amount);
		$amount = str_replace(',', '.', $amount);

		if (!is_numeric($amount)) {
			return null;
		}

		return (float) $amount;
	}
}

interface IRefundMissingCommissionControlFactory
{
	public function create(User $user): RefundMissingCommissionControl;
}
