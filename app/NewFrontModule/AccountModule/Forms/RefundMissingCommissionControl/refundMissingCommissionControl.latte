{form form, class => "mt-4"}
	<div class="bg-secondary-red text-white mt-3 rounded p-2 md:ml-5 md:mr-5" n:foreach="$form->errors as $error">
		{$error |noescape}
	</div>

	<div class="flex items-center justify-between mt-10 mb-2.5">
		<div class="text-lg font-medium leading-[31.5px] text-dark-1 pl-5 md:pl-10">{_'newFront.refund.missingCommission.title'}</div>
		<a class="flex items-center gap-2 text-sm leading-[24.5px] underline text-dark-1 xl:hover:no-underline" onclick="showSection('about-reward')" style="cursor: pointer;">
			<svg xmlns="http://www.w3.org/2000/svg" width="13" height="11" viewBox="0 0 13 11" fill="none">
				<path d="M12.2009 5.6038L0.994538 5.6038M0.994538 5.6038L5.74254 0.855893M0.994538 5.6038L5.74254 10.3519" stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
			</svg>
			{_'newFront.refund.back'}
		</a>
	</div>

	<div class="text-sm leading-[24.5px] text-dark-1 pl-5 md:pl-10 mb-5">
		{_'front.refund.missingCommission.refundsFormInfo' |noescape}
	</div>

	<div class="mb-5 px-5 pt-5 md:px-10 md:pt-10 pb-5 bg-white rounded-2xl">
		<div class="hidden mb-5 js-temu-error">
			<small class="flex my-3 text-xs leading-[21px] text-center bg-pastel-orange-light py-3 px-3 rounded border border-secondary-yellow">{_'newFront.refund.form.temuAlert' |noescape}</small>
		</div>

		<div class="md:w-1/2 md:mb-5 relative text-gray-600 focus-within:text-gray-400">
			<label for="purchase-date" class="block text-xs font-medium text-gray-700 mb-1">
				<span class="text-red-600">* </span> {_'newFront.refund.form.shops'}
			</label>
			<span class="absolute left-0 flex items-center pl-2 top-[32px]">
				<button type="submit" class="p-1 focus:outline-none focus:shadow-outline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
				  <path d="M12.247 12.247C11.0114 13.4826 9.33568 14.1767 7.58833 14.1767C5.84098 14.1767 4.16528 13.4826 2.92969 12.247C1.6941 11.0114 1 9.33568 1 7.58833C1 5.84098 1.6941 4.16528 2.92969 2.92969C4.16528 1.6941 5.84098 1 7.58833 1C9.33568 1 11.0114 1.6941 12.247 2.92969C13.4826 4.16528 14.1767 5.84098 14.1767 7.58833C14.1767 9.33568 13.4826 11.0114 12.247 12.247ZM12.247 12.247L17 17" stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				</button>
			</span>

			{*  @todo: NewFrontModule *}
			<input
					n:name="shopName"
					type="text"
					placeholder="{_'front.refund.form.shopsSearch'}"
					data-search-url="{plink searchShops!}"
					class="js-refund-shop-input py-2 text-sm text-black bg-white rounded-md pl-10 focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange block w-full border border-light-4 leading-[24.5px] px-3 pb-[11px] pt-3"
			>
		</div>

		<small class="js-refund-shop-no-shops hidden flex my-3 text-xs text-secondary-red font-bold leading-[21px] text-center bg-pastel-red-light py-3 px-3 rounded border border-secondary-red">{_'front.refund.form.noShops'}</small>

		<div class="refund-shop__wrapper grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-5 md:gap-5 mb-[25px]">
			{foreach range(1, 10) as $i}
				<div class="flex items-center justify-between md:flex-col border-b md:border border-light-4 md:rounded-[14px] p-3">
					<div class="md:py-9 md:px-6">
						<div class="w-[100px] h-[40px] m-auto"></div>
					</div>
					<svg class="hidden md:block m-auto" xmlns="http://www.w3.org/2000/svg" width="47" height="4" viewBox="0 0 47 4" fill="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M19.7482 0.19067C19.5961 0.175739 19.4416 0.184458 19.2973 0.216111C19.1529 0.247763 19.0227 0.301466 18.9173 0.372856L15.9413 2.38945L12.786 0.403588C12.5975 0.28532 12.3471 0.219627 12.0893 0.2208C11.8314 0.221972 11.587 0.289925 11.4093 0.409859L8.43277 2.42593L5.27858 0.440341C5.09034 0.321733 4.83981 0.255803 4.58181 0.256976C4.32381 0.25815 4.07935 0.326335 3.90194 0.446606L0.258952 2.91546C0.0846708 3.03656 -0.00818298 3.19897 0.000566741 3.36741C0.00931646 3.53585 0.118962 3.69675 0.305686 3.81518C0.49241 3.93361 0.741149 4.00003 0.99793 4C1.25471 3.99997 1.4988 3.9335 1.67723 3.81503L4.65272 1.79838L7.80809 3.78409C7.99662 3.90232 8.24703 3.96799 8.50484 3.96681C8.76265 3.96564 9.00701 3.89772 9.18475 3.77782L12.1609 1.76172L15.3162 3.74757C15.5048 3.8657 15.7551 3.93128 16.0129 3.93007C16.2706 3.92887 16.5149 3.86097 16.6926 3.74112L19.6678 1.72477L22.8232 3.71046C22.9162 3.77052 23.026 3.81814 23.1463 3.85056C23.2666 3.88298 23.395 3.89957 23.524 3.89935C23.653 3.89914 23.7801 3.88211 23.8978 3.84929C24.0156 3.81646 24.1217 3.76847 24.2101 3.70811C24.2984 3.64775 24.3672 3.57621 24.4124 3.49765C24.4576 3.41908 24.4784 3.33506 24.4736 3.25043C24.4688 3.16581 24.4384 3.08228 24.3842 3.00468C24.33 2.92707 24.2531 2.85695 24.158 2.79836L20.294 0.366593C20.1437 0.271916 19.9526 0.210307 19.7482 0.19067Z" fill="#D7DBE0"></path>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M42.2739 0.00709257C42.1218 -0.00783854 41.9673 0.000880556 41.823 0.0325329C41.6786 0.0641852 41.5484 0.117888 41.443 0.189278L38.467 2.20587L35.3117 0.22001C35.1232 0.101742 34.8728 0.0360495 34.615 0.037222C34.3571 0.0383945 34.1128 0.106347 33.9351 0.226281L30.9585 2.24235L27.8043 0.256763C27.616 0.138156 27.3655 0.0722251 27.1075 0.0733984C26.8495 0.0745717 26.6051 0.142758 26.4276 0.263028L22.7847 2.73188C22.6104 2.85298 22.5175 3.01539 22.5263 3.18383C22.535 3.35228 22.6447 3.51317 22.8314 3.6316C23.0181 3.75003 23.2669 3.81645 23.5236 3.81642C23.7804 3.81639 24.0245 3.74992 24.2029 3.63145L27.1784 1.6148L30.3338 3.60051C30.5223 3.71874 30.7727 3.78441 31.0305 3.78324C31.2884 3.78206 31.5327 3.71414 31.7105 3.59425L34.6866 1.57814L37.8419 3.56399C38.0305 3.68212 38.2808 3.7477 38.5386 3.7465C38.7963 3.74529 39.0406 3.67739 39.2183 3.55755L42.1935 1.54119L45.3489 3.52689C45.4419 3.58694 45.5517 3.63456 45.672 3.66698C45.7923 3.69941 45.9207 3.716 46.0497 3.71578C46.1787 3.71556 46.3058 3.69853 46.4235 3.66571C46.5413 3.63288 46.6474 3.5849 46.7358 3.52453C46.8241 3.46417 46.8929 3.39264 46.9381 3.31407C46.9834 3.23551 47.0042 3.15148 46.9993 3.06685C46.9945 2.98223 46.9641 2.8987 46.9099 2.8211C46.8557 2.74349 46.7788 2.67337 46.6837 2.61479L42.8197 0.183015C42.6695 0.088338 42.4783 0.0267296 42.2739 0.00709257Z" fill="#D7DBE0"></path>
					</svg>
					<div class="text-center text-xs leading-[24.5px] md:mt-[21px] md:mb-[25px]">...</div>
				</div>
			{/foreach}
		</div>

		<div class="js-refund-shop-id">
			{input shopId}
		</div>

		{*  @todo: NewFrontModule: V polsku je potřeba přidat i pole pro čas *}
		{ifset $form['purchasedAtTime']}
			<div class="md:w-1/2 mb-5">
				<label n:name="purchasedAt" class="block text-xs font-medium text-gray-700"><span class="text-red-600">* </span> {_'newFront.refund.form.date'}</label>
				<input n:name="purchasedAt" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				<div class="alert alert-info fz-s lh-m ta-left px-4 py-3 mt-3 mb-0 js-datepicker-alert" style="display: none;">{_'newFront.refund.form.dateAlert' |noescape}</div>
			</div>

			<div class="md:w-1/2 mb-5">
				<label n:name="purchasedAtTime" class="block text-xs font-medium text-gray-700">{_'newFront.refund.form.time'}</label>
				<input n:name="purchasedAtTime" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
				<div class="alert alert-info fz-s lh-m ta-left px-4 py-3 mt-3 mb-0" style="display: none;">{_'newFront.refund.form.dateAlert' |noescape}</div>

			</div>
		{else}
			<div class="md:w-1/2 mb-5">
				<label n:name="purchasedAt" class="block text-xs font-medium text-gray-700"><span class="text-red-600">* </span> {_'newFront.refund.form.date'}</label>
				<input n:name="purchasedAt" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
			</div>
		{/ifset}

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<label  n:name="invoice" class="block text-xs font-medium text-gray-700">
					<span class="text-red-600">* </span>{_'newFront.refund.form.invoice'}
				</label>
				<div class="mt-1 md:flex items-center border border-light-4 rounded-md">
					<label  n:name="invoice" class="bg-light-6 cursor-pointer leading-[24.5px] text-sm px-5 pt-[11px] pb-[10px] flex items-center rounded-l-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
						<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
							<path d="M13.4667 11.2V15.7333M11.2 13.4667H15.7333M6.66667 15.7333H2.13333C1.83276 15.7333 1.54448 15.614 1.33195 15.4014C1.1194 15.1889 1 14.9006 1 14.6V2.13333C1 1.83276 1.1194 1.54448 1.33195 1.33195C1.54448 1.1194 1.83276 1 2.13333 1H10.1641C10.4645 1.00006 10.7526 1.11936 10.965 1.33169L13.135 3.50164C13.3473 3.71411 13.4666 4.00216 13.4667 4.30253V6.66667M8.93333 13.4667C8.93333 14.669 9.41092 15.822 10.2611 16.6722C11.1113 17.5224 12.2644 18 13.4667 18C14.669 18 15.822 17.5224 16.6722 16.6722C17.5224 15.822 18 14.669 18 13.4667C18 12.2644 17.5224 11.1113 16.6722 10.2611C15.822 9.41092 14.669 8.93333 13.4667 8.93333C12.2644 8.93333 11.1113 9.41092 10.2611 10.2611C9.41092 11.1113 8.93333 12.2644 8.93333 13.4667Z" stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
						<span class="ml-2 text-sm leading-[24.5px] text-dark-1">
							{_'newFront.refund.form.chooseFile'}
						</span>
						<input n:name="invoice" class="sr-only" onchange="updateInvoice()">
					</label>
					<div class="block leading-[24.5px] text-dark-1 text-sm pl-[19px] pt-[11px] pb-[10px] bg-white border-l border-light-4 rounded-r-md flex-grow">
						<span id="file-name-invoice">{_'newFront.refund.form.noFile'}</span>
					</div>
				</div>
			</div>

			<div class="w-full">
				<label n:name="secondInvoice" class="block text-xs font-medium text-gray-700">
					{_'newFront.refund.form.secondInvoice'}
				</label>
				<div class="mt-1 md:flex items-center border border-light-4 rounded-md">
					<label n:name="secondInvoice" class="bg-light-6 cursor-pointer leading-[24.5px] text-sm px-5 pt-[11px] pb-[10px] flex items-center rounded-l-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
						<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
							<path d="M13.4667 11.2V15.7333M11.2 13.4667H15.7333M6.66667 15.7333H2.13333C1.83276 15.7333 1.54448 15.614 1.33195 15.4014C1.1194 15.1889 1 14.9006 1 14.6V2.13333C1 1.83276 1.1194 1.54448 1.33195 1.33195C1.54448 1.1194 1.83276 1 2.13333 1H10.1641C10.4645 1.00006 10.7526 1.11936 10.965 1.33169L13.135 3.50164C13.3473 3.71411 13.4666 4.00216 13.4667 4.30253V6.66667M8.93333 13.4667C8.93333 14.669 9.41092 15.822 10.2611 16.6722C11.1113 17.5224 12.2644 18 13.4667 18C14.669 18 15.822 17.5224 16.6722 16.6722C17.5224 15.822 18 14.669 18 13.4667C18 12.2644 17.5224 11.1113 16.6722 10.2611C15.822 9.41092 14.669 8.93333 13.4667 8.93333C12.2644 8.93333 11.1113 9.41092 10.2611 10.2611C9.41092 11.1113 8.93333 12.2644 8.93333 13.4667Z" stroke="#BDC2CC" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
						<span class="ml-2 text-sm leading-[24.5px] text-dark-1">{_'newFront.refund.form.chooseFile'}</span>
						<input n:name="secondInvoice" class="sr-only" onchange="updateOrder()">
					</label>
					<div class="block leading-[24.5px] text-dark-1 text-sm pl-[19px] pt-[11px] pb-[10px] bg-white border-l border-light-4 rounded-r-md flex-grow">
						<span id="file-name-order">{_'newFront.refund.form.noFile'}</span>
					</div>
				</div>
			</div>
		</div>

		<div class="w-full mb-[25px]">
			<label n:name="orderId" class="block text-xs font-medium text-gray-700"><span class="text-red-600">* </span>{_'newFront.refund.form.orderId'}</label>
			<input n:name="orderId" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
		</div>

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<label n:name="orderAmount" class="block text-xs font-medium text-gray-700"><span class="text-red-600">* </span>{_'newFront.refund.form.orderAmount'}</label>
				<input n:name="orderAmount" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
			</div>
			<div class="w-full">
				<label n:name="orderCurrency" class="block text-xs font-medium text-gray-700"><span class="text-red-600">* </span>{_'newFront.refund.form.orderCurrency'}</label>
				{input orderCurrency, class => "leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange"}
			</div>
		</div>

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<span class="block text-xs font-medium text-gray-700">{_'newFront.refund.form.couponUsed'}</span>

				<label n:name="couponUsed" class="inline-flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] mt-1 pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
					<div class="relative flex cursor-pointer items-center rounded-full mr-3" data-ripple-dark="true">
						<input class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10" n:name="couponUsed">
						<div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor" stroke="currentColor" stroke-width="1">
								<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
							</svg>
						</div>
					</div>
					{_'newFront.refund.form.couponUsed'}
				</label>
			</div>

			<div class="w-full">
				<div id="couponCode" class="hidden">
					<label n:name="couponCode" class="block text-xs font-medium text-gray-700">{_'newFront.refund.form.couponCode'}</label>
					<input n:name="couponCode" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange">
				</div>
			</div>
		</div>

		<div class="flex flex-col sm:flex-row gap-[25px] mb-[25px]">
			<div class="w-full">
				<div class="block text-xs font-medium text-gray-700 mb-1">{_'front.refund.form.usedPlatform'}</div>
				<div class="flex flex-col md:flex-row gap-3">
					{foreach $form[platform]->getItems() as $key => $label}
						<label class="flex w-full text-sm md:text-xs justify-start md:justify-between items-center border border-light-4 px-3 py-4 rounded-md cursor-pointer focus:outline-none focus:ring-1 focus:ring-primary-orange" n:name="platform:$key" data-ripple-dark="true">
							<input n:name="platform:$key" class="leading-[24.5px] checked:bg-primary-orange checked:hover:bg-primary-orange checked:active:bg-primary-orange checked:focus:bg-primary-orange focus:bg-primary-orange focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange mr-3">
							{$label}
						</label>
					{/foreach}
				</div>
			</div>
			<div class="w-full mt-4">
				<div id="useAddon" class="hidden">
					<label class="inline-flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] mt-1 pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
						<div class="relative flex cursor-pointer items-center rounded-full mr-3" data-ripple-dark="true">
							<input class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10" n:name="addonUsed">
							<div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor" stroke="currentColor" stroke-width="1">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
								</svg>
							</div>
						</div>
						{_'front.refund.form.addonUsed'}
					</label>
				</div>

				<div id="usePhoneApp" class="hidden">
					<label class="inline-flex text-sm items-center border border-light-4 leading-[24.5px] px-5 pb-[11px] mt-1 pt-3 rounded-md cursor-pointer focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange">
						<div class="relative flex cursor-pointer items-center rounded-full mr-3" data-ripple-dark="true">
							<input class="before:content[''] focus:ring-transparent text-primary-orange peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-primary-orange checked:bg-primary-orange checked:before:bg-primary-orange hover:before:opacity-10" n:name="mobileAppUsed">
							<div class="pointer-events-none absolute top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 text-white opacity-0 transition-opacity peer-checked:opacity-100">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor" stroke="currentColor" stroke-width="1">
									<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
								</svg>
							</div>
						</div>
						{_'front.refund.form.mobileAppUsed'}
					</label>
				</div>
			</div>
		</div>

		<div class="w-full mt-[25px]">
			<label for="message" class="block text-xs font-medium text-gray-700">{_'newFront.refund.form.userNote'}</label>
			<textarea n:name="message" rows="4" class="leading-[24.5px] text-sm mt-1 block w-full px-3 pb-[11px] pt-3 bg-white border border-light-4 rounded-md focus:outline-none focus:ring-1 focus:border-transparent focus:ring-primary-orange"></textarea>
		</div>

		{input submit, class => "mt-5 leading-7 font-bold text-white bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover"}
	</div>
{/form}

<script>
	// Use coupon
	document.getElementById('frm-refundMissingCommissionControl-form-couponUsed').addEventListener('change', function() {
		var couponCodeElement = document.getElementById('couponCode');

		if (this.checked) {
			couponCodeElement.classList.remove('hidden');
		} else {
			couponCodeElement.classList.add('hidden');
		}
	});

	document.querySelectorAll('input[name="platform"]').forEach(function(radio) {
		radio.addEventListener('change', function() {
			var useAddonElement = document.getElementById('useAddon');
			var usePhoneElement = document.getElementById('usePhoneApp');

			if (this.value === 'desktop') {
				useAddonElement.classList.remove('hidden');
				usePhoneElement.classList.add('hidden');
			} else if (this.value === 'mobile') {
				useAddonElement.classList.add('hidden');
				usePhoneElement.classList.remove('hidden');
			} else {
				useAddonElement.classList.add('hidden');
				usePhoneElement.classList.add('hidden');
			}
		});
	});



	// Missing Commision Shops search
	var searchUrl = document.querySelector('.js-refund-shop-input').dataset.searchUrl;

	function defaultSearch() {
		fetch(searchUrl)
		.then(response => response.json())
		.then(data => {
			var wrapper = document.querySelector('.refund-shop__wrapper');
			wrapper.innerHTML = '';

			data.forEach(function (val) {
				var listItem = document.createElement('li');
				listItem.className = 'refund-shop__item flex items-center justify-between md:flex-col border border-light-4 md:rounded-[14px] p-3 cursor-pointer js-refund-shop md:h-[212px]';
				listItem.dataset.shopId = val.id;
				listItem.dataset.refundHoursLimit = val.refundHoursLimit;

				listItem.innerHTML = '<div class="md:py-9 md:px-6"><img class="max-w-[100px] max-h-[40px] w-full m-auto" src="' + val.logo + '" alt="logo"></div><svg class="hidden md:block m-auto" xmlns="http://www.w3.org/2000/svg" width="47" height="4" viewBox="0 0 47 4" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M19.7482 0.19067C19.5961 0.175739 19.4416 0.184458 19.2973 0.216111C19.1529 0.247763 19.0227 0.301466 18.9173 0.372856L15.9413 2.38945L12.786 0.403588C12.5975 0.28532 12.3471 0.219627 12.0893 0.2208C11.8314 0.221972 11.587 0.289925 11.4093 0.409859L8.43277 2.42593L5.27858 0.440341C5.09034 0.321733 4.83981 0.255803 4.58181 0.256976C4.32381 0.25815 4.07935 0.326335 3.90194 0.446606L0.258952 2.91546C0.0846708 3.03656 -0.00818298 3.19897 0.000566741 3.36741C0.00931646 3.53585 0.118962 3.69675 0.305686 3.81518C0.49241 3.93361 0.741149 4.00003 0.99793 4C1.25471 3.99997 1.4988 3.9335 1.67723 3.81503L4.65272 1.79838L7.80809 3.78409C7.99662 3.90232 8.24703 3.96799 8.50484 3.96681C8.76265 3.96564 9.00701 3.89772 9.18475 3.77782L12.1609 1.76172L15.3162 3.74757C15.5048 3.8657 15.7551 3.93128 16.0129 3.93007C16.2706 3.92887 16.5149 3.86097 16.6926 3.74112L19.6678 1.72477L22.8232 3.71046C22.9162 3.77052 23.026 3.81814 23.1463 3.85056C23.2666 3.88298 23.395 3.89957 23.524 3.89935C23.653 3.89914 23.7801 3.88211 23.8978 3.84929C24.0156 3.81646 24.1217 3.76847 24.2101 3.70811C24.2984 3.64775 24.3672 3.57621 24.4124 3.49765C24.4576 3.41908 24.4784 3.33506 24.4736 3.25043C24.4688 3.16581 24.4384 3.08228 24.3842 3.00468C24.33 2.92707 24.2531 2.85695 24.158 2.79836L20.294 0.366593C20.1437 0.271916 19.9526 0.210307 19.7482 0.19067Z" fill="#D7DBE0"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M42.2739 0.00709257C42.1218 -0.00783854 41.9673 0.000880556 41.823 0.0325329C41.6786 0.0641852 41.5484 0.117888 41.443 0.189278L38.467 2.20587L35.3117 0.22001C35.1232 0.101742 34.8728 0.0360495 34.615 0.037222C34.3571 0.0383945 34.1128 0.106347 33.9351 0.226281L30.9585 2.24235L27.8043 0.256763C27.616 0.138156 27.3655 0.0722251 27.1075 0.0733984C26.8495 0.0745717 26.6051 0.142758 26.4276 0.263028L22.7847 2.73188C22.6104 2.85298 22.5175 3.01539 22.5263 3.18383C22.535 3.35228 22.6447 3.51317 22.8314 3.6316C23.0181 3.75003 23.2669 3.81645 23.5236 3.81642C23.7804 3.81639 24.0245 3.74992 24.2029 3.63145L27.1784 1.6148L30.3338 3.60051C30.5223 3.71874 30.7727 3.78441 31.0305 3.78324C31.2884 3.78206 31.5327 3.71414 31.7105 3.59425L34.6866 1.57814L37.8419 3.56399C38.0305 3.68212 38.2808 3.7477 38.5386 3.7465C38.7963 3.74529 39.0406 3.67739 39.2183 3.55755L42.1935 1.54119L45.3489 3.52689C45.4419 3.58694 45.5517 3.63456 45.672 3.66698C45.7923 3.69941 45.9207 3.716 46.0497 3.71578C46.1787 3.71556 46.3058 3.69853 46.4235 3.66571C46.5413 3.63288 46.6474 3.5849 46.7358 3.52453C46.8241 3.46417 46.8929 3.39264 46.9381 3.31407C46.9834 3.23551 47.0042 3.15148 46.9993 3.06685C46.9945 2.98223 46.9641 2.8987 46.9099 2.8211C46.8557 2.74349 46.7788 2.67337 46.6837 2.61479L42.8197 0.183015C42.6695 0.088338 42.4783 0.0267296 42.2739 0.00709257Z" fill="#D7DBE0"></path></svg><div class="text-center text-xs leading-[24.5px] md:mt-[21px] md:mb-[25px]">' + val.name + '</div></small>';

				wrapper.appendChild(listItem);
			});

			clickRefundShop();
		});
	}

	defaultSearch();

	// Search input type
	var searchInput = document.querySelector('.js-refund-shop-input');
	var timeoutId; // Proměnná pro uložení ID timeoutu
	var noShopsElement = document.querySelector('.js-refund-shop-no-shops');

	// Funkce, která obalí logiku volání API a zpracování odpovědi
	function fetchShopResults(query) {
		// Pokud je query prázdné
		if (query.trim() === '') {
			document.querySelector('.refund-shop__wrapper').innerHTML = '';
			document.querySelector('.js-refund-shop-id input').value = '';

			if (noShopsElement) {
				noShopsElement.classList.add('hidden');
			}

			defaultSearch();
			return;
		}

		var qSearchUrl = searchUrl + '&q=' + encodeURIComponent(query);

		fetch(qSearchUrl)
			.then(response => {
				// Kontrola, zda je odpověď OK (status 200-299)
				if (!response.ok) {
					// Zpracování chybové odpovědi (např. 429 Too Many Requests)
					console.error('API responded with status: ' + response.status);
					throw new Error('Network response was not ok.');
				}
				return response.json();
			})
			.then(data => {
				var wrapper = document.querySelector('.refund-shop__wrapper');
				wrapper.innerHTML = ''; // Vyčistíme předchozí výsledky

				if (noShopsElement) {
					noShopsElement.classList.add('hidden');
				}

				// Pokud data nejsou pole nebo jsou prázdná
				if (!Array.isArray(data) || data.length === 0) {
					// Zobrazime zprávu, že nebyly nalezeny žádné výsledky
					if (noShopsElement) {
						noShopsElement.classList.remove('hidden');
					}

					document.querySelector('.js-refund-shop-id input').value = '';
					return;
				}


				data.forEach(function (val) {
					var listItem = document.createElement('li');
					listItem.className = 'refund-shop__item flex items-center justify-between md:flex-col border border-light-4 md:rounded-[14px] p-3 cursor-pointer js-refund-shop md:h-[212px]';
					listItem.dataset.shopId = val.id;
					listItem.dataset.refundHoursLimit = val.refundHoursLimit;
					listItem.innerHTML = '<div class="md:py-9 md:px-6"><img class="max-w-[100px] max-h-[40px] w-full m-auto" src="' + val.logo + '" alt="logo"></div><svg class="hidden md:block m-auto" xmlns="http://www.w3.org/2000/svg" width="47" height="4" viewBox="0 0 47 4" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M19.7482 0.19067C19.5961 0.175739 19.4416 0.184458 19.2973 0.216111C19.1529 0.247763 19.0227 0.301466 18.9173 0.372856L15.9413 2.38945L12.786 0.403588C12.5975 0.28532 12.3471 0.219627 12.0893 0.2208C11.8314 0.221972 11.587 0.289925 11.4093 0.409859L8.43277 2.42593L5.27858 0.440341C5.09034 0.321733 4.83981 0.255803 4.58181 0.256976C4.32381 0.25815 4.07935 0.326335 3.90194 0.446606L0.258952 2.91546C0.0846708 3.03656 -0.00818298 3.19897 0.000566741 3.36741C0.00931646 3.53585 0.118962 3.69675 0.305686 3.81518C0.49241 3.93361 0.741149 4.00003 0.99793 4C1.25471 3.99997 1.4988 3.9335 1.67723 3.81503L4.65272 1.79838L7.80809 3.78409C7.99662 3.90232 8.24703 3.96799 8.50484 3.96681C8.76265 3.96564 9.00701 3.89772 9.18475 3.77782L12.1609 1.76172L15.3162 3.74757C15.5048 3.8657 15.7551 3.93128 16.0129 3.93007C16.2706 3.92887 16.5149 3.86097 16.6926 3.74112L19.6678 1.72477L22.8232 3.71046C22.9162 3.77052 23.026 3.81814 23.1463 3.85056C23.2666 3.88298 23.395 3.89957 23.524 3.89935C23.653 3.89914 23.7801 3.88211 23.8978 3.84929C24.0156 3.81646 24.1217 3.76847 24.2101 3.70811C24.2984 3.64775 24.3672 3.57621 24.4124 3.49765C24.4576 3.41908 24.4784 3.33506 24.4736 3.25043C24.4688 3.16581 24.4384 3.08228 24.3842 3.00468C24.33 2.92707 24.2531 2.85695 24.158 2.79836L20.294 0.366593C20.1437 0.271916 19.9526 0.210307 19.7482 0.19067Z" fill="#D7DBE0"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M42.2739 0.00709257C42.1218 -0.00783854 41.9673 0.000880556 41.823 0.0325329C41.6786 0.0641852 41.5484 0.117888 41.443 0.189278L38.467 2.20587L35.3117 0.22001C35.1232 0.101742 34.8728 0.0360495 34.615 0.037222C34.3571 0.0383945 34.1128 0.106347 33.9351 0.226281L30.9585 2.24235L27.8043 0.256763C27.616 0.138156 27.3655 0.0722251 27.1075 0.0733984C26.8495 0.0745717 26.6051 0.142758 26.4276 0.263028L22.7847 2.73188C22.6104 2.85298 22.5175 3.01539 22.5263 3.18383C22.535 3.35228 22.6447 3.51317 22.8314 3.6316C23.0181 3.75003 23.2669 3.81645 23.5236 3.81642C23.7804 3.81639 24.0245 3.74992 24.2029 3.63145L27.1784 1.6148L30.3338 3.60051C30.5223 3.71874 30.7727 3.78441 31.0305 3.78324C31.2884 3.78206 31.5327 3.71414 31.7105 3.59425L34.6866 1.57814L37.8419 3.56399C38.0305 3.68212 38.2808 3.7477 38.5386 3.7465C38.7963 3.74529 39.0406 3.67739 39.2183 3.55755L42.1935 1.54119L45.3489 3.52689C45.4419 3.58694 45.5517 3.63456 45.672 3.66698C45.7923 3.69941 45.9207 3.716 46.0497 3.71578C46.1787 3.71556 46.3058 3.69853 46.4235 3.66571C46.5413 3.63288 46.6474 3.5849 46.7358 3.52453C46.8241 3.46417 46.8929 3.39264 46.9381 3.31407C46.9834 3.23551 47.0042 3.15148 46.9993 3.06685C46.9945 2.98223 46.9641 2.8987 46.9099 2.8211C46.8557 2.74349 46.7788 2.67337 46.6837 2.61479L42.8197 0.183015C42.6695 0.088338 42.4783 0.0267296 42.2739 0.00709257Z" fill="#D7DBE0"></path></svg><div class="text-center text-xs leading-[24.5px] md:mt-[21px] md:mb-[25px]">' + val.name + '</div></small>';

					wrapper.appendChild(listItem);
				});

				document.querySelector('.js-refund-shop-id input').value = '';

				clickRefundShop();
			})
			.catch(error => {
				// Zachycení chyb z fetch (síťové problémy, response.ok === false atd.)
				console.error('Chyba při volání API:', error);
			});
	}

	// Debounce funkce - vezme funkci a zpoždění (v ms)
	function debounce(func, delay) {
		let timer;
		return function(...args) {
			const context = this;
			clearTimeout(timer);
			timer = setTimeout(() => {
				func.apply(context, args);
			}, delay);
		};
	}

	// Vytvoříme debouncovanou verzi naší funkce fetchShopResults
	// Nastavte zpoždění (např. 300ms - 500ms je obvyklé pro typeahead)
	const debouncedFetchShopResults = debounce(fetchShopResults, 400);

	// Použijeme debouncovanou funkci v event listeneru
	searchInput.addEventListener('input', function () {
		var sQuery = searchInput.value;
		// Voláme debouncovanou verzi, která se spustí se zpožděním
		debouncedFetchShopResults(sQuery);
	});

	// Click refund shop
	function clickRefundShop() {
		var refundShops = document.querySelectorAll('.js-refund-shop');
		refundShops.forEach(function (shop) {
			shop.addEventListener('click', function (e) {
				e.preventDefault();
				selectRefundShop(this);
			});
			//console.log(shop);
		});
	}

	function selectRefundShop(element) {
		var shopId = element.dataset.shopId;

		document.querySelectorAll('.refund-shop__item').forEach(function (item) {
			item.classList.remove('border-secondary-green', 'text-secondary-green', 'bg-pastel-green-light');
		});

		element.classList.add('border-secondary-green', 'text-secondary-green', 'bg-pastel-green-light');

		document.querySelector('.js-refund-shop-id input').value = shopId;

		// Shop is Aliexpress -> show textarea
		var productUrlTextarea = document.querySelector('.js-show-product-url-textarea');
		if (productUrlTextarea) {
			var shopUrlId = productUrlTextarea.dataset.shopId;

			if (shopUrlId == shopId) {
				productUrlTextarea.classList.remove('hide');
				console.log('show');
			} else {
				productUrlTextarea.classList.add('hide');
				console.log('hide');
			}
		}
	}

</script>

<script n:syntax="off">

	// update datepicker date range
	function restrictPurchasedAtTo48HoursBack() {
		const input = document.querySelector('input[name="purchasedAt"]');
		if (!input) return;

		const now = new Date();
		const maxDateObj = new Date(now.getTime() - 48 * 60 * 60 * 1000);

		const yyyy = maxDateObj.getFullYear();
		const mm = String(maxDateObj.getMonth() + 1).padStart(2, '0');
		const dd = String(maxDateObj.getDate()).padStart(2, '0');
		const maxDateStr = `${yyyy}-${mm}-${dd}`;

		input.setAttribute('max', maxDateStr);
		//console.log(`Input 'purchasedAt' omezen na max: ${maxDateStr}`);
	}

	document.addEventListener('DOMContentLoaded', restrictPurchasedAtTo48HoursBack);

	// update datepicker date range by shop hours
	document.querySelector('.refund-shop__wrapper')?.addEventListener('click', function (event) {
		const item = event.target.closest('.refund-shop__item');
		if (!item) return;

		const limitHours = parseInt(item.getAttribute('data-refund-hours-limit'), 10);
		const purchasedInput = document.querySelector('input[name="purchasedAt"]');
		if (!purchasedInput || isNaN(limitHours)) return;

		const now = new Date();
		const maxDateObj = new Date(now.getTime() - limitHours * 60 * 60 * 1000);

		const yyyy = maxDateObj.getFullYear();
		const mm = String(maxDateObj.getMonth() + 1).padStart(2, '0');
		const dd = String(maxDateObj.getDate()).padStart(2, '0');
		const maxDateStr = `${yyyy}-${mm}-${dd}`;

		purchasedInput.setAttribute('max', maxDateStr);

		const currentDateStr = purchasedInput.value;
		if (currentDateStr) {
			const currentDate = new Date(currentDateStr);
			if (currentDate > maxDateObj) {
			purchasedInput.value = maxDateStr;
			//console.log(`Datum bylo upraveno na maximálně povolené: ${maxDateStr}`);
			} else {
			//console.log(`Datum je v pořádku: ${currentDateStr}`);
			}
		} else {
			//console.log(`Nastaven maximální možný datum: ${maxDateStr}`);
		}
	});

	// show temu error message
	document.addEventListener('click', function (event) {
		const clickedItem = event.target.closest('.refund-shop__item');
		if (!clickedItem) return;

		const shopId = clickedItem.getAttribute('data-shop-id');
		const allowedShopIds = ['12067', '12068', '12069', '12070', '12071', '12072', '12073', '11488'];

		const errorEl = document.querySelector('.js-temu-error');
		if (!errorEl) return;

		if (allowedShopIds.includes(shopId)) {
			errorEl.classList.remove('hidden');
		} else {
			errorEl.classList.add('hidden');
		}
	});





</script>
