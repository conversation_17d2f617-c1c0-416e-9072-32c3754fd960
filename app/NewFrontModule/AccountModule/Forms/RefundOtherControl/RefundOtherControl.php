<?php

namespace tipli\NewFrontModule\AccountModule\Forms;

use Nette\Localization\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use tipli\Model\Account\Entities\User;
use tipli\Model\Freshdesk\FreshdeskFacade;
use tipli\Model\Localization\LocalizationFacade;

class RefundOtherControl extends Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var User */
	private $user;

	/** @var FreshdeskFacade */
	private $freshdeskFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var Translator */
	private $translator;

	public function __construct(User $user, FreshdeskFacade $freshdeskFacade, LocalizationFacade $localizationFacade, Translator $translator)
	{
				$this->user = $user;
		$this->freshdeskFacade = $freshdeskFacade;
		$this->localizationFacade = $localizationFacade;
		$this->translator = $translator;
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/refundOtherControl.latte');
		$this->template->render();
	}

	public function createComponentForm()
	{
		$form = new Form();

		$form->setTranslator($this->translator);

		$form->addTextArea('message')
			->setRequired($this->translator->translate('front.refund.form.errors.messageMissing'))
			->setMaxLength(4555);

		$form->addSubmit('submit', 'front.form.label.send');
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		$message = $values->message;

		$subject = $this->translator->translate('front.refund.other.subject');

		$localization = $this->localizationFacade->getCurrentLocalization();
		$locale = $localization->getLocale();

		$this->freshdeskFacade->createTicket($localization, $this->user->getEmail(), $subject, $message, $locale, $this->user->getId());

		$this->onSuccess();
	}
}

interface IRefundOtherControlFactory
{
	/**
	 * @param User $user
	 * @return RefundOtherControl
	 */
	public function create(User $user);
}
