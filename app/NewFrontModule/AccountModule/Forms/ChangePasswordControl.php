<?php

namespace tipli\NewFrontModule\AccountModule\Forms;

use tipli\Model\Doctrine\EntityManager;
use Nette\Localization\Translator;
use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\BrowserTokenLayer;
use tipli\Model\Layers\ClientLayer;

class ChangePasswordControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];
	public $onError = [];

	/**
	 * @var Translator
	 */
	private $translator;

	/**
	 * @var EntityManager
	 */
	private $em;

	/**
	 * @var User|null
	 */
	private $user;

	/**
	 * @var UserFacade
	 */
	private $userFacade;

	/**
	 * @var BrowserTokenLayer
	 */
	private $browserTokenLayer;
	/**
	 * @var ClientLayer
	 */
	private $clientLayer;

	public function __construct(?User $user, private $needVerification, Translator $translator, EntityManager $em, UserFacade $userFacade, BrowserTokenLayer $browserTokenLayer, ClientLayer $clientLayer)
	{
		$this->translator = $translator;
		$this->em = $em;
		$this->user = $user;
		$this->userFacade = $userFacade;
		$this->browserTokenLayer = $browserTokenLayer;
		$this->clientLayer = $clientLayer;
	}

	/**
	 * @return Form|null
	 */
	public function createComponentForm()
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		if ($this->user->hasFilledPassword()) {
			$form->addPassword('oldPassword', 'front.account.user.changePassword.form.oldPassword')
				->setRequired('front.account.user.changePassword.form.validator.oldPasswordRequired')
				->setMaxLength(100)
			;
		}

		$form->addPassword('newPassword', 'front.account.user.changePassword.form.newPassword')
			->setRequired('front.account.user.changePassword.form.validator.newPasswordRequired')
			->addRule($form::MIN_LENGTH, 'front.account.user.changePassword.form.validator.newPasswordNotValid', User::MINIMAL_PASSWORD_LENGTH)
			->setMaxLength(100)
		;

		if ($this->needVerification) {
			$form->addPassword('newPasswordVerify', 'front.account.user.changePassword.form.newPasswordVerify')
				->setRequired('front.account.user.changePassword.form.validator.newPasswordVerifyRequired')
				->addRule($form::EQUAL, 'front.account.user.changePassword.form.validator.passwordsNotEqual', $form['newPassword'])
				->setMaxLength(100)
			;
		}

		$form->addSubmit('submit', 'front.account.user.changePassword.form.submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if (ContentFilter::containsEmoji($values->newPassword)) {
				throw new InvalidArgumentException($this->translator->translate('front.account.user.changePassword.form.validator.passwordContainsInvalidCharacters'));
			}

			if ($this->user->hasFilledPassword() && !$this->userFacade->validatePassword($this->user, $values->oldPassword)) {
				throw new InvalidArgumentException($this->translator->translate('front.account.user.changePassword.form.validator.oldPasswordIncorrect'));
			}

			$change = $this->userFacade->createChange($this->user, $this->browserTokenLayer->getBrowserToken(), $this->clientLayer->getIp(), $this->clientLayer->getUserAgent(), false);
			$change->setPasswordChanged(true);

			$this->userFacade->saveChange($change);

			$this->userFacade->setPassword($this->user, $values->newPassword);

			$this->em->flush($this->user);
			$this->onSuccess($this);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
			$this->onError();
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

interface IChangePasswordControlFactory
{
	/**
	 * @return ChangePasswordControl
	 */
	public function create(?User $user = null, $needVerification = true);
}
