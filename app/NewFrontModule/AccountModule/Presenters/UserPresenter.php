<?php

namespace tipli\NewFrontModule\AccountModule\Presenters;

use tipli\FrontModule\AccountModule\Forms\ChangePasswordControl;
use tipli\FrontModule\AccountModule\Forms\IAccountNumberControlFactory;
use tipli\FrontModule\AccountModule\Forms\IChangePasswordControlFactory;
use tipli\FrontModule\AccountModule\Forms\IGuaranteeControlFactory;
use tipli\FrontModule\AccountModule\Forms\IPhoneNumberVerificationControlFactory;
use tipli\FrontModule\AccountModule\Forms\ISessionVerificationControlFactory;
use tipli\FrontModule\AccountModule\Forms\ITellFriendControlFactory;
use tipli\FrontModule\AccountModule\Forms\TellFriendControl;
use tipli\FrontModule\Forms\IAccountDeletionRequestControlFactory;
use tipli\NewFrontModule\AccountModule\Forms\UserProfileControl\UserProfileControl;
use tipli\NewFrontModule\AccountModule\Forms\UserProfileControl\UserProfileControlFactory;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\InvalidArgumentException;
use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\PhoneNumberVerificationManager;
use tipli\Model\Account\UserFacade;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Rewards\Entities\ConstantReward;
use tipli\Model\Session\SectionFactory;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\FrontModule\Forms\AccountDeletionRequestControl;
use tipli\TooManyRequestsException;

class UserPresenter extends AccountSectionPresenter
{
	/** @var UserProfileControlFactory @inject */
	public $userProfileControlFactory;

	/** @var IAccountNumberControlFactory @inject */
	public $accountNumberFactory;

	/** @var IChangePasswordControlFactory @inject */
	public $changePasswordControlFactory;

	/** @var IGuaranteeControlFactory @inject */
	public $guaranteeControlFactory;

	/** @var ITellFriendControlFactory @inject */
	public $tellFriendControlFactory;

	/** @var IPhoneNumberVerificationControlFactory @inject */
	public $phoneNumberVerificationControlFactory;

	/** @var ISessionVerificationControlFactory @inject */
	public $sessionVerificationControlFactory;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var SectionFactory @inject */
	public $sectionFactory;

	/** @var MessageFacade @inject */
	public $messageFacade;

	/** @var PayoutFacade @inject */
	public $payoutFacade;

	/** @var PhoneNumberVerificationManager @inject */
	public $phoneNumberVerificationManager;

	/** @var EmailSubscriptionManager @inject */
	public $emailSubscriptionManager;

	/** @var IAccountDeletionRequestControlFactory @inject */
	public $accountDeletionRequestControlFactory;


	/** @persistent */
	public $backLink = '';

	/** @persistent */
	public $verificationWay = null;

	public function startup()
	{
		parent::startup();

		$this->checkBackLinkUrl($this->backLink);
	}

	public function renderTellFriend()
	{
		$recommendedUsersQuery = $this->userFacade->createUsersQuery()
			->withParentUser($this->getUserIdentity())
			->onlyActive()
			->sortNewest();

		$recommendedUsers = $this->userFacade->fetch($recommendedUsersQuery);

		$this['paginator']->setItemsCount($recommendedUsers->getTotalCount());

		$paginator = $this['paginator']->getPaginator();
		$recommendedUsers->applyPaginator($paginator);

		$this->template->recommendedUsers = $recommendedUsers;

		$this->template->getRecommendedUserProgress = function (User $recommendedUser) {
			/** @var Transaction $transaction */
			$transaction = $this->transactionFacade->findRecommendationBonus($recommendedUser);

			if ($transaction && $transaction->isConfirmed()) {
				$bonusAmount = $transaction->getBonusAmount();
				$bonusCurrency = $transaction->getCurrency();

				return (object) [
					'confirmed' => true,
					'bonusAmount' => $bonusAmount,
					'bonusCurrency' => $bonusCurrency,
				];
			}

			if ($transaction) {
				$bonusAmount = $transaction->getBonusAmount();
				$bonusCurrency = $transaction->getCurrency();
				$confirmationTreshold = $transaction->getConfirmationTreshold();
				$recommendationBonusTreshold = $transaction->getRecommendationBonusTreshold();
			} else {
				/** @var ConstantReward $constantReward */
				$constantReward = $this->rewardFacade->findConstantRewardByType($recommendedUser->getLocalization(), ConstantReward::TYPE_BONUS_RECOMMENDATION, $recommendedUser->getCreatedAt());

				$bonusAmount = $constantReward->getAmount();
				$bonusCurrency = $constantReward->getCurrency();
				$confirmationTreshold = $constantReward->getConfirmationTreshold();
				$recommendationBonusTreshold = $constantReward->getRecommendationBonusTreshold();
			}

			$userBalance = $this->transactionFacade->getConfirmedCommissionBalance($this->getUserIdentity());
			$recommendedUserBalance = $this->transactionFacade->getConfirmedLifetimeCommissionBalance($recommendedUser);

			$userProgressInPercentage = $confirmationTreshold > 0 ? ($userBalance / $confirmationTreshold) * 100 : 100;
			$userProgressInPercentage = $userProgressInPercentage > 100 ? 100 : $userProgressInPercentage;

			$recommendedUserProgressInPercentage = $recommendationBonusTreshold > 0 ? ($recommendedUserBalance / $recommendationBonusTreshold) * 100 : 100;
			$recommendedUserProgressInPercentage = $recommendedUserProgressInPercentage > 100 ? 100 : $recommendedUserProgressInPercentage;
			$transactionProgressInPercentage = round(($userProgressInPercentage + $recommendedUserProgressInPercentage) / 2);

			$userMissingToConfirm = $confirmationTreshold - $userBalance;
			$userMissingToConfirm = $userMissingToConfirm < 0 ? 0 : $userMissingToConfirm;

			$recommendedUserMissingToConfirm = $recommendationBonusTreshold - $recommendedUserBalance;
			$recommendedUserMissingToConfirm = $recommendedUserMissingToConfirm < 0 ? 0 : $recommendedUserMissingToConfirm;

			return (object) [
				'confirmed' => false,
				'bonusAmount' => $bonusAmount,
				'bonusCurrency' => $bonusCurrency,
				'transactionProgressInPercentage' => $transactionProgressInPercentage,
				'userMissingToConfirm' => $userMissingToConfirm,
				'recommendedUserMissingToConfirm' => $recommendedUserMissingToConfirm,
			];
		};

		$this->template->getTopShops = (function () {
			return $this->shopFacade->findTopShops(8, $this->getLocalization(), true);
		});
	}

	public function renderDefault()
	{
		/** @var User $user */
		$user = $this->getUserIdentity();
		$this->template->isWaitingForAccountNumberVerification = !$user->hasVerifiedAccountNumber() && $user->getAccountNumber();
	}

	public function renderGuarantee()
	{
//		if (
//			$this->getLocalization()->isCzech()
//			|| $this->getLocalization()->isSlovak()
//			|| $this->getLocalization()->isPolish()
//			|| $this->getLocalization()->isRomanian()
//		) {
			$this->redirect('Refund:');
//		}
	}

	public function createComponentPaginator()
	{
		$control = $this->paginatorFactory->create();

		$control->setItemsPerPage(10);
		$control->disablePages();

		$control->onChange[] = function () {
			$this->redrawControl('recommendedUsers');
			$this->redrawControl('paginator');
		};

		return $control;
	}

	protected function createComponentUserProfileControl(): UserProfileControl
	{
		$control = $this->userProfileControlFactory->create();
		$control->onSuccess[] = function ($control, $needPhoneNumberVerification) {
			if ($needPhoneNumberVerification) {
				$this->redirect('phoneNumberVerification');
			} else {
				$this->flashMessage($this->translator->translate('front.account.user.settings.form.messages.success'));

				if ($this->backLink) {
					$this->redirectUrl($this->backLink);
				} else {
					$this->redirect('this');
				}
			}
		};

		return $control;
	}

	protected function createComponentAccountNumberControl()
	{
		$control = $this->accountNumberFactory->create($this->getUserIdentity());

		$control->onSuccess[] = function () {
				$this->flashMessage($this->translator->translate('front.account.user.settings.form.messages.accountNumberVerification'));
				$this->redirect('accountNumberChangeVerification');
		};

		$control->onError[] = function () {
			$this->redrawControl('accountNumberControlErrors');
		};

		return $control;
	}

	/**
	 * @return ChangePasswordControl
	 */
	protected function createComponentChangePasswordControl()
	{
		$control = $this->changePasswordControlFactory->create($this->getUserIdentity());
		$control->onSuccess[] = function () {
			$this->flashMessage($this->translator->translate('front.account.user.changePassword.form.messages.success'));
			$this->redirect('this');
		};

		$control->onError[] = function () {
			$this->redrawControl('changePasswordControlErrors');
		};

		return $control;
	}

	protected function createComponentGuaranteeControl()
	{
		$control = $this->guaranteeControlFactory->create();
		$control->onSuccess[] = function ($control) {
			$this->flashMessage($this->translator->translate('front.account.user.guarantee.form.messages.success'));
			$this->redirect('this');
		};

		return $control;
	}

	/**
	 * @return TellFriendControl
	 */
	protected function createComponentTellFriendControl()
	{
		$control = $this->tellFriendControlFactory->create();
		$control->onSuccess[] = function () {
			$this->flashMessage($this->translator->translate('front.account.user.tellFriend.form.messages.success'));
			$this->redirect('this');
		};

		return $control;
	}

	public function actionSettings($unsubscribeContentType = null)
	{
		if ($unsubscribeContentType !== null && !in_array($unsubscribeContentType, SendingPolicy::getEmailContentTypes())) {
			$this->redirect('this', ['unsubscribeContentType' => null]);
		}

		$this->template->account = $this->userFacade->find($this->getUserIdentity()->getId());
		$this->template->translator = $this->translator;
		$this->template->unsubscribeContentType = $unsubscribeContentType;

		$this->template->isThereLuckyShops = $this->configuration->isThereLuckyShops($this->getLocalization());
		$this->template->resendAccountNumberVerificationEmailAllowed = true;

		$lastEmail = $this->messageFacade->findLastAccountNumberVerificationEmail($this->getUserIdentity());

		if ($lastEmail && $lastEmail->getCreatedAt() > new \DateTime('-1 hour')) {
			$this->template->resendAccountNumberVerificationEmailAllowed = false;
		}
	}

	public function handleUnSubscribeEmail($contentType)
	{
		if (!in_array($contentType, SendingPolicy::getEmailContentTypes())) {
			throw new InvalidArgumentException('unknown unsubscribe email type: ' . $contentType);
		}

		$this->emailSubscriptionManager->unsubscribeContentType(
			$this->getUserIdentity(),
			null,
			$contentType,
			EmailSubscriptionManager::SITE_TIPLI,
			EmailSubscriptionManager::SOURCE_APPLICATION,
			'odhlaseno samotnym uzivatelem '
		);

		$this->flashMessage($this->translator->translate('front.account.user.settings.unsubscribeEmail.messages.success'));
		$this->redirect(':NewFront:Account:User:settings');
	}

	public function actionEmailVerified()
	{
		/** @var User $user */
		$user = $this->getUserIdentity();

		if ($user->hasVerifiedEmail()) {
			$this->flashMessage($this->translator->translate('front.verifymsg.success'));
		}

		$this->redirect(':NewFront:Homepage:default');
	}

	public function renderPhoneNumberVerification($sendVerificationSmsAgain = false)
	{
		/** @var User $user */
		$user = $this->getUser()->getIdentity();

		if ($user->hasVerifiedPhoneNumber()) {
			$this->redirect('settings');
		}

		try {
			$this->phoneNumberVerificationManager->requestVerification($user, $sendVerificationSmsAgain, $this->clientLayer->getIp(), $this->clientLayer->getPlatform());
		} catch (TooManyRequestsException $e) {
			$this->flashMessage($this->translator->translate('front.verifysms.tooManyRequests'));
		}

		if ($sendVerificationSmsAgain) {
			$this->redirect('this', ['sendVerificationSmsAgain' => false]);
		}
	}

	public function renderAccountNumberVerification()
	{
		/** @var User $user */
		$user = $this->getUser()->getIdentity();

		if ($user->hasVerifiedAccountNumber()) {
			$this->redirect('settings');
		}
	}

	public function createComponentPhoneNumberVerificationControl()
	{
		$control = $this->phoneNumberVerificationControlFactory->create($this->getUserIdentity());
		$control->onSuccess[] = (function () {
			if ($this->backLink) {
				$this->redirectUrl($this->backLink);
			} else {
				$this->flashMessage($this->translator->translate('front.verifysms.success'));
				$this->redirect('settings');
			}
		});

		return $control;
	}

	public function renderSessionVerification($sendVerificationSmsAgain, string $verificationWay = null, $backLink)
	{
		/** @var User $user */
		$user = $this->getUserIdentity();

		if (!$user->getPhoneNumber()) {
			return;
		}

		if ($this->verificationWay === null) {
			if ($user->getUserSettings()->getTwoFactorHash() !== null) {
				$this->redirect('this', ['backLink' => $backLink, 'verificationWay' => '2FA']);
			} else {
				$this->redirect('this', ['backLink' => $backLink, 'verificationWay' => 'sms']);
			}
		}

		if ($verificationWay === 'sms') {
			$session = $this->sessionFacade->findSessionToValid($user);

			if (!$session || $sendVerificationSmsAgain) {
				$sessionSection = $this->sectionFactory->getSessionRequestSecuritySection();
				$requestBlockedTill = $sessionSection->requestBlockedTill;

				if ($requestBlockedTill && $requestBlockedTill > new \DateTime()) {
					return;
				}

				$sessionSection->requestBlockedTill = (new \DateTime())->modify('+ 1 minute');

				$this->sessionFacade->requestSession($user);
			}
		}

		$this->template->verificationWay = $verificationWay;
	}

	public function createComponentSessionVerificationControl()
	{
		$control = $this->sessionVerificationControlFactory->create($this->getUserIdentity(), $this->verificationWay);
		$control->onSuccess[] = (function () {
			if ($this->backLink) {
				$this->redirectUrl($this->backLink);
			}

			$this->redirect(':NewFront:Homepage:default');
		});

		return $control;
	}

	public function actionChangeVerification($hash)
	{
		/** @var User $user */
		$user = $this->getUserIdentity();

		if (!$user->hasVerifiedAccountNumber() && $user->isAccountNumberHashValid($hash)) {
			$user->verifyAccountNumber();
			$this->userFacade->saveUser($user);
			$this->payoutFacade->updateAccountNumberInPayoutRequests($user);

			$this->flashMessage($this->translator->translate('front.account.user.changeVerifiedMessage'));
		}

		$this->redirect('settings');
	}

	protected function createComponentAccountDeletionRequestControl(): AccountDeletionRequestControl
	{
		$control = $this->accountDeletionRequestControlFactory->create($this->getUserIdentity());

		$control->onSuccess[] = function ($control) {
			$this->flashMessage($this->translator->translate('newFront.account.accountDeletionRequest.success'));
			$this->redirect(':NewFront:Account:User:settings');
		};

		return $control;
	}

	public function handleResendAccountNumberVerificationEmail(): void
	{
		$user = $this->getUserIdentity();

		$lastEmail = $this->messageFacade->findLastAccountNumberVerificationEmail($user);

		if ($lastEmail && $lastEmail->getCreatedAt() > new \DateTime('-1 hour')) {
			$this->redirect('this');
		}

		$this->messageFacade->sendAccountNumberVerificationEmail($user);

		$this->flashMessage($this->translator->translate('newFront.account.user.accountNumber.verification.title'));

		$this->redirect('this');
	}
}
