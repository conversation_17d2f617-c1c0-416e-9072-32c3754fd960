<?php

namespace tipli\NewFrontModule\AccountModule\Presenters;

use tipli\FrontModule\Components\CampaignProgressBarTrait;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\NewFrontModule\Components\Paginator;
use tipli\Model\Account\Entities\User;
use tipli\Model\Refunds\RefundFacade;

class RefundsPresenter extends AccountSectionPresenter
{
	use CampaignProgressBarTrait;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var RefundFacade @inject */
	public $refundFacade;

	public function actionDefault()
	{
		/** @var User $user */
		$user = $this->getUserIdentity();

		$refunds = $this->refundFacade->findUserRefunds($user);

		$paginator = $this['paginator'];
		$refundsPaginator = $paginator->getPaginator();
		$refundsPaginator->setItemCount($refunds->getTotalCount());

		$refunds->applyPaginator($refundsPaginator);

		$this->template->getRefunds = function () use ($user, $refundsPaginator) {
			$refunds = $this->refundFacade->findUserRefunds($user);
			$refunds->applyPaginator($refundsPaginator);

			return $refunds;
		};
	}

	public function createComponentPaginator(): Paginator
	{
		$control = $this->paginatorFactory->create();

		$control->setItemsPerPage(10);

		$control->onChange[] = function () {
			$this->redrawControl('refunds');
			$this->redrawControl('refundsWrapper');
			$this->redrawControl('paginator');
		};

		return $control;
	}
}
