<?php

namespace tipli\NewFrontModule\AccountModule\Presenters;

use tipli\Model\Account\UserFacade;

class ConditionPresenter extends AccountSectionPresenter
{
	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	public function renderDefault()
	{
		$this->template->approvals = $this->userFacade->getUserApprovals($this->getUserIdentity());
	}

	public function renderApproval($id)
	{
		$approval = $this->userFacade->findApprovalByUser($this->getUserIdentity(), $id);

		if (!$approval) {
			$this->redirect('default');
		}

		$this->template->approval = $approval;
	}
}
