<?php

namespace tipli\NewFrontModule\AccountModule\Presenters;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\Model\Account\Entities\User;
use tipli\Model\Inbox\NotificationFacade;
use tipli\Model\Inbox\Queries\NotificationsQuery;
use tipli\NewFrontModule\Components\Paginator;
use tipli\NewFrontModule\Presenters\BasePresenter;

class NotificationPresenter extends AccountSectionPresenter
{
	/** @var NotificationFacade @inject */
	public $notificationFacade;

	/** @var IPaginatorFactory @inject */
	public $paginatorFactory;

	/** @var Storage @inject */
	public $storage;

	public function startup()
	{
		parent::startup();

		$cache = new Cache($this->storage, BasePresenter::class);

		$cache->save('unreadNotificationsCount-' . $this->getUser()->getId(), 0, [Cache::EXPIRATION => '10 minute', Cache::TAGS => ['user/' . $this->getUser()->getId()]]);
	}

	public function renderDefault()
	{
		/** @var NotificationsQuery $query */
		$query = $this->notificationFacade->createNotificationsQuery();
		$query->withUser($this->getUser()->getIdentity());
		$query->onlyPublished();
		$query->sortNewest();
		$query->onlyVisible();

		$notifications = $this->notificationFacade->fetch($query);

		$this['paginator']->setItemsCount($notifications->getTotalCount());
		$notifications->applyPaginator($this['paginator']->getPaginator());

		$user = $this->getUser()->getIdentity();

		$this->notificationFacade->scheduleMarkAllAsOpenedForUser($user);

		$this->template->userNotifications = $notifications;
		$this->template->localization = $this->getLocalization();
	}

	public function actionMarkAllOpened()
	{
		/** @var User $user */
		$user = $this->getUser()->getIdentity();
		$this->notificationFacade->scheduleMarkAllAsOpenedForUser($user);
		$this->terminate();
	}

	public function createComponentPaginator(): Paginator
	{
		$control = $this->paginatorFactory->create()
			->disablePages()
			->setItemsPerPage(15);

		$control->onChange[] = function () {
			$this->redrawControl('paginator');
			$this->redrawControl('notifications');
		};

		return $control;
	}
}
