{block title}{_'front.account.payout.default.title'}{/block}

{block innerContent}
<div class="relative mt-5 md:mt-[35px] bg-white text-center text-[22px] md:text-[35px] leading-[31px] leading-[58px] text-dark-1 rounded-2xl pt-[41px] pb-[34px] mb-5">
	<img class="hidden md:block absolute top-[-113px] left-1/2 transform -translate-x-1/2 " src="{$basePath}/new-design/settings-donkey.png" alt="oslik">
    {_'newFront.account.payout.request.forPayout', [amount => ($confirmedBalance |amount)]|noescape}
    <div class="text-base text-dark-1 leading-7" n:if="$registeredBalance > 0">
        {_'front.account.payout.request.waiting', [count => ($registeredBalance |amount)]|noescape}
    </div>
</div>

<div class="flex flex-col md:flex-row items-center justify-between pl-[30px] pr-[24px] py-5 border border-secondary-red bg-[#FFEBED] rounded-lg">
    <div>
        <div class="flex items-center text-lg gap-[10px] leading-[31.5px] font-medium mb-[7px]">
            <svg xmlns="http://www.w3.org/2000/svg" width="26" height="24" viewBox="0 0 26 24" fill="none">
                <path d="M13.0002 9.00059V14.0006M13.0002 17.0006H13.0122M24.3518 19.0006L15.0185 2.66725C14.815 2.30816 14.5199 2.00947 14.1632 1.80167C13.8066 1.59386 13.4012 1.48437 12.9885 1.48438C12.5757 1.48437 12.1704 1.59386 11.8138 1.80167C11.4571 2.00947 11.162 2.30816 10.9585 2.66725L1.62516 19.0006C1.41946 19.3568 1.31159 19.7611 1.31251 20.1725C1.31342 20.5839 1.42307 20.9877 1.63035 21.343C1.83763 21.6984 2.13517 21.9926 2.49281 22.1959C2.85045 22.3992 3.25547 22.5043 3.66683 22.5006H22.3335C22.7429 22.5002 23.1449 22.392 23.4993 22.1871C23.8537 21.9821 24.1479 21.6875 24.3524 21.3329C24.5569 20.9782 24.6645 20.576 24.6644 20.1667C24.6643 19.7573 24.5565 19.3551 24.3518 19.0006Z" stroke="#F72F49" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {_'front.account.payout.request.noFullProfile.phoneNumberVerificationTitle'}
        </div>
        <div class="text-sm text-dark-1 leading-[24.5px]">
            {_'front.account.payout.request.noFullProfile.phoneNumberVerificationText'}
        </div>
    </div>
    <a n:href="User:phoneNumberVerification, backLink => $presenter->link('//this')" class="w-full sm:w-auto mt-3 lg:mt-0 bg-primary-blue-dark text-white font-bold leading-7 px-[36px] py-[14px] rounded-xl xl:hover:bg-primary-blue-second-dark">
        {_'front.account.payout.request.noFullProfile.phoneNumberVerificationButton'}
    </a>
</div>

{include payoutsGrid}
