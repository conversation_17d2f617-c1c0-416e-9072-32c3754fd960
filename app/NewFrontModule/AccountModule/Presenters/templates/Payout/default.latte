{block title}{_'front.account.payout.default.title'}{/block}

{block #scripts}
	<script src="{$basePath}/js/account/account.payout.js?v=0.1" defer></script>

	{if !$userHasAddonBonus && $user->isLoggedIn() && !$user->getIdentity()->hasInstalledAddon() && $localization->isCzech() && $confirmedCommissionBalance > 100}
		<script src="{$basePath}/js/account/account.addon.js?v=0.1" defer></script>
	{/if}
{/block}

{block innerContent}
<div class="relative mt-5 md:mt-[35px] bg-white text-center text-[22px] text-[35px] leading-[31.5px] leading-[58px] text-dark-1 rounded-2xl pt-[41px] pb-[34px] mb-5">
	<img class="hidden md:block absolute top-[-113px] left-1/2 transform -translate-x-1/2 " src="{$basePath}/new-design/settings-donkey.png" alt="oslik">
	{_'newFront.account.payout.request.forPayout', [amount => ($confirmedBalance |amount)]|noescape}
	<div class="text-base text-dark-1 leading-7">
		<span n:if="$paidBalance > 0">{_'front.account.payout.request.payout', [count => ($paidBalance |amount)]|noescape}</span>
		<span n:if="$paidBalance > 0 && $registeredBalance > 0">|</span>
		<span n:if="$registeredBalance > 0">{_'front.account.payout.request.waiting', [count => ($registeredBalance |amount)]|noescape}</span>
	</div>
</div>

<div class="flex flex-col pl-[30px] pr-[24px] py-5 border border-primary-orange bg-pastel-orange-light rounded-lg mb-10">
	<div class="flex flex-col lg:flex-row items-center text-lg gap-[10px] leading-[31.5px] font-medium mb-5 text-primary-orange">
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
			<path d="M12 16V11M12 8H12.0121M23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12Z" stroke="#EF7F1A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
		</svg>

		{_'newFront.account.payout.request.accountNumber', [accountNumber => $user->getIdentity()->getAccountNumber()]|noescape}
	</div>

	{form requestPayoutControl-form, class: "flex flex-col lg:flex-row items-center justify-between bg-[#ef7f1a1a] rounded-lg pl-5 pb-[21px] pt-4 pr-[15px]"}
		<label n:name="$form[conditions]" class="flex gap-3 md:w-[482px] cursor-pointer">
			<div class="mt-1">
				<input n:name="$form[conditions]" class="accent-primary-orange w-4 h-4 rounded-md">
			</div>
			<div class="text-sm leading-[24.5px] text-dark-1">
				{_'front.account.payout.request.condition', [number => $user->getIdentity()->getAccountNumber()] |noescape}
			</div>
		</label>

		<ul class="error" n:foreach="$form->errors as $error">
			<li>{$error |noescape}</li>
		</ul>

		<button n:name="submit" class="payout-submit w-full sm:w-auto mt-3 lg:mt-0 bg-primary-blue-dark text-white font-bold leading-7 px-[28px] py-[14px] rounded-xl  xl:hover:bg-primary-blue-second-dark">{_'newFront.account.payout.request.submit'}</button>
	{/form}
</div>

{include payoutsGrid}
