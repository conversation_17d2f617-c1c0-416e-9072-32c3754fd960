{var $countOfTransactions = $getCountOfTransactions()}

{if $countOfTransactions > 0}
	<div class="account-transaction mb-5" n:snippet="allTransactions">
		<div class="account-transaction__row-wrapper">
			<div class="md:hidden" n:snippet="transactionsMobile" data-ajax-append="true">
				{var $transactions = $getTransactions()}
				{foreach $transactions as $transaction}
					{capture $rewardCssClass}
						{if $transaction->isConfirmed() && !$transaction->isCancelled()}
							bg-pastel-green-light text-secondary-green
						{elseif $transaction->isCancelled()}
							bg-[#FFEBED] text-secondary-red
						{else}
							bg-pastel-orange-light text-primary-orange
						{/if}
					{/capture}

					<div class="transaction-container bg-white rounded-xl p-5 mb-5">
						<div class="flex justify-between">
							<div>
								<div class="text-lg leading-[30px] font-bold text-dark-1">
									{$transaction->getName()|lower|firstUpper}
								</div>
								<div class="text-xs leading-[21px] text-dark-1">
									{$transaction->getRegisteredAt() |localDate: $localization->getDateFormat('d.m.Y')}
								</div>
							</div>
							<div class="flex flex-col">
								<div class="flex items-center justify-between rounded-[14.5px] pl-2.5 pr-2 gap-2.5 ml-auto mt-1 mb-1 text-sm font-bold leading-[24.5px] {$rewardCssClass}">
									{if $transaction->getUserCommissionAmount() > 0 || $transaction->getBonusAmount() > 0}
										{if $transaction->getUser() && $transaction->getUser()->getLocalization()->isSlovak()}
											{$transaction->getAmount() |amount:3, ',', true, true|noescape}&nbsp;{$transaction->getCurrency() |currency}
										{else}
											{$transaction->getAmount()|amount:3}&nbsp;{$transaction->getCurrency() |currency}
										{/if}
									{else}
										{if $transaction->isCancelled()}
											{if $transaction->getUser() && $transaction->getUser()->getLocalization()->isSlovak()}
												{$transaction->getAmount() |amount:3, ',', true, true|noescape}&nbsp;{$transaction->getCurrency() |currency}
											{else}
												{$transaction->getAmount()|amount:3}&nbsp;{$transaction->getCurrency() |currency}
											{/if}
										{else}
											<svg id="unknownCommissionAmount-tooltip" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
												<path d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
													stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
													stroke-linejoin="round" />
												<path d="M8.41797 7.60938V12.2089" stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10"
													stroke-linecap="round" stroke-linejoin="round" />
												<circle cx="8.41848" cy="4.88918" r="0.906757" fill="#8B95A4" />
											</svg>
										{/if}
									{/if}

									{if $transaction->isConfirmed() && !$transaction->isCancelled()}
										<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z"
												  stroke="#66B940" stroke-width="1.5" stroke-linecap="round" />
										</svg>
									{elseif $transaction->isCancelled()}
										<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M5.48965 10.5199L8.01471 7.99995M8.01471 7.99995L10.5398 5.47993M8.01471 7.99995L10.5398 10.5199M8.01471 7.99995L5.48961 5.47993M1 8C1 9.85653 1.73899 11.637 3.0544 12.9497C4.36981 14.2625 6.15389 15 8.01416 15C9.87445 15 11.6585 14.2625 12.9739 12.9497C15.6754 10.2539 15.6753 5.74614 12.9739 3.05025C11.6585 1.7375 9.87445 1 8.01416 1C6.15389 1 4.36981 1.7375 3.0544 3.05025C1.73899 4.36301 1 6.14349 1 8Z"
												  stroke="#F72F49" stroke-width="1.5" stroke-linecap="round" />
										</svg>
									{else}
										<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="#EF7F1A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									{/if}
								</div>
								<div class="flex items-center justify-end gap-[5px] text-xs leading-[21px] text-dark-4">
									<span class="show-more">{_'newFront.account.transactionsList.table.moreInfo'}</span>
									<span class="show-less hidden">{_'newFront.account.transactionsList.table.lessInfo'}</span>
									<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
										<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656"
											  stroke="#ADB3BF" />
									</svg>
								</div>
							</div>
						</div>

						<div class="transaction-detail-desc hidden">
							<div class="w-full h-px bg-light-5 my-5 mb-3"></div>
							<div>
								<div class="flex justify-between mb-2">
									<div class="text-xs leading-[21px] text-dark-1">{_'newFront.account.transactionsList.table.state'}</div>
									<div class="flex items-center gap-2 text-sm leading-[24.5px] font-medium">
										{if $transaction->isConfirmed()}
											{if $transaction->isCancelled()}
												{_'newFront.account.transactionsList.table.canceled'}
												<svg id="reward-canceled-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
													<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
														stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
														stroke-linejoin="round" />
													<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10"
														stroke-linecap="round" stroke-linejoin="round" />
													<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF" />
												</svg>
											{else}
												{_'newFront.account.transactionsList.table.confirmed'}
											{/if}
										{else}
											{include 'unconfirmedTransactionState.latte', 'transaction' => $transaction}

											{var $score = $transactionProgressResolver->resolveTransactionProgress($transaction)}
											{if $score !== null}
												<span class="hidden transaction-progress" data-toggle="tooltip" data-placement="top" title="" data-original-title="{$transaction |transactionStatusHelper}">
													<span class="transaction-progress__status transaction-progress__status--green" style="width: {$score |noescape}%">&nbsp;</span>
												</span>
											{/if}
										{/if}
									</div>
								</div>

								{var $estimatedConfirmedAt = $transaction->getEstimatedConfirmedAt()}
								<div class="flex justify-between" n:if="$transaction->isConfirmed() === false && $estimatedConfirmedAt">
									{capture $estimatedConfirmedAt}{$estimatedConfirmedAt |localDate: $localization->getDateFormat('d.m.Y')}{/capture}

									<div class="text-xs leading-[21px] text-dark-1">{_'newFront.account.transactionsList.table.estimatedConfirmationMobile'}</div>
									<div class="text-sm leading-[24.5px] text-dark-1">{$estimatedConfirmedAt}</div>
								</div>
							</div>
						</div>
					</div>
				{/foreach}
			</div>

			<div class="hidden md:block mt-5">
				<div class="not-prose relative bg-[#F8F8F9] rounded-xl ">
					<div class="relative rounded-xl overflow-auto">
						<div>
							<table class="border-collapse table-auto w-full text-sm">
								<thead>
								<tr class="text-xs">
									<th class="font-medium pt-[18px] pb-[19px] px-[50px] text-center">
										{_'newFront.account.transactionsList.table.name'}
									</th>
									<th class="font-medium pt-[18px] pb-[19px] px-[26px] text-left">
										{_'newFront.account.transactionsList.table.amount'}
									</th>
									<th class="font-medium pt-[18px] pb-[19px] px-[25px] text-left">
										{_'newFront.account.transactionsList.table.moreInfo'}
									</th>
									<th class="font-medium pt-[18px] pb-[19px] px-[18px] text-left">
										{_'newFront.account.transactionsList.table.registeredAt'}
									</th>
									<th class="font-medium pt-[18px] pb-[19px] text-left">
										{_'newFront.account.transactionsList.table.confirmedAt'}
									</th>
									<th class="font-medium pt-[18px] pb-[19px] text-right pr-5">
										{_'newFront.account.transactionsList.table.state'}
									</th>
								</tr>
								</thead>
								<tbody class="bg-white" n:snippet="transactions" data-ajax-append="true">
									{var $transactions = $getTransactions()}
									<tr class="border-b border-light-4 h-[58px] text-sm leading-[24.5px] text-dark-1 text-center bg-white" n:foreach="$transactions as $transaction">
										{capture $rewardCssClass}
											{if $transaction->isConfirmed() && !$transaction->isCancelled()}
												bg-pastel-green-light text-secondary-green
											{elseif $transaction->isCancelled()}
												bg-[#FFEBED] text-secondary-red
											{else}
												bg-pastel-orange-light text-primary-orange
											{/if}
										{/capture}

										<td class="pl-5">
											{if $shop = $transaction->getShop()}
												<a n:href=":NewFront:Shops:Redirection:shop $shop">
													<img src="{$shop->getCurrentLogo()|image:340,0,'fit',false}" title="{$shop->getName()}" alt="{$shop->getName()}" class="max-w-[100px] max-h-[40px] w-full m-auto">
												</a>
											{else}
												<span class="account-transaction__logo-inner">
													{if $localization->isHungarian()}
														<img src="{$basePath}/images/tiplino_logo_new_color.svg" width="70px" title="Tipli" alt="Tiplino" class="max-w-[100px] max-h-[40px] w-full m-auto">
													{else}
														<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px] w-full m-auto" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
															<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
														</svg>
													{/if}
												</span>
											{/if}
										</td>
										<td>
											<div class="{$rewardCssClass} inline-flex items-center rounded-[14.5px] pl-2.5 pr-[7px] gap-2.5 mb-[3px] text-sm font-bold leading-[24.5px]">
												{if $transaction->getUserCommissionAmount() > 0 || $transaction->getBonusAmount() > 0}
													{if $transaction->getUser() && $transaction->getUser()->getLocalization()->isSlovak()}
														{$transaction->getAmount() |amount:3, ',', true, true|noescape}&nbsp;{$transaction->getCurrency() |currency}
													{else}
														{$transaction->getAmount()|amount:3}&nbsp;{$transaction->getCurrency() |currency}
													{/if}
												{else}
													{if $transaction->isCancelled()}
														{if $transaction->getUser() && $transaction->getUser()->getLocalization()->isSlovak()}
															{$transaction->getAmount() |amount:3, ',', true, true|noescape}&nbsp;{$transaction->getCurrency() |currency}
														{else}
															{$transaction->getAmount()|amount:3}&nbsp;{$transaction->getCurrency() |currency}
														{/if}
													{else}
														<svg id="unknownCommissionAmount2-tooltip" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
															<path d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
																stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
																stroke-linejoin="round" />
															<path d="M8.41797 7.60938V12.2089" stroke="#8B95A4" stroke-width="1.5" stroke-miterlimit="10"
																stroke-linecap="round" stroke-linejoin="round" />
															<circle cx="8.41848" cy="4.88918" r="0.906757" fill="#8B95A4" />
														</svg>
													{/if}
												{/if}
											</div>
										</td>
										<td class="text-sm text-dark-1 leading-[24.5px]">
											<div class="flex items-center justify-center gap-2">
												{if $transaction->isBonusLuckyShop()}
													{_'model.transaction.type.bonus_lucky_shop'}
												{elseif $transaction->isBonusVoucher()}
													{_'model.transaction.type.bonus_voucher'}
												{elseif $transaction->isBonus()}
													Bonus
												{else}
													{var $type = 'model.transaction.type.' . $transaction->getType()}
													{$type |translate}
												{/if}

												{if $transaction->isBonusRecommendation()}
													{var $relatedUser = $transaction->getRelatedRecommendeduser()}
													<br><em>({$relatedUser->getFullName() ? : ($relatedUser->getEmail() |censorEmail)})</em>
												{/if}
											</div>

{*											{if $transaction->isCommission() && $transaction->getUserCommissionInPercent() < 35}*}
{*												{var $cashbackInPercentage = $transaction->getUserCommissionInPercent()}*}
{*												{var $cashbackInPercentage = str_replace(".", ",", $cashbackInPercentage)}*}
{*												<div>{$cashbackInPercentage}&nbsp;%</div>*}
{*											{/if}*}
										</td>
										<td>{$transaction->getRegisteredAt() |localDate: $localization->getDateFormat('d.m.Y')}</td>
										<td class="text-left">
											{if $transaction->isConfirmed()}
												<span class="account-transaction__confirmed-date">{$transaction->getConfirmedAt() |localDate: $localization->getDateFormat('d.m.Y')}</span>

												{if $transaction->isCancelled()}
													<svg id="reward-canceled-tooltip" class="inline ml-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
														<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
															  stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
															  stroke-linejoin="round" />
														<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10"
															  stroke-linecap="round" stroke-linejoin="round" />
														<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF" />
													</svg>
												{/if}
											{else}

												<span class="flex gap-2 items-center account-transaction__confirmed-value">
													{include 'unconfirmedTransactionState.latte', 'transaction' => $transaction}
												</span>

												<div class="h-[5px] w-full bg-light-6 rounded-md">
													{var $score = $transactionProgressResolver->resolveTransactionProgress($transaction)}
													<div class="h-[5px] bg-secondary-green rounded-md" style="width: {$score |noescape}%"></div>
												</div>
											{/if}
										</td>
										<td>
											{if $transaction->isConfirmed() && !$transaction->isCancelled()}
												<svg xmlns="http://www.w3.org/2000/svg" width="73" height="92" viewBox="0 0 73 92" fill="none">
													<rect width="73" height="92" transform="matrix(-1 0 0 1 73 0)" fill="white" />
													<rect x="57" y="26" width="40" height="40" rx="14" transform="rotate(90 57 26)"
														  fill="#F0F8EC" />
													<path d="M33.9671 46.4603L35.9007 48.2731L40.0277 44.4852M28.6641 46.0003C28.6641 48.2105 29.542 50.3301 31.1048 51.8929C32.6676 53.4557 34.7873 54.3337 36.9974 54.3337C39.2076 54.3337 41.3272 53.4557 42.8899 51.8929C44.4528 50.3301 45.3307 48.2105 45.3307 46.0003C45.3307 43.7902 44.4528 41.6706 42.8899 40.1078C41.3272 38.545 39.2076 37.667 36.9974 37.667C34.7873 37.667 32.6676 38.545 31.1048 40.1078C29.542 41.6706 28.6641 43.7902 28.6641 46.0003Z"
														  stroke="#66B940" stroke-width="1.5" stroke-linecap="round" />
												</svg>
											{elseif $transaction->isCancelled()}
												<svg xmlns="http://www.w3.org/2000/svg" width="73" height="92" viewBox="0 0 73 92" fill="none">
													<path d="M0 0H73V62C73 76.1421 73 83.2132 68.6066 87.6066C64.2132 92 57.1421 92 43 92H0V0Z"
														  fill="white" />
													<rect x="57" y="26" width="40" height="40" rx="14" transform="rotate(90 57 26)"
														  fill="#FFEBED" />
													<path d="M33.9981 49.0002L36.998 46.0003M36.998 46.0003L39.998 43.0002M36.998 46.0003L39.998 49.0002M36.998 46.0003L33.998 43.0002M28.6641 46.0003C28.6641 48.2105 29.542 50.3301 31.1048 51.8929C32.6676 53.4557 34.7873 54.3337 36.9974 54.3337C39.2076 54.3337 41.3272 53.4557 42.8899 51.8929C46.0995 48.6835 46.0994 43.3172 42.8899 40.1078C41.3272 38.545 39.2076 37.667 36.9974 37.667C34.7873 37.667 32.6676 38.545 31.1048 40.1078C29.542 41.6706 28.6641 43.7902 28.6641 46.0003Z"
														  stroke="#F72F49" stroke-width="1.5" stroke-linecap="round" />
												</svg>
											{else}
												<svg xmlns="http://www.w3.org/2000/svg" width="73" height="92" viewBox="0 0 73 92" fill="none">
													<rect width="73" height="92" transform="matrix(-1 0 0 1 73 0)" fill="white" />
													<rect x="57" y="26" width="40" height="40" rx="14" transform="rotate(90 57 26)"
														  fill="#FEF3E9" />
													<path d="M36.9974 43.0241V46.0003L40.7172 49.721M28.6641 46.0003C28.6641 48.2105 29.542 50.3301 31.1048 51.8929C32.6676 53.4557 34.7873 54.3337 36.9974 54.3337C39.2076 54.3337 41.3272 53.4557 42.8899 51.8929C44.4528 50.3301 45.3307 48.2105 45.3307 46.0003C45.3307 43.7902 44.4528 41.6706 42.8899 40.1078C41.3272 38.545 39.2076 37.667 36.9974 37.667C34.7873 37.667 32.6676 38.545 31.1048 40.1078C29.542 41.6706 28.6641 43.7902 28.6641 46.0003Z"
														  stroke="#EF7F1A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
												</svg>
											{/if}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="datalist account-transaction__load-more">
		{snippet transactionsPaginator}
			{control transactionsPaginator}
		{/snippet}
	</div>

{else}
	<div class="bg-pastel-orange-light text-primary-orange pt-[25px] pb-[26px] pl-5 font-medium leading-7 rounded-lg border border-primary-orange mb-5">
		{_'front.account.transactionsList.empty'}
	</div>
{/if}

{snippet scripts}

<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/animations/scale.css" />
<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/border.css" />

<script>

	tippy('#reward-tooltip', {
		animation: 'scale',
		content: {_newFront.account.transactionsList.table.infoValueHelp},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#reward-canceled-tooltip', {
		animation: 'scale',
		content: {_front.account.transactionsList.table.isCancelled},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#reward-bonus-tooltip', {
		animation: 'scale',
		content: '?',
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#unknownCommissionAmount-tooltip', {
		animation: 'scale',
		content: {_newFront.account.transactionsList.unknownCommissionAmount},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	tippy('#unknownCommissionAmount2-tooltip', {
		animation: 'scale',
		content: {_newFront.account.transactionsList.unknownCommissionAmount},
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});

	// Najdi všechny elementy s classou 'show-more'
    const showMoreButtons = document.querySelectorAll('.show-more');

    showMoreButtons.forEach(button => {
        // Přidej událost kliknutí na každý z těchto elementů
        button.addEventListener('click', function() {
            // Najdi nejbližší element s classou 'transaction-detail-desc'
            const transactionDetail = this.closest('.transaction-container').querySelector('.transaction-detail-desc');

            // Odstraň classu 'hidden' z transactionDetail
            transactionDetail.classList.remove('hidden');

            // Přidej classu 'hidden' na kliknutý element
            this.classList.add('hidden');

            // Najdi nejbližší element s classou 'show-less' a odstraň z něj classu 'hidden'
            const showLessButton = this.closest('.transaction-container').querySelector('.show-less');
            showLessButton.classList.remove('hidden');
        });
    });

    // Najdi všechny elementy s classou 'show-less'
    const showLessButtons = document.querySelectorAll('.show-less');

    showLessButtons.forEach(button => {
        // Přidej událost kliknutí na každý z těchto elementů
        button.addEventListener('click', function() {
            // Najdi nejbližší element s classou 'transaction-detail-desc'
            const transactionDetail = this.closest('.transaction-container').querySelector('.transaction-detail-desc');

            // Přidej classu 'hidden' na transactionDetail
            transactionDetail.classList.add('hidden');

            // Přidej classu 'hidden' na kliknutý element
            this.classList.add('hidden');

            // Najdi nejbližší element s classou 'show-more' a odstraň z něj classu 'hidden'
            const showMoreButton = this.closest('.transaction-container').querySelector('.show-more');
            showMoreButton.classList.remove('hidden');
        });
    });
</script>
{/snippet}
