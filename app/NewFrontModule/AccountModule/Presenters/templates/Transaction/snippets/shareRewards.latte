{if count($shareRewards) > 0}
	<div class="text-dark-1 font-medium leading-[31.5px] mb-2.5">
		{_'newFront.account.shareRewardsList.title'}
	</div>
	<table class="w-full">
		<thead>
		<tr class="bg-[#F8F8F9] text-xs leading-[21px] text-dark-1 h-[58px] rounded-2xl">
			<th>
				{_'newFront.account.shareRewardsList.table.name'}
			</th>
			<th>
				{_'newFront.account.shareRewardsList.table.shop'}
			</th>
			<th>
				{_'newFront.account.shareRewardsList.table.validity'}
			</th>
		</tr>
		</thead>
		<tbody class="bg-white" n:snippet="shareRewards" data-ajax-append="true">
		<tr class="h-[58px] text-sm leading-[24.5px] text-dark-1 text-center bg-white, {$shareReward->isValid() ? 'success'}" n:foreach="$shareRewards as $shareReward">
			<td class="no-ellipsis">
				{$shareReward->getName() ? $shareReward->getName()}
			</td>
			<td>
				{if !$shareReward->getShops()->isEmpty()}
					{foreach $shareReward->getShops() as $shop}
						<a href="{plink :NewFront:Shops:Shop:default $shop}">
							{$shop->getName()}
						</a>
						{sep},{/sep}
					{/foreach}
				{else}
					{_'newFront.account.shareRewardsList.table.allShops'}
				{/if}
			</td>
			<td>
				{if $shareReward->getValidTill() >= (new \DateTime)->modify('+3 years')}
					{_'newFront.account.shareRewardsList.table.validForever'}
				{else}
					{$shareReward->getValidSince()|localDate} - {$shareReward->getValidTill()|localDate}
				{/if}
			</td>
		</tr>
		</tbody>
	</table>

	{snippet shareRewardsPaginator}
		{control shareRewardsPaginator}
	{/snippet}
{/if}

