<h2>{_'front.account.expireTransactionsList.title'}</h2>
<p>{_'front.account.expireTransactionsList.text'}</p>

<div class="mb-3">
	<a href="" class="btn btn-green-inverse js-show-expire">{_'front.account.expireTransactionsList.button'}</a>
</div>

<div class="expire-transaction hide">
	<div class="account-transaction">
		<div class="account-transaction__row account-transaction__row--head">
			<div class="account-transaction__logo">
				<span class="account-transaction__label">{_'front.account.expireTransactionsList.table.name'}</span>
			</div>

			<div class="account-transaction__amount">
				<span class="account-transaction__label">{_'front.account.expireTransactionsList.table.amount'}</span>
			</div>

			<div class="account-transaction__info account-transaction--column">
				<span class="account-transaction__label">{_'front.account.expireTransactionsList.table.moreInfo'}</span>
			</div>

			<div class="account-transaction__registered">
				<span class="account-transaction__label">{_'front.account.expireTransactionsList.table.registeredAt'}</span>
			</div>

			<div class="account-transaction__confirmed">
				<span class="account-transaction__label">{_'front.account.expireTransactionsList.table.expiredAt'}</span>
			</div>

			<div class="account-transaction__status"></div>
		</div>

		<div n:snippet="expiredTransactions" data-ajax-append="true" class="account-transaction__row-wrapper">
		{var $expiredTransactions = $getExpiredTransactions()}

		{foreach $expiredTransactions as $expiredTransaction}
			<div class="account-transaction__row account-transaction__row--grey">
				<div class="account-transaction__logo">
					{if $expiredTransaction->getShop()}
						<a href="{plink :NewFront:Shops:Shop:default $expiredTransaction->getShop()}" class="account-transaction__logo-inner" title="{$expiredTransaction->getShop()->getName()}">
							<img src="{$expiredTransaction->getShop()->getCurrentLogo() |image:170,0}" title="{$expiredTransaction->getShop()->getName()}" alt="{$expiredTransaction->getShop()->getName()}" class="max-w-[100px] max-h-[40px] w-full m-auto">
						</a>
					{else}
						<span class="account-transaction__logo-inner">
							<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px] w-full m-auto" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						</span>
					{/if}
				</div>

				<div class="account-transaction__amount">
					<span class="account-transaction__label account-transaction__label--mobile">{_'front.account.expireTransactionsList.table.amount'}</span>
					<strong class="account-transaction__amount-value">
						{if $expiredTransaction->getUserCommissionAmount() > 0 && $expiredTransaction->getBonusAmount() > 0}
							{$expiredTransaction->getUserCommissionAmount()|amount: 3}&nbsp;<small>{$expiredTransaction->getCurrency()|currency}</small>
							<br />
							<small>+{$expiredTransaction->getBonusAmount()|amount: 3}&nbsp;{$expiredTransaction->getCurrency()|currency}</small>
						{else}
						{$expiredTransaction->getAmount()|amount: 3}&nbsp;<small>{$expiredTransaction->getCurrency()|currency}</small>
						{/if}
					</strong>
				</div>

				<div class="account-transaction__info">
			<span class="account-transaction__label account-transaction__label--mobile">
				{_'front.account.expireTransactionsList.table.moreInfo'}
			</span>

					<span class="account-transaction__info-value">
				{if $expiredTransaction->getOrderAmount()}
					{$expiredTransaction->getOrderAmount()|amount}&nbsp;{$expiredTransaction->getCurrency()|currency}
					<span class="help-tooltip ml-1" data-toggle="tooltip" data-placement="top" title="" data-original-title="{_'front.account.transactionsList.table.infoValueHelp'}">?</span>
					<br>
					{if $expiredTransaction->getUserCommissionInPercent() < 35}
						{var $cashbackInPercentage = $expiredTransaction->getUserCommissionInPercent()}
						{var $cashbackInPercentage = str_replace(".", ",", $cashbackInPercentage)}
						{$cashbackInPercentage}&nbsp;%
					{/if}
				{else}
					{var $type = 'model.transaction.type.' . $expiredTransaction->getType()}
					{_$type}
				{/if}

				{if $expiredTransaction->isBonusRecommendation()}
							{var $relatedUser = $expiredTransaction->getRelatedRecommendeduser()}
							<br><em>({$relatedUser->getFullName() ? : ($relatedUser->getEmail() |censorEmail)})</em>
						{/if}
			</span>
				</div>

				<div class="account-transaction__registered">
					<span class="account-transaction__label account-transaction__label--mobile">{_'front.account.transactionsList.table.registeredAt'}</span>
					<span class="account-transaction__registered-value">
			{$expiredTransaction->getRegisteredAt() |localDate}
		</span>
				</div>

				<div class="account-transaction__confirmed">
					<span class="account-transaction__label account-transaction__label--mobile">{_'front.account.expireTransactionsList.table.expiredAt'}</span>
					<span class="account-transaction__confirmed-date">{$expiredTransaction->getExpiredAt()->format('d.m.Y')}</span>
				</div>

				<div class="account-transaction__status">
				</div>
			</div>
		{/foreach}
		</div>
	</div>

	<div class="datalist account-transaction__load-more">
		{snippet expiredTransactionsPaginator}
			{control expiredTransactionsPaginator}
		{/snippet}
	</div>
</div>
