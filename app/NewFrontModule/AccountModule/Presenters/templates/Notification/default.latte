{block title}{_'newFront.account.user.notification.title'}{/block}
{block heading}{_'newFront.account.user.notification.title'}{/block}

{block innerContent}
<div class="lg:bg-white lg:p-5 rounded-2xl">
	<div class="hidden lg:block text-lg font-medium leading-[31.5px] text-dark-1 pl-5 pb-5">
		{_'newFront.account.user.notification.subtitle'}
	</div>
	<div class="hidden lg:block w-full h-px bg-light-5"></div>

	{if count($userNotifications) == 0}
		<div class="mt-4">{_'newFront.account.user.notification.notFound'}</div>
	{else}
	<div class="datalist datalist-data" data-ajax-append="true" n:snippet="notifications">
		{foreach $userNotifications as $notification}
			{if $notification->isValid() || $notification->getBody()->getUrl() !== null}
				<a n:href=":NewFront:Redirection:notification, 'notification' => $notification" class="ml-auto shrink-0 xl:hover:bg-light-6 py-[18px]">
			{/if}

			<div class="flex flex-col lg:flex-row lg:items-center bg-white p-5 lg:p-0 rounded-2xl mb-5 lg:mb-0 py-[18px] ">
				<div class="flex gap-[15px] justify-start items-center lg:gap-0 lg:py-[19px]">
					<div class="w-[130px] h-[80px] flex items-center justify-center border border-light-4 rounded-xl lg:border-0 lg:py-0 lg:px-0 lg:shrink-0">
						{if $notification->getBody()->getImage()}
							<img src="{$notification->getBody()->getImage()|image:120,120}"
								 alt="{$notification->getBody()->getTitle()}" class="max-w-[100px] max-h-[60px] w-auto h-auto">
						{elseif $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
						{else}
							<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						{/if}
					</div>
					<div class="lg:ml-5 lg:mr-5">
						<div class="text-dark-3 text-xs leading-[21px]">{$notification->getScheduledAt() |localDate:'d.m.Y H:i'}
						</div>
						<div class="font-bold leading-7 text-dark-1">{$notification->getBody()->getTitle()}</div>
						<div class="hidden lg:block text-sm leading-[24.5px] text-dark-1">
							{$notification->getBody()->getContent() |noescape}
						</div>
					</div>
				</div>

				<div class="lg:hidden mt-[15px]">
					<div class="text-sm leading-[24.5px] text-dark-1">{$notification->getBody()->getContent() |noescape}
					</div>
				</div>

				{if $notification->isValid() || $notification->getBody()->getUrl() !== null}
				<span class="hidden lg:block ml-auto shrink-0">
					<svg xmlns="http://www.w3.org/2000/svg" width="37" height="72"
						viewBox="0 0 37 72" fill="none">
						<rect width="37" height="72" rx="10" fill="#F4F4F6" />
						<path d="M16 43L23 35.5L16 28" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10"
							stroke-linecap="round" stroke-linejoin="round" />
					</svg>
				</span>

				<span
					class="lg:hidden text-sm underline leading-[24.5px] text-primary-orange ml-auto flex items-center gap-[5px]">
					{_'newFront.account.user.notification.cta'}
					<svg xmlns="http://www.w3.org/2000/svg" width="5" height="7" viewBox="0 0 5 7" fill="none">
						<path
							d="M1.17188 6.29883L3.62606 4.15142C3.98195 3.84001 3.98195 3.28638 3.62606 2.97498L1.17187 0.827564"
							stroke="#EF7F1A" />
					</svg>
				</span>
				{/if}
			</div>

			{if $notification->isValid() || $notification->getBody()->getUrl() !== null}
				</a>
			{/if}

			<div class="w-full lg:h-px bg-light-5"></div>
		{/foreach}
	</div>

	<div class="mt-5 md:mt-10">
		{snippet paginator}
		{control paginator}
		{/snippet}
	</div>
	{/if}
</div>
