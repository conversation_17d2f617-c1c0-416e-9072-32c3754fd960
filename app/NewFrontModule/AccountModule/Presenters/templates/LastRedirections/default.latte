{extends '../@layout.latte'}

{block scripts}
	<script src="https://unpkg.com/@popperjs/core@2"></script>
	<script src="https://unpkg.com/tippy.js@6"></script>
{/block}

{block head}
	<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/animations/scale.css" />
	<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/border.css" />
{/block}

{block heading}{_'newFront.account.lastRedirections.default.title'}{/block}

{block innerContent}
	{varType tipli\Model\Shops\Entities\Redirection[] $redirections}
	{if count($redirections) > 0}
		<div class="grow">
			<div class="bg-white rounded-2xl p-[30px] md:py-10 text-center mt-[124px] md:mt-[45px] mb-10 relative">
				<div
						class="w-[245px] m-auto md:w-auto text-[20px] md:text-[30px] text-dark-1 leading-[32px] md:leading-[50px] mb-2.5 md:mb-[5px]">
					{_'newFront.account.lastRedirections.default.redirections.title'}
					<a n:href=":NewFront:Account:Refund:default#missing-reward">
						<span class="text-primary-orange font-bold">{_'newFront.account.lastRedirections.default.redirections.titleLink'}</span>
					</a>
				</div>
				<div class="w-full max-w-[737px] m-auto leading-7">
					{_'newFront.account.lastRedirections.default.redirections.dectiption'}
				</div>

				<div class="absolute top-[-46px] right-[-30px]">
					<div class="relative w-[129px] h-[129px]">
						<svg xmlns="http://www.w3.org/2000/svg" width="123" height="122" viewBox="0 0 123 122"
							 fill="none">
							<path
									d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z"
									fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571" />
							<defs>
								<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126"
												y2="102.866" gradientUnits="userSpaceOnUse">
									<stop stop-color="#FDBB47" />
									<stop offset="0.495" stop-color="white" />
									<stop offset="1" stop-color="#FDBB47" />
								</linearGradient>
							</defs>
						</svg>
						<div
								class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-sm font-bold text-white">
							{_'newFront.account.lastRedirections.default.redirections.guarantee'}
						</div>
					</div>
				</div>

				<img class="absolute top-[-113px] left-1/2 transform -translate-x-1/2 "
					 src="{$basePath}/new-design/settings-donkey.png" alt="oslik">
			</div>

			<div
					class="text-base md:text-lg leading-7 md:leading-[31.5px] font-medium text-dark-1 mb-5 w-[236px] md:w-auto pl-5 md:pl-0">
				{_'newFront.account.lastRedirections.default.redirections.table.title'}
			</div>

{*			mobile table*}
			<div class="mb-5">
				<div class="md:hidden datalist-data" data-ajax-append="true" n:snippet="redirections">
					{foreach $redirections as $redirection}
						<div class="bg-white rounded-xl p-5 mb-5">
							<div class="flex justify-between">
								<div>
									<div
											class="border border-light-5 rounded-xl flex justify-center items-center w-[97px] h-[55px]">
										<img class="max-w-[58px] max-h-[38px]" alt="{$redirection->getShop()->getName()}"
											 src="{$redirection->getShop()->getCurrentLogo()|image:300}"
											 loading="lazy">
									</div>
								</div>
								<div class="flex flex-col">
									<div
											class="flex items-center gap-[7px] text-sm leading-[24.5px] mb-1">
										<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
											<path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="#BDC2CC" stroke-width="1.5" stroke-linecap="round"/>
										</svg>
										<div class="text-center">
											{$redirection->getCreatedAt()|timeAgo}
										</div>
									</div>
									{if isset($hasTransaction[$redirection->getId()])}
										<div
												id="reward-success-tooltip-mobile-{$iterator->counter}"
												class="text-xs leading-[21px] text-light-2 underline cursor-pointer text-secondary-green"
												data-shopName="{$redirection->getShop()->getName()}"
												data-createdAt="{$redirection->getCreatedAt()->format('c')}"
											>
											{_'newFront.account.lastRedirections.default.redirections.table.reward'}
										</div>
									{else}
										{if $canReportMissingReward[$redirection->getId()]}
											<a n:href=":NewFront:Account:Refund:default#missing-reward, redirectionId => $redirection->getId()">
												<div class="text-xs leading-[21px] text-primary-orange underline">
													{_'newFront.account.lastRedirections.default.redirections.table.report'}
												</div>
											</a>
										{else}
											<!-- DISABLED -->
											<div
												id="reward-canceled-tooltip-mobile-{$iterator->counter}"
												class="text-xs leading-[21px] text-light-2 underline cursor-pointer"
												data-shopName="{$redirection->getShop()->getName()}"
												data-refundHoursLimit="{$redirection->getShop()->getRefundHoursLimit()}"
												data-createdAt="{$redirection->getCreatedAt()->format('c')}"
											>
												{_'newFront.account.lastRedirections.default.redirections.table.report'}
											</div>
										{/if}
									{/if}
								</div>
							</div>

							<div class="hidden">
								<div class="w-full h-px bg-light-5 my-5 mb-3"></div>
								<div>
									<div class="flex justify-between mb-2">
										<div class="text-xs leading-[21px] text-dark-1">Stav</div>
										<div class="flex items-center gap-2 text-sm leading-[24.5px] font-medium">
											Zamítnuta
											<svg id="reward-canceled-tooltip" xmlns="http://www.w3.org/2000/svg"
												 width="16" height="16" viewBox="0 0 16 16" fill="none">
												<path
														d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
														stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
														stroke-linejoin="round"></path>
												<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF"
													  stroke-miterlimit="10" stroke-linecap="round"
													  stroke-linejoin="round"></path>
												<circle cx="7.92443" cy="4.62951" r="0.846307"
														fill="#ADB3BF"></circle>
											</svg>
										</div>
									</div>
									<div class="flex justify-between mb-2">
										<div class="text-xs leading-[21px] text-dark-1">
											Výše objednávky
										</div>
										<div class="flex items-center gap-2 text-sm leading-[24.5px] text-dark-1">
											10 000&nbsp;Kč
											<svg id="reward-tooltip" xmlns="http://www.w3.org/2000/svg" width="16"
												 height="16" viewBox="0 0 16 16" fill="none">
												<path
														d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
														stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
														stroke-linejoin="round"></path>
												<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF"
													  stroke-miterlimit="10" stroke-linecap="round"
													  stroke-linejoin="round"></path>
												<circle cx="7.92443" cy="4.62951" r="0.846307"
														fill="#ADB3BF"></circle>
											</svg>

										</div>
									</div>
								</div>
							</div>
						</div>
					{/foreach}
				</div>

				<div>
					<div class="hidden md:block mt-5 mb-5">
						<div class="not-prose relative bg-[#F8F8F9] rounded-xl ">
							<div class="relative rounded-xl overflow-auto">
								<div>
									<table class="border-collapse table-auto w-full text-sm">
										<thead>
										<tr class="text-xs">
											<th class="font-medium pt-[18px] pb-[19px] px-[50px] text-center">
												{_'newFront.account.lastRedirections.default.redirections.table.shop'}
											</th>
											<th class="font-medium pt-[18px] pb-[19px] px-[26px] text-center">
												{_'newFront.account.lastRedirections.default.redirections.table.redirectionTime'}
											</th>
											<th class="font-medium pt-[18px] pb-[19px] px-[25px] text-center">
												{_'newFront.account.lastRedirections.default.redirections.table.information'}
											</th>
										</tr>
										</thead>
										<tbody class="bg-white"  data-ajax-append="true" n:snippet="redirectionsDesktop">

										{foreach $redirections as $redirection}
											<tr class="border-b border-light-4 h-[58px] text-sm leading-[24.5px] text-dark-1 text-center bg-white">
												<td class="pl-5">
													<img
															src="{$redirection->getShop()->getCurrentLogo()|image:300}"
															title="{$redirection->getShop()->getName()}" alt="{$redirection->getShop()->getName()}"
															class="max-w-[100px] max-h-[40px] w-full m-auto">
												</td>
												<td>
													<div class="text-center">
														{$redirection->getCreatedAt()|timeAgo}
													</div>
												</td>
												<td>
													{if isset($hasTransaction[$redirection->getId()])}
														<a n:href=":NewFront:Account:Transaction:default">
															<div
																	id="reward-success-tooltip-{$iterator->counter}"
																	data-shopName="{$redirection->getShop()->getName()}"
																	data-createdAt="{$redirection->getCreatedAt()->format('c')}"
																	class="cursor-pointer inline-flex flex-start text-light-2 text-sm font-medium rounded-lg text-secondary-green">
																{_'newFront.account.lastRedirections.default.redirections.table.reward'}
															</div>
														</a>
													{else}
														{if $canReportMissingReward[$redirection->getId()]}
															<a n:href=":NewFront:Account:Refund:default#missing-reward, redirectionId => $redirection->getId()">
																<div
																		class="cursor-pointer inline-flex flex-start text-primary-orange text-sm leading-[24.5px] font-medium py-2 px-5 rounded-lg border border-primary-orange/20 xl:hover:bg-primary-orange xl:hover:text-white">
																	{_'newFront.account.lastRedirections.default.redirections.table.report'}
																</div>
															</a>
														{else}
															<!-- DISABLED -->
															<div
																	id="reward-canceled-tooltip-{$iterator->counter}"
																	style="background: #FAFAFB;"
																	data-shopName="{$redirection->getShop()->getName()}"
																	data-refundHoursLimit="{$redirection->getShop()->getRefundHoursLimit()}"
																	data-createdAt="{$redirection->getCreatedAt()->format('c')}"
																	class="cursor-pointer inline-flex flex-start text-light-2 text-sm leading-[24.5px] font-medium py-2 px-5 rounded-lg border border-transparent">
																{_'newFront.account.lastRedirections.default.redirections.table.report'}
															</div>
														{/if}
													{/if}
												</td>
											</tr>
										{/foreach}
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="flex justify-center">
					{snippet redirectionsPaginator}
						{control redirectionsPaginator}
					{/snippet}
				</div>
			</div>
		</div>
	{else}
		<div class="grow">
			<div class="bg-white rounded-2xl p-[30px] md:py-10 mb-10 relative mt-[28px]">
				<div
						class="flex items-center gap-3 text-primary-orange text-[20px] mdtext-[30px] font-bold leading-[32px] md:leading-[37px] mb-[15px]">
					<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
						<rect width="30" height="30" fill="url(#pattern0_1468_5636)"/>
						<defs>
							<pattern id="pattern0_1468_5636" patternContentUnits="objectBoundingBox" width="1" height="1">
								<use xlink:href="#image0_1468_5636" transform="scale(0.0138889)"/>
							</pattern>
							<image id="image0_1468_5636" width="72" height="72" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAC+lBMVEUAAADgx8fZsbLkoqPaUVninp/bVV1WVlaJFBW5m5vqh4q7T1N9FBW0WFnTXl+8ZWXFcHDlhYbhjo7W2t+mP0CcPj/Mbm3YkI+4wszfkpKoq7HQaGrgZ23Rhobel5dxcHGjR0e4X1/UdHPQe3rpgofgf3+yt7ywcnOxw9bdXWS9XmDLgoGpbm6kc3S3R0rYaWqou83cdnXwgYajgXuulJVxZWi8OEPHZWPCaWrScHDLf36PUlKrSUnrdHmcOj3idnzLd3ezW1u5x9TAYF2BcGx+Hh3id3qYPz+HhX+dj4mJYGDPWFtZVFW0v9BZWlrAy9VdYGGgQELBytLCQ0itU1LY3+XUV1y9xMWZg4SkkZPO2eHU4Op7d3NWWFnn6Ojm5ubk5ebk4+Ti4uLg4OHd3t/a2trc3NzV1dabHh/m6OjS0tO+vb3Ozs+lICHCwsOSMS9/JCSvIiPSUleDKyvlam3p6+zLy8vVSFDROkSRFxjt8PKpW1zIQEmvOz6OKCbhZWqrT0/FPESZODfY19i1UlLYTFLNSVHMNT+mLCzk3t7kyMjf1cPZXWPlX2DMRE3GPz23PDyvLS2iGBje0r7kqanngYJhXl6/OjuEHRybFRWHDw/moKHmcHLfW2LVQku5LS2IJSTk2Nnk09PmlZXleHjQTUaIMDDHxsfLwcK4tbXisK+4rK63j5CvgoTKVVvBP07QP0nDOUi8REfENEHHMj2ONTabLix7HyCUDQ3o4ODIycnCrq+zYWLGTlCvRUa4zN/lt7m3paaEfn/pZmnNPzrFMjG2HiDmzM3lwMDnjIzaiofYeXXwa2/ZXFriVVnFSUVyGBjcx8fPkpGIiImjgYf4goXehH+5dHWjaWu7LzW+IySkoqayoKF3eHr1dHnbamynODaqFBXc0dLWw77Ht7jYp6acm5yQjI7AgoH1+PjhoaP5k5RtXWJaOjuTKSeWKCeni41lZWXZtrf/pqbTgnl/bHC4aGhsT0+JT1ZQKirqyMiyo46SeVn/uba5leTDAAAAXnRSTlMACA8X/iD9/v78VP7+/fjhpGJE/v720zw4M/z7yU8o/v3xs5N0cBj7+OrQfSr8+9qxo5j+/f3858PBV1Lx4sO2bqR7+9/Zzp6ATDvr5NzLk3t3Ut7c1KTyzMWupJuVVYLR7AAADL1JREFUWMOF12dYU1cYB3D23oIyBHEjrtZVt9YOW7v3zt47IQQjBEIYggIByt57g4iCbNl7iaLgAveerdr5PH3vvSEBQfv/oNdw7u++5z3n3IjWtGjr21gvWWJto6v+RGfPG2j26Kg/0p2HjtHXnnan5hJGmLw5f4WBnp7BivlL9FWf2c85icbNXvWB/pLJMW+awPNezjJ7ex2T+alBafv3H4SUi+abYM+Yt+HxGSTrP8QebDJfL20/DNq/v9zLYL61jr39smnOT26Ou1YgDAIdP378WMRHzuhPVu10tXO1s7PbuQr9p80Kr3IYgz3OJ9hi1ydub0yV7L8r6EWelOblBQMPArTJfZ4WJm3b9sP327aBg8TBvbw8zSsoyCsNHeUj6i12/HIKtOrd8aA0GKFnYGAgivAHB61A18Fkybp1u4yNd61bt8TEQVcbRn4bpGeQmppq4FWOSEeDLVbbT4FMYFrApPr5+YWJgn2ObXpbV8vFZN37O8R+qal6JaVwr594x/vrrF20db9tbfUTi/0MgkE6BtIKE43jsFbPyytoINxPnCIG6OimL/RN1u0Qp+p5RUT4+PiXrvaH+ESIwsZ3rDPRX7rjckqKXxhUfgygI2VrbdTLvssgCJjWvhR0gM9Xu623WIh84Nbg4GDoWllZWgSI/vFj8f5lTluWgiRGK/cHCKRdk9tgaZie3kBr3+XLfWLkQV9ZbTaNjBJG+QdDV6FrsGv0gmARyiN8xq5Ec2WmmxevbcWgo+AcaXBaoipovoFeeN/IyAhAYSL/Ne7bQxjcqLGDaXqpqWFlDaaBEFPThuDgiPKD7cLo6sSq7e4f6RmIgiN8wIGUzdXHOm1hkNo38ttv9y73pYrGqmwVDEZIensaIKbey0uNi+sgxcalq73XVPjsbz8vTKyWKlxl/hEA+QBTUVFhYY1u1jdFiPMvQOERwsqNlVJG1PmDBmGm3qXFtXQKmUTuaqKQKTR6fnGpd6P/8fMd3OqkvBbvqOP+sBD+8ZGRkQ2LtJHDsza4tS88Z2SkNWiMMfTi0v1HwnYvUeDoRQmFRCAQiURSUUwokUQikylz8i+ONsaDJH3m+GwoUZiubFMq45VK0Vpkbg4fKQeC2pVKZXsHV/DngwV/CNtFpqPNLDJBFWJMcigKkoDiNI/KxjqujN568KcHI1rY3tamzM3NtUCOgXNDeroQCdduO//PB4qOtmDvDQVXSZiCx+OJydmheLjEqGvXN8hBqnzwoHK7XXV0enp6fFuuBbIpt67hRiOR7nSfkCY87ChvGGWTs5o6AUGCw+Gzu0OZcIXOs/PGNXrhqKzjCt8sSeG+00gqZYSETARuRV4U2xnSxMRqo71WtonV3PNppus5TCapKDkTUSAAxdagfwMVevfuVcoc3nrbjiuMpERbq71mZgmPqqq22yDN/trXyChJ8MXuNQxwggIfz/FkMpmHuouAwaBugJCA3BTbRIROSR7bpl+B4YErv3gv4eEj/tcuyPK/nWc29N62eW+FgDNg+pgCDtKY2E41lAMQmqzY7kwi2nNMCnnLZtt7Gb6Kt9EX8Va7off26SyqSIw+P1B2huLpiTya2ZmTTcRppoYlWdzEJGDSGVk67PI1i3T37fS124q939/eu0rL2kKYeH4gbPWcA+BA4KbWJtXtmVmqi0Pi7ho8HpNqV0eOCRO5Tku1Vu2Flw4m6Wjpz2+40jGQ4i05gEHINFKgkOnpbj2Ew6MSiTKH7b1pTBht+r4Lcr86Sy180r1GzJvVDqQpBv8S1FSExxaPxCtkzbnYeDReKIOjrwkUJAoTXR5fXYNBuNcGj6M5nj7NCyhYvelIhazsfX2NAx0KE/vdMy+o+auzhon7v0hOL2i58PTGjTr50YY1a6BL6ugs6hf7jfSWHsD9lZzcVfN6huS44EnLhuGeu6fopZuOmMrKFmla5LCiP8fvXn3BnTu4ruyc7EOvc1i34p603O49W5RJoRTK4ysiK5zmaVrdL06J7V7ueQc6lBmTIs7OfKXDXmD4ZNRpPLsTjjCFtTweJIuVk472ov4c8T2nZsSBdHaLu3A4CmWm4km7GWd46aLT2S4iHjYTLcA4Mn4wUjM3/bX9KeJ79VdRCFLTWYOjnL7EngHVthjG3eLRD4UyCeiupPLkyoaKhrUuKsjGot8vJXY5Dhz1ylPhnptUgDUhOj4xXHCBQyUy8QQMosHcBgcHnZzVu7G/v2+8BBymGiLX3oJZsKdMi37L0LCFTacQkL2NSWRaSbxyUBk22aTFoiBRa1ndNIhI4zguUBWFx6u6fLOWSsLj8GjgLUcmk+silbnK/sWAYF8jubnhpgXq44qGQKazT0NRPDwu6zCOAl1eUMyiIS8EzAEI5na9sS13sH+RNrYdtzQoc4PMC1TPUp8EIpVzAYoq9Dx8GHfTMA4OBRmG4DAGHFg2aoG8LTdXtAU7/bpzB+OVbeb5FBKRgGdC1EVRkKIu8Q7/6njpEtJlvKocZBScXGg3xxyghrn6KigyPb7du+BpUVFMTExychNRXRSJymE7tqxff5otgS4jn2DpSk6+ezcmpqjoWj1MbVADRUWle9f1nD2bkyJuDY8NVZUE0yBQqNTCf/4ppFGQciYlYnc48l+gnLM9N+rbBuMr1JBMCFDBqVOH0GRNeY8ARWL9/TcLZfDqZKIDT0Hq2yMjGwHCmi3jRnXIC2hk+FJkQjQtR28nX7tGRrqjCbQS2o3sSIl5u2yica6u6qg1ckOEjdepIBGJ2Pfi1KIIv/5K0CBTFo1CDSgITJ8Iadyio9qQCCQrDqBRyBoIqMmyDs8OwerT6yqiJkLkb2pj0MpARgg3yphOpUBJBI2k2lgaSONg2zGAbhwZMlEVCDsbzVZzgISlLJgbBqkzK0RQz4zOuR1Vpah6x3ryBWlZxWAIvfPVc2NOL2A6hPx4cmb5z4VVVYq3Jl+ROpttGVXRsmI6lQYQHt8Zk/VKCB6TGdNFIKAFserWMBQPbdETgnVbLq1KjCrlBNDIOHxmsji8i/kaqDM8PDuLCAWxJCVRDIVv4GLtSch54cNHMDcei0oJbYoNz2kizlKRpj9dseEpMQE0Oof3PPphFf8dE80vapsV0kfRMmMO/VRyTk5MJrLdZoMIWJihRTl+PU9ZEuNIqcLXFva1Oivl0kdSYT37Rk9PTJZq+EyIoAosSNbd3t7h6+e4vnxfbPHV68aX+ibKSoaHr6m2NwS9UwVhyNSlvzZ8uyRSyvdF1kwT7cWuRg+lXO9iCZ06KeFJoVgBABGxi9BQvHoLsfLr3onO4Geg21qTeZZ8I9/qqPU8jloiZsP7ZAqEfTJ5yFi17NtRRkhBNiigKUme4JuQKNvAw2qCHd4VHkucrEjVteyBGILaMa6Q+vIzAqGgaXFxV5j5GkXLL6ISHLrQ2PBDeKyQohskTMoSi7MmnWbzxARFhu1aB62XsnuhR0JGEte7mSdhBVAppKLwbCIS2MRnezPx2HXMQDJ6xPLZzfVcI34G/52lWi9Hx0p+ApUusmtZdOrVnJwsIgm9PRQg7FcIUubZ8VOY85ybxPeFielozYjLZruhjARUyufQb4wXwfcfCYJCyAUZ8nR8mCXhFV6sD0mCBrnOdZnBoCunGBKcSOLKNxTyrv/ecxVaRUEwWk9vFqJQKHDeh53q2IUl5lxwBLawYrNmq2XlHyWV8JvB+mLj29dZ9AAqlQYYref3q/AnjQqbh3P93O3m0kZGEv/MH4q34D30Cmk5j3BGYFYd4l1qzM6XcMACLAAg+JMOjITHNi6tj5Im5K12c1xurf0qyP6bA+z7L/LMkhInRmF+QHFYLDpr+HcOnQVKLY9daLxcxjDKqHyx8YLbJ/avcpb9eIDzLO7FkEBglsSQ1ZcUs3n5tRKOZPh2gURSm89jF5c8b+QaJfD5ggeGzxxPvrFsdkf7swOUlrh3vSuHTnggVIh8eUlzIZvHMy7hQS3NJecCJxhGCbBaAoX3x4Ytbic/05kV2uPpeSvu3Q93W7p6oJSZESNEFlh/Dk19oCyEIU0AxlfAl1uu3Pdp3C03tz2zdelLwoEL4MA7xWqhnQAoD8EJwKQMNFKjhASBBx+q4bsutII3x76P79886fblbI2+U3x/ATgwR2erha55J04IPPJAE5yAK4GHR15lnocggy9fuMgZndG2jzdeODlLw/fAgm38QFWqjo2VpbldngDBgMgD0AOwSldzSytgsHxuCNJnM6APTm8ER9N5l91gye0qKwFBylG4ykHZ7TJlyOeG91tWzlz7Xz79QGf6Kuo7r7Ta7G65EGLpvtlqpbNGwer+/NOfl2mGqy+WYZfTMV19BxtnZxsHfd1pP515y3/azmW35li5jAAAAABJRU5ErkJggg=="/>
						</defs>
					</svg>
					{_'newFront.account.lastRedirections.default.withoutRedirections.title'}
				</div>
				<div class="text-sm leading-[24.5px] text-dark-1 w-full max-w-[461px] text-start">
					{_'newFront.account.lastRedirections.default.withoutRedirections.desctiption'|noescape}
				</div>

				<div class="absolute -top-[60px] right-[-20px] md:top-[20px] md:right-[190px]">
					<div class="relative">
						<svg class="w-[92px] h-[92px] md:w-[129px] md:h-[129px]" xmlns="http://www.w3.org/2000/svg" width="123" height="122"
							 viewBox="0 0 123 122"
							 fill="none">
							<path
									d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z"
									fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571" />
							<defs>
								<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126"
												y2="102.866" gradientUnits="userSpaceOnUse">
									<stop stop-color="#FDBB47" />
									<stop offset="0.495" stop-color="white" />
									<stop offset="1" stop-color="#FDBB47" />
								</linearGradient>
							</defs>
						</svg>
						<div
								class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[10px] md:text-sm font-bold text-white">
							{_'newFront.account.lastRedirections.default.withoutRedirections.guarantee'}
						</div>
					</div>
				</div>

				<img
						class="absolute bottom-[-46px] right-[-19px] md:top-[-10px] md:right-[90px] md:bottom-0 w-[75px] h-[122px] md:w-[144px] md:h-[233px]"
						src="{$basePath}/new-design/tipli-oslik-redirect-2.png" alt="oslik">
			</div>

			<div class="text-base md:text-lg leading-7 md:leading-[31.5px] font-medium text-dark-1 mb-5 w-[236px] md:w-auto pl-5 md:pl-0">
				{_'newFront.account.lastRedirections.default.withoutRedirections.table.title'}
			</div>

			<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2.5 md:gap-5 mb-5">
				{var tipli\Model\Shops\Entities\Shop[] $topShops = $getTopShops()}
				{foreach $topShops as $shop}
					<a
							class="hidden md:flex bg-white flex-col items-center justify-center rounded-xl cursor-pointer shadow-hover p-2 border border-transparent" href="{plink :NewFront:Shops:Shop:default $shop}">
						<div class="h-[92px] px-4 py-4 flex items-center justify-center">
							<img class="max-w-[100px] max-h-[60px] w-full" loading="lazy" alt="{$shop->getName()}" src="{$shop->getCurrentLogo()|image:300}">
						</div>
						<img class="m-auto" src="/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">

						<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
							{($shop|reward:true, 'extended')|noescape}
						</div>
					</a>

					<a href="{plink :NewFront:Shops:Shop:default $shop}" class="md:hidden group flex gap-4 items-center">
						<div
								class="border border-light-5 bg-white rounded-xl flex justify-center items-center w-[97px] h-[55px]">
							<img class="max-w-[58px] max-h-[38px]" alt="{$shop->getName()}" src="{$shop->getCurrentLogo()|image:300}" loading="lazy">
						</div>

						<div class="text-sm text-dark-1 leading-[18.75px]">
							<div class="leading-[24.5px] mb-1.5 line-clamp-1">{$shop->getName()}</div>
							<div class="similar-shop__value">
								{($shop|reward:true, 'extended')|noescape}
							</div>
						</div>
					</a>
				{/foreach}
			</div>

			<div>
				<div class="flex flex-col md:flex-row items-center justify-center gap-2.5 md:gap-[19px]">
					<a n:href=":NewFront:Shops:Shops:default"
					   class="w-full md:w-auto inline-flex items-center justify-center gap-[11px] text-base font-bold text-white leading-7 bg-orange-gradient pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
						{_'newFront.account.lastRedirections.default.withoutRedirections.table.allShops'}
					</a>
					<a n:href=":NewFront:Account:Refund:default"
					   class="w-full md:w-auto inline-flex items-center justify-center gap-[11px] text-base font-bold text-dark-1 leading-7 border border-bg-light-4 pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-gray-gradient-hover">
						{_'newFront.account.lastRedirections.default.withoutRedirections.table.help'}
					</a>
				</div>
			</div>
		</div>
	{/if}

	<script>
		window.onload = function() {
			document.querySelectorAll('[id^="reward-success-tooltip-"]').forEach(function(element) {
				let shopName = element.getAttribute('data-shopName');

				let translatedText;
				translatedText = {_"newFront.account.lastRedirections.default.withoutRedirections.table.tooltipContentSuccess"};
				translatedText = translatedText
						.replace('%shop%', shopName);

				tippy(element, {
					animation: 'scale',
					content: translatedText,
					theme: 'transaction-tooltip',
					placement: 'bottom',
					allowHTML: true,
				});
			});

			document.querySelectorAll('[id^="reward-success-tooltip-mobile-"]').forEach(function(element) {
				let shopName = element.getAttribute('data-shopName');

				let translatedText;
				translatedText = {_"newFront.account.lastRedirections.default.withoutRedirections.table.tooltipContentSuccess"};
				translatedText = translatedText
						.replace('%shop%', shopName);

				tippy(element, {
					animation: 'scale',
					content: translatedText,
					theme: 'transaction-tooltip',
					placement: 'bottom',
					trigger: 'click',
					allowHTML: true,
				});
			});

			document.querySelectorAll('[id^="reward-canceled-tooltip-"]').forEach(function(element) {
				let shopName = element.getAttribute('data-shopName');
				let refundHoursLimit = parseInt(element.getAttribute('data-refundHoursLimit')) || 48;
				let createdAtStr = element.getAttribute('data-createdAt');

				if (!createdAtStr) {
					console.error("Missing createdAt for element:", element);
					return;
				}

				let createdAt = new Date(createdAtStr);
				let now = new Date();

				if (isNaN(createdAt.getTime())) {
					console.error("Invalid date format:", createdAtStr);
					return;
				}

				let createdAtSeconds = Math.floor(createdAt.getTime() / 1000);
				let nowSeconds = Math.floor(now.getTime() / 1000);

				let timePassed = nowSeconds - createdAtSeconds;
				let totalSeconds = refundHoursLimit * 60 * 60;
				let secondsLeft = Math.max(0, totalSeconds - timePassed);

				let hours = Math.floor(secondsLeft / 3600);
				let minutes = Math.floor((secondsLeft % 3600) / 60);

				let translatedText;
				translatedText = {_"newFront.account.lastRedirections.default.withoutRedirections.table.tooltipContent"};
				translatedText = translatedText
						.replace('%shopName%', shopName)
						.replace('%hours%', hours)
						.replace('%minutes%', minutes);

				tippy(element, {
					animation: 'scale',
					content: translatedText,
					theme: 'transaction-tooltip',
					placement: 'bottom',
					allowHTML: true,
				});
			});

			document.querySelectorAll('[id^="reward-canceled-tooltip-mobile-"]').forEach(function(element) {
				let shopName = element.getAttribute('data-shopName');
				let refundHoursLimit = parseInt(element.getAttribute('data-refundHoursLimit')) || 48;
				let createdAtStr = element.getAttribute('data-createdAt');

				if (!createdAtStr) {
					console.error("Missing createdAt for element:", element);
					return;
				}

				let createdAt = new Date(createdAtStr);
				let now = new Date();

				if (isNaN(createdAt.getTime())) {
					console.error("Invalid date format:", createdAtStr);
					return;
				}

				let createdAtSeconds = Math.floor(createdAt.getTime() / 1000);
				let nowSeconds = Math.floor(now.getTime() / 1000);

				let timePassed = nowSeconds - createdAtSeconds;
				let totalSeconds = refundHoursLimit * 60 * 60;
				let secondsLeft = Math.max(0, totalSeconds - timePassed);

				let hours = Math.floor(secondsLeft / 3600);
				let minutes = Math.floor((secondsLeft % 3600) / 60);

				let translatedText;
				translatedText = {_"newFront.account.lastRedirections.default.withoutRedirections.table.tooltipContent"};
				translatedText = translatedText
						.replace('%shopName%', shopName)
						.replace('%hours%', hours)
						.replace('%minutes%', minutes);

				tippy(element, {
					animation: 'scale',
					content: translatedText,
					theme: 'transaction-tooltip',
					placement: 'bottom',
					trigger: 'click',
					allowHTML: true,
				});
			});
		};
	</script>
{/block}
