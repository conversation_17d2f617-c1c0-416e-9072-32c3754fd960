

{snippet}
    {ifset $deal}
		{var $shop = $deal->getShop()}
		{capture $dealRedirectionUrl}{plink //:NewFront:Shops:Redirection:deal $deal}{/capture}
		{var $dealRedirectionUrl = $dealRedirectionUrl}

        <div id="copy-coupon-popup" class="fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5">
            <div class="bg-white m-auto w-[463px] max-w-full rounded-2xl">
                <div class="pt-[26px] rounded-t-2xl border bg-light-6 relative">
                    <div class="copy-coupon-popup-close hover:cursor-pointer absolute top-[-19px] right-[-28px]">
                        <img src="{$basePath}/new-design/close-btn.svg" alt="close">
                    </div>
                    <div class="flex items-center justify-center w-[169px] h-[96px] bg-white border border-light-5 rounded-2xl mx-auto mb-5">
						<img class="m-auto max-w-[100px] max-h-[60px] w-full" src="{$deal->getShop()->getCurrentLogo() |image:200,0,'fit',false,$deal->getShop()->getName()}" alt="">
                    </div>

                    <div class="text-center md:text-xl leading-[24.5px] md:leading-[35px] text-dark-1 md:px-[59px] pb-6">
                        {$deal->getName()}
                    </div>
                </div>

                <div class="mt-[30px] px-5 pb-5 md:px-[79px] relative" n:if="$deal->getCode()">
                    <button class="flex pt-4 pb-[15px] items-center justify-between relative w-full md:py-3 rounded-xl text-xl leading-[28px] border-dashed border border-dark-4 js-copy-code-button" data-coupon-code="{$deal->getCode()}" data-coupon-copied="{_'newFront.deals.deal.copied'}">
                        <span class="js-copy-change-text m-auto">{$deal->getCode()}</span>
                        <img class="absolute right-[15px] js-copy-icon" n:if="$deal->getCode()" src="{$basePath}/new-design/copy-icon.svg" alt="copy">
						<img class="absolute right-[15px] hidden js-copy-icon-copied" n:if="$deal->getCode()" src="{$basePath}/new-design/copy-icon-copied.svg" alt="copy">
                    </button>
                    <a href="{$dealRedirectionUrl}" target="_blank" class="block text-center w-full py-[14px] rounded-xl bg-orange-gradient text-white font-bold mt-2 leading-7 cursor-pointer xl:hover:bg-orange-gradient-hover">
                        {_newFront.popups.dealDetail.cta, ['shop' => $deal->getShop()->getName()]}
                    </a>
                </div>

                <div class="hidden flex items-center justify-center gap-[7px] text-sm leading-[24.5px] text-dark-4 mt-2.5">
                    {_newFront.popups.dealDetail.conditions}
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="11" height="7" viewBox="0 0 11 7" fill="none">
                            <path d="M1 1L4.91903 5.35448C5.22952 5.69947 5.77048 5.69947 6.08097 5.35448L10 1" stroke="#ADB3BF"/>
                        </svg>
                    </div>
                </div>

				<div n:if="$deal->getDescription()" class="p-5 pt-2.5">
					<div class="bg-pastel-orange-light text-center text-xs text-black p-5 rounded-lg">
                		{$deal->getDescription()}
					</div>
				</div>
            </div>
        </div>

		<script>
			let copyCouponPopup = document.getElementById("copy-coupon-popup");;
			let closeCopyCouponPopup = document.querySelector(".copy-coupon-popup-close");

			closeCopyCouponPopup.onclick = function() {
				copyCouponPopup.style.display = "none";
			}

			window.onclick = function(event) {
				if (event.target == copyCouponPopup) {
					copyCouponPopup.style.display = "none";
				}
			}

			// Copy coupon code
			var copyButtons = document.querySelectorAll('.js-copy-code-button');

			if (copyButtons.length > 0) {
				// Přidání event listeneru pro každé tlačítko
				copyButtons.forEach(function (button) {
					button.addEventListener('click', function () {
						// Získání hodnoty data atributů
						var couponCode = button.getAttribute('data-coupon-code');
						var couponCopiedText = button.getAttribute('data-coupon-copied');

						// Vytvoření dočasného textarea elementu pro zkopírování textu
						var tempTextarea = document.createElement('textarea');
						tempTextarea.value = couponCode;
						document.body.appendChild(tempTextarea);

						// Zkopírování obsahu do schránky
						tempTextarea.select();
						document.execCommand('copy');

						// Odstranění dočasného textarea elementu
						document.body.removeChild(tempTextarea);

						// Najděte element s třídou 'js-copy-change-text' uvnitř tlačítka
						var textElement = button.querySelector('.js-copy-change-text');
						if (textElement) {
							// Uložení původního textu
							var originalText = textElement.textContent;

							// Změna textu na hodnotu z data-coupon-copied
							textElement.textContent = couponCopiedText;

							// Přidání vizuálních tříd
							button.classList.add('border-secondary-green', 'text-secondary-green', 'font-medium', 'bg-pastel-green-light');

							// Změna ikonky
							button.querySelector('.js-copy-icon').classList.add('hidden')
							button.querySelector('.js-copy-icon-copied').classList.remove('hidden')

							// Po 1 sekundě vrátíme původní text a odstraníme třídy
							setTimeout(function () {
								textElement.textContent = originalText;
								button.classList.remove('border-secondary-green', 'text-secondary-green', 'font-medium', 'bg-pastel-green-light');

								// Změna ikonky
								button.querySelector('.js-copy-icon').classList.remove('hidden')
								button.querySelector('.js-copy-icon-copied').classList.add('hidden')
							}, 1000);
						}
					});
				});
			}
		</script>
	{/ifset}
{/snippet}
