{var $id = $deal->getId()}
{var $isDetailable = !$user->isLoggedIn() && $deal->getCode() && !empty($deal->getCode())}
{var $isDognet = $deal->getShop()->getPartnerSystem() !== null ? $deal->getShop()->getPartnerSystem()->isDognet() : false}
{capture $dealDetailUrl}{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}{/capture}

<div class="deal-item" data-type="banner" data-category="deal" data-id="1891" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event" id="deal-{$deal->getId()}" {$deal->getLocalization()->isRomanian() ? 'data-hit="event" data-category="defaultLogged" data-action="click" data-label="shopsOrDeals"' |noescape}>

	<a n:if="$isAdmin && $deal->getSteveId() !== null" href="https://letaky.tipli.cz/admin/deals/deal/{$deal->getSteveId()}" target="_blank" class="deal-item__edit"><svg class="deal-item__edit-icon"><use xlink:href="/images/svg/dist/svg.svg#edit-solid"></use></svg></a>

    <div class="deal-item__aside deal-item--bg-gradient-green ">
		<div class="count-of-deal count-of-deal--red" title="{$deal->getLevelName()} / {$deal->getPriority()}" n:if="$isAdmin">
			{$deal->getLevelName()|first} / {$deal->getPriority()}
		</div>
        {capture $image}
           <img src="{$basePath}/images/pixel.png" data-src="{$deal->getShop()->getCurrentLogo() |image:170,0,'fit',false,$deal->getShop()->getName()}" data-src-retina="{$deal->getShop()->getCurrentLogo() |image:340,0,'fit',false,$deal->getShop()->getName()}" alt="{$deal->getShop()->getName()}" class="deal-item__logo-image unveil">
        {/capture}

        {if $isDognet === true && $user->isLoggedIn() === false}
            <span data-type="banner" data-category="deal" data-id="{$deal->getId()}" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event" class="deal-item__image-wrapper">
                {$image}
            </span>
        {else}
            <a n:href="//:NewFront:Shops:Redirection:deal $deal"  {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} rel="nofollow" data-type="banner" data-category="deal" data-id="{$deal->getId()}" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event" class="deal-item__image-wrapper">
                {$image}
            </a>
        {/if}
    </div>

    <div class="deal-item__content">
        <div class="deal-item__row deal-item__row--direction-column deal-item__text-content">
            <h3 class="deal-item__title mb-3">
                {if $isDognet === true && $user->isLoggedIn() === false}
                    <span class="deal-item__title-link" data-type="banner" data-category="deal" data-id="{$deal->getId()}" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event">
                        <span n:if="$deal->isExclusive()" class="deal-item__tag">{_'front.deals.deal.exclusive'}</span>
                        {$deal |dealName:true|noescape}
                    </span>
                {else}
                    <a n:href="//:NewFront:Shops:Redirection:deal $deal"  {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} rel="nofollow" class="deal-item__title-link" data-type="banner" data-category="deal" data-id="{$deal->getId()}" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event">
                        <span n:if="$deal->isExclusive()" class="deal-item__tag">{_'front.deals.deal.exclusive'}</span>
                        {$deal |dealName:true|noescape}
                    </a>
                {/if}
            </h3>

            <p class="deal-item__text">
               <span class="deal-item__show-more--short">
                    <a href="{$dealDetailUrl}" class="color-grey hover-color-grey ajax" data-ajax-call="js-open-deal-detail">
                        {$deal->getDescription() |striptags|truncate:65|noescape}
                    </a>

                    <a href="{$dealDetailUrl}" n:if="strlen($deal->getDescription()) > 65" class="deal-item__link ajax color-grey" data-ajax-call="js-open-deal-detail">{_'front.deals.deal.showMore'}</a>
               </span>
            </p>
        </div>

        <div class="deal-item__row mt-auto deal-item__row--bottom {if $user->isLoggedIn() && $deal->getCode() && !empty($deal->getCode())}deal-item__row--code-button flex-wrap-sm-no-wrap{/if}">
            {if ($user->isLoggedIn() || $isDognet) && $deal->getCode() && !empty($deal->getCode())}
                <div class="p-relative flex-grow-1 w100">
                    <div class="deal-item__code js-copy-coupon js-copy-coupon-content" data-copy="{$deal->getCode()}" title="{_'front.deals.deal.copy'}" data-copy-complete="{_'front.deals.deal.copyComplete'}">
                        <span class="ellipsis">{$deal->getCode()}</span>
                        <button class="deal-item__code-icon js-copy-coupon" data-copy="{$deal->getCode()}" title="{_'front.deals.deal.copy'}"><svg>{('cut-solid'|svg)|noescape}</svg></button>
                    </div>
                </div>
            {/if}

            {if $isDognet === false || $user->isLoggedIn()}
                <a n:href=":NewFront:Shops:Redirection:deal $deal" {if $isDetailable}onclick="javascript:window.open('{$dealDetailUrl |noescape}', '_blank');"{else}target="_blank"{/if} rel="nofollow" class="deal-item__button" data-type="banner" data-category="deal" data-id="{$deal->getId()}" data-name="{$deal->getName()}" data-creative="deal" data-action="click" data-hit="event" data-gtm-vis-first-on-screen-2690234_47="437" data-gtm-vis-total-visible-time-2690234_47="100" data-gtm-vis-has-fired-2690234_47="1" >
                    {if $deal->isSaleType()}
                        {_'front.deals.deal.button.getSale'}
                    {elseif ($deal->getCode() && !empty($deal->getCode()))}
                        {if $user->isLoggedIn()}
                            {_'front.deals.deal.button.useCode'}
                        {else}
                            <span class="deal-item__button-text">
                                {_'front.deals.deal.button.getCode'}
                            </span>
                            <span class="deal-item__button-code">
                                {if $deal->getCode() !== null && Nette\Utils\Strings::length($deal->getCode()) > 3}{mb_substr($deal->getCode(), -3, 3)}
                                {else}{$deal->getCode()}{/if}
                            </span>
                            <span class="deal-item__button-corner"></span>
                        {/if}
                    {elseif $deal->isCouponType()}
                        {_'front.deals.deal.button.useCode'}
                    {/if}
                </a>
            {/if}
        </div>
    </div>
</div>

{capture $url |trim}
    {plink "//this!#deal-$id", openDeal => $deal->getFullSlug()}
{/capture}

{capture $startDate |trim}
    {var $currentDate = new DateTime()}
    {if $currentDate->format('d') > 15}
        {$currentDate->modify('first day of this month')->format('Y-m-d')}
    {else}
        {$currentDate->modify('first day of previous month')->format('Y-m-d')}
    {/if}
{/capture}

{capture $endDate |trim}
    {if $deal->getValidTill() < (new \DateTime())->modify('+ 3 months')}
        {$deal->getValidTill()->format('Y-m-d')}
    {else}
        {(new \DateTime())->modify('+ 10 days')->format('Y-m-d')}
    {/if}
{/capture}

{capture $image |trim}
    {$deal->getShop()->getCurrentLogo() |image:170,0,'fit',false,$deal->getShop()->getName(), false, true}
{/capture}

{if $deal->isCouponType()}
    <script type="application/ld+json">
	{
		"@context": "http://schema.org",
		"@type": "SaleEvent",
		"name": {$deal |dealName},
		"description": {$deal->getDescription()},
		"url": {$url|noescape},
		"startDate": {$startDate|noescape},
		"endDate": {$endDate|noescape},
		"image": {$image|noescape},
		"location": {
			"@type": "Place",
			"name": {$deal->getShop()->getName()},
			"address": {$deal->getShop()->getName()}
		},
		"eventAttendanceMode": "online"
	}
	</script>
{/if}
