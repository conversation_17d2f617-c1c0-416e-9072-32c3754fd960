<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Components\PhoneNumberVerification;

use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Localization\Translator;
use Nette\Utils\ArrayHash;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserDuplicity;
use tipli\Model\Account\PhoneNumberVerificationManager;
use tipli\Model\Account\UserDuplicityCheckerFacade;
use tipli\Model\Account\UserFacade;
use tipli\Model\Configuration;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\BrowserTokenLayer;
use tipli\Model\Layers\ClientLayer;
use tipli\TooManyRequestsException;
use Tracy\Debugger;

class PhoneNumberVerificationControl extends Control
{
	public array $onSuccess = [];

	private int $step = 1;

	private ?string $phoneNumber = null;

	public function __construct(
		private User $user,
		private Configuration $configuration,
		private PhoneNumberVerificationManager $phoneNumberVerificationManager,
		private ClientLayer $clientLayer,
		private UserFacade $userFacade,
		private BrowserTokenLayer $browserTokenLayer,
		private UserDuplicityCheckerFacade $userDuplicityCheckerFacade,
		private Translator $translator
	) {
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		if ($this->user->isAdmin()) {
			$allowedPhoneCountryCodes = ['+420', '+421', '+48', '+40', '+36', '+386', '+385', '+359'];
		} else {
			$allowedPhoneCountryCodes = $this->configuration->getAllowedPhoneCountryCodes($this->user->getLocalization());
		}

		if ($userPhoneNumberCountryCode = $this->user->getPhoneNumberCountryCode()) {
			$allowedPhoneCountryCodes[] = $userPhoneNumberCountryCode;
		}

		$form->addSelect('phoneNumberCountryCode')
			->setItems($allowedPhoneCountryCodes, false)
			->setRequired()
		;

		$form->addText('phoneNumber', 'newFront.account.user.settings.form.phoneNumber')
			->setRequired()
			->addRule(Form::PATTERN, $this->translator->translate('newFront.account.user.settings.form.validator.phoneNumber'), '^[()0-9 -]{9,11}$')
		;

		$form->setDefaults([
			'phoneNumberCountryCode' => $this->user->getPhoneNumberCountryCode(),
			'phoneNumber' => $this->user->getPhoneNumberWithoutCountryCode(),
		]);

		$form->addSubmit('submit', 'newFront.account.user.settings.form.submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$user = $this->user;

		$phoneNumber = str_replace(' ', '', $values->phoneNumber);

		if (($values->phoneNumberCountryCode . ' ' . str_replace(' ', '', $values->phoneNumber)) !== $user->getPhoneNumber()) {
			$change = $this->userFacade->createChange(
				$user,
				$this->browserTokenLayer->getBrowserToken(),
				$this->clientLayer->getIp(),
				$this->clientLayer->getUserAgent()
			);

			$phoneNumberWithCountryCode = ContentFilter::stringSanitize($values->phoneNumberCountryCode . ' ' . $phoneNumber);
			$change->setPhoneNumber($phoneNumberWithCountryCode);

			if ($phoneNumber) {
				$this->userDuplicityCheckerFacade->scheduleUserDuplicityCheck($user, UserDuplicity::TYPE_PHONE_NUMBER, $phoneNumberWithCountryCode);
			}
		}

		if (!empty($values->phoneNumber) && !empty($values->phoneNumberCountryCode)) {
			$user->setPhoneNumber($values->phoneNumberCountryCode, str_replace(' ', '', $values->phoneNumber));
		}

		try {
			Debugger::log($this->user->getId() . ': ' . $user->getPhoneNumber(), 'lucky-shop-phone-number-verification');
			$this->phoneNumberVerificationManager->requestVerification($this->user, false, $this->clientLayer->getIp(), $this->clientLayer->getPlatform());
		} catch (TooManyRequestsException $e) {
			$this->flashMessage($this->translator->translate('front.verifysms.tooManyRequests'));
		}

		$this->userFacade->saveUser($user);

		$this->step = 2;
		$this->phoneNumber = $values->phoneNumberCountryCode . ' ' . $values->phoneNumber;

		$this->redrawControl('steps');
	}

	public function createComponentVerificationForm()
	{
		$form = new Form();

		$form->addText('code')
			->setRequired();

		$form->addHidden('phoneNumber')
			->setDefaultValue($this->phoneNumber)
		;

		$form->addSubmit('submit');

		$form->onSuccess[] = [$this, 'verificationFormSucceeded'];

		return $form;
	}

	public function verificationFormSucceeded(Form $form, ArrayHash $values)
	{
		try {
			$user = $this->user;
			$code = $values->code;

			$verificationCode = $this->phoneNumberVerificationManager->findValidVerificationCode($user, $user->getPhoneNumber(), $code);
			if ($verificationCode) {
				$this->phoneNumberVerificationManager->useVerificationCode($verificationCode);

				$this->userFacade->verifyUserPhoneNumber($user);
			} else {
				throw new InvalidArgumentException($this->translator->translate('newFront.account.user.settings.form.validator.invalidCode'));
			}

			$this->onSuccess($this);
		} catch (InvalidArgumentException $e) {
			$this->step = 2;
			$this->phoneNumber = $values->phoneNumber;

			$form->addError($e->getMessage());
			$this->redrawControl('steps');
		}
	}

	public function handleChangePhoneNumber(): void
	{
		$this->step = 1;
		$this->redrawControl('steps');
	}

	public function render()
	{
		$this->template->step = $this->step;
		$this->template->phoneNumber = $this->phoneNumber;
		$this->template->setFile(__DIR__ . '/phoneNumberVerificationControl.latte');
		$this->template->render();
	}
}
