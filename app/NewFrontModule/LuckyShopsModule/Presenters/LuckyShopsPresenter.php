<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Presenters;

use Nette\DI\Attributes\Inject;
use tipli\FrontModule\Forms\EmailSignUpControl;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\NewFrontModule\Components\IPaginatorFactory;
use tipli\NewFrontModule\Components\Paginator;
use tipli\NewFrontModule\LuckyShopsModule\Components\LuckyShopPicker\LuckyShopPickerControl;
use tipli\NewFrontModule\LuckyShopsModule\Components\LuckyShopPicker\LuckyShopPickerControlFactory;
use tipli\NewFrontModule\LuckyShopsModule\Components\NotificationSettingsControl\NotificationSettingsControl;
use tipli\NewFrontModule\LuckyShopsModule\Components\NotificationSettingsControl\NotificationSettingsControlFactory;
use tipli\NewFrontModule\LuckyShopsModule\Components\PhoneNumberVerification\PhoneNumberVerificationControl;
use tipli\NewFrontModule\LuckyShopsModule\Components\PhoneNumberVerification\PhoneNumberVerificationControlFactory;
use tipli\NewFrontModule\LuckyShopsModule\Components\UserLuckyShopPopupControl\UserLuckyShopPopupControl;
use tipli\NewFrontModule\LuckyShopsModule\Components\UserLuckyShopPopupControl\UserLuckyShopPopupControlFactory;
use tipli\NewFrontModule\Presenters\BasePresenter;
use Tracy\Debugger;

class LuckyShopsPresenter extends BasePresenter
{
	#[Inject]
	public UserLuckyShopPopupControlFactory $userLuckyShopPopupControlFactory;

	#[Inject]
	public LuckyShopPickerControlFactory $luckyShopPickerControlFactory;

	#[Inject]
	public IPaginatorFactory $paginatorFactory;

	#[Inject]
	public PhoneNumberVerificationControlFactory $phoneNumberVerificationControlFactory;

	#[Inject]
	public NotificationSettingsControlFactory $notificationSettingsControlFactory;

	public const STATE_NEXT_LUCKY_SHOP = 'nextLuckyShop';
	public const STATE_LUCKY_SHOP_REVEAL = 'luckyShopReveal';
	public const STATE_WIN = 'win';
	public const STATE_LOSE = 'lose';

	public function startup(): void
	{
		parent::startup();

		if (
			!$this->getLocalization()->isSlovak() &&
			!$this->getLocalization()->isCzech() &&
			!$this->getLocalization()->isPolish() &&
			!$this->getLocalization()->isRomanian() &&
			!$this->getLocalization()->isHungarian() &&
			!$this->getLocalization()->isCroatian() &&
			$this->configuration->getMode() !== 'test' &&
			!(
			($this->user->isLoggedIn() && $this->getUserIdentity()->isAdmin()) ||
			($this->getHttpRequest()->getCookie('YeACt4VB315szpD0yCED') !== null)
			)
		) {
			$this->error('Not found');
		}

		if (($this->getParameter('at') || $this->getParameter('userToken')) && $this->getParameter('do') === null) {
			$params = $this->getParameters();
			$params['at'] = null;
			$params['userToken'] = null;

			$this->redirect('this', $params);
		}

		if ($this->user->isLoggedIn() && $this->getParameter('from') && $this->getParameter('from') === 'push') {
			$user = $this->getUserIdentity();
			$luckyShopData = $user->getUserLuckyShopData();

			$luckyShopData->setLastPushNotificationOpenedAt(new \DateTime());

			$this->userFacade->saveUser($user);

			$this->redirect('this');
		}
	}

	public function actionDefault(): void
	{
		if ($this->user->isLoggedIn() === false || $this->getUserIdentity()->hasUserLuckyShopData() === false) {
			$this->redirect('intro');
		}
	}

	public function actionIntro()
	{
		if ($this->user->isLoggedIn() === false) {
			$this->setView('signUp');
		}

		if ($this->user->isLoggedIn() && $this->getUserIdentity()->hasUserLuckyShopData() === true) {
			$this->redirect('default');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
	}

	public function renderIntro(): void
	{
		$this->template->isRegisteredFromLuckyShops = $this->sectionFactory->getFrontendSection()->registeredFromLuckyShop;
	}

	public function actionTest()
	{
	}

	public function renderDefault(): void
	{
		$isLuckyShopPaginatorRequest = $this->getParameter('do') === 'luckyShopsPaginator-page';

		if ($isLuckyShopPaginatorRequest || $this->getParameter('openDeal') !== null || $this->isAjax() === false) {
			$defaultLuckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($this->getLocalization(), LuckyShopCampaign::NAME_DEFAULT);
			$currentLuckyShop = $this->luckyShopFacade->findCurrentLuckyShopByCampaign($defaultLuckyShopCampaign);

			$currentStateForUser = $this->resolveCurrentStateForUser($currentLuckyShop, $defaultLuckyShopCampaign);
			$this->template->currentStateForUser = $currentStateForUser;

			$userLuckyShopsWithShop = $this->luckyShopFacade->findValidUserLuckyShops($this->getUserIdentity(), onlyWithShop: true);

			$activeUserLuckyShopsWithShop = array_filter($userLuckyShopsWithShop, static fn (UserLuckyShop $userLuckyShop) => $userLuckyShop->isDelisted() === false);

			usort($userLuckyShopsWithShop, static function ($a, $b) {
				return $a->isDelisted() <=> $b->isDelisted();
			});

			$userLuckyShopsWithoutShop = $this->luckyShopFacade->findValidUserLuckyShops($this->getUserIdentity(), onlyWithoutShop: true);
			$userLuckyShops = array_merge($userLuckyShopsWithShop, $userLuckyShopsWithoutShop);

			$this->template->activeUserLuckyShopsWithShop = $activeUserLuckyShopsWithShop;
			$this->template->userLuckyShopsWithShop = $userLuckyShopsWithShop;
			$this->template->userLuckyShopsWithoutShop = $userLuckyShopsWithoutShop;
			$this->template->userLuckyShops = $userLuckyShops;

			$this->template->userLuckyShopSources = array_map(static fn ($userLuckyShop) => $userLuckyShop->getSource(), $userLuckyShops);

			$this->template->luckyShopCampaign = $defaultLuckyShopCampaign;
			$this->template->currentLuckyShop = $currentLuckyShop;

			if ($currentLuckyShop) {
				$usersChecksWithLuckyShops = $this->luckyShopFacade->findUsersChecksWithLuckyShop($currentLuckyShop);
				$this->template->countOfUsersWithLuckyShop = $currentLuckyShop->getCountOfUsers();
				$this->template->countOfUsersChecksWithLuckyShop = $currentLuckyShop->getCountOfUsersWithCheck();
				$this->template->rewardAmountPerUser = $defaultLuckyShopCampaign->getRewardAmountPerUser(count($usersChecksWithLuckyShops));
			}

			$luckyShopsQuery = $this->luckyShopFacade->createLuckyShopsQuery()
				->withLuckyShopCampaign($defaultLuckyShopCampaign)
				->onlyValid()
			;

			if ($currentStateForUser === self::STATE_LUCKY_SHOP_REVEAL) {
				$luckyShopsQuery->onlyClosed();
			}

			$luckyShops = $this->luckyShopFacade->fetchLuckyShops($luckyShopsQuery);
			$luckyShops->applyPaginator($this['luckyShopsPaginator']->getPaginator());

			$this->template->luckyShopsHistory = $luckyShops;

			$this->template->getCountOfUsersChecksWithLuckyShop = function (LuckyShop $luckyShop) {
				return count($this->luckyShopFacade->findUsersChecksWithLuckyShop($luckyShop));
			};

			$this->template->isAdmin = $this->getUserIdentity()->isAdmin();

			$this->template->getTopDailyDeals = function () {
				$sortedDealIds = $this->dealFacade->findTopDailyDealIds($this->getLocalization(), $this->getUserIdentity(), 2, true);
				$sortedCouponsQuery = $this->dealFacade->createDealsQuery($this->getLocalization())
					->withIds($sortedDealIds)
					->sortByIds(array_reverse($sortedDealIds))
				;

				return $this->dealFacade->fetch($sortedCouponsQuery);
			};

			$this->template->getUserLuckyShopsForLuckyShop = function (LuckyShop $luckyShop) {
				return $this->luckyShopFacade->findUserLuckyShopsForLuckyShop($this->getUserIdentity(), $luckyShop);
			};

			$this->template->requestPhoneNumberVerification = false;

			if (
				$this->getUserIdentity()->hasVerifiedPhoneNumber() === false &&
				$this->luckyShopFacade->findUserLuckyShopForLuckyShop($this->getUserIdentity(), $currentLuckyShop)
			) {
				$this->template->requestPhoneNumberVerification = true;
			}

			$this->listenOpenDeal('dealDetailControl');
		}
	}

	public function actionSetUserLuckyShop(?string $source = null)
	{
		$userLuckyShopWithoutShop = $this->luckyShopFacade->findValidUserLuckyShopWithoutShop($this->getUserIdentity(), $source);

		if (!$userLuckyShopWithoutShop) {
			$this->redirect('default');
		}

		$this->redirect('default', [
			'userLuckyShopPopupControl-userLuckyShopId' => $userLuckyShopWithoutShop->getId(),
			'do' => 'userLuckyShopPopupControl-open',
		]);
	}

	protected function createComponentHpEmailSignUpControl(): EmailSignUpControl
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function () {
			$this->afterSignUpFromLuckyShop();
			$this->redirect('this');
		};

		return $control;
	}

	public function handleResetTestData()
	{
		if ($this->getUserIdentity()->isAdmin() === false) {
			$this->error('Not found');
		}

		$this->luckyShopFacade->resetTestData($this->getUserIdentity());
		$this->redirect('this');
	}

	/**
	 * @crossOrigin
	 */
	public function handleCheckLuckyShop(): void
	{
		$user = $this->getUserIdentity();

		$defaultLuckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($this->getLocalization(), LuckyShopCampaign::NAME_DEFAULT);

		$currentLuckyShop = $this->luckyShopFacade->findCurrentLuckyShopByCampaign($defaultLuckyShopCampaign);

		if ($currentLuckyShop->isCheckAllowed() === false) {
			$this->redirect('this');
		}

		$userLuckyShop = $this->luckyShopFacade->findUserLuckyShopForLuckyShop($user, $currentLuckyShop);

		if ($userLuckyShop && $user->hasVerifiedPhoneNumber() === false) {
			$this->redirect('this');
		}

		if ($this->luckyShopFacade->findUserLuckyShopCheck($user, $currentLuckyShop) !== null) {
			$this->redirect('this');
		}

		$this->luckyShopFacade->createUserLuckyShopCheck(
			$user,
			$currentLuckyShop,
			$defaultLuckyShopCampaign,
			$userLuckyShop
		);

		$this->redirect('this');
	}

	public function handleProcessLuckyShop()
	{
		if ($this->getUserIdentity()->isAdmin() === false) {
			$this->error('Not found');
		}

		$defaultLuckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($this->getLocalization(), LuckyShopCampaign::NAME_DEFAULT);
		$shop = $this->shopFacade->find(1);

		$this->luckyShopFacade->createLuckyShopForCampaign($defaultLuckyShopCampaign, $shop);

		$this->redirect('this');
	}

	protected function createComponentUserLuckyShopPopupControl(): UserLuckyShopPopupControl
	{
		return $this->userLuckyShopPopupControlFactory->create($this->getUserIdentity());
	}

	protected function createComponentLuckyShopPickerControl(): LuckyShopPickerControl
	{
		$control =  $this->luckyShopPickerControlFactory->create(
			$this->getUserIdentity()
		);

		$control->onSuccess[] = function () {
			$this->redirect('this');
		};

		return $control;
	}

	protected function createComponentLuckyShopPickerModalControl(): LuckyShopPickerControl
	{
		$control =  $this->luckyShopPickerControlFactory->create(
			$this->getUserIdentity(),
			null,
			true
		);

		$control->onSuccess[] = function () {
			$this->redirect('this');
		};

		return $control;
	}

	private function resolveCurrentStateForUser(LuckyShop $currentLuckyShop, LuckyShopCampaign $luckyShopCampaign): string
	{
		$userLuckyShopCheck = $this->luckyShopFacade->findUserLuckyShopCheck($this->getUserIdentity(), $currentLuckyShop);
		$userLuckyShopsForCampaign = $this->luckyShopFacade->findUserLuckyShopsForCampaign($this->getUserIdentity(), $luckyShopCampaign);

		if ($currentLuckyShop->isUserRewardRequestAllowed() && count($userLuckyShopsForCampaign) > 0) {
			if ($userLuckyShopCheck) {
				if ($userLuckyShopCheck->hasWin()) {
					return self::STATE_WIN;
				} else {
					return self::STATE_LOSE;
				}
			} else {
				return self::STATE_LUCKY_SHOP_REVEAL;
			}
		}

		return self::STATE_NEXT_LUCKY_SHOP;
	}

	public function createComponentLuckyShopsPaginator(): Paginator
	{
		$control = $this->paginatorFactory->create()
			->disableNext()
			->enableAjax()
		;

		$control->setTheme('light');
		$control->setItemsPerPage(3);

		$control->onChange[] = function () {
			$this->redrawControl('luckyShopHistory');
		};

		return $control;
	}

	protected function createComponentPhoneNumberVerificationControl(): PhoneNumberVerificationControl
	{
		$control = $this->phoneNumberVerificationControlFactory->create(
			$this->getUserIdentity()
		);

		$control->onSuccess[] = function () {
			$this->redirect('checkLuckyShop!');
		};

		return $control;
	}

	protected function createComponentNotificationSettingsControl(): NotificationSettingsControl
	{
		$control = $this->notificationSettingsControlFactory->create($this->getUserIdentity());

		$control->onSuccess[] = function () {
			$this->redrawControl('notificationSettingsControl');
		};

		return $control;
	}

	public function actionClaimMissedReward()
	{
		$user = $this->getUserIdentity();

		$userLuckyShop = $this->luckyShopFacade->createOrUpdateUserLuckyShop($user, UserLuckyShop::SOURCE_MISSED_REWARD);

		if ($userLuckyShop) {
			$this->redirect('default', [
				'userLuckyShopPopupControl-userLuckyShopId' => $userLuckyShop->getId(),
				'do' => 'userLuckyShopPopupControl-open',
			]);
		} else {
			$this->redirect('default');
		}
	}

	public function handleVerifyRequest(): void
	{
		$data = json_decode($this->getHttpRequest()->getRawBody(), true);
		$token = $data['token'] ?? null;

		if (!$token) {
			$this->sendJson(['success' => false, 'error' => 'Missing token']);
		}

		$response = file_get_contents('https://challenges.cloudflare.com/turnstile/v0/siteverify', false, stream_context_create([
			'http' => [
				'method'  => 'POST',
				'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
				'content' => http_build_query([
					'secret'   => '0x4AAAAAABlXAjIiQjMYfADvxm0izym8XIE',
					'response' => $token,
					'remoteip' => $this->getHttpRequest()->getRemoteAddress(),
				]),
			],
		]));

		$result = json_decode($response, true);

		$logData = [
			'isBot'  => !($result['success'] ?? false),
			'timestamp' => date('Y-m-d H:i:s'),
			'ip' => $this->getHttpRequest()->getRemoteAddress(),
			'hostname' => $result['hostname'] ?? null,
			'challenge_ts' => $result['challenge_ts'] ?? null,
			'errorCodes'  => $result['error-codes'] ?? null,
		];

		Debugger::log($logData, 'lucky-shops-turnstile');

		$this->sendJson(['success' => true]);
	}
}
