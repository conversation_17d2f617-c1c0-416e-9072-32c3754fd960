{block content}
<div class="bg-primary-blue-dark min-h-screen">
    <div class="flex items-center justify-center gap-2.5 relative py-3 z-30 bg-secondary-green/10 text-white overflow-hidden">
        <div class="flex items-center gap-2 text-sm font-bold leading-[24.5px] uppercase whitespace-nowrap">
            <div class="flex items-center gap-2">
                {var $nextLuckyShopAt = $luckyShopCampaign->nextLuckyShopAfter()}
                <span>{_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop |noescape}</span>
                <div class="">
                    <div class="flex flex-col items-center">flex justify-center text-[20px] leading-[19px] text-secondary-green tracking-[5px] font-normal js-count-down
                        <span class="js-count-down-hour">{$nextLuckyShopAt->format('%H')}</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <span>:</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <span class="js-count-down-minute">{$nextLuckyShopAt->format('%I')}</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <span>:</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <span class="js-count-down-second">{$nextLuckyShopAt->format('%S')}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container relative">
        <div class="hidden lg:block absolute top-0 left-[-250px] z-20">
            <img src="{$basePath}/new-design/gives-out-bg-star.svg" alt="star">
        </div>

        <div class="hidden lg:block absolute top-[-50px] left-[-1200px] z-20">
            <img class="blur-3xl" src="{$basePath}/new-design/gives-out-bg-circle.png" alt="circle">
        </div>

        <div class="absolute right-0 sm:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="93" height="430" viewBox="0 0 93 430" fill="none">
                <g filter="url(#filter0_f_1182_3874)">
                    <path d="M244.582 57.2222C331.135 73.9053 387.765 158.082 370.989 245.118C354.213 332.153 270.352 389.25 183.799 372.567C97.2459 355.884 40.6154 271.707 57.3914 184.671C74.1675 97.6363 158.029 40.5391 244.582 57.2222Z" stroke="white" stroke-width="5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <defs>
                    <filter id="filter0_f_1182_3874" x="0.547264" y="0.42617" width="427.286" height="428.937" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                        <feGaussianBlur stdDeviation="25.7" result="effect1_foregroundBlur_1182_3874"/>
                    </filter>
                </defs>
            </svg>
        </div>


        <div class="flex flex-col lg:flex-row items-start justify-center gap-[30px] lg:gap-[110px] relative z-40 pt-5 lg:pt-[70px] original-parent-class">
            <div class="px-5 lg:px-0">
                <div class="flex items-center text-white lg:text-primary-orange text-lg lg:text-[33px] font-bold leading-[60px] text-nowrap lg:mt-0">
                    {_newFront.luckyShops.title}
                    <div class="relative mx-2 lg:ml-4 lg:mr-0">
                        <img class="hidden lg:block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-shop-bg-star.svg" alt="donkey">
                        <img class="lg:hidden absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
                        <div class="relative text-center font-bold text-white">
                            <span class="text-[26px] lg:text-[33px] text-white text-nowrap leading-[67px] rounded-[13px] bg-orange-gradient px-2 py-1 lg:px-[17px]">
                                {_newFront.luckyShops.amount}
                            </span>
                        </div>
                    </div>
                    <span class="lg:hidden">{_newFront.luckyShops.everyDay}!</span>
                </div>
                <div class="text-white text-lg lg:text-[33px] font-bold text-nowrap mb-2.5 hidden lg:block">{_newFront.luckyShops.everyDay}</div>

                <div class="w-full max-w-[371px] text-white text-sm lg:text-base leading-[24.5px] lg:leading-[28px]">
                    {_newFront.luckyShops.text}
                </div>
            </div>

            <div class="flex flex-col w-full">
                <div class="flex flex-col lg:hidden items-center my-10 order-1 lg:order-2 last-lucky-shop">
                    {if $currentLuckyShop && $currentLuckyShop->isUserRewardRequestAllowed() && count($userLuckyShopsForCampaign) > 0}
                        {if $userLuckyShopCheck}
                            {if $userLuckyShopCheck->hasWin()}
                                <div class="text-lg text-white leading-[31.5px] font-bold relative">
                                    <img class="absolute left-[-14px] top-[-20px]" src="{$basePath}/new-design/gives-out-text-stars.svg" alt="hviezdy">
                                    {_newFront.luckyShops.default.checkLuckyShop.isLucky |noescape}
                                </div>

                                <div class="flex items-center justify-center bg-white w-[115px] min-h-[53px] rounded-xl my-5">
                                    <img class="max-w-[70px] max-h-[40px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="{$currentLuckyShop->getShop()->getName()}">
                                </div>

                                <div class="text-white text-center">
                                    <div class="text-lg leading-[31.5px] font-bold mb-2.5">{_newFront.luckyShops.default.checkLuckyShop.currentWinAmountPerUser}
                                        <span class="text-[22px] text-white text-nowrap rounded-[13px] bg-orange-gradient px-2 py-1 ">{$rewardAmountPerUser |amount} {$localization |currency}</span>
                                    </div>
                                    <div class="text-xs leading-[21px]">
                                        {_newFront.luckyShops.default.checkLuckyShop.totalRewardText}
                                    </div>
                                </div>

                                <div class="flex justify-between items-center text-xs text-white leading-[21px] w-full max-w-[372px]">
                                    <div>{_newFront.luckyShops.default.checkLuckyShop.winners} <span class="text-primary-orange">{$countOfUsersWithLuckyShop}x</span></div>
                                    <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest} <span class="text-primary-orange">{$getCountOfUsersChecksWithLuckyShop($currentLuckyShop)}x</span></div>
                                </div>
                            {else}
                                <div class="text-lg text-center text-white leading-[31.5px] font-bold" n:if="$currentLuckyShop">
                                    {_newFront.luckyShops.default.checkLuckyShop.isNotLucky |noescape} {$currentLuckyShop->getShop()->getName()}
                                </div>

                                <div class="flex items-center justify-center bg-white w-[115px] min-h-[53px] rounded-xl my-5">
                                    <img class="w-[70px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="{$currentLuckyShop->getShop()->getName()}">
                                </div>

                                <div class="flex items-center text-[20px] gap-1 leading-[35px] text-white font-bold">
                                    {_newFront.luckyShops.default.checkLuckyShop.tryAgain}

                                    <span class="relative inline-block">
                                    <img style="transform: rotate(3.457deg);" class="relative top-[2px]" src="{$basePath}/new-design/gives-out-text-bg.svg" alt="text">
                                    <span class="absolute inset-0 flex items-center justify-center">{_newFront.luckyShops.default.checkLuckyShop.tomorrow}</span>
                                    </span>😎
                                </div>
                            {/if}
                        {else}
                            <div class="bg-[#30415D] px-3 pt-[25px] pb-[21px] rounded-2xl w-full relative mt-[33px]">
                                <div class="absolute top-[-90px] left-1/2 transform -translate-x-1/2">
                                    <img src="{$basePath}/new-design/tipli-rozdava-mb-donkey.png" alt="oslik">
                                </div>
                                <div class="text-lg text-white text-center leading-[31.5px] font-bold mb-[21px]">
                                    {_newFront.luckyShops.default.checkLuckyShop.title}
                                </div>

                                <div class="relative w-full text-center mb-2.5">
                                    <a n:href="checkLuckyShop!"
                                            class="block w-full m-auto text-white font-bold leading-7 bg-orange-gradient py-5 w-full max-w-[372px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10" style="box-shadow: 6px 6px 13px 0 rgba(239, 127, 26, 0.51);">
                                        {_newFront.luckyShops.default.checkLuckyShop.ctaCheckLuckyShop}
                                    </a>
                                </div>

                                <div class="flex justify-between items-center text-xs text-white leading-[21px] w-full max-w-[372px]">
                                    <div>{_newFront.luckyShops.default.checkLuckyShop.winners} <span class="text-primary-orange">{$countOfUsersWithLuckyShop}x</span></div>
                                    <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest} <span class="text-primary-orange">{$getCountOfUsersChecksWithLuckyShop($currentLuckyShop)}x</span></div>
                                </div>
                            </div>
                        {/if}
                    {else}
                        <div n:if="$currentLuckyShop" class="lg:hidden flex m-auto text-white text-xs leading-[24px] z-10 w-full max-w-[294px]">
                            <div class="mb-2.5">{_newFront.luckyShops.default.currentLuckyShop}
                                <span class="font-bold">
                                            {$currentLuckyShop->getShop()->getName()}
                                        </span>
                            </div>
                            <a n:href=":NewFront:Shops:Shop:default $currentLuckyShop->getShop()" target='_blank' class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-xl m-auto">
                                <img class="max-w-[70px] max-h-[40px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="obchod">
                            </a>
                        </div>
                        <div class="lg:hidden h-px bg-white/10 w-full my-[30px]"></div>
                    {/if}
                </div>

                <div class="relative w-full sm:w-auto order-2 lg:order-1">
                    <div
                            class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur text-white w-full lg:max-w-[455px] lg:ml-auto rounded-2xl pb-[18px] lg:pb-5 pt-5 lg:pt-[25px] shadow-custom relative px-5 lg:px-[30px]">
                        <div class="text-sm font-medium lg:text-lg lg:font-bold leading-[24.5px] lg:leading-[31.5px] text-white mb-5">
                            {_newFront.luckyShops.detail.userLuckyShops}
                        </div>

                        <div class="grid grid-cols-4 gap-[18px] lg:gap-[35px]">
                            {foreach $userLuckyShops as $userLuckyShop}
                                {var $shop = $userLuckyShop->getShop()}
                                {if $userLuckyShop->isValid()}
                                    {if $shop}
                                        {if $shop->isCashbackAllowed()}
                                            <a n:href="userLuckyShopPopupControl-open! userLuckyShopPopupControl-userLuckyShopId: $userLuckyShop->getId()" class="ajax w-[60px] h-[60px] lg:w-[72px] relative lg:h-[72px] bg-white rounded-full flex items-center justify-center border-2 border-primary-orange hover:cursor-pointer">
                                                <img src="{$shop->getCurrentLogo() |image:40,40}" class="max-w-[45px] flex-shrink-0" alt="{$shop->getName()}">

                                                {if $userLuckyShop->isDefault()}
                                                    <svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                                                        <rect width="30" height="30" rx="15" fill="#EF7F1A"/>
                                                        <path d="M17 18.1451C17.6348 18.6404 18.4247 18.9435 19.2637 19C22.1356 19 23.9306 16.0833 22.4946 13.75C21.8282 12.6671 20.5965 12 19.2637 12C16.5989 12 15 15.5 15 15.5C15 15.5 13.4011 19 10.7363 19C7.86439 19 6.06943 16.0833 7.50538 13.75C8.17184 12.6671 9.40346 12 10.7363 12C11.5755 12.0574 12.3653 12.3612 13 12.857" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                {else}
                                                    <div class="absolute bottom-[-12px] right-[-6px]">
                                                        <div class="relative w-[30px] h-[30px]">
                                                            <svg class="absolute inset-0 w-full h-full" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <g filter="url(#filter0_b_1210_4324)">
                                                                    <rect width="30" height="30" rx="15" fill="#66B940" fill-opacity="0.3"/>
                                                                </g>
                                                                <defs>
                                                                    <filter id="filter0_b_1210_4324" x="-10.9" y="-10.9" width="51.8" height="51.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                                                        <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.45"/>
                                                                        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1210_4324"/>
                                                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1210_4324" result="shape"/>
                                                                    </filter>
                                                                </defs>
                                                            </svg>
                                                            <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                                                                {if $userLuckyShop->isStreakSource()}
                                                                    🔥
                                                                {else}
                                                                    {$userLuckyShop->getValidTillDays()}d
                                                                {/if}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {/if}
                                            </a>
                                        {else}
                                            <a n:href="delistedUserLuckyShopModal! $userLuckyShop->getSource()" id="delistedOpenModalBtn" class="w-[60px] h-[60px] lg:w-[72px] bg-secondary-red/30 backdrop-blur-[1px] relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-secondary-red hover:cursor-pointer">
                                                <img class="max-w-[45px] flex-shrink-0 blur-[2px]" src="{$shop->getCurrentLogo() |image:40,40}" alt="{$shop->getName()}">
                                                <svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                                                    <rect width="30" height="30" rx="15" fill="#F72F49"/>
                                                    <path d="M11 20L20 11M20 20L11 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                                                </svg>
                                            </a>
                                        {/if}
                                    {else}
                                        <a n:href="userLuckyShopPopupControl-open! userLuckyShopPopupControl-userLuckyShopId: $userLuckyShop->getId()" class="ajax relative">
                                            <img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px] hover:cursor-pointer" src="{$basePath}/new-design/gives-out-add-lucky-shop.svg" alt="obchod">
                                            <div class="absolute bottom-[-12px] right-[-6px]">
                                                <div class="relative w-[30px] h-[30px]">
                                                    <svg class="absolute inset-0 w-full h-full" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g filter="url(#filter0_b_1210_4324)">
                                                            <rect width="30" height="30" rx="15" fill="#66B940" fill-opacity="0.3"/>
                                                        </g>
                                                        <defs>
                                                            <filter id="filter0_b_1210_4324" x="-10.9" y="-10.9" width="51.8" height="51.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                                                <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.45"/>
                                                                <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1210_4324"/>
                                                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1210_4324" result="shape"/>
                                                            </filter>
                                                        </defs>
                                                    </svg>
                                                    <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                                                        {if $userLuckyShop->isStreakSource()}
                                                            🔥
                                                        {else}
                                                            {$userLuckyShop->getValidTillDays()}d
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    {/if}
                                {/if}
                            {/foreach}

							{for $i = 0; $i < (4 - count($userLuckyShops)); $i++}
                                {if $i === 0}
                                    <div id="moreSlotsOpenModalBtn" class="relative">
                                        <img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px]" src="/new-design/gives-out-add-lucky-shop.svg" alt="obchod">
                                    </div>
                                {else}
                                    <div>
                                        <img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px]" src="{$basePath}/new-design/gives-out-empty-lucky-shop.svg" alt="obchod">
                                    </div>
                                {/if}
                            {/for}

										{*
										<div id="addOpenModalBtn" class="relative">
											<img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px]" src="{$basePath}/new-design/gives-out-add-lucky-shop.svg" alt="obchod">
											<div class="absolute bottom-[-12px] right-[-6px]">
												<div class="relative w-[30px] h-[30px]">
													<svg class="absolute inset-0 w-full h-full" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
														<g filter="url(#filter0_b_1210_4324)">
															<rect width="30" height="30" rx="15" fill="#66B940" fill-opacity="0.3"/>
														</g>
														<defs>
															<filter id="filter0_b_1210_4324" x="-10.9" y="-10.9" width="51.8" height="51.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
																<feFlood flood-opacity="0" result="BackgroundImageFix"/>
																<feGaussianBlur in="BackgroundImageFix" stdDeviation="5.45"/>
																<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1210_4324"/>
																<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1210_4324" result="shape"/>
															</filter>
														</defs>
													</svg>
													<div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
														7d
													</div>
												</div>
											</div>
										</div>
										<div id="delistedOpenModalBtn" class="w-[60px] h-[60px] lg:w-[72px] bg-secondary-red/30 backdrop-blur-[1px] relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-secondary-red hover:cursor-pointer">
											<img class="max-w-[45px] flex-shrink-0 blur-[2px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="obchod">
											<svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
												<rect width="30" height="30" rx="15" fill="#F72F49"/>
												<path d="M11 20L20 11M20 20L11 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
											</svg>
										</div>
										<div id="moreSlotsOpenModalBtn">
											<img class="w-[60px] h-[60px] lg:w-[72px] lg:h-[72px] hover:cursor-pointer" src="{$basePath}/new-design/gives-out-empty-lucky-shop.svg" alt="obchod">
										</div>

										<div id="expiredOpenModalBtn" class="w-[60px] h-[60px] lg:w-[72px] relative lg:h-[72px] bg-white rounded-full flex items-center justify-center border-2 border-light-1 hover:cursor-pointer">
											<img class="max-w-[45px] flex-shrink-0" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="obchod">
											<div class="absolute bottom-[-12px] right-[-6px]">
												<div class="relative w-[30px] h-[30px]">
													<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
														<rect width="30" height="30" rx="15" fill="#F72F49"/>
													</svg>
													<div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
														Od
													</div>
												</div>
											</div>
										</div>
										<div id="expiredOpenModalBtn" class="w-[60px] h-[60px] lg:w-[72px] relative lg:h-[72px] bg-white rounded-full flex items-center justify-center border-2 border-light-1 hover:cursor-pointer">
											<img class="max-w-[45px] flex-shrink-0" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="obchod">
											<div class="absolute bottom-[-12px] right-[-6px]">
												<div class="relative w-[30px] h-[30px]">
													<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
														<rect width="30" height="30" rx="15" fill="#F72F49"/>
													</svg>
													<div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
														Od
													</div>
												</div>
											</div>
										</div>
										<div id="delistedOpenModalBtn" class="w-[60px] h-[60px] lg:w-[72px] bg-secondary-red/30 backdrop-blur-[1px] relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-secondary-red hover:cursor-pointer">
											<img class="max-w-[45px] flex-shrink-0 blur-[2px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="obchod">
											<svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
												<rect width="30" height="30" rx="15" fill="#F72F49"/>
												<path d="M11 20L20 11M20 20L11 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
											</svg>
										</div>
										*}
                        </div>
                        <div class="h-px bg-white/10 w-full mb-5 mt-[27px]"></div>

                        <div class="flex items-center justify-between">
                            <div class="text-sm leading-[24.5px] text-white">
                                {_newFront.luckyShops.detail.checkLuckyShops}
                            </div>
                            <div class="text-sm font-bold leading-[24.5px]">
                                🔥 {_newFront.luckyShops.detail.checkLuckyShopsStreak, ['count' => $userIdentity->getLuckyShopCheckStreak()] |noescape}
                            </div>
                        </div>

                        {if $userIdentity->isAdmin()}
                            <a n:href="processLuckyShop!" style="color: #ded4d4; font-weight: bold" class="mt-2">[Next Lucky Shop (shopId 1)]</a><br>
                            <a n:href="resetTestData!" style="color: #ded4d4; font-weight: bold" class="mt-2">[Reset data]</a><br>
                        {/if}
                    </div>
                </div>
            </div>
        </div>

        {control userLuckyShopPopupControl}

        <!-- DELISTED Modal -->
        {ifset $delistedUserLuckyShopModal}
            <div id="delistedModalOverlay" class="fixed inset-0 px-5 backdrop-blur-sm bg-primary-blue-dark/90 flex flex-col gap-10 items-center justify-center z-50">
                <div class="bg-white p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
                    <div id="delistedCloseModalBtn" class="absolute right-[-20px] top-[-16px] hover:cursor-pointer">
                        <img src="{$basePath}/new-design/gives-out-close-modal-icon.svg" alt="zavriet">
                    </div>
                    <div class="text-lg font-bold leading-[31.5px]">{_newFront.luckyShops.default.delistedShop.title}</div>
                    <div class="h-px bg-black/10 w-full my-5"></div>
                    <div class="flex items-center gap-5 mb-5">
                        <div class="w-[60px] h-[60px] lg:w-[72px] bg-white relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-secondary-red hover:cursor-pointer">
                            <img class="max-w-[45px] flex-shrink-0" src="{$delistedShop->getCurrentLogo() |image:200,0}" alt="{$delistedShop->getName()}">
                            <svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                                <rect width="30" height="30" rx="15" fill="#F72F49"/>
                                <path d="M11 20L20 11M20 20L11 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div>
                            <div class="text-dark-1 leading-6 font-bold">
                                {$delistedShop->getName()}
                            </div>
                            <a href="#" class="text-sm underline leading-[24.5px] text-dark-1 hover:cursor-pointer">
                                {_newFront.luckyShops.default.delistedShop.changeShop}
                            </a>
                        </div>
                    </div>
                    <div
                            class="text-xs leading-[21px] text-center bg-pastel-red-light py-[15px] px-[19px] rounded border border-secondary-red">
                        <div class="font-bold">{_newFront.luckyShops.default.delistedShop.message.title, ['shop' => $delistedShop->getName()]}</div>
                        <div>
                            {_newFront.luckyShops.default.delistedShop.message.text}
                        </div>
                    </div>
                </div>
            </div>
        {/ifset}

        <!-- ADD MODAL -->
        <div id="addModalOverlay" class="fixed hidden inset-0 px-5 backdrop-blur-sm bg-primary-blue-dark/90 flex flex-col gap-10 items-center justify-center z-50">
            <div class="bg-white p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
                <div id="addCloseModalBtn" class="absolute right-[-20px] top-[-16px] hover:cursor-pointer">
                    <img src="{$basePath}/new-design/gives-out-close-modal-icon.svg" alt="zavriet">
                </div>
                <div class="text-lg font-bold leading-[31.5px]">Vyber si svoj šťastný obchod</div>

                <div class="mt-5 -mx-5 -mb-10 rounded-full" style="box-shadow: 5px 0 15.4px 0 rgba(0, 0, 0, 0.13);"></div>
            </div>

            <div class="text-center">

            </div>
        </div>

        <!-- EXPIRED MODAL -->
        <div id="expiredModalOverlay" class="fixed hidden inset-0 px-5 backdrop-blur-sm bg-primary-blue-dark/90 flex flex-col gap-5 items-center justify-center z-50">
            <div class="bg-light-6 p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
                <div id="expiredCloseModalBtn" class="absolute right-[-20px] top-[-16px] hover:cursor-pointer">
                    <img src="{$basePath}/new-design/gives-out-close-modal-icon.svg" alt="zavriet">
                </div>
                <div class="text-lg font-bold leading-[31.5px]">Delistovaný obchod</div>
                <div class="h-px bg-black/10 w-full my-5"></div>
                <div class="flex items-center gap-5 mb-5">
                    <div class="w-[60px] h-[60px] lg:w-[72px] bg-white relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-light-2 hover:cursor-pointer">
                        <img class="max-w-[45px] flex-shrink-0" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="obchod">

                        <div class="absolute bottom-[-12px] right-[-6px]">
                            <div class="relative w-[30px] h-[30px]">
                                <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="30" height="30" rx="15" fill="#F72F49"/>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                                    Od
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="text-dark-1 leading-6 font-bold">CCC</div>
                        <div class="text-sm underline leading-[24.5px] text-dark-1 hover:cursor-pointer">Neaktívne</div>
                    </div>
                </div>
            </div>

            <div class="bg-white p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
                <div class="text-lg font-bold leading-[31.5px]">Ako predĺžiť šťastný slot?</div>
                <div class="h-px bg-black/10 w-full my-5"></div>
                <ol class="text-dark-1 text-sm font-medium list-decimal pl-[19px] max-h-[350px] md:max-h-[500px] overflow-auto">
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 60 dní. Ak však pozvaný kamarát cez Tipli
                            aj nakúpi, získate až 5 €
                            .</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">Pozvaním priateľa do Tipli</div>
                        <div class="text-xs leading-[21px]">Získate okienko s platnosťou 30 dní. Platí pre každý nákup.</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            Chcem pozvať priateľa
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                </ol>
            </div>
        </div>

        <!-- MORE SLOTS MODAL -->
        <div id="moreSlotsModalOverlay" class="fixed hidden inset-0 px-5 backdrop-blur-sm bg-primary-blue-dark/90 flex flex-col gap-5 items-center justify-center z-50">
            <div class="bg-white p-5 rounded-2xl shadow-lg max-w-lg w-full mx-5 relative">
                <div id="moreSlotsCloseModalBtn" class="absolute right-[-20px] top-[-16px] hover:cursor-pointer">
                    <img src="{$basePath}/new-design/gives-out-close-modal-icon.svg" alt="zavriet">
                </div>
                <div class="text-lg font-bold leading-[31.5px] mb-[5px]">
                    {_newFront.luckyShops.default.moreSlotsPopup.title}
                </div>
                <div class="text-xs leading-[21px]">
                    {_newFront.luckyShops.default.moreSlotsPopup.text |noescape}
                </div>
                <div class="h-px bg-black/10 w-full my-5"></div>
                <ol class="text-dark-1 text-sm font-medium list-decimal pl-[19px] max-h-[350px] md:max-h-[500px] overflow-auto">
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step2.title}</div>
                        <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step2.text}</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step2.link |noescape}
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step3.title}</div>
                        <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step3.text}</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step3.link |noescape}
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>

                            <span n:if="in_array('addon', $userLuckyShopSources)" class='bg-green-500 px-2 py-1 text-white rounded-md'>OK</span>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step4.title}</div>
                        <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step4.text}</div>

                        <span n:if="in_array('streak', $userLuckyShopSources)" class='bg-green-500 px-2 py-1 text-white rounded-md'>OK</span>

                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                    <li>
                        <div class="text-sm font-medium leading-[24.5px] mb-[5px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step1.title}</div>
                        <div class="text-xs leading-[21px]">{_newFront.luckyShops.default.moreSlotsPopup.steps.step1.text}</div>
                        <div class="flex items-center gap-2.5 text-xs leading-[21px] text-primary-orange mt-2.5 mb-5 font-bold">
                            {_newFront.luckyShops.default.moreSlotsPopup.steps.step1.link |noescape}
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                                <path d="M1 9L9 1M8.99868 7.77928L8.99868 1.00026L2.21965 1.00025" stroke="#EF7F1A" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="h-px bg-black/10 w-full my-5"></div>
                    </li>
                </ol>
            </div>
        </div>

        {if $currentLuckyShop && $currentLuckyShop->isUserRewardRequestAllowed() && count($userLuckyShopsForCampaign) > 0}
            {if $userLuckyShopCheck}
                {if $userLuckyShopCheck->hasWin()}
                    <div class="hidden lg:flex justify-between items-center mt-[80px] mb-[50px] text-center py-[25px] px-[30px] border border-secondary-green rounded-2xl relative">
                        <div class="relative text-lg font-bold leading-[31.5px] text-nowrap">
                            <div class="absolute inset-0 bg-secondary-green/20 backdrop-blur-2xl" style="background: rgba(102, 185, 64, 0.40); filter: blur(46.7px); z-index: 1; border-radius: inherit;"></div>
                            <div class="relative z-10">
                                <div class="text-secondary-green">
                                    {_newFront.luckyShops.detail.win.title}
                                </div>
                                <div class="text-lg text-white mb-[15px]">
                                    {_newFront.luckyShops.detail.win.text}
                                </div>
                                <div class="flex items-center justify-center bg-white max-w-[115px] min-h-[57px] rounded-xl m-auto">
                                    <img class="max-w-[60px] max-h-[35px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="{$currentLuckyShop->getShop()->getName()}">
                                </div>
                            </div>
                        </div>

                        <div class="text-white">
                            {_newFront.luckyShops.default.checkLuckyShop.currentWinAmountPerUser} <span class='text-primary-orange'>{$rewardAmountPerUser |amount} {$localization |currency}</span>
                        </div>

                        <div class="flex flex-col items-end text-white text-nowrap z-10">
                            <div>{_newFront.luckyShops.default.checkLuckyShop.winners} <span class="text-primary-orange">{$countOfUsersWithLuckyShop}x</span></div>
                            <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest} <span class="text-primary-orange">{$getCountOfUsersChecksWithLuckyShop($currentLuckyShop)}x</span></div>
                        </div>
                    </div>
                {else}
                    <div class="hidden lg:flex justify-between items-center mt-[80px] mb-[50px] text-center py-[25px] px-[30px] border border-primary-orange rounded-2xl relative">
                        <div class="relative text-lg font-bold leading-[31.5px] text-nowrap">
                            <div class="absolute inset-0 bg-secondary-green/20 backdrop-blur-2xl" style="background: rgba(247, 47, 73, 0.20); filter: blur(46.7px); z-index: 1; border-radius: inherit;"></div>
                            <div class="flex items-center relative z-10 gap-[15px]">
                                <div class="text-lg font-bold leading-[31.5px] text-white">
                                    {_newFront.luckyShops.default.checkLuckyShop.isNotLucky |noescape}
                                </div>
                                <a n:href=":NewFront:Shops:Shop:default $currentLuckyShop->getShop()" target='_blank' class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-xl m-auto">
                                    <img class="max-w-[70px] max-h-[40px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="{$currentLuckyShop->getShop()->getName()}">
                                </a>
                            </div>
                        </div>
                        <div
                                class="inline-block h-[91px] min-h-[1em] w-px bg-white/20"></div>
                        <div class="flex items-center text-[20px] gap-1 leading-[35px] text-white font-bold">
                            {_newFront.luckyShops.default.checkLuckyShop.tryAgain}
                            <span class="relative inline-block">
                              <img style="transform: rotate(3.457deg);" class="relative top-[2px]" src="/new-design/gives-out-text-bg.svg" alt="text">
                              <span class="absolute inset-0 flex items-center justify-center">
                                {_newFront.luckyShops.default.checkLuckyShop.tomorrow}
                              </span>
                            </span>😎
                        </div>
                    </div>
                {/if}
            {else}
                <div class="hidden lg:flex justify-between items-center mt-[80px] mb-[50px] text-center py-[25px] px-[30px] border border-primary-orange rounded-2xl relative">
                    <div class="absolute top-[-84%] left-[52%] transform -translate-x-1/2 w-[372px] h-[100px] z-20" style="background: linear-gradient(90deg, rgba(24, 43, 74, 0.00) 0%, #182B4A 28.5%, #182B4A 73%, rgba(24, 43, 74, 0.00) 100%);"></div>
                    <div class="text-lg font-medium leading-[31.5px] text-white z-10 text-nowrap">
                        {_newFront.luckyShops.default.checkLuckyShop.title}
                    </div>
                    <div class="relative w-full">
                        <a n:href="checkLuckyShop!" class="inline-block text-white font-bold leading-7 bg-orange-gradient py-5 w-full max-w-[372px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10" style="box-shadow: 6px 6px 13px 0 rgba(239, 127, 26, 0.51);">
                            {_newFront.luckyShops.default.checkLuckyShop.ctaCheckLuckyShop}
                        </a>
                        <a n:href="checkLuckyShop!">
                            <img class="absolute top-[-49%] left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30" src="{$basePath}/new-design/gives-out-donkey-with-stars.png" alt="oslik">
                        </a>
                    </div>
                    <div class="flex flex-col items-end text-white text-nowrap z-10">
                        <div>{_newFront.luckyShops.default.checkLuckyShop.winners} <span class="text-primary-orange">{$countOfUsersWithLuckyShop}x</span></div>
                        <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest} <span class="text-primary-orange">{$getCountOfUsersChecksWithLuckyShop($currentLuckyShop)}x</span></div>
                    </div>

                    <div class="absolute left-[-160px] top-[-130px] z-40">
                        <div class="relative w-[147px] h-[146px]">
                            <img src="{$basePath}/new-design/gives-out-sticker.png" alt="nalepka">
                            <div class="absolute top-[45%] left-[45%] -translate-x-1/2 -translate-y-1/2 text-center font-bold text-white">
                                {_newFront.luckyShops.default.checkLuckyShop.ctaCheckLuckyShop}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        {else}
            <div class="hidden countdown w-full">
                <div
                        class="lg:flex justify-between items-center mt-[30px] lg:mt-[60px] mb-[30px] lg:mb-[50px] text-center py-[25px] px-[30px] border border-secondary-green rounded-2xl relative bg-secondary-green/10 lg:bg-transparent w-full">
                    <div class="relative text-lg font-bold leading-[31.5px] text-nowrap">
                        <div class="hidden lg:block absolute inset-0 bg-secondary-green/20 backdrop-blur-2xl" style="background:
				rgba(102, 185, 64, 0.40); filter: blur(46.7px); z-index: 1; border-radius: inherit;"></div>
                        <div class="relative z-10">
                            {var $nextLuckyShopAt = $luckyShopCampaign->nextLuckyShopAfter()}
                            <div class="hidden lg:block font-bold leading-[24px] text-white mb-2.5">
                                {_newFront.luckyShops.default.checkLuckyShop.nextLuckyShop |noescape}
                            </div>
                            <div class="flex justify-center text-[32px] leading-[41px] text-secondary-green tracking-[5px] font-normal js-count-down">
                                <div class="flex flex-col items-center">
                                    <span class="js-count-down-hour">{$nextLuckyShopAt->format('%H')}</span>
                                    <span class="text-xs tracking-normal font-light text-white/40"> {_newFront.luckyShops.default.nextLuckyShop.hours}</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <span>:</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <span class="js-count-down-minute">{$nextLuckyShopAt->format('%I')}</span>
                                    <span class="text-xs tracking-normal font-light text-white/40"> {_newFront.luckyShops.default.nextLuckyShop.minutes}</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <span>:</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <span class="js-count-down-second">{$nextLuckyShopAt->format('%S')}</span>
                                    <span class="text-xs tracking-normal font-light text-white/40"> {_newFront.luckyShops.default.nextLuckyShop.seconds}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="lg:hidden bg-secondary-green text-xs text-white absolute leading-[21px] font-medium top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-[14px] rounded-full py-[2px] text-nowrap">
                        {_newFront.luckyShops.default.nextLuckyShop.next |noescape}
                    </div>

                    <div class="hidden lg:block relative w-full w-full max-w-[314px]">
                        <div class="text-lg text-white leading-[31.5px] mb-[9px]">
                            {_newFront.luckyShops.default.nextLuckyShop.text |noescape}
                        </div>
                        <div class="flex items-center justify-center text-[20px] gap-1 leading-[35px] text-primary-orange font-bold">
                            {_newFront.luckyShops.default.nextLuckyShop.check}
                            <span class="relative inline-block">
                          <img class="relative top-[2px]" src="/new-design/gives-out-text-bg.svg" alt="text">
                          <span class="text-white absolute inset-0 flex items-center justify-center">
                            {_newFront.luckyShops.default.nextLuckyShop.check2}
                          </span>
                        </span>😎
                        </div>
                    </div>

                    <div class="hidden lg:flex inline-block h-[91px] min-h-[1em] w-px bg-white/20"></div>

                    <div n:if="$currentLuckyShop" class="hidden lg:flex flex-col items-end text-white text-sm leading-[24px] z-10 w-full max-w-[189px]">
                        <div class="mb-2.5">
                            {_newFront.luckyShops.default.currentLuckyShop}
                            <span class="font-bold">{$currentLuckyShop->getShop()->getName()}</span></div>
                        <a n:href=":NewFront:Shops:Shop:default $currentLuckyShop->getShop()" target='_blank' class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-xl m-auto">
                            <img class="max-w-[70px] max-h-[40px]" src="{$currentLuckyShop->getShop()->getCurrentLogo() |image:200,0}" alt="obchod">
                        </a>
                    </div>
                </div>

                <div class="lg:hidden relative text-center ">
                    <div class="text-lg text-white leading-[31.5px] mb-[9px]">
                        {_newFront.luckyShops.default.nextLuckyShop.text |noescape}
                    </div>
                    <div class="flex items-center justify-center text-[20px] gap-1 leading-[35px] text-primary-orange font-bold">
                        {_newFront.luckyShops.default.nextLuckyShop.check}
                        <span class="relative inline-block">
                          <img class="relative top-[2px]" src="/new-design/gives-out-text-bg.svg" alt="text">
                          <span class="text-white absolute inset-0 flex items-center justify-center">
                            {_newFront.luckyShops.default.nextLuckyShop.check2}
                          </span>
                        </span>😎
                    </div>
                </div>
            </div>
            <div class="is-win-content">
                <div
                        class="py-[6px] absolute w-[116px] md:w-[250px] top-0 left-1/2 -translate-x-[152%] md:-translate-x-[125%] translate-y-[-155px] md:translate-y-[-135px] text-end text-xs md:text-sm text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-[24.5px] font-bold bg-secondary-green/10 border border-secondary-green">
                    Ďalší šťastný obchod vyberáme za:

                    <div
                            class="flex justify-end text-lg md:text-[32px] md:leading-[41px] text-secondary-green tracking-[2px] md:tracking-[5px] font-normal js-count-down">
                        <div class="flex flex-col items-center">
                            <span class="js-count-down-hour">00</span>
                            <span class="hidden md:block text-xs tracking-normal font-light text-white/40"> hodin</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <span>:</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <span class="js-count-down-minute">08</span>
                            <span class="hidden md:block text-xs tracking-normal font-light text-white/40"> minut
							</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <span>:</span>
                        </div>
                        <div class="flex flex-col items-center">
                            <span class="js-count-down-second">26</span>
                            <span class="hidden md:block text-xs tracking-normal font-light text-white/40"> sekund
							</span>
                        </div>
                    </div>
                </div>

                <div class="absolute w-[124px] md:w-[220px] top-0 left-1/2 translate-x-[59%] md:translate-x-[31%] translate-y-[-105px] md:translate-y-[-105px] text-xs md:text-sm text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 bg-secondary-green/10 border border-secondary-green">
                    Šťastný obchod, ktorý bol naposledy vybraný, je: <span class="font-bold">Notino</span>
                </div>
            </div>
        {/if}

        <div class="relative z-20 mt-10">
            <div class="flex flex-col md:flex-row gap-10">
                <div class="flex flex-col w-full">
                    {*
                    <div class="bg-pastel-orange-light pt-5 pb-2.5 lg:py-[28px] mb-[30px] rounded-2xl text-center px-2.5">
                        <div class="text-lg text-dark-1 font-bold leading-[31.5px] mb-1">Tipli koleso štastia 🎡</div>
                        <div class="text-xs text-dark-1 leading-[21px] mb-5">Medzitým si môžete zatočiť Tipli koleso štastia</div>
                        <button class="text-white font-bold leading-7 bg-orange-gradient py-[14px] w-full max-w-[372px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10" style="box-shadow: 6px 6px 13px 0 rgba(239, 127, 26, 0.51);">
                            Prejisť do kolesa šťastia
                        </button>
                    </div>
                    *}

                    <div class="flex gap-[15px] mb-4 pb-1 text-nowrap overflow-x-auto">
                        <div id="tab-1"
                             class="item-tab flex items-center gap-[9px] py-2 px-[14px] text-xs lg:text-sm text-white leading-[21.5px] lg:leading-[24.5px] border border-[#46556E] rounded-lg hover:cursor-pointer hover:bg-[#30415D]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M6.01419 6.38579C6.01419 5.97891 6.13557 5.58117 6.36288 5.24287C6.59019 4.90456 6.91338 4.64088 7.29142 4.48518C7.66946 4.32948 8.08547 4.28873 8.48679 4.36811C8.88811 4.44749 9.25677 4.64343 9.54609 4.93113C9.83553 5.21883 10.0325 5.5854 10.1124 5.98445C10.1922 6.3835 10.1512 6.79714 9.99464 7.17306C9.83806 7.54896 9.5728 7.87025 9.23261 8.0963C8.89241 8.32235 8.4923 8.44299 8.08315 8.44299M6.06208 1.27692C4.76145 1.65525 3.59886 2.39971 2.71434 3.42047C1.82982 4.44123 1.26132 5.69466 1.07727 7.02957C0.643464 10.1777 2.00039 13.5564 6.06704 14.7463C7.85142 15.237 9.7584 15.0102 11.3762 14.1151C12.994 13.22 14.193 11.7283 14.7145 9.96198C15.2359 8.19559 15.0379 6.29618 14.1632 4.67366C12.5833 1.74261 9.18276 0.369116 6.06208 1.27692ZM8.08315 11.7346C7.85462 11.7346 7.66936 11.5504 7.66936 11.3231C7.66936 11.0959 7.85462 10.9116 8.08315 10.9116C8.31167 10.9116 8.49694 11.0959 8.49694 11.3231C8.49694 11.5504 8.31167 11.7346 8.08315 11.7346Z" stroke="white" stroke-linecap="round"/>
                            </svg>
                            {_newFront.luckyShops.default.tabs.howItWorks.title}
                        </div>
                        <div id="tab-2" class="item-tab flex items-center gap-[9px] py-2 px-[14px] text-xs lg:text-sm text-white leading-[21.5px] lg:leading-[24.5px] border border-[#46556E] rounded-lg hover:cursor-pointer hover:bg-[#30415D]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            {_newFront.luckyShops.default.tabs.history.title}
                        </div>
                    </div>

                    <div id="content-1" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">
                                {_newFront.luckyShops.default.tabs.howItWorks.steps.step1.title}
                            </div>
                            <div class="text-sm leading-[24.5px] text-white">
                                {_newFront.luckyShops.default.tabs.howItWorks.steps.step1.text}
                            </div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">
                                {_newFront.luckyShops.default.tabs.howItWorks.steps.step2.title}
                            </div>
                            <div class="text-sm leading-[24.5px] text-white">
                                {_newFront.luckyShops.default.tabs.howItWorks.steps.step2.text}
                            </div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.title}</div>
                            <div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.text}</div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.title}</div>
                            <div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.text}</div>
                        </div>
                    </div>

                    <div id="content-2" class="hidden w-full flex flex-col gap-5">
                        {foreach $luckyShopsHistory as $luckyShop}
                            {var $shop = $luckyShop->getShop()}
                            {var $userLuckyShopsForLuckyShop = $getUserLuckyShopsForLuckyShop($luckyShop)}

                            <div class="bg-[#30415D] lg:bg-[#30415D]/50 backdrop-blur p-5 pb-3 rounded-2xl">
                                <div class="flex justify-between items-center text-white text-sm leading-[24.5px] font-bold uppercase">
                                    <a n:href=":NewFront:Shops:Shop:default $shop" target="_blank">
                                        {$shop->getName()}
                                        <span>{$shop |reward: true, 'common' |noescape}</span>
                                    </a>
                                    <div class="font-normal">{$luckyShop->getProcessedAt() |localDate}</div>
                                </div>
                                <div class="h-px bg-white/10 w-full mt-2.5 mb-[14px]"></div>
                                <div class="flex items-center justify-between gap-[15px] w-full">
                                    <a n:href=":NewFront:Shops:Shop:default $shop" target="_blank" class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-xl">
                                        <img class="max-w-[70px] max-h-[40px]" src="{$shop->getCurrentLogo() |image:200,0}">
                                    </a>
                                    <div class="flex flex-col items-end text-white text-nowrap z-10">
                                        <div>{_newFront.luckyShops.default.tabs.history.totalWin}: <span class="text-primary-orange">{$luckyShop->getRewardAmountPerUser() |amount} {$localization |currency}</span></div>
                                        <div>{_newFront.luckyShops.default.checkLuckyShop.winners}: <span class="text-primary-orange">{$luckyShop->getCountOfUsers()}x</span></div>
                                        <div>{_newFront.luckyShops.default.checkLuckyShop.winnersWithRewardRequest}: <span class="text-primary-orange">{$luckyShop->getCountOfUsersWithCheck()}x</span></div>
                                    </div>
                                </div>
                                {if $userLuckyShopsForLuckyShop}
                                    <div class="h-px bg-white/10 w-full mt-[14px] mb-2"></div>
                                    <div class="flex justify-between items-center">
                                        <div class="text-sm text-white leading-[21px]">{_newFront.luckyShops.default.tabs.history.userLuckyShops}:</div>
                                        <div class="flex items-center gap-2.5">
                                            <a n:href=":NewFront:Shops:Shop:default $userLuckyShop->getShop()" target="_blank" n:foreach="$userLuckyShopsForLuckyShop as $userLuckyShop"
                                                    class="flex items-center justify-center bg-white rounded-full w-12 h-12 border-2 border-primary-blue-dark">
                                                <img class="min-w-[30px] max-w-[30px]" src="{$userLuckyShop->getShop()->getCurrentLogo() |image:40,40}" alt="">
                                            </a>
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        {/foreach}
                    </div>

                    <div id="content-3" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">1. Vyberte si svoj štastný obchod</div>
                            <div class="text-sm leading-[24.5px] text-white">Z celkového počtu 824 obchodov si stačí vybrať jeden, ktorý považujete za svoj štastný.</div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">2. Čakajte na výsledky</div>
                            <div class="text-sm leading-[24.5px] text-white">Každý deň náhodne vyberáme jeden “štastný obchod” zo všetkých 824 obchodov.</div>
                        </div>

                        <div class="h-px bg-white/10 w-full my-5"></div>

                        <div>
                            <div class="text-white font-bold leading-6 mb-[5px]">3. Získajte výhru</div>
                            <div class="text-sm leading-[24.5px] text-white">Ak sa náš náhodne vybraný štastný obchod zhoduje s tým, ktorý ste si vybrali vy, môžete sa prihlásiť o výhru 20 € za daný deň.</div>
                        </div>
                    </div>
                </div>

                {cache 'luckyShops-topDailyDeals-' . $localization->getId() . '-' . $isAdmin . '-' . ($user->isLoggedIn() ? 1 : 0), expire => '1 hour'}
                    {var $topDailyDeals = $getTopDailyDeals()}
                    <div class="flex flex-col gap-5 lg:max-w-[326px]">
                        <div n:foreach="$topDailyDeals as $deal" class="bg-[#30415D] lg:bg-[#30415D]/50 backdrop-blur p-5 rounded-2xl">
                            {var $id = $deal->getId()}

                            <div class="flex items-center gap-[15px]">
                                <a n:href=":NewFront:Shops:Shop:default $deal->getShop()" target='_blank' class="flex items-center justify-center bg-white min-w-[85px] h-[57px] rounded-xl">
                                    <img class="max-w-[60px] max-h-[35px]" src="{$deal->getShop()->getCurrentLogo() |image:120,0,'fit',false,$deal->getShop()->getName()}" alt="obchod">
                                </a>
                                <a href="{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}" class="ajax text-white text-sm font-bold leading-[24.5px] line-clamp-2">
                                    {$deal->getName()}
                                </a>
                            </div>

                            <div class="flex gap-2.5 mt-5">
                                <a href="{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}" class="ajax flex items-center justify-center rounded-xl bg-secondary-green/20 text-sm text-secondary-green font-bold leading-[24.5px] border border-secondary-green px-3 min-w-[85px] h-[45px]">
                                    -{$deal->getValue()}{if !($deal->getUnit() == 'percentage' && $deal->getLocalization()->isPolish())} {/if}{$deal->getUnitSymbol()}
                                </a>

                                <a href="{plink "this!#deal-$id", openDeal => $deal->getFullSlug()}" class="ajax bg-white/20 block relative rounded-2xl h-[45px] w-[177px] border-t border-b border-r border-dashed border-dark-4 overflow-hidden overflow-visible cursor-pointer hover:border-primary-orange hover:border-solid" href="#">
                                    <div class="relative z-20 h-[45px] top-[-1px]">
                                        <svg width="146" height="45" viewBox="0 0 146 45" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22.501 0C12.6385 0 7.70731 0 4.38826 2.72387C3.78065 3.22253 3.2235 3.77967 2.72485 4.38728C0.000976562 7.70633 0.000976562 12.6376 0.000976562 22.5C0.000976562 32.3624 0.000976562 37.2937 2.72485 40.6127C3.2235 41.2203 3.78065 41.7775 4.38826 42.2761C7.70731 45 12.6385 45 22.501 45H145.984L136.018 0H22.501Z" fill="url(#paint0_linear_0_4)"/>
                                            <defs>
                                                <linearGradient id="paint0_linear_0_4" x1="145.984" y1="45" x2="118.284" y2="-27.9177" gradientUnits="userSpaceOnUse">
                                                    <stop stop-color="#EF7F1A"/>
                                                    <stop offset="1" stop-color="#FFA439"/>
                                                </linearGradient>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div class="absolute inset-0 flex items-center pl-6 z-30">
                                      <span class="text-sm font-bold text-white leading-[24.5px]">
                                        {_'newFront.deals.deal.getCode'}
                                      </span>
                                    </div>
                                    <svg class="absolute top-0 right-[31px] z-40" xmlns="http://www.w3.org/2000/svg" width="28" height="45" viewBox="0 0 28 45" fill="none">
                                        <path d="M2.57262 17.6954L18 0L28 45L4.26316 29.6733C0.168389 27.0294 -0.630456 21.3693 2.57262 17.6954Z" fill="#CE6D14"/>
                                    </svg>
                                    <div class="text-white text-sm absolute right-[14px] top-[11px]">
                                        {if $deal->getCode() !== null && Nette\Utils\Strings::length($deal->getCode()) > 3}
                                            {mb_substr($deal->getCode(), -3, 3)}
                                        {else}
                                            {$deal->getCode()}
                                        {/if}
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                {/cache}
            </div>
        </div>
    </div>

    <div class="overflow-x-hidden hidden lg:block relative z-10 text-[#2F67C2] ml-[-500px] mt-[-60px] md:pb-24 opacity-10 text-[160px] leading-[178px] font-bold text-nowrap">
        {_newFront.luckyShops.bottomLargeText}
    </div>
</div>

<script src="https://unpkg.com/@popperjs/core@2"></script>
<script src="https://unpkg.com/tippy.js@6"></script>
<script n:syntax="off">

    // TEST JS SMS VERIFICATION MODAL
    document.addEventListener("DOMContentLoaded", () => {
        function hideAllSteps() {
            document.getElementById("first-step").style.display = "none";
            document.getElementById("second-step").style.display = "none";
            document.getElementById("third-step").style.display = "none";
        }
        function showStep(stepId) {
            hideAllSteps();
            document.getElementById(stepId).style.display = "block";
        }
        showStep("first-step");
        // Pridaj event listener na tlačidlá
        document.querySelectorAll(".step").forEach((button, index) => {
            button.addEventListener("click", () => {
                if (index === 0) showStep("first-step");
                if (index === 1) showStep("second-step");
                if (index === 2) showStep("third-step");
            });
        });
    });

    // MOVE SLOTS
    document.addEventListener("DOMContentLoaded", () => {
        const targetDiv = document.querySelector(".countdown");
        const destinationDiv = document.querySelector(".last-lucky-shop");
        const originalParent = document.querySelector(".original-parent-class");

        if (!targetDiv || !destinationDiv || !originalParent) {
            return;
        }

        function moveCountdown() {
            if (window.matchMedia("(max-width: 1024px)").matches) {
                if (!destinationDiv.contains(targetDiv)) {
                    destinationDiv.appendChild(targetDiv);
                }
            } else {
                if (originalParent.nextElementSibling !== targetDiv) {
                    originalParent.insertAdjacentElement("afterend", targetDiv);
                }
            }
        }
        moveCountdown();

        window.addEventListener("resize", moveCountdown);
    });

    document.addEventListener('DOMContentLoaded', () => {
        const modals = [
            //{ overlay: 'modalOverlay', openBtn: 'openEditModal', closeBtn: 'closeModalBtn' },
            // { overlay: 'delistedModalOverlay', openBtn: 'delistedOpenModalBtn', closeBtn: 'delistedCloseModalBtn' },
            // { overlay: 'addModalOverlay', openBtn: 'addOpenModalBtn', closeBtn: 'addCloseModalBtn' },
            //  { overlay: 'expiredModalOverlay', openBtn: 'expiredOpenModalBtn', closeBtn: 'expiredCloseModalBtn' },
            { overlay: 'moreSlotsModalOverlay', openBtn: 'moreSlotsOpenModalBtn', closeBtn: 'moreSlotsCloseModalBtn' },
            { overlay: 'smsVerificationModalOverlay', openBtn: 'smsVerificationOpenModalBtn', closeBtn: 'smsVerificationCloseModalBtn' }
        ];

        function handleModal(modal) {
            const modalOverlay = document.getElementById(modal.overlay);
            const openModalBtn = document.getElementById(modal.openBtn);
            const closeModalBtn = document.getElementById(modal.closeBtn);

            openModalBtn.addEventListener('click', () => {
                modalOverlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            closeModalBtn.addEventListener('click', () => {
                modalOverlay.classList.add('hidden');
                document.body.style.overflow = '';
            });

            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    modalOverlay.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            });
        }

        modals.forEach(handleModal);

        // TOOLTIPS
        tippy('#popular-shop-tooltip', {
            content: `
				<div class="text-xs text-black px-[19px] py-[15px] bg-pastel-orange-light text-center border border-primary-orange rounded-md">
					<strong class="mb-[5px]">Šanca, že sa vyberie obchod je vždy rovnaká.</strong>
					<p>Čím je obchod populárnejší, tým je ale vyššia šanca, že sa o výhru prihlási viacero záujemcov.</p>
				</div>
			`,
            allowHTML: true,
            placement: 'bottom-end',
            theme: 'tooltip-shop',
        });
    })

    // TABS
    const tabs = document.querySelectorAll('.item-tab');

    document.getElementById('content-1').classList.remove('hidden');

    tabs[0].classList.add('bg-[#30415D]');
    tabs.forEach((tab, index) => {
        tab.addEventListener('click', function() {
            document.querySelectorAll('#content-1, #content-2, #content-3').forEach(content => content.classList.add('hidden'));
            document.getElementById(`content-${index + 1}`).classList.remove('hidden');

            tabs.forEach(t => t.classList.remove('bg-[#30415D]'));
            tab.classList.add('bg-[#30415D]');
        });
    });

    class CountdownTimer {
        constructor(hourElement, minuteElement, secondElement) {
            this.hourElement = hourElement;
            this.minuteElement = minuteElement;
            this.secondElement = secondElement;
            this.interval = null;
        }

        getTimeInSeconds() {
            const hours = parseInt(this.hourElement.textContent) || 0;
            const minutes = parseInt(this.minuteElement.textContent) || 0;
            const seconds = parseInt(this.secondElement.textContent) || 0;
            return hours * 3600 + minutes * 60 + seconds;
        }

        updateDisplay(totalSeconds) {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            this.hourElement.textContent = hours.toString().padStart(2, '0');
            this.minuteElement.textContent = minutes.toString().padStart(2, '0');
            this.secondElement.textContent = seconds.toString().padStart(2, '0');
        }

        start() {
            if (this.interval) return;

            let totalSeconds = this.getTimeInSeconds();

            this.interval = setInterval(() => {
                if (totalSeconds <= 0) {
                    this.stop();
                    location.reload();
                    return;
                }

                totalSeconds--;
                this.updateDisplay(totalSeconds);
            }, 1000);
        }

        stop() {
            if (this.interval) {
                clearInterval(this.interval);
                this.interval = null;
            }
        }

        reset(hours = 0, minutes = 0, seconds = 0) {
            this.stop();
            this.updateDisplay(hours * 3600 + minutes * 60 + seconds);
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        const countdownElements = document.querySelectorAll('.js-count-down');

        if (countdownElements.length === 0) {
            console.warn('Žiadne odpočítavacie elementy nenájdené.');
            return;
        }

        countdownElements.forEach((countdownElement) => {
            const hourElement = countdownElement.querySelector('.js-count-down-hour');
            const minuteElement = countdownElement.querySelector('.js-count-down-minute');
            const secondElement = countdownElement.querySelector('.js-count-down-second');

            if (!hourElement || !minuteElement || !secondElement) {
                console.warn('Některé potřebné elementy pro odpočítávání chybí');
                return;
            }

            const timer = new CountdownTimer(hourElement, minuteElement, secondElement);
            timer.start();
        });
    });
</script>

<style>
    .tippy-box[data-theme~='tooltip-shop'] {
        background-color: transparent !important;
        box-shadow: none !important;
        border: none !important;
    }

    .tippy-box[data-theme~='tooltip-shop'] .tippy-content {
        padding: 0 !important;
        background-color: transparent !important;
    }
</style>
