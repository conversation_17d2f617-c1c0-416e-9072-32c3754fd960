<?php

namespace tipli\NewFrontModule\Presenters;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use tipli\Model\Layers\ClientLayer;
use tipli\NewFrontModule\Forms\IContactControlFactory;
use tipli\FrontModule\Forms\IAccountDeletionRequestControlFactory;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Addon\FeedbackFacade;
use tipli\Model\Articles\ArticleFacade;
use tipli\Model\Conditions\ConditionsFacade;
use tipli\Model\Conditions\Entities\Document;
use tipli\Model\Marketing\BannersRecommender;
use tipli\Model\Marketing\Entities\Banner;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\PartnerOrganizations\PartnerOrganizationFacade;
use tipli\Model\Questions\QuestionFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Reviews\ReviewFacade;
use tipli\Model\Rewards\ShareRewardFacade;
use tipli\Model\Shops\ShopFacade;

class StaticPresenter extends BasePresenter
{
	/** @var IContactControlFactory @inject */
	public $contactControlFactory;

	/** @var IAccountDeletionRequestControlFactory @inject */
	public $accountDeletionRequestControlFactory;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var PartnerOrganizationFacade @inject */
	public $partnerOrganizationFacade;

	/** @var MessageFacade @inject */
	public $messageFacade;

	/** @var ReviewFacade @inject */
	public $reviewFacade;

	/** @var ArticleFacade @inject */
	public $articleFacade;

	/** @var BannersRecommender @inject */
	public $bannersRecommender;

	/** @var FeedbackFacade @inject */
	public $feedbackFacade;

	/** @var ConditionsFacade @inject */
	public $conditionsFacade;

	/** @var ShareRewardFacade @inject */
	public $shareRewardFacade;

	/** @var QuestionFacade @inject */
	public $questionFacade;

	/** @var StatisticDataProvider @autowire */
	public $statisticDataProvider;

	/** @var ClientLayer @autowire */
	public $clientLayer;

	public function renderHowItWorks()
	{
		$this->template->topShops = $this->shopFacade->findTopShops(10, $this->getLocalization());

		$this->template->activeShops = $this->shopFacade->getCountOfActiveAndVisibleShops($this->getLocalization());
		$this->template->reviews = (function () {
			return $this->reviewFacade->findTipliPrioritizedReviews($this->getLocalization())->applyPaging(0, 6);
		});

		$this->template->questionsTree = $this->questionFacade->getQuestionsTree();

		$this->template->getTopShops = (function () {
			return $this->shopFacade->findTopShops(18, $this->getLocalization(), true, [], true);
		});

		$this->template->countOfTransactions = (function () {
			return $this->statisticDataProvider->getCountOfConfirmedCommissionTransactions(
				(new \DateTime())->modify('-1 month'),
				new \DateTime(),
				$this->getLocalization()
			);
		});
	}

	protected function createComponentContactControl()
	{
		$control = $this->contactControlFactory->create($this->getUserIdentity());
		$control->onSuccess[] = function ($control) {
			$this->flashMessage($this->translator->translate('front.static.messages.sendContactSuccess'));
			$this->redirect('this');
		};

		return $control;
	}

	protected function createComponentAccountDeletionRequestControl()
	{
		$control = $this->accountDeletionRequestControlFactory->create($this->getUserIdentity());
		$control->onSuccess[] = function ($control) {
			$this->flashMessage($this->translator->translate('newFront.account.accountDeletionRequest.success'));
			$this->redirect('this');
		};

		return $control;
	}

	public function actionUnsubscribeEmail($contentType = null)
	{
		$this->redirect(':NewFront:Account:User:settings', ['unsubscribeContentType' => $contentType]);
	}

	public function actionGuarantee()
	{
		if ($this->user->isLoggedIn()) {
			$this->redirect(':NewFront:Account:User:guarantee');
		}
	}

	public function actionComplaints()
	{
		if ($this->user->isLoggedIn()) {
			$this->redirect(':NewFront:Account:Refund:default');
		} else {
			$this->redirect(':NewFront:Static:guarantee');
		}
	}

	public function renderWelcome()
	{
		$this->redirect(':NewFront:Homepage:default');

//		if (!$this->user->isLoggedIn()) {
//			$this->redirect(':NewFront:Homepage:default');
//		}

//		$this->template->topShops = $this->shopFacade->findTopShops(6, $this->getLocalization(), true);
	}

	public function renderO2Benefity()
	{
		$this->template->partnerOrganization = $this->partnerOrganizationFacade->find(12);
	}

	public function renderFaq()
	{
		$this->template->questionsTree = $this->questionFacade->getQuestionsTree();
	}

	public function createComponentGreysonEmailSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function (User $user) {
			$partnerOrganization = $this->partnerOrganizationFacade->find($this->getLocalization()->isCzech() ? 7 : 8);
			$user->setPartnerOrganization($partnerOrganization);

			$this->userFacade->saveUser($user);

//            $this->redirect(':NewFront:Homepage:default');
			$this->afterSignUp();
		};

		$control->onFailure[] = function () {
			$this->redrawControl('registrationForm');
		};

		return $control;
	}

	public function createComponentO2BenefityEmailSignUpControl()
	{
		$control = $this->emailSignUpControlFactory->create();

		$control->onSuccess[] = function (User $user) {
			$partnerOrganization = $this->partnerOrganizationFacade->find(12);
			$user->setPartnerOrganization($partnerOrganization);

			$this->userFacade->saveUser($user);

//            $this->redirect(':NewFront:Homepage:default');
			$this->afterSignUp();
		};

		return $control;
	}

	public function renderTopOffers()
	{
		/** @var User $user */
		$user = $this->getUserIdentity();

		$this->template->fullBanners = (function () use ($user) {
			if ($user) {
				$topBanner = $this->bannerFacade->findValidBanners($this->getLocalization(), 1, Banner::FORMAT_HOMEPAGE, $user->getSegment(), Banner::SIZE_FULL, null, $this->getUserIdentity());
				$topBanner = $topBanner[0] ?? null;

				$recommendedBanners = $this->bannersRecommender->getRecommendedFullBannersForUser($user, 1);
				$topBanners = array_filter(array_merge([$topBanner], $recommendedBanners));
				$nextBanners = $this->bannerFacade->findValidBanners($this->getLocalization(), 200, Banner::FORMAT_HOMEPAGE, $user->getSegment(), Banner::SIZE_FULL, $topBanners, $this->getUserIdentity());

				return array_merge($topBanners, $nextBanners);
			} else {
				return $this->bannerFacade->findValidBanners($this->getLocalization(), 200, Banner::FORMAT_HOMEPAGE, null, Banner::SIZE_FULL, null, $this->getUserIdentity());
			}
		});


		$this->template->topShops = (function () {
			return $this->shopFacade->findTopShops(100, $this->getLocalization(), true);
		});
	}

	public function renderFinance()
	{
		$this->template->articles = (function ($limit = 5) {
			$tag = $this->tagFacade->findBySlug('finance', $this->getLocalization());

			$articlesQuery = $this->articleFacade->createArticlesQuery()
				->withTags([$tag])
				->withLocalization($this->getLocalization())
				->sortNewest()
				->onlyPublished();

			return $this->articleFacade->fetch($articlesQuery)->applyPaging(0, $limit);
		});
	}

	public function renderFinancePl()
	{
		$this->template->articles = (function ($limit = 5) {
			$tag = $this->tagFacade->findBySlug('finanse', $this->getLocalization());

			$articlesQuery = $this->articleFacade->createArticlesQuery()
				->withTags([$tag])
				->withLocalization($this->getLocalization())
				->sortNewest()
				->onlyPublished();

			return $this->articleFacade->fetch($articlesQuery)->applyPaging(0, $limit);
		});
	}

	public function actionOnBoard()
	{
		if (!$this->user->isLoggedIn()) {
			$this->redirect(':NewFront:Sign:in', ['backLink' => $this->link('//this')]);
		}

		if (!$this->clientLayer->isChrome() && !$this->clientLayer->isFirefox()) {
			$this->redirect(':NewFront:Homepage:default');
		}
	}

	public function renderOnBoard()
	{
		$this->template->isFirefox = $this->clientLayer->isFirefox();
		$this->template->isChrome = $this->clientLayer->isChrome();
	}

	protected function createComponentAddonRejectFeedbackForm()
	{
		$form = new Form();

		$form->addTextArea('content');

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'addonRejectFeedbackFormSucceeded'];

		return $form;
	}

	public function addonRejectFeedbackFormSucceeded(Form $form, ArrayHash $values)
	{
		if (($user = $this->getUserIdentity())) {
			try {
				$this->feedbackFacade->createFeedback($values->content, $user, $this->getLocalization());
			} catch (UniqueConstraintViolationException $e) {
				// User already sent feedback
			}

			$this->flashMessage($this->translator->translate('front.onboard.feedback.success'));
			$this->redirect('Homepage:default');
		}
	}

	public function renderConditions()
	{
		$this->template->document = $this->conditionsFacade->findLatestPublishedDocument($this->getLocalization(), Document::DOCUMENT_CONDITIONS);
	}

	public function renderZasilkovnaConditions()
	{
		$this->template->document = $this->conditionsFacade->findLatestPublishedDocument($this->getLocalization(), Document::DOCUMENT_ZASILKOVNA);
	}

	public function renderPrivacyPolicy()
	{
		$this->template->document = $this->conditionsFacade->findLatestPublishedDocument($this->getLocalization(), Document::DOCUMENT_PRIVACY);
	}

	public function renderDziekujemy()
	{
		if (!$this->getLocalization()->isPolish()) {
			$this->error();
		}

		$campaignId = 262;
		$campaign = $this->shareRewardFacade->findCampaign($campaignId);
		$shareReward = null;

		if ($campaign && $this->user->isLoggedIn()) {
			$user = $this->getUserIdentity();

			$shareReward = $this->shareRewardFacade->findShareRewardByCampaignAndUser($campaign, null, $user);

			if (!$shareReward) {
				$this->shareRewardFacade->applyCampaignBonus($campaign, $user);

				$shareReward = $this->shareRewardFacade->findShareRewardByCampaignAndUser($campaign, null, $user);
			}
		}

		$this->template->shareReward = $shareReward;

		$this->template->topShops = $this->shopFacade->findTopShops(12, $this->getLocalization(), true);
	}

	public function renderSoutezDoporuc()
	{
		if ($this->user->isLoggedIn()) {
			$countOfTickets = $this->statisticDataProvider->getCountOfTicketsInSoutezDoporuc($this->getUserIdentity());
			$this->template->countOfTickets = $countOfTickets < 30 ? $countOfTickets : 30;
		}
	}

	public function renderNewLayout()
	{
		\Tracy\Debugger::$showBar = false;

		$this->template->amp = $this->getParameter('amp') == 1;

		$this->template->inlineCss = file_get_contents("../www/css/amp/main.amp.css");
	}

	public function renderAddon()
	{
		if ($this->configuration->isThereAddon($this->getLocalization()) === false && ($this->getUserIdentity() === null || $this->getUserIdentity()->isAdmin() === false)) {
			$this->redirect(':NewFront:Homepage:default');
		}

		if ($this->clientLayer->isChrome()) {
			$this->template->addonLink = $this->getLocalization()->getChromeAddonUrl();
		} elseif ($this->clientLayer->isFirefox()) {
			$this->template->addonLink = $this->getLocalization()->getFirefoxAddonUrl();
		} elseif ($this->clientLayer->isSafari()) {
			$this->template->addonLink = $this->getLocalization()->getSafariAddonUrl();
		} else {
			$this->template->addonLink = null;
		}
	}

	public function actionPhoneApp()
	{
		if ($this->configuration->isThereMobileApp($this->getLocalization()) === false) {
			$this->redirect(':NewFront:Homepage:default');
		}

		$shop = $this->shopFacade->findTopShops(1, $this->getLocalization(), onlyWithCashbackAllowed: true)
			->toArray()
		;

		$shop = reset($shop);

		$this->template->cashbackShop = $shop;

		$this->template->getNavigationTag = (function () use ($shop) {
			$parentTags = array_map(static function ($map) {
				return $map['entity'];
			}, $this->tagFacade->getNavigationTagsTree($shop->getLocalization()));

			foreach ($parentTags as $parentTag) {
				if ($shop->hasTag($parentTag)) {
					return $parentTag;
				}
			}

			return null;
		});
	}

	public function actionAfterSignUp(): void
	{
		if ($this->getUserIdentity() === null) {
			$this->redirect(':NewFront:Homepage:default');
		}

		if (
			$this->getUserIdentity()->isAdmin() === false && (
				$this->getUserIdentity()->isRegisteredToday() === false ||
				$this->sectionFactory->getFrontendSection()->hasAddonPromoPage === null
			)
		) {
			$this->redirect(':NewFront:Homepage:default');
		}

		$this->template->clientLayer = $this->clientLayer;

		if ($this->clientLayer->isChrome()) {
			$this->template->addonLink = $this->getLocalization()->getChromeAddonUrl();
		} elseif ($this->clientLayer->isFirefox()) {
			$this->template->addonLink = $this->getLocalization()->getFirefoxAddonUrl();
		} elseif ($this->clientLayer->isSafari()) {
			$this->template->addonLink = $this->getLocalization()->getSafariAddonUrl();
		} else {
			$this->template->addonLink = null;
		}
	}

	public function actionAfterSignIn(): void
	{
		if ($this->getUserIdentity() === null) {
			$this->redirect(':NewFront:Homepage:default');
		}

		$this->template->clientLayer = $this->clientLayer;

		if ($this->clientLayer->isChrome()) {
			$this->template->addonLink = $this->getLocalization()->getChromeAddonUrl();
		} elseif ($this->clientLayer->isFirefox()) {
			$this->template->addonLink = $this->getLocalization()->getFirefoxAddonUrl();
		} elseif ($this->clientLayer->isSafari()) {
			$this->template->addonLink = $this->getLocalization()->getSafariAddonUrl();
		} else {
			$this->template->addonLink = null;
		}
	}
}
