<!DOCTYPE html>
<html lang="cs">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
	<meta name="skype_toolbar" content="skype_toolbar_parser_compatible">

	<!-- Metadata info -->
	<title></title>
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta name="robots" content="">
	<meta name="googlebot" content="">
	<meta name="author" content="">

	<!-- Favicon images -->

	<!-- Viewport for mobile devices -->
	<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">

	<!-- Stylesheet -->
	<link rel="stylesheet" href="{$basePath}/css2/output.css">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght,HEXP@160..700,0..100&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
		rel="stylesheet">

	<script src="https://unpkg.com/@popperjs/core@2"></script>
	<script src="https://unpkg.com/tippy.js@6"></script>
	<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/animations/scale.css" />
	<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/border.css" />
</head>
<body class="bg-light-6">
<header class="w-full">
	<div class="border-b w-full border-b-light-4">
		<div class="relative flex justify-between container h-[64px] items-center">
			<div class="flex items-center">
				<a href="/new/vitejte">
					<svg width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd"
							  d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z"
							  fill="#646C7C"></path>
						<path fill-rule="evenodd" clip-rule="evenodd"
							  d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z"
							  fill="#EF7F1A"></path>
					</svg>
				</a>

				<form action=""
					  class="flex items-center w-[365px] h-[47px] bg-white rounded-[14px] border border-zinc-200 pl-4 pr-[3px] ml-9">
					<input type="text" placeholder="Hľadať zľavy, vouchery a obchody..."
						   class="text-sm w-3/4 outline-none">
					<button class="w-[41px] h-[41px] ml-auto"
							style="background-image: url('{$basePath}/new-design/search.svg')"></button>
				</form>
			</div>

			<div class="flex gap-4">
				<div class="w-px h-[63px] bg-zinc-200"></div>
				<div class="flex items-center">
					<div
						class="flex items-center justify-center min-w-[69px] h-[27px] rounded-md border border-slate-700 text-slate-700 text-sm font-medium">
						2 345 €
					</div>
					<div
						class="relative flex justify-center items-center w-[41px] h-[41px] bg-zinc-200 rounded-xl mr-4">
						<div class=" text-zinc-950 text-sm font-medium">OG</div>
						<div
							class="absolute flex items-center justify-center right-[-3px] top-[-3px] text-white text-[9px] font-bold leading-[17.50px] w-3.5 h-3.5 bg-red-500 rounded-[300px]">
							3
						</div>
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="11" viewBox="0 0 16 11" fill="none"
							 class="absolute right-[-3px] bottom-[-3px]">
							<path
								d="M7.58872 1.59488C7.78752 1.30734 8.21248 1.30734 8.41128 1.59489L10.8115 5.06668C10.9744 5.30225 11.3015 5.35316 11.5282 5.17823L13.9308 3.32484C14.2982 3.04139 14.82 3.36788 14.7258 3.82227L13.5271 9.60154C13.479 9.83361 13.2745 10 13.0375 10H2.96249C2.72549 10 2.52104 9.83361 2.47291 9.60154L1.27425 3.82227C1.18 3.36788 1.7018 3.04139 2.06923 3.32484L4.47178 5.17823C4.69854 5.35316 5.0256 5.30225 5.18847 5.06668L7.58872 1.59488Z"
								fill="white" stroke="#BBA24D"></path>
						</svg>
					</div>
					<svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none">
						<path d="M1 6.99991L7.00018 1L13 7" stroke="#080B10" stroke-miterlimit="10"
							  stroke-linecap="round" stroke-linejoin="round"></path>
					</svg>
				</div>
				<div class="w-px h-[63px] bg-zinc-200"></div>
			</div>
		</div>
	</div>

	<div class="border-b border-b-light-4">
		<div class="flex py-3 container">
			<a class="flex items-center cursor-pointer gap-2 js-show-shops" href="/new/obchody">
				<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
					<path
						d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
					<path
						d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
					<path
						d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
				</svg>

				<div class="hidden font-medium text-sm leading-[24.5px] md:block">
					Obchod s odmenami
				</div>

				<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M6.00015 6L5.46983 6.53034C5.76273 6.82323 6.2376 6.82322 6.53048 6.53032L6.00015 6ZM1.53032 0.469734C1.23742 0.176847 0.762545 0.176858 0.469658 0.469758C0.176771 0.762657 0.176782 1.23753 0.469682 1.53042L1.53032 0.469734ZM11.5303 1.53032C11.8232 1.23742 11.8232 0.762551 11.5303 0.469662C11.2374 0.176773 10.7626 0.17678 10.4697 0.469678L11.5303 1.53032ZM6.53046 5.46966L1.53032 0.469734L0.469682 1.53042L5.46983 6.53034L6.53046 5.46966ZM6.53048 6.53032L11.5303 1.53032L10.4697 0.469678L5.46981 5.46968L6.53048 6.53032Z"
						fill="#080B10"></path>
				</svg>
			</a>

			<a class="flex items-center cursor-pointer gap-2 ml-9" href="/new/slevy">
				<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
					<path
						d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
					<path
						d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
				</svg>

				<div class="hidden font-medium text-sm leading-[24.5px] md:block">
					Zľavové kupóny
				</div>
			</a>
		</div>
	</div>
</header>

<div class="bg-img">
	<div class="container">
		<div
			class="text-lg md:text-[26px] leading-[39px] font-medium text-white mt-10 md:my-[34px] pl-5 lg:pl-0">
			Posledné
			presmerovania
		</div>

		<div class="lg:flex gap-5">
			<div class="lg:w-[285px]">
				<div class="hidden md:block w-full bg-white text-center rounded-2xl pt-[29px] pb-[23px] px-5 mb-5">
					<div>
						<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="69" height="69"
							 viewBox="0 0 69 69"
							 fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd"
								  d="M34.5 0C53.5538 0 69 15.4462 69 34.5C69 53.5538 53.5538 69 34.5 69C15.4462 69 0 53.5538 0 34.5C0 15.4462 15.4462 0 34.5 0ZM34.5 3C51.897 3 66 17.103 66 34.5C66 51.897 51.897 66 34.5 66C17.103 66 3 51.897 3 34.5C3 17.103 17.103 3 34.5 3Z"
								  fill="#F4F4F6" />
							<path fill-rule="evenodd" clip-rule="evenodd"
								  d="M67.474 33.9998C68.3113 33.9998 68.99 33.3132 68.9417 32.4772C67.9333 15.034 53.9657 1.06647 36.5226 0.0580943C35.6866 0.0097697 35 0.688509 35 1.52584C35 2.34572 35.659 3.01016 36.4773 3.06084C52.2992 4.0408 64.959 16.7006 65.9389 32.5225C65.9896 33.3408 66.6541 33.9998 67.474 33.9998Z"
								  fill="#66B940" />
							<rect width="53" height="53" rx="26.5" transform="matrix(-1 0 0 1 61 8)" fill="#ECEDF0" />
							<path
								d="M34.4945 33.778C34.3845 33.767 34.2526 33.767 34.1316 33.778C31.5143 33.6898 29.4359 31.5402 29.4359 28.8945C29.4359 26.1937 31.6133 24 34.3186 24C37.0128 24 39.2013 26.1937 39.2013 28.8945C39.1903 31.5402 37.1118 33.6898 34.4945 33.778Z"
								fill="#080B10" />
							<path
								d="M28.996 37.0078C26.3347 38.7937 26.3347 41.7039 28.996 43.4787C32.0202 45.5071 36.9798 45.5071 40.004 43.4787C42.6653 41.6929 42.6653 38.7827 40.004 37.0078C36.9908 34.9905 32.0312 34.9905 28.996 37.0078Z"
								fill="#080B10" />
						</svg>
					</div>
					<div class="text-dark-1 text-lg font-medium leading-[31.5px]">Oliver Gaňa</div>
					<div class="text-sm leading-[24.5px] text-dark-3"><EMAIL></div>
					<div class="w-full h-px bg-light-5 my-[18px]"></div>
					<div class="text-sm uppercase leading-[24.5px] text-dark-1">Celkom ste už získali</div>
					<div class="text-[26px] leading-[39px] font-bold text-secondary-green">25,12 €</div>
				</div>

				<div class="hidden md:block bg-white rounded-2xl p-5">
					<div class="text-dark-1 font-medium leading-[31.5px] text-lg">Navigácia</div>
					<div class="w-full h-px bg-light-5 mt-[18px] mb-[21px]"></div>

					<div class="flex flex-col gap-[25px]">
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19"
								 fill="none">
								<path
									d="M7.85714 7.22979C7.85714 7.41424 7.7849 7.59113 7.6563 7.72156C7.5277 7.85198 7.35329 7.92525 7.17143 7.92525H1.68571C1.50386 7.92525 1.32944 7.85198 1.20084 7.72156C1.07224 7.59113 1 7.41424 1 7.22979V1.66616C1 1.48172 1.07224 1.30482 1.20084 1.17439C1.32944 1.04397 1.50386 0.970703 1.68571 0.970703H7.17143C7.35329 0.970703 7.5277 1.04397 7.6563 1.17439C7.7849 1.30482 7.85714 1.48172 7.85714 1.66616V7.22979Z"
									stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round" />
								<path
									d="M17 7.22979C17 7.41424 16.9278 7.59113 16.7992 7.72156C16.6706 7.85198 16.4961 7.92525 16.3143 7.92525H10.8286C10.6467 7.92525 10.4723 7.85198 10.3437 7.72156C10.2151 7.59113 10.1429 7.41424 10.1429 7.22979V1.66616C10.1429 1.48172 10.2151 1.30482 10.3437 1.17439C10.4723 1.04397 10.6467 0.970703 10.8286 0.970703H16.3143C16.4961 0.970703 16.6706 1.04397 16.7992 1.17439C16.9278 1.30482 17 1.48172 17 1.66616V7.22979Z"
									stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round" />
								<path
									d="M17 17.2752C17 17.4597 16.9278 17.6366 16.7992 17.767C16.6706 17.8974 16.4961 17.9707 16.3143 17.9707H10.8286C10.6467 17.9707 10.4723 17.8974 10.3437 17.767C10.2151 17.6366 10.1429 17.4597 10.1429 17.2752V11.7116C10.1429 11.5272 10.2151 11.3503 10.3437 11.2198C10.4723 11.0894 10.6467 11.0162 10.8286 11.0162H16.3143C16.4961 11.0162 16.6706 11.0894 16.7992 11.2198C16.9278 11.3503 17 11.5272 17 11.7116V17.2752Z"
									stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round" />
								<path
									d="M7.85714 17.2752C7.85714 17.4597 7.7849 17.6366 7.6563 17.767C7.5277 17.8974 7.35329 17.9707 7.17143 17.9707H1.68571C1.50386 17.9707 1.32944 17.8974 1.20084 17.767C1.07224 17.6366 1 17.4597 1 17.2752V11.7116C1 11.5272 1.07224 11.3503 1.20084 11.2198C1.32944 11.0894 1.50386 11.0162 1.68571 11.0162H7.17143C7.35329 11.0162 7.5277 11.0894 7.6563 11.2198C7.7849 11.3503 7.85714 11.5272 7.85714 11.7116V17.2752Z"
									stroke="#EF7F1A" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-primary-orange">Prehľad mojich
								odmien
							</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19"
								 fill="none">
								<path
									d="M1.72931 1.24244C1.46414 1.16536 1.18669 1.31784 1.10962 1.58301C1.03254 1.84817 1.18502 2.12562 1.45019 2.20269L1.72931 1.24244ZM10.5651 4.33142L10.7243 3.85744C10.7178 3.85526 10.7112 3.85321 10.7047 3.8513L10.5651 4.33142ZM12.0854 5.45394L12.4904 5.16069L12.4904 5.16065L12.0854 5.45394ZM12.692 7.26839H13.1921L13.1919 7.25792L12.692 7.26839ZM12.692 16.0877H12.192C12.192 16.1029 12.1927 16.1182 12.1941 16.1334L12.692 16.0877ZM12.5723 16.9052L13.036 17.0923L13.036 17.0922L12.5723 16.9052ZM12.0936 17.5715L11.7694 17.1909L11.7693 17.191L12.0936 17.5715ZM11.3665 17.9329L11.4729 18.4215L11.473 18.4214L11.3665 17.9329ZM10.5585 17.9061L10.6971 17.4256L10.6925 17.4243L10.5585 17.9061ZM3.13406 15.8418L2.98248 16.3183C2.98833 16.3201 2.99421 16.3219 3.00012 16.3235L3.13406 15.8418ZM1.6096 14.7434L2.0124 14.4471L2.01239 14.4471L1.6096 14.7434ZM1.00058 12.9409H0.500477L0.500679 12.9509L1.00058 12.9409ZM1.00058 3.33118H1.5007L1.50046 3.32012L1.00058 3.33118ZM3.1915 0.970703V0.470703C3.18646 0.470703 3.18143 0.470779 3.1764 0.470931L3.1915 0.970703ZM17 11.0008L17.5 11.0051V11.0008H17ZM16.243 12.8209L16.5968 13.1742L16.5969 13.1741L16.243 12.8209ZM15.4173 13.3722L15.6071 13.8348L15.6072 13.8348L15.4173 13.3722ZM14.448 13.5577L14.4541 13.0577H14.448V13.5577ZM12.692 13.0577C12.4159 13.0577 12.192 13.2815 12.192 13.5577C12.192 13.8338 12.4159 14.0577 12.692 14.0577V13.0577ZM17 7.76419C17.2761 7.76419 17.5 7.54033 17.5 7.26419C17.5 6.98805 17.2761 6.76419 17 6.76419V7.76419ZM12.692 6.76419C12.4159 6.76419 12.192 6.98805 12.192 7.26419C12.192 7.54033 12.4159 7.76419 12.692 7.76419V6.76419ZM1.45019 2.20269L10.4255 4.81155L10.7047 3.8513L1.72931 1.24244L1.45019 2.20269ZM10.4059 4.80541C10.914 4.97607 11.3598 5.3044 11.6805 5.74723L12.4904 5.16065C12.0491 4.55139 11.4326 4.09532 10.7243 3.85744L10.4059 4.80541ZM11.6805 5.74718C12.0012 6.19014 12.1805 6.72529 12.1921 7.27885L13.1919 7.25792C13.1761 6.5026 12.9315 5.76982 12.4904 5.16069L11.6805 5.74718ZM12.192 7.26839V16.0877H13.192V7.26839H12.192ZM12.1941 16.1334C12.2125 16.3329 12.1829 16.5337 12.1085 16.7183L13.036 17.0922C13.1701 16.7597 13.2228 16.3993 13.1899 16.0419L12.1941 16.1334ZM12.1086 16.7182C12.0341 16.9028 11.9173 17.0648 11.7694 17.1909L12.4179 17.9521C12.6901 17.7202 12.9019 17.4248 13.036 17.0923L12.1086 16.7182ZM11.7693 17.191C11.6213 17.3171 11.4465 17.4037 11.26 17.4444L11.473 18.4214C11.8216 18.3454 12.1458 18.184 12.418 17.952L11.7693 17.191ZM11.2602 17.4444C11.0738 17.4849 10.8806 17.4786 10.6971 17.4256L10.4199 18.3865C10.7629 18.4854 11.1245 18.4973 11.4729 18.4215L11.2602 17.4444ZM10.6925 17.4243L3.268 15.3601L3.00012 16.3235L10.4246 18.3878L10.6925 17.4243ZM3.28564 15.3653C2.77943 15.2043 2.33355 14.8838 2.0124 14.4471L1.20681 15.0396C1.65143 15.6441 2.27218 16.0923 2.98248 16.3183L3.28564 15.3653ZM2.01239 14.4471C1.69114 14.0103 1.5115 13.4799 1.50048 12.9309L0.500679 12.9509C0.515803 13.7044 0.762277 14.4352 1.20682 15.0396L2.01239 14.4471ZM1.50058 12.9409V3.33118H0.500578V12.9409H1.50058ZM1.50046 3.32012C1.48981 2.83903 1.66582 2.37516 1.98679 2.02935L1.25385 1.34906C0.753946 1.88765 0.484378 2.60428 0.5007 3.34224L1.50046 3.32012ZM1.98679 2.02935C2.30742 1.68391 2.74622 1.48439 3.2066 1.47048L3.1764 0.470931C2.4449 0.493035 1.75411 0.810084 1.25385 1.34906L1.98679 2.02935ZM3.1915 1.4707H14.5383V0.470703H3.1915V1.4707ZM14.5383 1.4707C15.055 1.4707 15.5528 1.68048 15.9216 2.05761L16.6365 1.35846C16.082 0.791378 15.3275 0.470703 14.5383 0.470703V1.4707ZM15.9216 2.05761C16.2907 2.43511 16.5 2.94946 16.5 3.4881H17.5C17.5 2.69143 17.1907 1.92516 16.6365 1.35846L15.9216 2.05761ZM16.5 3.4881V11.0008H17.5V3.4881H16.5ZM16.5 10.9966C16.4977 11.2718 16.4423 11.5434 16.3372 11.796L17.2605 12.18C17.4153 11.808 17.4966 11.4088 17.5 11.0051L16.5 10.9966ZM16.3372 11.796C16.2322 12.0486 16.0797 12.2767 15.8891 12.4677L16.5969 13.1741C16.8805 12.89 17.1058 12.5521 17.2605 12.18L16.3372 11.796ZM15.8892 12.4676C15.6985 12.6586 15.4735 12.8086 15.2274 12.9097L15.6072 13.8348C15.9771 13.6829 16.3133 13.4582 16.5968 13.1742L15.8892 12.4676ZM15.2275 12.9097C14.9813 13.0107 14.7186 13.0609 14.4541 13.0577L14.442 14.0576C14.8413 14.0625 15.2373 13.9865 15.6071 13.8348L15.2275 12.9097ZM14.448 13.0577H12.692V14.0577H14.448V13.0577ZM9.30716 10.5403C9.42385 10.5403 9.49945 10.6339 9.49945 10.7256H8.49945C8.49945 11.1649 8.85059 11.5403 9.30716 11.5403V10.5403ZM9.49945 10.7256C9.49945 10.8174 9.42385 10.9109 9.30716 10.9109V9.91093C8.85059 9.91093 8.49945 10.2863 8.49945 10.7256H9.49945ZM9.30716 11.5403C9.76373 11.5403 10.1149 11.1649 10.1149 10.7256H9.11487C9.11487 10.6339 9.19047 10.5403 9.30716 10.5403V11.5403ZM10.1149 10.7256C10.1149 10.2863 9.76373 9.91093 9.30716 9.91093V10.9109C9.19047 10.9109 9.11487 10.8174 9.11487 10.7256H10.1149ZM17 6.76419H12.692V7.76419H17V6.76419Z"
									fill="#080B10" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium">Výplata odmien</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17"
								 fill="none">
								<path
									d="M8.22546 1.44149C8.29773 1.29993 8.40838 1.18096 8.54526 1.09784C8.68196 1.01472 8.83936 0.970703 8.99997 0.970703C9.16059 0.970703 9.31799 1.01472 9.45487 1.09784C9.59157 1.18096 9.70221 1.29993 9.77449 1.44149L11.5751 5.02419C11.6374 5.147 11.7288 5.25312 11.8416 5.33348C11.9544 5.41385 12.0854 5.46606 12.223 5.48568L16.2633 6.06166C16.421 6.08354 16.5697 6.14836 16.6923 6.24883C16.8149 6.34932 16.9066 6.48151 16.9575 6.6306C17.0073 6.7791 17.0135 6.93851 16.9752 7.09032C16.937 7.24214 16.856 7.38016 16.7416 7.4884L13.8184 10.2961C13.7197 10.3901 13.6456 10.5064 13.6028 10.635C13.5598 10.7636 13.5493 10.9006 13.5721 11.0341L14.2645 14.992C14.2908 15.1468 14.272 15.3058 14.2108 15.4507C14.1496 15.5956 14.0482 15.7205 13.9183 15.8111C13.7875 15.9032 13.6333 15.9578 13.4731 15.9687C13.3126 15.9796 13.1524 15.9463 13.01 15.8727L9.40329 14.0162C9.27908 13.9511 9.1406 13.9171 8.99997 13.9171C8.85935 13.9171 8.72086 13.9511 8.59665 14.0162L4.99892 15.8727C4.85655 15.9463 4.69627 15.9796 4.53591 15.9687C4.37554 15.9578 4.22139 15.9032 4.09056 15.8111C3.96068 15.7205 3.85931 15.5956 3.79806 15.4507C3.73683 15.3058 3.71822 15.1468 3.74435 14.992L4.43677 11.0341C4.45958 10.9006 4.44907 10.7636 4.40615 10.635C4.36323 10.5064 4.28922 10.3901 4.1905 10.2961L1.2584 7.4884C1.14398 7.38016 1.06297 7.24214 1.02474 7.09032C0.986537 6.93851 0.992676 6.7791 1.04247 6.6306C1.09327 6.48151 1.18511 6.34932 1.30773 6.24883C1.43033 6.14836 1.57886 6.08354 1.73667 6.06166L5.77699 5.48568C5.91476 5.46606 6.04557 5.41385 6.15836 5.33348C6.27114 5.25312 6.36251 5.147 6.4248 5.02419L8.22546 1.44149Z"
									stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Garancia spokojnosti</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 20 18"
								 fill="none">
								<path
									d="M1 7.58824H5.73684V17H1M8.24537 3.43477C8.24537 2.78907 8.50965 2.1698 8.98009 1.71318C9.45053 1.25656 10.0886 1 10.7539 1C11.4192 1 12.0572 1.25656 12.5277 1.71318C12.9981 2.1698 13.2624 2.78907 13.2624 3.43477V5.27129C13.2625 5.50322 13.3432 5.72819 13.4915 5.91004C13.6398 6.09188 13.847 6.21974 14.0795 6.27303L16.5593 6.49563C17.2834 6.67442 17.9215 7.09069 18.3656 7.67393C18.8097 8.25702 19.0324 8.97131 18.9962 9.69562L18.7812 13.2434C18.6742 14.2766 18.1744 15.234 17.3791 15.9285C16.584 16.6232 15.5506 17.0051 14.4808 16.9999H10.6392C10.2411 17.0009 9.84494 16.9447 9.46379 16.833L6.85493 16.1234C6.49321 16.0127 6.11613 15.9563 5.73684 15.9565V9.20867C6.59655 8.50815 7.27235 7.61925 7.7085 6.61529C8.14465 5.61146 8.32866 4.52124 8.24537 3.43477Z"
									stroke="black" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Odporuč a získaj 6 €</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19"
								 fill="none">
								<path
									d="M9.00009 11.523C10.1789 11.523 11.1346 10.6041 11.1346 9.47068C11.1346 8.33721 10.1789 7.41836 9.00009 7.41836C7.82115 7.41836 6.86562 8.33721 6.86562 9.47068C6.86562 10.6041 7.82115 11.523 9.00009 11.523Z"
									stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round"
									stroke-linejoin="round" />
								<path
									d="M10.7076 2.09948L11.2767 3.87816C11.419 4.56226 12.1305 4.83592 12.842 4.6991L14.8342 4.28862C16.5418 3.87816 17.6801 5.93046 16.5418 7.16186L15.1188 8.53007C14.6919 9.07735 14.6919 9.76148 15.1188 10.3087L16.5418 11.6769C17.6801 12.9083 16.5418 14.8238 14.8342 14.5502L12.842 14.1397C12.1305 14.0029 11.5613 14.4133 11.2767 14.9606L10.7076 16.7393C10.1384 18.3812 7.86159 18.3812 7.2924 16.7393L6.72321 14.9606C6.58091 14.2765 5.86947 14.0029 5.15798 14.1397L3.16582 14.5502C1.45825 14.9606 0.319848 12.9083 1.45823 11.6769L2.88121 10.3087C3.3081 9.76148 3.3081 9.07735 2.88121 8.53007L1.45823 7.16186C0.319848 5.93046 1.45825 4.01498 3.16582 4.28862L5.15798 4.6991C5.86947 4.83592 6.43862 4.42544 6.72321 3.87816L7.2924 2.09948C7.86159 0.594444 10.2807 0.594444 10.7076 2.09948Z"
									stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round"
									stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Nastavenie účtu</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"
								 fill="none">
								<path
									d="M4.20094 2.07291C4.12274 2.07242 4.04598 2.09411 3.97937 2.13551C3.91276 2.1769 3.85897 2.23634 3.82413 2.30707L2.93612 4.07267H1.42389C1.34064 4.07061 1.25871 4.09399 1.18883 4.13972C1.11893 4.18546 1.06433 4.25145 1.03215 4.32904C0.999966 4.40664 0.991707 4.49224 1.00845 4.57466C1.02519 4.65707 1.06616 4.73246 1.12599 4.79097L2.51452 6.16868L1.74809 7.95366C1.71418 8.0354 1.70655 8.12584 1.72626 8.21225C1.74596 8.29859 1.79203 8.37653 1.85794 8.43507C1.92386 8.49354 2.00629 8.52967 2.09362 8.53829C2.18094 8.54691 2.26873 8.52759 2.3446 8.48305L4.20094 7.42644L4.69294 7.70584M13.7991 2.07291C13.8773 2.07242 13.954 2.09411 14.0206 2.13551C14.0872 2.1769 14.141 2.23634 14.1759 2.30707L15.0639 4.07267H16.5761C16.6594 4.07061 16.7413 4.09399 16.8112 4.13972C16.8811 4.18546 16.9357 4.25145 16.9679 4.32904C17 4.40664 17.0083 4.49224 16.9915 4.57466C16.9748 4.65707 16.9338 4.73246 16.874 4.79097L15.4855 6.16868L16.2519 7.95366C16.2858 8.0354 16.2934 8.12584 16.2737 8.21225C16.254 8.29859 16.208 8.37653 16.1421 8.43507C16.0762 8.49354 15.9937 8.52967 15.9064 8.53829C15.8191 8.54691 15.7313 8.52759 15.6554 8.48305L13.7991 7.42644L13.3071 7.70584M1.00156 10.2682V12.4231C1.00156 12.4231 3.13448 10.2682 9 10.2682C14.8655 10.2682 16.9984 12.4231 16.9984 12.4231V10.2682M2.3453 13.6958C2.03898 14.2215 1.56528 14.6266 1.00156 14.8451H2.06802C2.35086 14.8451 2.62212 14.9586 2.82212 15.1606C3.02212 15.3628 3.13448 15.6368 3.13448 15.9225V17C3.13448 17 6.00681 16.5087 6.30399 12.3238M15.6547 13.6958C15.961 14.2215 16.4347 14.6266 16.9984 14.8451H15.932C15.6492 14.8451 15.3778 14.9586 15.1778 15.1606C14.9779 15.3628 14.8655 15.6368 14.8655 15.9225V17C14.8655 17 11.9932 16.5087 11.696 12.3238M9.37752 1.22964L10.2648 2.99523H11.7771C11.8603 2.99318 11.9422 3.01655 12.0121 3.06229C12.082 3.10803 12.1366 3.17401 12.1688 3.25161C12.201 3.3292 12.2093 3.41481 12.1925 3.49722C12.1758 3.57963 12.1348 3.65502 12.075 3.71354L10.6864 5.09125L11.4557 6.87624C11.4896 6.95799 11.4973 7.04844 11.4775 7.13481C11.4578 7.22119 11.4118 7.29913 11.3459 7.35762C11.2799 7.41612 11.1975 7.45222 11.1102 7.46084C11.0229 7.46946 10.9351 7.45014 10.8592 7.40563L9 6.349L7.14081 7.40563C7.06495 7.45014 6.97715 7.46946 6.88983 7.46084C6.80251 7.45222 6.72008 7.41612 6.65417 7.35762C6.58825 7.29913 6.54218 7.22119 6.52247 7.13481C6.50277 7.04844 6.5104 6.95799 6.54431 6.87624L7.31358 5.09125L5.92505 3.71066C5.86522 3.65215 5.82426 3.57676 5.80751 3.49435C5.79077 3.41193 5.79903 3.32633 5.83121 3.24874C5.86339 3.17114 5.918 3.10516 5.98789 3.05942C6.05778 3.01368 6.1397 2.99031 6.22295 2.99236H7.73518L8.62318 1.22964C8.65944 1.16033 8.71369 1.10231 8.78017 1.06185C8.84664 1.02138 8.92279 1 9.00035 1C9.07799 1 9.15407 1.02138 9.22054 1.06185C9.28702 1.10231 9.34127 1.16033 9.37752 1.22964Z"
									stroke="black" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Moje hodnotenie</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20"
								 fill="none">
								<path
									d="M12.1406 5.0837C12.1406 5.72828 12.3967 6.34645 12.8525 6.80224C13.3083 7.25802 13.9265 7.51408 14.571 7.51408C15.2155 7.51408 15.8337 7.25802 16.2895 6.80224C16.7453 6.34645 17.0014 5.72828 17.0014 5.0837C17.0014 4.43912 16.7453 3.82095 16.2895 3.36516C15.8337 2.90938 15.2155 2.65332 14.571 2.65332C13.9265 2.65332 13.3083 2.90938 12.8525 3.36516C12.3967 3.82095 12.1406 4.43912 12.1406 5.0837Z"
									fill="#F72F49" stroke="#F72F49" stroke-linecap="round" stroke-linejoin="round" />
								<path
									d="M6.67089 17.9834C6.77379 18.3339 6.98745 18.6417 7.2799 18.8606C7.57231 19.0795 7.92788 19.1979 8.29316 19.1979C8.65845 19.1979 9.01401 19.0795 9.30639 18.8606C9.59884 18.6417 9.81256 18.3339 9.91544 17.9834M8.29114 2.79349V0.970703M14.3857 10.053C14.5388 14.7161 15.5823 15.553 15.5823 15.553H1C1 15.553 2.21519 14.0008 2.21519 8.86944C2.2161 7.91753 2.44043 6.97916 2.87013 6.1298C3.29983 5.28044 3.9229 4.5438 4.68921 3.97915C5.45553 3.41451 6.34368 3.03762 7.28221 2.87882C8.22074 2.72002 9.18341 2.78373 10.0929 3.06485"
									stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Notifikácie <span
								class="text-secondary-red">(3)</span></div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"
								 fill="none">
								<path
									d="M7.34605 16.7978C5.93404 16.4995 4.62905 15.8247 3.56933 14.845C2.50961 13.8654 1.73464 12.6173 1.32654 11.233C0.918423 9.84871 0.892372 8.37984 1.25114 6.98193C1.60991 5.58406 2.34014 4.30926 3.36445 3.29262C4.38877 2.27598 5.66902 1.55536 7.06955 1.20711C8.47009 0.858862 9.93875 0.895956 11.3199 1.31446C12.7011 1.73296 13.9433 2.5173 14.915 3.58435C15.8867 4.65141 16.5517 5.96144 16.8394 7.37564M4.03937 15.2335L4.75048 11.6423H5.51848C5.68067 11.6428 5.84079 11.6059 5.98635 11.5344C6.13191 11.4629 6.25896 11.3586 6.3576 11.2299C6.45918 11.1041 6.52955 10.9561 6.56292 10.7978C6.59629 10.6396 6.59173 10.4758 6.5496 10.3197L6.01626 8.18635C5.96037 7.95502 5.82826 7.7493 5.64122 7.60224C5.45418 7.4552 5.22308 7.37538 4.98515 7.37565H1.19492M15.4172 4.17565H12.5158C12.2779 4.17538 12.0468 4.2552 11.8597 4.40226C11.6727 4.54931 11.5406 4.75504 11.4847 4.98632L11.2856 5.76854M11.6452 12.1635C11.6452 12.4464 11.7576 12.7177 11.9577 12.9177C12.1577 13.1178 12.429 13.2302 12.7119 13.2302C12.9948 13.2302 13.2661 13.1178 13.4661 12.9177C13.6662 12.7177 13.7785 12.4464 13.7785 12.1635C13.7785 11.8806 13.6662 11.6093 13.4661 11.4092C13.2661 11.2092 12.9948 11.0969 12.7119 11.0969C12.429 11.0969 12.1577 11.2092 11.9577 11.4092C11.7576 11.6093 11.6452 11.8806 11.6452 12.1635ZM13.615 8.03908L13.9563 9.07019C14.0092 9.24036 14.1241 9.3845 14.2781 9.47403C14.4322 9.56363 14.6143 9.592 14.7883 9.55374L15.8337 9.31197C16.0325 9.26624 16.2408 9.28558 16.4278 9.36708C16.6148 9.44857 16.7707 9.58795 16.8726 9.76466C16.9745 9.94137 17.017 10.1462 16.9938 10.3488C16.9706 10.5515 16.883 10.7414 16.7439 10.8906L16.0328 11.68C15.9108 11.8115 15.8431 11.9842 15.8431 12.1635C15.8431 12.3429 15.9108 12.5155 16.0328 12.6471L16.7439 13.4364C16.883 13.5855 16.9706 13.7755 16.9938 13.9781C17.017 14.1808 16.9745 14.3856 16.8726 14.5623C16.7707 14.7391 16.6148 14.8784 16.4278 14.9599C16.2408 15.0414 16.0325 15.0607 15.8337 15.0151L14.7883 14.7733C14.6143 14.735 14.4322 14.7634 14.2781 14.8529C14.1241 14.9425 14.0092 15.0866 13.9563 15.2569L13.6363 16.288C13.5806 16.4845 13.4623 16.6576 13.2993 16.7808C13.1363 16.904 12.9376 16.9707 12.7332 16.9707C12.5289 16.9707 12.3301 16.904 12.1672 16.7808C12.0042 16.6576 11.8858 16.4845 11.8301 16.288L11.5101 15.2569C11.4573 15.0866 11.3424 14.9425 11.1883 14.8529C11.0343 14.7634 10.8522 14.735 10.6781 14.7733L9.63276 15.0151C9.43393 15.0607 9.22572 15.0414 9.03869 14.9599C8.85167 14.8784 8.69572 14.7391 8.59382 14.5623C8.49192 14.3856 8.44947 14.1808 8.47265 13.9781C8.49583 13.7755 8.58337 13.5855 8.72253 13.4364L9.43365 12.6471C9.5556 12.5155 9.62337 12.3429 9.62337 12.1635C9.62337 11.9842 9.5556 11.8115 9.43365 11.68L8.72253 10.8906C8.58337 10.7414 8.49583 10.5515 8.47265 10.3488C8.44947 10.1462 8.49192 9.94137 8.59382 9.76466C8.69572 9.58795 8.85167 9.44857 9.03869 9.36708C9.22572 9.28558 9.43393 9.26624 9.63276 9.31197L10.6781 9.55374C10.8522 9.592 11.0343 9.56363 11.1883 9.47403C11.3424 9.3845 11.4573 9.24036 11.5101 9.07019L11.823 8.03908C11.8826 7.84807 12.0018 7.68117 12.1629 7.56263C12.3241 7.44409 12.5189 7.38017 12.719 7.38017C12.9191 7.38017 13.1139 7.44409 13.2751 7.56263C13.4363 7.68117 13.5554 7.84807 13.615 8.03908Z"
									stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Tipli do prehliadača</div>
						</div>
						<div class="flex items-center gap-[14px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="21" viewBox="0 0 18 21"
								 fill="none">
								<path
									d="M8.99998 0.970703V9.79282M6.33334 4.14934C4.55411 4.78968 3.05454 6.04968 2.09966 7.70666C1.14479 9.36366 0.796107 11.3109 1.11523 13.2042C1.43435 15.0976 2.40073 16.8152 3.84358 18.0534C5.28642 19.2915 7.11284 19.9707 9.00001 19.9707C10.8871 19.9707 12.7136 19.2915 14.1564 18.0534C15.5993 16.8152 16.5657 15.0976 16.8848 13.2042C17.2039 11.3109 16.8552 9.36366 15.9003 7.70666C14.9455 6.04968 13.4459 4.78968 11.6667 4.14934"
									stroke="black" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
							<div class="text-sm leading-[24.5px] font-medium text-dark-1">Odhlásiť sa</div>
						</div>
					</div>
				</div>
			</div>

		{* Pokud uzivatel ma nejake presmerovani true pokud ne false *}
		{if true}
			<div class="grow">
				<div class="bg-white rounded-2xl p-[30px] md:py-10 text-center mt-[124px] md:mt-[45px] mb-10 relative">
					<div
						class="w-[245px] m-auto md:w-auto text-[20px] md:text-[30px] text-dark-1 leading-[32px] md:leading-[50px] mb-2.5 md:mb-[5px]">
						Chýbajúci nákup?
						<span class="text-primary-orange font-bold">Jednoducho ho nahláste</span>
					</div>
					<div class="w-full max-w-[737px] m-auto leading-7">
						Ak váš nákup nebol potvrdený obchodom do 48 hodín, no viete, že ste ho uskutočnili, môžete ho
						jednoducho nahlásiť. Radi ho preveríme a v prípade oprávnenosti vám pripíšeme príslušnú odmenu.
					</div>

					<div class="absolute top-[-46px] right-[-30px]">
						<div class="relative w-[129px] h-[129px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="123" height="122" viewBox="0 0 123 122"
								 fill="none">
								<path
									d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z"
									fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571" />
								<defs>
									<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126"
													y2="102.866" gradientUnits="userSpaceOnUse">
										<stop stop-color="#FDBB47" />
										<stop offset="0.495" stop-color="white" />
										<stop offset="1" stop-color="#FDBB47" />
									</linearGradient>
								</defs>
							</svg>
							<div
								class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-sm font-bold text-white">
								Garancia pripísania odmeny
							</div>
						</div>
					</div>

					<img class="absolute top-[-113px] left-1/2 transform -translate-x-1/2 "
						 src="{$basePath}/new-design/settings-donkey.png" alt="oslik">
				</div>

				<div
					class="text-base md:text-lg leading-7 md:leading-[31.5px] font-medium text-dark-1 mb-5 w-[236px] md:w-auto pl-5 md:pl-0">
					Vaše posledné
					presmerovania do obchodov cez Tipli
				</div>

				<div class="mb-5">
					<div>
						<div class="md:hidden" data-ajax-append="true">
							{for $i = 1; $i <= 4; $i++}
								<div class="bg-white rounded-xl p-5 mb-5">
									<div class="flex justify-between">
										<div>
											<div
												class="border border-light-5 rounded-xl flex justify-center items-center w-[97px] h-[55px]">
												<img class="max-w-[58px] max-h-[38px]" alt="BENU Lékárna"
													 src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/wcwZNBuSvjwM88tPgLs6jgEgQ3Qmt3hPVObDZFw36P0/resize:fit:116:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzE1LnBuZw.png"
													 loading="lazy">
											</div>
										</div>
										<div class="flex flex-col">
											<div
												class="flex items-center gap-[7px] text-sm leading-[24.5px] mb-1">
												<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
													<path d="M8 5.5V8L11.1247 11.1253M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="#BDC2CC" stroke-width="1.5" stroke-linecap="round"/>
												</svg>
												Pred 3 minútami
											</div>
											{if true}
											<div class="text-xs leading-[21px] text-primary-orange underline">
												Nahlásiť chýbajúci nákup
											</div>
											{else}
											<!-- DISABLED -->
											<div class="text-xs leading-[21px] text-light-2 underline">
												Nahlásiť chýbajúci nákup
											</div>
											{/if}
										</div>
									</div>

									<div class="hidden">
										<div class="w-full h-px bg-light-5 my-5 mb-3"></div>
										<div>
											<div class="flex justify-between mb-2">
												<div class="text-xs leading-[21px] text-dark-1">Stav</div>
												<div class="flex items-center gap-2 text-sm leading-[24.5px] font-medium">
													Zamítnuta
													<svg id="reward-canceled-tooltip" xmlns="http://www.w3.org/2000/svg"
														 width="16" height="16" viewBox="0 0 16 16" fill="none">
														<path
															d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
															stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
															stroke-linejoin="round"></path>
														<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF"
															  stroke-miterlimit="10" stroke-linecap="round"
															  stroke-linejoin="round"></path>
														<circle cx="7.92443" cy="4.62951" r="0.846307"
																fill="#ADB3BF"></circle>
													</svg>
												</div>
											</div>
											<div class="flex justify-between mb-2">
												<div class="text-xs leading-[21px] text-dark-1">
													Výše objednávky
												</div>
												<div class="flex items-center gap-2 text-sm leading-[24.5px] text-dark-1">
													10 000&nbsp;Kč
													<svg id="reward-tooltip" xmlns="http://www.w3.org/2000/svg" width="16"
														 height="16" viewBox="0 0 16 16" fill="none">
														<path
															d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
															stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round"
															stroke-linejoin="round"></path>
														<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF"
															  stroke-miterlimit="10" stroke-linecap="round"
															  stroke-linejoin="round"></path>
														<circle cx="7.92443" cy="4.62951" r="0.846307"
																fill="#ADB3BF"></circle>
													</svg>

												</div>
											</div>
										</div>
									</div>
								</div>
							{/for}
						</div>

						<div class="hidden md:block mt-5 mb-5">
							<div class="not-prose relative bg-[#F8F8F9] rounded-xl ">
								<div class="relative rounded-xl overflow-auto">
									<div>
										<table class="border-collapse table-auto w-full text-sm">
											<thead>
											<tr class="text-xs">
												<th class="font-medium pt-[18px] pb-[19px] px-[50px] text-center">
													Obchod
												</th>
												<th class="font-medium pt-[18px] pb-[19px] px-[26px] text-center">
													Čas návštevy
												</th>
												<th class="font-medium pt-[18px] pb-[19px] px-[25px] text-center">
													Informácia o nákupe
												</th>
											</tr>
											</thead>
											<tbody class="bg-white">

											{for $i = 1; $i <= 4; $i++}
												<tr class="border-b border-light-4 h-[58px] text-sm leading-[24.5px] text-dark-1 text-center bg-white">
													<td class="pl-5">
														<img
															src="https://www.tipli.cz/upload/images/shops-shop-logo/788952.svg"
															title="AliExpress" alt="AliExpress"
															class="max-w-[100px] max-h-[40px] w-full m-auto">
													</td>
													<td>
														<div class="text-center">
															Pred 3 minútami
														</div>
													</td>
													<td>
														{if true}
														<div
															class="cursor-pointer inline-flex flex-start text-primary-orange text-sm leading-[24.5px] font-medium py-2 px-5 rounded-lg border border-primary-orange/20 xl:hover:bg-primary-orange xl:hover:text-white">
															Nahlásiť chýbajúci nákup
														</div>
														{else}
														<!-- DISABLED -->
														<div
															id="reward-canceled-tooltip"
															style="background: #FAFAFB;"
															class="cursor-pointer inline-flex flex-start text-light-2 text-sm leading-[24.5px] font-medium py-2 px-5 rounded-lg border border-transparent">
															Nahlásiť chýbajúci nákup
														</div>
													{/if}

													</td>
												</tr>
											{/for}
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="flex justify-center">
						<button
							class="border w-full md:max-w-[294px] border-light-2 leading-[28px] font-medium rounded-xl pt-[15px] pb-[13px] xl:hover:bg-gray-gradient-hover">
							Ďalšie položky
						</button>
					</div>
				</div>
			</div>
		{else}
			<div class="grow">
				<div class="bg-white rounded-2xl p-[30px] md:py-10 text-center mb-10 relative mt-[28px]">
					<div
						class="flex items-center gap-3 text-primary-orange text-[20px] mdtext-[30px] font-bold leading-[32px] leading-[37px] mb-[15px]">
						<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
							<rect width="30" height="30" fill="url(#pattern0_1468_5636)"/>
							<defs>
								<pattern id="pattern0_1468_5636" patternContentUnits="objectBoundingBox" width="1" height="1">
									<use xlink:href="#image0_1468_5636" transform="scale(0.0138889)"/>
								</pattern>
								<image id="image0_1468_5636" width="72" height="72" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAC+lBMVEUAAADgx8fZsbLkoqPaUVninp/bVV1WVlaJFBW5m5vqh4q7T1N9FBW0WFnTXl+8ZWXFcHDlhYbhjo7W2t+mP0CcPj/Mbm3YkI+4wszfkpKoq7HQaGrgZ23Rhobel5dxcHGjR0e4X1/UdHPQe3rpgofgf3+yt7ywcnOxw9bdXWS9XmDLgoGpbm6kc3S3R0rYaWqou83cdnXwgYajgXuulJVxZWi8OEPHZWPCaWrScHDLf36PUlKrSUnrdHmcOj3idnzLd3ezW1u5x9TAYF2BcGx+Hh3id3qYPz+HhX+dj4mJYGDPWFtZVFW0v9BZWlrAy9VdYGGgQELBytLCQ0itU1LY3+XUV1y9xMWZg4SkkZPO2eHU4Op7d3NWWFnn6Ojm5ubk5ebk4+Ti4uLg4OHd3t/a2trc3NzV1dabHh/m6OjS0tO+vb3Ozs+lICHCwsOSMS9/JCSvIiPSUleDKyvlam3p6+zLy8vVSFDROkSRFxjt8PKpW1zIQEmvOz6OKCbhZWqrT0/FPESZODfY19i1UlLYTFLNSVHMNT+mLCzk3t7kyMjf1cPZXWPlX2DMRE3GPz23PDyvLS2iGBje0r7kqanngYJhXl6/OjuEHRybFRWHDw/moKHmcHLfW2LVQku5LS2IJSTk2Nnk09PmlZXleHjQTUaIMDDHxsfLwcK4tbXisK+4rK63j5CvgoTKVVvBP07QP0nDOUi8REfENEHHMj2ONTabLix7HyCUDQ3o4ODIycnCrq+zYWLGTlCvRUa4zN/lt7m3paaEfn/pZmnNPzrFMjG2HiDmzM3lwMDnjIzaiofYeXXwa2/ZXFriVVnFSUVyGBjcx8fPkpGIiImjgYf4goXehH+5dHWjaWu7LzW+IySkoqayoKF3eHr1dHnbamynODaqFBXc0dLWw77Ht7jYp6acm5yQjI7AgoH1+PjhoaP5k5RtXWJaOjuTKSeWKCeni41lZWXZtrf/pqbTgnl/bHC4aGhsT0+JT1ZQKirqyMiyo46SeVn/uba5leTDAAAAXnRSTlMACA8X/iD9/v78VP7+/fjhpGJE/v720zw4M/z7yU8o/v3xs5N0cBj7+OrQfSr8+9qxo5j+/f3858PBV1Lx4sO2bqR7+9/Zzp6ATDvr5NzLk3t3Ut7c1KTyzMWupJuVVYLR7AAADL1JREFUWMOF12dYU1cYB3D23oIyBHEjrtZVt9YOW7v3zt47IQQjBEIYggIByt57g4iCbNl7iaLgAveerdr5PH3vvSEBQfv/oNdw7u++5z3n3IjWtGjr21gvWWJto6v+RGfPG2j26Kg/0p2HjtHXnnan5hJGmLw5f4WBnp7BivlL9FWf2c85icbNXvWB/pLJMW+awPNezjJ7ex2T+alBafv3H4SUi+abYM+Yt+HxGSTrP8QebDJfL20/DNq/v9zLYL61jr39smnOT26Ou1YgDAIdP378WMRHzuhPVu10tXO1s7PbuQr9p80Kr3IYgz3OJ9hi1ydub0yV7L8r6EWelOblBQMPArTJfZ4WJm3b9sP327aBg8TBvbw8zSsoyCsNHeUj6i12/HIKtOrd8aA0GKFnYGAgivAHB61A18Fkybp1u4yNd61bt8TEQVcbRn4bpGeQmppq4FWOSEeDLVbbT4FMYFrApPr5+YWJgn2ObXpbV8vFZN37O8R+qal6JaVwr594x/vrrF20db9tbfUTi/0MgkE6BtIKE43jsFbPyytoINxPnCIG6OimL/RN1u0Qp+p5RUT4+PiXrvaH+ESIwsZ3rDPRX7rjckqKXxhUfgygI2VrbdTLvssgCJjWvhR0gM9Xu623WIh84Nbg4GDoWllZWgSI/vFj8f5lTluWgiRGK/cHCKRdk9tgaZie3kBr3+XLfWLkQV9ZbTaNjBJG+QdDV6FrsGv0gmARyiN8xq5Ec2WmmxevbcWgo+AcaXBaoipovoFeeN/IyAhAYSL/Ne7bQxjcqLGDaXqpqWFlDaaBEFPThuDgiPKD7cLo6sSq7e4f6RmIgiN8wIGUzdXHOm1hkNo38ttv9y73pYrGqmwVDEZIensaIKbey0uNi+sgxcalq73XVPjsbz8vTKyWKlxl/hEA+QBTUVFhYY1u1jdFiPMvQOERwsqNlVJG1PmDBmGm3qXFtXQKmUTuaqKQKTR6fnGpd6P/8fMd3OqkvBbvqOP+sBD+8ZGRkQ2LtJHDsza4tS88Z2SkNWiMMfTi0v1HwnYvUeDoRQmFRCAQiURSUUwokUQikylz8i+ONsaDJH3m+GwoUZiubFMq45VK0Vpkbg4fKQeC2pVKZXsHV/DngwV/CNtFpqPNLDJBFWJMcigKkoDiNI/KxjqujN568KcHI1rY3tamzM3NtUCOgXNDeroQCdduO//PB4qOtmDvDQVXSZiCx+OJydmheLjEqGvXN8hBqnzwoHK7XXV0enp6fFuuBbIpt67hRiOR7nSfkCY87ChvGGWTs5o6AUGCw+Gzu0OZcIXOs/PGNXrhqKzjCt8sSeG+00gqZYSETARuRV4U2xnSxMRqo71WtonV3PNppus5TCapKDkTUSAAxdagfwMVevfuVcoc3nrbjiuMpERbq71mZgmPqqq22yDN/trXyChJ8MXuNQxwggIfz/FkMpmHuouAwaBugJCA3BTbRIROSR7bpl+B4YErv3gv4eEj/tcuyPK/nWc29N62eW+FgDNg+pgCDtKY2E41lAMQmqzY7kwi2nNMCnnLZtt7Gb6Kt9EX8Va7off26SyqSIw+P1B2huLpiTya2ZmTTcRppoYlWdzEJGDSGVk67PI1i3T37fS124q939/eu0rL2kKYeH4gbPWcA+BA4KbWJtXtmVmqi0Pi7ho8HpNqV0eOCRO5Tku1Vu2Flw4m6Wjpz2+40jGQ4i05gEHINFKgkOnpbj2Ew6MSiTKH7b1pTBht+r4Lcr86Sy180r1GzJvVDqQpBv8S1FSExxaPxCtkzbnYeDReKIOjrwkUJAoTXR5fXYNBuNcGj6M5nj7NCyhYvelIhazsfX2NAx0KE/vdMy+o+auzhon7v0hOL2i58PTGjTr50YY1a6BL6ugs6hf7jfSWHsD9lZzcVfN6huS44EnLhuGeu6fopZuOmMrKFmla5LCiP8fvXn3BnTu4ruyc7EOvc1i34p603O49W5RJoRTK4ysiK5zmaVrdL06J7V7ueQc6lBmTIs7OfKXDXmD4ZNRpPLsTjjCFtTweJIuVk472ov4c8T2nZsSBdHaLu3A4CmWm4km7GWd46aLT2S4iHjYTLcA4Mn4wUjM3/bX9KeJ79VdRCFLTWYOjnL7EngHVthjG3eLRD4UyCeiupPLkyoaKhrUuKsjGot8vJXY5Dhz1ylPhnptUgDUhOj4xXHCBQyUy8QQMosHcBgcHnZzVu7G/v2+8BBymGiLX3oJZsKdMi37L0LCFTacQkL2NSWRaSbxyUBk22aTFoiBRa1ndNIhI4zguUBWFx6u6fLOWSsLj8GjgLUcmk+silbnK/sWAYF8jubnhpgXq44qGQKazT0NRPDwu6zCOAl1eUMyiIS8EzAEI5na9sS13sH+RNrYdtzQoc4PMC1TPUp8EIpVzAYoq9Dx8GHfTMA4OBRmG4DAGHFg2aoG8LTdXtAU7/bpzB+OVbeb5FBKRgGdC1EVRkKIu8Q7/6njpEtJlvKocZBScXGg3xxyghrn6KigyPb7du+BpUVFMTExychNRXRSJymE7tqxff5otgS4jn2DpSk6+ezcmpqjoWj1MbVADRUWle9f1nD2bkyJuDY8NVZUE0yBQqNTCf/4ppFGQciYlYnc48l+gnLM9N+rbBuMr1JBMCFDBqVOH0GRNeY8ARWL9/TcLZfDqZKIDT0Hq2yMjGwHCmi3jRnXIC2hk+FJkQjQtR28nX7tGRrqjCbQS2o3sSIl5u2yica6u6qg1ckOEjdepIBGJ2Pfi1KIIv/5K0CBTFo1CDSgITJ8Iadyio9qQCCQrDqBRyBoIqMmyDs8OwerT6yqiJkLkb2pj0MpARgg3yphOpUBJBI2k2lgaSONg2zGAbhwZMlEVCDsbzVZzgISlLJgbBqkzK0RQz4zOuR1Vpah6x3ryBWlZxWAIvfPVc2NOL2A6hPx4cmb5z4VVVYq3Jl+ROpttGVXRsmI6lQYQHt8Zk/VKCB6TGdNFIKAFserWMBQPbdETgnVbLq1KjCrlBNDIOHxmsji8i/kaqDM8PDuLCAWxJCVRDIVv4GLtSch54cNHMDcei0oJbYoNz2kizlKRpj9dseEpMQE0Oof3PPphFf8dE80vapsV0kfRMmMO/VRyTk5MJrLdZoMIWJihRTl+PU9ZEuNIqcLXFva1Oivl0kdSYT37Rk9PTJZq+EyIoAosSNbd3t7h6+e4vnxfbPHV68aX+ibKSoaHr6m2NwS9UwVhyNSlvzZ8uyRSyvdF1kwT7cWuRg+lXO9iCZ06KeFJoVgBABGxi9BQvHoLsfLr3onO4Geg21qTeZZ8I9/qqPU8jloiZsP7ZAqEfTJ5yFi17NtRRkhBNiigKUme4JuQKNvAw2qCHd4VHkucrEjVteyBGILaMa6Q+vIzAqGgaXFxV5j5GkXLL6ISHLrQ2PBDeKyQohskTMoSi7MmnWbzxARFhu1aB62XsnuhR0JGEte7mSdhBVAppKLwbCIS2MRnezPx2HXMQDJ6xPLZzfVcI34G/52lWi9Hx0p+ApUusmtZdOrVnJwsIgm9PRQg7FcIUubZ8VOY85ybxPeFielozYjLZruhjARUyufQb4wXwfcfCYJCyAUZ8nR8mCXhFV6sD0mCBrnOdZnBoCunGBKcSOLKNxTyrv/ecxVaRUEwWk9vFqJQKHDeh53q2IUl5lxwBLawYrNmq2XlHyWV8JvB+mLj29dZ9AAqlQYYref3q/AnjQqbh3P93O3m0kZGEv/MH4q34D30Cmk5j3BGYFYd4l1qzM6XcMACLAAg+JMOjITHNi6tj5Im5K12c1xurf0qyP6bA+z7L/LMkhInRmF+QHFYLDpr+HcOnQVKLY9daLxcxjDKqHyx8YLbJ/avcpb9eIDzLO7FkEBglsSQ1ZcUs3n5tRKOZPh2gURSm89jF5c8b+QaJfD5ggeGzxxPvrFsdkf7swOUlrh3vSuHTnggVIh8eUlzIZvHMy7hQS3NJecCJxhGCbBaAoX3x4Ytbic/05kV2uPpeSvu3Q93W7p6oJSZESNEFlh/Dk19oCyEIU0AxlfAl1uu3Pdp3C03tz2zdelLwoEL4MA7xWqhnQAoD8EJwKQMNFKjhASBBx+q4bsutII3x76P79886fblbI2+U3x/ATgwR2erha55J04IPPJAE5yAK4GHR15lnocggy9fuMgZndG2jzdeODlLw/fAgm38QFWqjo2VpbldngDBgMgD0AOwSldzSytgsHxuCNJnM6APTm8ER9N5l91gye0qKwFBylG4ykHZ7TJlyOeG91tWzlz7Xz79QGf6Kuo7r7Ta7G65EGLpvtlqpbNGwer+/NOfl2mGqy+WYZfTMV19BxtnZxsHfd1pP515y3/azmW35li5jAAAAABJRU5ErkJggg=="/>
							</defs>
						</svg>
						Čakáme na váš prvý nákup
					</div>
					<div class="text-sm leading-[24.5px] text-dark-1 w-full max-w-[461px] text-start">
						Po nákupe sa vaša odmena zobrazí v prehľade odmien obvykle do 48h.
						<span class="font-bold">Vyskúšajte urobiť svoj prvý nákup cez Tipli a získajte odmenu.</span>
					</div>

					<div class="absolute top-[59px] right-[-20px] md:top-[20px] md:right-[190px]">
						<div class="relative">
							<svg class="w-[92px] h-[92px] md:w-[129px] md:h-[129px]" xmlns="http://www.w3.org/2000/svg" width="123" height="122"
								 viewBox="0 0 123 122"
								 fill="none">
								<path
									d="M56.3043 3.25694C59.2387 0.548694 63.7613 0.548691 66.6957 3.25693L73.2599 9.3153C75.172 11.08 77.7981 11.8511 80.3607 11.4003L89.1583 9.85257C93.0911 9.16071 96.8957 11.6058 97.9001 15.4705L100.147 24.1161C100.801 26.6344 102.594 28.7029 104.993 29.7091L113.231 33.1634C116.914 34.7076 118.792 38.8214 117.548 42.6157L114.764 51.1035C113.953 53.5758 114.342 56.285 115.817 58.4287L120.879 65.7884C123.143 69.0783 122.499 73.5548 119.401 76.0739L112.47 81.7092C110.451 83.3507 109.314 85.8403 109.395 88.441L109.675 97.3693C109.8 101.361 106.839 104.778 102.87 105.223L93.9932 106.216C91.4074 106.506 89.1049 107.985 87.7674 110.217L83.1759 117.88C81.1234 121.305 76.784 122.579 73.2055 120.807L65.2003 116.844C62.8685 115.689 60.1315 115.689 57.7997 116.844L49.7945 120.807C46.216 122.579 41.8766 121.305 39.8241 117.88L35.2326 110.217C33.8951 107.985 31.5926 106.506 29.0068 106.216L20.1295 105.223C16.1611 104.778 13.1995 101.361 13.3246 97.3693L13.6046 88.441C13.6861 85.8403 12.5491 83.3507 10.5303 81.7092L3.59937 76.0739C0.501081 73.5548 -0.142546 69.0783 2.12052 65.7884L7.18303 58.4287C8.65764 56.285 9.04716 53.5758 8.23624 51.1035L5.45225 42.6157C4.20774 38.8214 6.08647 34.7076 9.76896 33.1634L18.0068 29.7091C20.4063 28.7029 22.1986 26.6344 22.8531 24.1161L25.0999 15.4706C26.1043 11.6058 29.9089 9.16071 33.8416 9.85257L42.6393 11.4003C45.2019 11.8511 47.828 11.08 49.7401 9.31531L56.3043 3.25694Z"
									fill="#FDBB47" stroke="url(#paint0_linear_1468_3906)" stroke-width="0.678571" />
								<defs>
									<linearGradient id="paint0_linear_1468_3906" x1="3.54074" y1="44.7646" x2="126"
													y2="102.866" gradientUnits="userSpaceOnUse">
										<stop stop-color="#FDBB47" />
										<stop offset="0.495" stop-color="white" />
										<stop offset="1" stop-color="#FDBB47" />
									</linearGradient>
								</defs>
							</svg>
							<div
								class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-[10px] md:text-sm font-bold text-white">
								Garancia pripísania odmeny
							</div>
						</div>
					</div>

					<img
						class="absolute bottom-[-46px] right-[-19px] md:top-[-10px] md:right-[90px] md:bottom-0 w-[75px] h-[122px] md:w-[144px] md:h-[233px]"
						 src="{$basePath}/new-design/tipli-oslik-redirect-2.png" alt="oslik">
				</div>

				<div class="text-base md:text-lg leading-7 md:leading-[31.5px] font-medium text-dark-1 mb-5 w-[236px] md:w-auto pl-5 md:pl-0">
					Vaše posledné presmerovania do obchodov cez Tipli
				</div>

				<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-2.5 md:gap-5 mb-5">
					{for $i = 1; $i <= 10; $i++}
					<a
						class="hidden md:flex bg-white flex-col items-center justify-center rounded-xl cursor-pointer shadow-hover p-2 border border-transparent" href="#">
						<div class="h-[92px] px-4 py-4 flex items-center justify-center">
							<img class="max-w-[100px] max-h-[60px] w-full" loading="lazy" alt="Dr. Max" src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/pOWRfFPIggziHgBYTt81s_lz2xkALZjL_KPW-VC5pTw/resize:fit:282:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuc2svdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vODA0NDYucG5n.png">
						</div>
						<img class="m-auto" src="/new-design/hp-icons/smaller-wave.svg" alt="wave" loading="lazy">

						<div class="h-[54px] text-center my-[18px] leading-[25px] text-sm lg:text-base">
							<span class="_upTo">až</span> <span class="_value">3</span>&nbsp;<span class="_symbol">%</span> <span class="_suffix">naspäť z nákupu</span>
						</div>
					</a>

					<a href="#" class="md:hidden group flex gap-4 items-center">
						<div
							class="border border-light-5 bg-white rounded-xl flex justify-center items-center w-[97px] h-[55px]">
							<img class="max-w-[58px] max-h-[38px]" alt="Raj nechtov" src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/fEDG1LH4no5Fjvm_ygn0sc8dpIRj80O5LEo1LukmfLg/resize:fit:116:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuc2svdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzU3MDYucG5n.png" loading="lazy">
						</div>

						<div class="text-sm text-dark-1 leading-[18.75px]">
							<div class="leading-[24.5px] mb-1.5 line-clamp-1">Raj nechtov</div>
							<div class="similar-shop__value">
								<span class="_value">2,5</span>&nbsp;<span class="_symbol">%</span> <span class="_suffix">naspäť z nákupu</span>
							</div>
						</div>
					</a>
					{/for}
				</div>

				<div>
					<div class="flex flex-col md:flex-row items-center justify-center gap-2.5 md:gap-[19px]">
						<a href="#"
						   class="w-full md:w-auto inline-flex items-center justify-center gap-[11px] text-base font-bold text-white leading-7 bg-orange-gradient pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
							Zobraziť všetky obchody
						</a>
						<a href="#"
						   class="w-full md:w-auto inline-flex items-center justify-center gap-[11px] text-base font-bold text-dark-1 leading-7 border border-bg-light-4 pt-[15px] pb-[13px] pl-[47px] pr-[37px] rounded-xl cursor-pointer xl:hover:bg-gray-gradient-hover">
							Potrebujete s niečím pomôcť?
						</a>
					</div>
				</div>
			</div>
		{/if}
		</div>
	</div>
</div>
</body>
</html>

<style>
	.bg-img {
		left: 0;
		z-index: 10;
		position: absolute;
		width: 100%;
		height: 213px;
		background-image: url(/new-design/bg-top-settings.svg);
		background-repeat: no-repeat;
		background-size: cover;
		background-position: bottom;
	}

	@media only screen and (max-width: 768px) {
		.bg-img {
			background-image: url(/new-design/bg-mobile-settings.svg);
			background-repeat: no-repeat;
			height: 350px;
		}
	}
</style>

<script>
	tippy('#reward-canceled-tooltip', {
		animation: 'scale',
		content: 'Čaká na info o nákupe od Dr.Max. Zostáva ešte 47h:35m',
		theme: 'transaction-tooltip',
		placement: 'bottom',
	});
</script>
