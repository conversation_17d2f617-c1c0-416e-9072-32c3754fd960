{block title}{_'front.addon.title'}{/block}
{block keywords}{_'front.addon.metaKeywords'}{/block}
{block description}{_'front.addon.metaDescription'}{/block}

{block #scripts}
    <script type="text/javascript" src="{$basePath}/js/addon/addon.default.js?v=0.5" defer></script>
{/block}

{block footerBackgroundColor}bg-light-6{/block}

{block content}

{var $isSupportedBrowser = $addonLink !== null}

<div class="bg-light-6 relative z-10">
	<div class="container p-5">
		<div class="mt-[30px] lg:mt-[119px] flex flex-col lg:flex-row items-center">
			<div class="lg:w-1/2">
				<div class="lg:w-[425px] max-w-full">
					<h1 class="text-[40px] font-bold text-dark-1 leading-[50px]">
						{_'newFront.addon.tier1.title'|noescape}
					</h1>
					<div class="text-dark-1 leading-7 mb-5">{_'newFront.addon.tier1.text', ['count' => ($countOfCashbackShops |amount)] |noescape}</div>
					<a n:if="$isSupportedBrowser" href="{$addonLink}" target="_blank" class="inline-flex items-center gap-[11px] text-white font-bold leading-7 pt-[15px] pb-[13px] pl-[58px] bg-orange-gradient pr-[49px] rounded-xl mb-5 cursor-pointer xl:hover:bg-orange-gradient-hover">
						{_'newFront.addon.tier1.button'}
						<svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
							<path d="M4.19576 15.0328L14.0938 5.13477" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</a>

					{if $isSupportedBrowser === false}
						<div class='md:border md:border-primary-orange bg-pastel-orange-light rounded-md mt-2 mb-4 p-3'>
							{_'newFront.addon.tier1.noSupport'}
						</div>
					{/if}

					<div class="leading-[24.5px] text-dark-4 text-sm">
						<svg class="mb-1" xmlns="http://www.w3.org/2000/svg" width="136" height="24" viewBox="0 0 136 24" fill="none">
							<path d="M11.0489 2.92705C11.3483 2.00574 12.6517 2.00574 12.9511 2.92705L14.4697 7.60081C14.6035 8.01284 14.9875 8.2918 15.4207 8.2918H20.335C21.3037 8.2918 21.7065 9.53141 20.9228 10.1008L16.947 12.9894C16.5966 13.244 16.4499 13.6954 16.5838 14.1074L18.1024 18.7812C18.4017 19.7025 17.3472 20.4686 16.5635 19.8992L12.5878 17.0106C12.2373 16.756 11.7627 16.756 11.4122 17.0106L7.43648 19.8992C6.65276 20.4686 5.59828 19.7025 5.89763 18.7812L7.41623 14.1074C7.55011 13.6954 7.40345 13.244 7.05296 12.9894L3.07722 10.1008C2.29351 9.53141 2.69628 8.2918 3.66501 8.2918H8.57929C9.01252 8.2918 9.39647 8.01284 9.53035 7.60081L11.0489 2.92705Z" fill="#FDBB47"/>
							<path d="M39.0489 2.92705C39.3483 2.00574 40.6517 2.00574 40.9511 2.92705L42.4697 7.60081C42.6035 8.01284 42.9875 8.2918 43.4207 8.2918H48.335C49.3037 8.2918 49.7065 9.53141 48.9228 10.1008L44.947 12.9894C44.5966 13.244 44.4499 13.6954 44.5838 14.1074L46.1024 18.7812C46.4017 19.7025 45.3472 20.4686 44.5635 19.8992L40.5878 17.0106C40.2373 16.756 39.7627 16.756 39.4122 17.0106L35.4365 19.8992C34.6528 20.4686 33.5983 19.7025 33.8976 18.7812L35.4162 14.1074C35.5501 13.6954 35.4034 13.244 35.053 12.9894L31.0772 10.1008C30.2935 9.53141 30.6963 8.2918 31.665 8.2918H36.5793C37.0125 8.2918 37.3965 8.01284 37.5303 7.60081L39.0489 2.92705Z" fill="#FDBB47"/>
							<path d="M67.0489 2.92705C67.3483 2.00574 68.6517 2.00574 68.9511 2.92705L70.4697 7.60081C70.6035 8.01284 70.9875 8.2918 71.4207 8.2918H76.335C77.3037 8.2918 77.7065 9.53141 76.9228 10.1008L72.947 12.9894C72.5966 13.244 72.4499 13.6954 72.5838 14.1074L74.1024 18.7812C74.4017 19.7025 73.3472 20.4686 72.5635 19.8992L68.5878 17.0106C68.2373 16.756 67.7627 16.756 67.4122 17.0106L63.4365 19.8992C62.6528 20.4686 61.5983 19.7025 61.8976 18.7812L63.4162 14.1074C63.5501 13.6954 63.4034 13.244 63.053 12.9894L59.0772 10.1008C58.2935 9.53141 58.6963 8.2918 59.665 8.2918H64.5793C65.0125 8.2918 65.3965 8.01284 65.5303 7.60081L67.0489 2.92705Z" fill="#FDBB47"/>
							<path d="M95.0489 2.92705C95.3483 2.00574 96.6517 2.00574 96.9511 2.92705L98.4697 7.60081C98.6035 8.01284 98.9875 8.2918 99.4207 8.2918H104.335C105.304 8.2918 105.706 9.53141 104.923 10.1008L100.947 12.9894C100.597 13.244 100.45 13.6954 100.584 14.1074L102.102 18.7812C102.402 19.7025 101.347 20.4686 100.564 19.8992L96.5878 17.0106C96.2373 16.756 95.7627 16.756 95.4122 17.0106L91.4365 19.8992C90.6528 20.4686 89.5983 19.7025 89.8976 18.7812L91.4162 14.1074C91.5501 13.6954 91.4034 13.244 91.053 12.9894L87.0772 10.1008C86.2935 9.53141 86.6963 8.2918 87.665 8.2918H92.5793C93.0125 8.2918 93.3965 8.01284 93.5303 7.60081L95.0489 2.92705Z" fill="#FDBB47"/>
							<path d="M123.049 2.92705C123.348 2.00574 124.652 2.00574 124.951 2.92705L126.47 7.60081C126.604 8.01284 126.987 8.2918 127.421 8.2918H132.335C133.304 8.2918 133.706 9.53141 132.923 10.1008L128.947 12.9894C128.597 13.244 128.45 13.6954 128.584 14.1074L130.102 18.7812C130.402 19.7025 129.347 20.4686 128.564 19.8992L124.588 17.0106C124.237 16.756 123.763 16.756 123.412 17.0106L119.436 19.8992C118.653 20.4686 117.598 19.7025 117.898 18.7812L119.416 14.1074C119.55 13.6954 119.403 13.244 119.053 12.9894L115.077 10.1008C114.294 9.53141 114.696 8.2918 115.665 8.2918H120.579C121.013 8.2918 121.396 8.01284 121.53 7.60081L123.049 2.92705Z" fill="#FDBB47"/>
							<mask id="mask0_783_3312" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="114" y="2" width="20" height="19">
								<path d="M123.049 2.92705C123.348 2.00574 124.652 2.00574 124.951 2.92705L126.47 7.60081C126.604 8.01284 126.987 8.2918 127.421 8.2918H132.335C133.304 8.2918 133.706 9.53141 132.923 10.1008L128.947 12.9894C128.597 13.244 128.45 13.6954 128.584 14.1074L130.102 18.7812C130.402 19.7025 129.347 20.4686 128.564 19.8992L124.588 17.0106C124.237 16.756 123.763 16.756 123.412 17.0106L119.436 19.8992C118.653 20.4686 117.598 19.7025 117.898 18.7812L119.416 14.1074C119.55 13.6954 119.403 13.244 119.053 12.9894L115.077 10.1008C114.294 9.53141 114.696 8.2918 115.665 8.2918H120.579C121.013 8.2918 121.396 8.01284 121.53 7.60081L123.049 2.92705Z" fill="#ECC600"/>
							</mask>
							<g mask="url(#mask0_783_3312)">
								<path d="M124 0L133.682 1.38312C134.157 1.45096 134.516 1.84639 134.539 2.32551L135.435 21.1415C135.467 21.8153 134.837 22.327 134.184 22.1567L124.748 19.695C124.307 19.5802 124 19.1825 124 18.7274V0Z" fill="#ECEDF0"/>
							</g>
						</svg>
						<span class="font-bold text-dark-1">{_newFront.addon.rating.score}</span> ({_newFront.addon.rating.users})
					</div>
				</div>
			</div>

			<div class="mt-12 lg:mt-0 lg:w-1/2">
				<div class="relative">
					<img class="lg:w-[567]  lg:h-[350px]" src="{$basePath}/new-design/01_notebook.png" alt="pocitac">
					<div class="absolute top-[56px] lg:top-[26px] left-[-2px] lg:left-[-42px] w-[186px] bg-white p-5 rounded-[10px] rotate-3">
						<div class="flex justify-between">
							{if $localization->isHungarian()}
								<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
							{else}
								<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
									<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
								</svg>
							{/if}
							<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
								<path d="M1.17053 9.0205L9.32664 1.80457M8.85656 9.4906L1.64064 1.33447" stroke="#80899C" stroke-width="0.888507" stroke-linecap="round"/>
							</svg>
						</div>

						<div class="text-center bg-pastel-green-light py-[22px] my-[9px] rounded-[10px]">
							<div class="text-[56px] font-black leading-[59px] text-secondary-green tracking-[-4px]">10 %</div>
							<div class="text-sm leading-[27px] text-dark-1">{_newFront.addon.sampleBox.reward}</div>
						</div>

						<a n:if="$isSupportedBrowser" href="{$addonLink}" target="_blank" class="block text-center w-full font-bold py-3.5 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-xs md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
							{_newFront.addon.sampleBox.cta}
						</a>

						<div class="text-center text-[10px] leading-[16px] underline text-dark-2">{_newFront.addon.sampleBox.coupons}</div>
					</div>

					<div class="absolute top-[-38px] right-[18px] lg:right-[52px]">
						<div class="relative w-[147px] h-[146px]">
							<svg xmlns="http://www.w3.org/2000/svg" width="147" height="146" viewBox="0 0 147 146" fill="#fff">
								<g filter="url(#filter0_b_783_3299)">
									<path d="M66.5981 3.09626C70.3286 -0.346777 76.0782 -0.346774 79.8087 3.09626L87.4954 10.1905C89.7284 12.2514 92.7954 13.152 95.7882 12.6255L106.09 10.8132C111.09 9.93357 115.927 13.042 117.204 17.9554L119.835 28.0792C120.599 31.0202 122.692 33.4359 125.494 34.611L135.141 38.656C139.822 40.6191 142.211 45.8491 140.629 50.6729L137.369 60.6119C136.422 63.4993 136.877 66.6633 138.599 69.1668L144.527 77.7849C147.404 81.9675 146.586 87.6585 142.647 90.8611L134.531 97.4599C132.173 99.3769 130.845 102.285 130.94 105.322L131.268 115.777C131.427 120.851 127.662 125.196 122.617 125.761L112.222 126.924C109.202 127.262 106.513 128.99 104.951 131.597L99.5744 140.569C96.9649 144.924 91.4483 146.544 86.8988 144.291L77.5248 139.65C74.8016 138.301 71.6052 138.302 68.882 139.65L59.508 144.291C54.9585 146.544 49.4418 144.924 46.8324 140.569L41.4558 131.597C39.8939 128.99 37.2048 127.262 34.185 126.924L23.7898 125.761C18.7447 125.196 14.9795 120.851 15.1386 115.777L15.4664 105.322C15.5616 102.285 14.2338 99.3769 11.876 97.4599L3.76007 90.8611C-0.178839 87.6585 -0.997086 81.9675 1.87999 77.7849L7.8081 69.1668C9.53026 66.6633 9.98516 63.4993 9.03811 60.6119L5.77811 50.6729C4.19594 45.8491 6.5844 40.6191 11.266 38.656L20.9123 34.611C23.7147 33.4359 25.8079 31.0202 26.5722 28.0792L29.2032 17.9554C30.4801 13.042 35.317 9.93357 40.3167 10.8132L50.6186 12.6255C53.6114 13.152 56.6784 12.2514 58.9114 10.1905L66.5981 3.09626Z" fill="white" fill-opacity="0.95"/>
									<path d="M66.9372 3.46369C70.4762 0.197414 75.9306 0.197416 79.4696 3.46369L87.1563 10.5579C89.5039 12.7247 92.7284 13.6714 95.8748 13.1179L106.177 11.3056C110.92 10.4712 115.508 13.42 116.72 18.0811L119.351 28.2049C120.154 31.2969 122.355 33.8367 125.301 35.0721L134.947 39.1171C139.389 40.9795 141.655 45.9409 140.154 50.517L136.894 60.4561C135.898 63.4917 136.376 66.8181 138.187 69.4502L144.115 78.0683C146.844 82.0361 146.068 87.435 142.331 90.4732L134.215 97.072C131.737 99.0874 130.341 102.144 130.441 105.337L130.768 115.792C130.919 120.606 127.347 124.728 122.561 125.264L112.166 126.427C108.991 126.783 106.164 128.599 104.522 131.34L99.1455 140.312C96.67 144.443 91.4366 145.98 87.1207 143.843L77.7467 139.202C74.8837 137.784 71.5231 137.784 68.6601 139.202L59.2861 143.843C54.9702 145.98 49.7368 144.443 47.2613 140.312L41.8847 131.34C40.2426 128.599 37.4155 126.783 34.2406 126.427L23.8454 125.264C19.0593 124.728 15.4875 120.606 15.6384 115.792L15.9662 105.337C16.0663 102.144 14.6702 99.0874 12.1915 97.072L4.07549 90.4732C0.338809 87.435 -0.437431 82.0361 2.29194 78.0683L8.22005 69.4502C10.0306 66.8181 10.5089 63.4917 9.51321 60.4561L6.25321 50.517C4.75227 45.9409 7.01811 40.9795 11.4594 39.1171L21.1057 35.0721C24.0519 33.8367 26.2526 31.2969 27.0561 28.2049L29.6871 18.0811C30.8985 13.42 35.487 10.4712 40.2301 11.3056L50.532 13.1179C53.6784 13.6714 56.9028 12.7247 59.2505 10.5579L66.9372 3.46369Z" stroke="white"/>
								</g>
								<defs>
									<filter id="filter0_b_783_3299" x="-19.8359" y="-19.4858" width="186.078" height="184.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
										<feFlood flood-opacity="0" result="BackgroundImageFix"/>
										<feGaussianBlur in="BackgroundImageFix" stdDeviation="10"/>
										<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_783_3299"/>
										<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_783_3299" result="shape"/>
									</filter>
								</defs>
							</svg>
							<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-bold text-secondary-green">
								{_'newFront.addon.tier1.smallTitle'|noescape}
							</div>
						</div>
					</div>
					<div class="hidden lg:block absolute right-[-640px] top-[-710px] left-[26px] z-[-1]">
						<img src="{$basePath}/new-design/application-circle.png" alt="circle">
					</div>
				</div>
			</div>
		</div>

		<div class="relative mt-[203px]">
			<div class="flex flex-col lg:flex-row items-center justify-center gap-[26px] lg:gap-[276px]">
				<div class="w-[271px] bg-white p-5 rounded-[10px] relative z-20 -rotate-3">
					<div class="flex justify-between">
						{if $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
						{else}
							<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						{/if}
						<svg class="rotate-3" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path d="M2.10689 12.7403L12.6217 0.855518M13.3067 12.0553L1.42189 1.54053" stroke="#80899C" stroke-width="1.2947" stroke-linecap="round"/>
						</svg>
					</div>

					<div class="text-center bg-pastel-green-light py-[33px] my-[9px] rounded-[10px]">
						<div class="text-[82px] font-black leading-[86px] text-secondary-green tracking-[-4px]">10 %</div>
						<div class="text-[20px] text-dark-1 leading-[40px]">
							{_newFront.addon.sampleBox.reward}
						</div>
					</div>

					<svg class="hidden lg:block absolute right-[-220px] top-[113px]" xmlns="http://www.w3.org/2000/svg" width="189" height="183" viewBox="0 0 189 183" fill="none">
						<path d="M118.002 38.4998L118.941 38.1566L118.002 38.4998ZM139.322 32.3244L140.317 32.4234L139.322 32.3244ZM0.843421 176.252C0.428075 175.888 0.386459 175.256 0.750472 174.841L6.68241 168.072C7.04643 167.657 7.67822 167.615 8.09357 167.979C8.50891 168.343 8.55053 168.975 8.18652 169.39L2.91368 175.407L8.93009 180.68C9.34544 181.044 9.38705 181.675 9.02304 182.091C8.65902 182.506 8.02723 182.548 7.61188 182.184L0.843421 176.252ZM187.504 69.4998C188.056 69.4998 188.504 69.9475 188.504 70.4998C188.504 71.052 188.056 71.4998 187.504 71.4998L187.504 69.4998ZM117.062 38.843C113.577 29.3027 113.493 20.838 115.307 14.3036C117.112 7.80352 120.833 3.11859 125.041 1.35045C127.162 0.4593 129.409 0.313968 131.535 1.08832C133.656 1.86077 135.555 3.51101 137.061 6.03028C140.054 11.0365 141.592 19.6139 140.317 32.4234L138.327 32.2254C139.585 19.5798 138.013 11.5188 135.344 7.05665C134.02 4.84169 132.45 3.55019 130.851 2.96754C129.256 2.3868 127.532 2.47303 125.816 3.19427C122.349 4.6509 118.935 8.71402 117.234 14.8387C115.543 20.9289 115.583 28.9655 118.941 38.1566L117.062 38.843ZM140.317 32.4234C137.506 60.6763 123.843 95.2811 100.402 123.522C76.95 151.777 43.6532 173.726 1.56824 176.497L1.4368 174.502C42.8532 171.774 75.6761 150.18 98.8633 122.245C122.062 94.2955 135.558 60.0626 138.327 32.2254L140.317 32.4234ZM187.504 71.4998C162.399 71.4998 146.134 67.0604 135.454 60.7265C124.747 54.3757 119.74 46.1708 117.062 38.843L118.941 38.1566C121.492 45.1397 126.238 52.9348 136.475 59.0063C146.74 65.0946 162.612 69.4998 187.504 69.4998L187.504 71.4998Z" fill="#D7DBE0"/>
					</svg>

					<a n:if="$isSupportedBrowser" href="{$addonLink}" target="_blank" class="block text-center w-full font-bold py-3.5 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-xs md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
						{_newFront.addon.sampleBox.cta}
					</a>

					<div class="text-center text-[13px] leading-[24px] underline text-dark-2">
						{_newFront.addon.sampleBox.coupons}
					</div>
				</div>

				<div class="w-[315px] max-w-full">
					<h2 class="text-[40px] leading-[50px] text-dark-1 font-bold mb-5">
						{_'newFront.addon.tier2.title'}
					</h2>
					<div class="text-dark-1 leading-7">
						{_'newFront.addon.tier2.text' |noescape}
					</div>
				</div>
			</div>

			<div class="absolute top-[12px] left-[282px] lg:left-[432px]">
				<div class="relative w-[147px] h-[146px]">
					<svg xmlns="http://www.w3.org/2000/svg" width="147" height="146" viewBox="0 0 147 146" fill="#66B940">
						<g filter="url(#filter0_b_783_3299)">
							<path d="M66.5981 3.09626C70.3286 -0.346777 76.0782 -0.346774 79.8087 3.09626L87.4954 10.1905C89.7284 12.2514 92.7954 13.152 95.7882 12.6255L106.09 10.8132C111.09 9.93357 115.927 13.042 117.204 17.9554L119.835 28.0792C120.599 31.0202 122.692 33.4359 125.494 34.611L135.141 38.656C139.822 40.6191 142.211 45.8491 140.629 50.6729L137.369 60.6119C136.422 63.4993 136.877 66.6633 138.599 69.1668L144.527 77.7849C147.404 81.9675 146.586 87.6585 142.647 90.8611L134.531 97.4599C132.173 99.3769 130.845 102.285 130.94 105.322L131.268 115.777C131.427 120.851 127.662 125.196 122.617 125.761L112.222 126.924C109.202 127.262 106.513 128.99 104.951 131.597L99.5744 140.569C96.9649 144.924 91.4483 146.544 86.8988 144.291L77.5248 139.65C74.8016 138.301 71.6052 138.302 68.882 139.65L59.508 144.291C54.9585 146.544 49.4418 144.924 46.8324 140.569L41.4558 131.597C39.8939 128.99 37.2048 127.262 34.185 126.924L23.7898 125.761C18.7447 125.196 14.9795 120.851 15.1386 115.777L15.4664 105.322C15.5616 102.285 14.2338 99.3769 11.876 97.4599L3.76007 90.8611C-0.178839 87.6585 -0.997086 81.9675 1.87999 77.7849L7.8081 69.1668C9.53026 66.6633 9.98516 63.4993 9.03811 60.6119L5.77811 50.6729C4.19594 45.8491 6.5844 40.6191 11.266 38.656L20.9123 34.611C23.7147 33.4359 25.8079 31.0202 26.5722 28.0792L29.2032 17.9554C30.4801 13.042 35.317 9.93357 40.3167 10.8132L50.6186 12.6255C53.6114 13.152 56.6784 12.2514 58.9114 10.1905L66.5981 3.09626Z" fill="white" fill-opacity="0.95"></path>
							<path d="M66.9372 3.46369C70.4762 0.197414 75.9306 0.197416 79.4696 3.46369L87.1563 10.5579C89.5039 12.7247 92.7284 13.6714 95.8748 13.1179L106.177 11.3056C110.92 10.4712 115.508 13.42 116.72 18.0811L119.351 28.2049C120.154 31.2969 122.355 33.8367 125.301 35.0721L134.947 39.1171C139.389 40.9795 141.655 45.9409 140.154 50.517L136.894 60.4561C135.898 63.4917 136.376 66.8181 138.187 69.4502L144.115 78.0683C146.844 82.0361 146.068 87.435 142.331 90.4732L134.215 97.072C131.737 99.0874 130.341 102.144 130.441 105.337L130.768 115.792C130.919 120.606 127.347 124.728 122.561 125.264L112.166 126.427C108.991 126.783 106.164 128.599 104.522 131.34L99.1455 140.312C96.67 144.443 91.4366 145.98 87.1207 143.843L77.7467 139.202C74.8837 137.784 71.5231 137.784 68.6601 139.202L59.2861 143.843C54.9702 145.98 49.7368 144.443 47.2613 140.312L41.8847 131.34C40.2426 128.599 37.4155 126.783 34.2406 126.427L23.8454 125.264C19.0593 124.728 15.4875 120.606 15.6384 115.792L15.9662 105.337C16.0663 102.144 14.6702 99.0874 12.1915 97.072L4.07549 90.4732C0.338809 87.435 -0.437431 82.0361 2.29194 78.0683L8.22005 69.4502C10.0306 66.8181 10.5089 63.4917 9.51321 60.4561L6.25321 50.517C4.75227 45.9409 7.01811 40.9795 11.4594 39.1171L21.1057 35.0721C24.0519 33.8367 26.2526 31.2969 27.0561 28.2049L29.6871 18.0811C30.8985 13.42 35.487 10.4712 40.2301 11.3056L50.532 13.1179C53.6784 13.6714 56.9028 12.7247 59.2505 10.5579L66.9372 3.46369Z" stroke="white"></path>
						</g>
						<defs>
							<filter id="filter0_b_783_3299" x="-19.8359" y="-19.4858" width="186.078" height="184.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
								<feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
								<feGaussianBlur in="BackgroundImageFix" stdDeviation="10"></feGaussianBlur>
								<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_783_3299"></feComposite>
								<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_783_3299" result="shape"></feBlend>
							</filter>
						</defs>
					</svg>
					<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-bold text-white">
						{_'newFront.addon.tier2.smallTitle'}
					</div>
				</div>
			</div>

			<div class="absolute z-10 top-[-23%] right-[40%] lg:right-[71%] ml-5 h-[560px] bg-light-5 w-[200%] rounded-r-[300px]"></div>
		</div>

		<div class="relative mt-[203px]">
			<div class="flex flex-col lg:flex-row items-center justify-center gap-[49px] lg:gap-[309px]">
				<div class="w-[315px] max-w-full">
					<h2 class="text-[40px] leading-[50px] text-dark-1 font-bold mb-5">
						{_'newFront.addon.tier3.title'}
					</h2>
					<div class="text-dark-1 leading-7">{_'newFront.addon.tier3.text' |noescape}</div>
				</div>

				<div class="w-[252px] bg-white p-5 rounded-[10px] relative z-20 rotate-3 shadow-lg right-[-70px] lg:right-auto">
					<div class="flex justify-between">
						{if $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
						{else}
							<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						{/if}
						<svg class="rotate-3" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path d="M2.10689 12.7403L12.6217 0.855518M13.3067 12.0553L1.42189 1.54053" stroke="#80899C" stroke-width="1.2947" stroke-linecap="round"/>
						</svg>
					</div>

					<div class="text-center bg-pastel-green-light pt-[33px] my-[9px] rounded-[10px]">
						<div class="inline-block text-[21px] font-medium leading-[21px] text-secondary-green mb-2 border border-secondary-green rounded-lg py-1 px-3">25NIKE</div>
						<div class="inline-block text-[21px] font-medium leading-[21px] text-secondary-green mb-2 border border-secondary-green rounded-lg py-1 px-3">FITNESS10</div>
						<div class="inline-block text-[21px] font-medium leading-[21px] text-secondary-green mb-2 border border-secondary-green rounded-lg py-1 px-3">SEASON15</div>
						<div class="text-[16px] text-dark-1 leading-[37px]">{_newFront.addon.sampleBox.moreCoupons}</div>
					</div>

					<svg class="absolute right-[430px] top-[153px]" xmlns="http://www.w3.org/2000/svg" width="165" height="108" viewBox="0 0 165 108" fill="none">
						<path d="M164.704 8.43961C165.095 8.04909 165.095 7.41592 164.704 7.0254L158.34 0.661442C157.95 0.27091 157.317 0.270914 156.926 0.661436C156.536 1.05196 156.536 1.68513 156.926 2.07566L162.583 7.73251L156.926 13.3894C156.536 13.7799 156.536 14.4131 156.926 14.8036C157.317 15.1941 157.95 15.1941 158.34 14.8036L164.704 8.43961ZM21.7217 74.7633L20.7386 74.5799L21.7217 74.7633ZM50.9946 81.4681L51.9684 81.2407L50.9946 81.4681ZM1.498 40.5003C0.94572 40.5003 0.498001 40.948 0.498005 41.5003C0.498004 42.0526 0.945717 42.5003 1.498 42.5003L1.498 40.5003ZM163.997 6.73251C121.412 6.7325 87.58 13.3018 63.403 25.0493C39.2092 36.805 24.6181 53.7875 20.7386 74.5799L22.7047 74.9467C26.4374 54.9413 40.4855 38.4084 64.2771 26.8482C88.0856 15.2797 121.586 8.73251 163.997 8.7325L163.997 6.73251ZM20.7386 74.5799C18.5723 86.1906 19.7239 94.4227 22.7539 99.7384C25.8113 105.102 30.7272 107.39 35.7124 107.134C45.609 106.627 55.4579 96.1855 51.9684 81.2407L50.0208 81.6955C53.2475 95.515 44.1727 104.698 35.6102 105.137C31.3658 105.354 27.1683 103.444 24.4915 98.748C21.7872 94.0039 20.5838 86.3135 22.7047 74.9467L20.7386 74.5799ZM51.9684 81.2407C46.4955 57.8012 25.9875 40.5003 1.498 40.5003L1.498 42.5003C25.0085 42.5003 44.7477 59.1119 50.0208 81.6955L51.9684 81.2407Z" fill="#D7DBE0"/>
					</svg>

					<a n:if="$isSupportedBrowser" href="{$addonLink}" target="_blank" class="block text-center w-full font-bold py-3.5 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-xs md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
						{_newFront.addon.sampleBox.useCoupons}
					</a>

					<div class="text-center text-[13px] leading-[24px] underline text-dark-2">
						{_newFront.addon.sampleBox.coupons}
					</div>
				</div>
			</div>

			<div class="absolute right-[191px] lg:right-[387px] top-[270px] lg:top-[43px] z-10">
				<div class="flex flex-col bg-white rounded-2xl relative w-[204px]">
					<div class="ml-[16px] mt-[15px]">
						{if $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
						{else}
							<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						{/if}
					</div>
					<div class="mt-5">
						<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="40" height="34" viewBox="0 0 40 34" fill="none">
							<path d="M32.74 20.0813C33.1925 17.7583 35.4451 16.2404 37.768 16.693L38.096 15.0097C39.4078 8.27661 38.0525 6.26538 31.3194 4.95355L14.4867 1.67397C7.75359 0.362137 5.74235 1.71745 4.43052 8.45055L4.26655 9.29218C6.58946 9.74477 8.10742 11.9973 7.65484 14.3203C7.20225 16.6432 4.94967 18.1611 2.62676 17.7086L2.46278 18.5502C1.15095 25.2833 2.50626 27.2945 9.23936 28.6063L26.0721 31.8859C32.8052 33.1978 34.8164 31.8424 36.1283 25.1093C33.8053 24.6568 32.2874 22.4042 32.74 20.0813Z" stroke="#EC7700" stroke-width="2.28323" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M19.5312 2.65723L14.2839 29.5896" stroke="#EC7700" stroke-width="2.28323" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="3.93 3.93"/>
						</svg>
						<div class="text-center text-[26px] text-dark-1 font-bold leading-[37px] mb-[2px]">
							{_newFront.addon.sampleBox.countOfCoupons}
						</div>
						<div class="text-center text-[13px] text-dark-1 leading-[22px]">
							{_newFront.addon.sampleBox.fromTipli}
						</div>
					</div>

					<img class="absolute -left-[23px] bottom-[155px]" src="{$basePath}/new-design/ellipse.svg" alt="ellipse">

					<div class="my-4">
						<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="81" height="4" viewBox="0 0 81 4" fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M17.6298 0.566481C17.4988 0.554813 17.3658 0.561627 17.2415 0.586362C17.1172 0.611097 17.0051 0.653063 16.9143 0.708852L14.3518 2.28473L11.6348 0.732868C11.4725 0.640446 11.2569 0.58911 11.0348 0.590026C10.8128 0.590942 10.6024 0.644045 10.4494 0.737768L7.88631 2.31324L5.17029 0.761589C5.0082 0.668902 4.79247 0.61738 4.57031 0.618297C4.34815 0.619213 4.13766 0.672498 3.98489 0.766484L0.847979 2.69579C0.697908 2.79042 0.617954 2.91734 0.625488 3.04897C0.633022 3.1806 0.727436 3.30633 0.888221 3.39888C1.04901 3.49143 1.26319 3.54333 1.4843 3.54331C1.70541 3.54329 1.91559 3.49134 2.06923 3.39876L4.63137 1.82284L7.34841 3.37459C7.51075 3.46698 7.72637 3.51829 7.94837 3.51738C8.17036 3.51646 8.38078 3.46338 8.53383 3.36969L11.0965 1.79419L13.8135 3.34604C13.9759 3.43836 14.1915 3.48961 14.4134 3.48867C14.6353 3.48773 14.8457 3.43466 14.9987 3.34101L17.5606 1.76532L20.2777 3.31705C20.3577 3.36398 20.4523 3.40119 20.5559 3.42653C20.6595 3.45187 20.77 3.46483 20.8811 3.46466C20.9921 3.46449 21.1016 3.45119 21.203 3.42553C21.3044 3.39988 21.3958 3.36238 21.4718 3.31521C21.5479 3.26804 21.6071 3.21214 21.6461 3.15075C21.685 3.08935 21.703 3.02369 21.6988 2.95756C21.6946 2.89143 21.6684 2.82615 21.6218 2.76551C21.5751 2.70486 21.5089 2.65007 21.427 2.60428L18.0998 0.703957C17.9704 0.629971 17.8058 0.581827 17.6298 0.566481Z" fill="#D7DBE0"/>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0263 0.423023C36.8953 0.411355 36.7623 0.418169 36.638 0.442904C36.5137 0.467639 36.4016 0.509605 36.3108 0.565394L33.7483 2.14128L31.0313 0.589409C30.869 0.496988 30.6533 0.445652 30.4313 0.446568C30.2093 0.447484 29.9989 0.500586 29.8459 0.59431L27.2828 2.16978L24.5668 0.61813C24.4047 0.525443 24.189 0.473921 23.9668 0.474838C23.7446 0.475755 23.5341 0.52904 23.3814 0.623026L20.2445 2.55233C20.0944 2.64697 20.0144 2.77388 20.022 2.90551C20.0295 3.03714 20.1239 3.16288 20.2847 3.25542C20.4455 3.34797 20.6597 3.39988 20.8808 3.39985C21.1019 3.39983 21.3121 3.34789 21.4657 3.2553L24.0279 1.67938L26.7449 3.23113C26.9072 3.32352 27.1229 3.37484 27.3448 3.37392C27.5668 3.373 27.7773 3.31993 27.9303 3.22623L30.493 1.65073L33.21 3.20259C33.3724 3.2949 33.588 3.34615 33.8099 3.34521C34.0318 3.34427 34.2422 3.2912 34.3952 3.19755L36.9571 1.62186L39.6741 3.17359C39.7542 3.22052 39.8488 3.25774 39.9524 3.28307C40.056 3.30841 40.1665 3.32137 40.2776 3.3212C40.3886 3.32103 40.4981 3.30773 40.5995 3.28208C40.7009 3.25642 40.7923 3.21893 40.8683 3.17176C40.9444 3.12459 41.0036 3.06868 41.0426 3.00729C41.0815 2.94589 41.0994 2.88023 41.0953 2.8141C41.0911 2.74797 41.0649 2.68269 41.0183 2.62205C40.9716 2.56141 40.9054 2.50661 40.8235 2.46082L37.4963 0.560499C37.3669 0.486513 37.2023 0.438369 37.0263 0.423023Z" fill="#D7DBE0"/>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M56.7708 0.566481C56.6399 0.554813 56.5069 0.561627 56.3826 0.586362C56.2583 0.611097 56.1462 0.653063 56.0554 0.708852L53.4928 2.28473L50.7758 0.732868C50.6135 0.640446 50.3979 0.58911 50.1759 0.590026C49.9539 0.590942 49.7434 0.644045 49.5904 0.737768L47.0274 2.31324L44.3113 0.761589C44.1492 0.668902 43.9335 0.61738 43.7114 0.618297C43.4892 0.619213 43.2787 0.672498 43.1259 0.766484L39.989 2.69579C39.839 2.79042 39.759 2.91734 39.7665 3.04897C39.7741 3.1806 39.8685 3.30633 40.0293 3.39888C40.19 3.49143 40.4042 3.54333 40.6253 3.54331C40.8465 3.54329 41.0566 3.49134 41.2103 3.39876L43.7724 1.82284L46.4895 3.37459C46.6518 3.46698 46.8674 3.51829 47.0894 3.51738C47.3114 3.51646 47.5218 3.46338 47.6749 3.36969L50.2375 1.79419L52.9546 3.34604C53.1169 3.43836 53.3325 3.48961 53.5544 3.48867C53.7764 3.48773 53.9867 3.43466 54.1398 3.34101L56.7017 1.76532L59.4187 3.31705C59.4988 3.36398 59.5933 3.40119 59.6969 3.42653C59.8005 3.45187 59.911 3.46483 60.0221 3.46466C60.1332 3.46449 60.2426 3.45119 60.344 3.42553C60.4454 3.39988 60.5368 3.36238 60.6129 3.31521C60.6889 3.26804 60.7482 3.21214 60.7871 3.15075C60.8261 3.08935 60.844 3.02369 60.8398 2.95756C60.8357 2.89143 60.8095 2.82615 60.7628 2.76551C60.7162 2.70486 60.65 2.65007 60.5681 2.60428L57.2408 0.703957C57.1115 0.629971 56.9469 0.581827 56.7708 0.566481Z" fill="#D7DBE0"/>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M76.1673 0.423023C76.0364 0.411355 75.9034 0.418169 75.7791 0.442904C75.6547 0.467639 75.5427 0.509605 75.4519 0.565394L72.8893 2.14128L70.1723 0.589409C70.01 0.496988 69.7944 0.445652 69.5724 0.446568C69.3504 0.447484 69.1399 0.500586 68.9869 0.59431L66.4238 2.16978L63.7078 0.61813C63.5457 0.525443 63.33 0.473921 63.1078 0.474838C62.8857 0.475755 62.6752 0.52904 62.5224 0.623026L59.3855 2.55233C59.2354 2.64697 59.1555 2.77388 59.163 2.90551C59.1706 3.03714 59.265 3.16288 59.4257 3.25542C59.5865 3.34797 59.8007 3.39988 60.0218 3.39985C60.2429 3.39983 60.4531 3.34789 60.6068 3.2553L63.1689 1.67938L65.8859 3.23113C66.0483 3.32352 66.2639 3.37484 66.4859 3.37392C66.7079 3.373 66.9183 3.31993 67.0714 3.22623L69.634 1.65073L72.351 3.20259C72.5134 3.2949 72.729 3.34615 72.9509 3.34521C73.1729 3.34427 73.3832 3.2912 73.5363 3.19755L76.0981 1.62186L78.8152 3.17359C78.8953 3.22052 78.9898 3.25774 79.0934 3.28307C79.197 3.30841 79.3075 3.32137 79.4186 3.3212C79.5297 3.32103 79.6391 3.30773 79.7405 3.28208C79.8419 3.25642 79.9333 3.21893 80.0094 3.17176C80.0854 3.12459 80.1447 3.06868 80.1836 3.00729C80.2226 2.94589 80.2405 2.88023 80.2363 2.8141C80.2321 2.74797 80.206 2.68269 80.1593 2.62205C80.1127 2.56141 80.0465 2.50661 79.9645 2.46082L76.6373 0.560499C76.5079 0.486513 76.3434 0.438369 76.1673 0.423023Z" fill="#D7DBE0"/>
						</svg>
					</div>

					<div class="text-center text-dark-1 text-[13px] leading-[22px] mb-[9px] font-bold">
						{_newFront.addon.sampleBox.sampleCoupon}
					</div>

					<div class="px-4 pb-2 w-full">
						<a n:if="$isSupportedBrowser" href="{$addonLink}" target="_blank" class="block text-center w-full font-bold py-3.5 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-xs md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
							{_newFront.addon.sampleBox.useCoupon}
						</a>
						<div class="text-center text-[10px] leading-[18px] text-dark-2">
							{_newFront.addon.sampleBox.otherCoupons}
						</div>
					</div>
				</div>
			</div>

			<div class="absolute top-[-137px] lg:top-[3px] right-[33px] z-[15]">
				<div class="relative w-[147px] h-[146px]">
					<svg xmlns="http://www.w3.org/2000/svg" width="147" height="146" viewBox="0 0 147 146" fill="none">
						<g filter="url(#filter0_b_783_3356)">
							<path d="M66.5981 3.02204C70.3286 -0.420995 76.0782 -0.420993 79.8087 3.02204L87.4954 10.1163C89.7284 12.1772 92.7954 13.0778 95.7882 12.5513L106.09 10.7389C111.09 9.85935 115.927 12.9678 117.204 17.8812L119.835 28.005C120.599 30.946 122.692 33.3617 125.494 34.5368L135.141 38.5818C139.822 40.5449 142.211 45.7749 140.629 50.5986L137.369 60.5377C136.422 63.4251 136.877 66.589 138.599 69.0926L144.527 77.7107C147.404 81.8932 146.586 87.5843 142.647 90.7869L134.531 97.3857C132.173 99.3027 130.845 102.21 130.94 105.248L131.268 115.702C131.427 120.777 127.662 125.122 122.617 125.686L112.222 126.85C109.202 127.188 106.513 128.916 104.951 131.523L99.5744 140.495C96.9649 144.85 91.4483 146.469 86.8988 144.217L77.5248 139.576C74.8016 138.227 71.6052 138.227 68.882 139.576L59.508 144.217C54.9585 146.469 49.4418 144.85 46.8324 140.495L41.4558 131.523C39.8939 128.916 37.2048 127.188 34.185 126.85L23.7898 125.686C18.7447 125.122 14.9795 120.777 15.1386 115.702L15.4664 105.248C15.5616 102.21 14.2338 99.3027 11.876 97.3857L3.76007 90.7869C-0.178839 87.5843 -0.997086 81.8932 1.87999 77.7107L7.8081 69.0926C9.53026 66.589 9.98516 63.4251 9.03811 60.5377L5.77811 50.5986C4.19594 45.7749 6.5844 40.5449 11.266 38.5818L20.9123 34.5368C23.7147 33.3617 25.8079 30.946 26.5722 28.0049L29.2032 17.8812C30.4801 12.9678 35.317 9.85936 40.3167 10.7389L50.6186 12.5513C53.6114 13.0778 56.6784 12.1772 58.9114 10.1163L66.5981 3.02204Z" fill="url(#paint0_linear_783_3356)"/>
							<path d="M66.9372 3.38947C70.4762 0.123195 75.9306 0.123197 79.4696 3.38947L87.1563 10.4837C89.5039 12.6504 92.7284 13.5972 95.8748 13.0437L106.177 11.2314C110.92 10.3969 115.508 13.3458 116.72 18.0069L119.351 28.1307C120.154 31.2227 122.355 33.7625 125.301 34.9979L134.947 39.0429C139.389 40.9052 141.655 45.8667 140.154 50.4428L136.894 60.3819C135.898 63.4175 136.376 66.7439 138.187 69.376L144.115 77.994C146.844 81.9619 146.068 87.3608 142.331 90.3989L134.215 96.9977C131.737 99.0132 130.341 102.07 130.441 105.263L130.768 115.718C130.919 120.532 127.347 124.654 122.561 125.19L112.166 126.353C108.991 126.708 106.164 128.525 104.522 131.266L99.1455 140.238C96.67 144.369 91.4366 145.906 87.1207 143.769L77.7467 139.128C74.8837 137.71 71.5231 137.71 68.6601 139.128L59.2861 143.769C54.9702 145.906 49.7368 144.369 47.2613 140.238L41.8847 131.266C40.2426 128.525 37.4155 126.708 34.2406 126.353L23.8454 125.19C19.0593 124.654 15.4875 120.532 15.6384 115.718L15.9662 105.263C16.0663 102.07 14.6702 99.0132 12.1915 96.9977L4.07549 90.3989C0.338809 87.3608 -0.437431 81.9619 2.29194 77.994L8.22005 69.376C10.0306 66.7439 10.5089 63.4175 9.51321 60.3819L6.25321 50.4428C4.75227 45.8667 7.01811 40.9052 11.4594 39.0429L21.1057 34.9979C24.0519 33.7625 26.2526 31.2227 27.0561 28.1307L29.6871 18.0069C30.8985 13.3458 35.487 10.3969 40.2301 11.2314L50.532 13.0437C53.6784 13.5972 56.9028 12.6504 59.2505 10.4837L66.9372 3.38947Z" stroke="white"/>
						</g>
						<defs>
							<filter id="filter0_b_783_3356" x="-19.8359" y="-19.5601" width="186.078" height="184.79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
								<feFlood flood-opacity="0" result="BackgroundImageFix"/>
								<feGaussianBlur in="BackgroundImageFix" stdDeviation="10"/>
								<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_783_3356"/>
								<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_783_3356" result="shape"/>
							</filter>
							<linearGradient id="paint0_linear_783_3356" x1="-4" y1="151.333" x2="136.085" y2="37.6642" gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A"/>
								<stop offset="1" stop-color="#FFA439"/>
							</linearGradient>
						</defs>
					</svg>
					<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-bold text-white">
						{_'newFront.addon.tier3.smallTitle'}
					</div>
				</div>
			</div>
			<div class="absolute -z-10 lg:z-10 top-[-23%] left-[40%] lg:left-[71%] ml-5 h-[560px] bg-light-5 w-[200%] rounded-l-[300px]"></div>
		</div>

		{if $isSupportedBrowser}
			<div class="w-full max-w-[564px] mx-auto flex flex-col items-center relative mt-[356px] pb-10 md:pb-36">
				<img src="{$basePath}/new-design/donkey-with-mark-hp.png" alt="oslík" class="absolute top-[-200px]">
				<a href="{$addonLink}" target="_blank"  class="rounded-[300px] bg-secondary-green py-[58px] w-[564px] max-w-full flex justify-center items-center gap-[17px] text-[26px] font-bold leading-[39px] text-white xl:hover:bg-white xl:hover:text-secondary-green border border-secondary-green">
					{_'newFront.addon.cta.button'}
					<svg xmlns="http://www.w3.org/2000/svg" width="29" height="30" viewBox="0 0 29 30" fill="none">
						<path d="M6.6538 22.7944L22.1484 7.2998" stroke="currentColor" stroke-width="3.13087" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M22.1509 20.4301L22.1509 7.30027L9.02104 7.30025" stroke="currentColor" stroke-width="3.13087" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
				</a>
			</div>
		{/if}
	</div>
</div>
