<!DOCTYPE html>
<html lang="cs">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
	<meta name="skype_toolbar" content="skype_toolbar_parser_compatible">

	<!-- Metadata info -->
	<title></title>
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta name="robots" content="">
	<meta name="googlebot" content="">
	<meta name="author" content="">

	<!-- Favicon images -->

	<!-- Viewport for mobile devices -->
	<meta content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">

	<!-- Stylesheet -->
	<link rel="stylesheet" href="{$basePath}/css2/output.css">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght,HEXP@160..700,0..100&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
		rel="stylesheet">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
	<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
</head>
<body class="bg-[#182B4A]">
	<header id="header" class="bg-white w-full relative z-30">
	<div class="border-b w-full border-b-light-4">
		<div class="relative flex justify-between container p-0 h-[64px] items-center">
			<div class="flex items-center pl-5 w-full">
				<div id="mobileMenu" class="absolute left-0 flex justify-center items-center w-[61px] h-[63px] block md:hidden">
					<svg id="hamburgerIcon" xmlns="http://www.w3.org/2000/svg" width="21" height="18" viewBox="0 0 21 18" fill="none" class="block md:hidden">
						<rect width="21" height="2" rx="1" transform="matrix(-1 0 0 1 21 0)" fill="#080B10"></rect>
						<rect x="21" y="10" width="21" height="2" rx="1" transform="rotate(180 21 10)" fill="#080B10"></rect>
						<rect x="10.5" y="18" width="10.5" height="2" rx="1" transform="rotate(180 10.5 18)" fill="#080B10"></rect>
					</svg>

					<svg id="closeIcon" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none" class="hidden">
						<path d="M0.294033 0.294038C0.686077 -0.0980127 1.32171 -0.0980127 1.71375 0.294038L16.7057 15.2862C17.0977 15.6783 17.0977 16.3139 16.7057 16.706C16.3137 17.098 15.678 17.098 15.286 16.706L0.294034 1.71378C-0.0980106 1.32173 -0.0980117 0.686089 0.294033 0.294038Z" fill="#080B10"></path>
						<path d="M16.706 0.294038C16.3139 -0.0980127 15.6783 -0.0980127 15.2863 0.294038L0.294309 15.2862C-0.0977358 15.6783 -0.0977369 16.3139 0.294308 16.706C0.686352 17.098 1.32198 17.098 1.71403 16.706L16.706 1.71378C17.098 1.32173 17.098 0.686089 16.706 0.294038Z" fill="#080B10"></path>
					</svg>
				</div>
				<div class="w-px h-[63px] bg-zinc-200 ml-10 mr-5 md:hidden"></div>
				<div class="overlay fixed inset-0 bg-black bg-opacity-50 hidden" style="top: 65px; background: rgba(24, 43, 74, 0.70); backdrop-filter: blur(3px);"></div>

				<div id="sidebar" class="hidden sidebar fixed top-[65px] left-0 h-full w-[313px] shadow-lg z-50 bg-white">
					<div class="pl-[21px]">
						<a class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]" href="/obchody">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
								<path d="M7.85714 7.22979C7.85714 7.41424 7.7849 7.59113 7.6563 7.72156C7.5277 7.85198 7.35329 7.92525 7.17143 7.92525H1.68571C1.50386 7.92525 1.32944 7.85198 1.20084 7.72156C1.07224 7.59113 1 7.41424 1 7.22979V1.66616C1 1.48172 1.07224 1.30482 1.20084 1.17439C1.32944 1.04397 1.50386 0.970703 1.68571 0.970703H7.17143C7.35329 0.970703 7.5277 1.04397 7.6563 1.17439C7.7849 1.30482 7.85714 1.48172 7.85714 1.66616V7.22979Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M17 7.22979C17 7.41424 16.9278 7.59113 16.7992 7.72156C16.6706 7.85198 16.4961 7.92525 16.3143 7.92525H10.8286C10.6467 7.92525 10.4723 7.85198 10.3437 7.72156C10.2151 7.59113 10.1429 7.41424 10.1429 7.22979V1.66616C10.1429 1.48172 10.2151 1.30482 10.3437 1.17439C10.4723 1.04397 10.6467 0.970703 10.8286 0.970703H16.3143C16.4961 0.970703 16.6706 1.04397 16.7992 1.17439C16.9278 1.30482 17 1.48172 17 1.66616V7.22979Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M17 17.2752C17 17.4597 16.9278 17.6366 16.7992 17.767C16.6706 17.8974 16.4961 17.9707 16.3143 17.9707H10.8286C10.6467 17.9707 10.4723 17.8974 10.3437 17.767C10.2151 17.6366 10.1429 17.4597 10.1429 17.2752V11.7116C10.1429 11.5272 10.2151 11.3503 10.3437 11.2198C10.4723 11.0894 10.6467 11.0162 10.8286 11.0162H16.3143C16.4961 11.0162 16.6706 11.0894 16.7992 11.2198C16.9278 11.3503 17 11.5272 17 11.7116V17.2752Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M7.85714 17.2752C7.85714 17.4597 7.7849 17.6366 7.6563 17.767C7.5277 17.8974 7.35329 17.9707 7.17143 17.9707H1.68571C1.50386 17.9707 1.32944 17.8974 1.20084 17.767C1.07224 17.6366 1 17.4597 1 17.2752V11.7116C1 11.5272 1.07224 11.3503 1.20084 11.2198C1.32944 11.0894 1.50386 11.0162 1.68571 11.0162H7.17143C7.35329 11.0162 7.5277 11.0894 7.6563 11.2198C7.7849 11.3503 7.85714 11.5272 7.85714 11.7116V17.2752Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
							Obchody s odměnami
						</a>
						<a class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]" href="/slevy">
							<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
								<path d="M12.4071 4.34222C12.4071 4.61379 12.5152 4.87423 12.7075 5.06626C12.8998 5.25829 13.1606 5.36617 13.4325 5.36617C13.7045 5.36617 13.9653 5.25829 14.1576 5.06626C14.3499 4.87423 14.4579 4.61379 14.4579 4.34222C14.4579 4.07066 14.3499 3.81021 14.1576 3.61819C13.9653 3.42616 13.7045 3.31828 13.4325 3.31828C13.1606 3.31828 12.8998 3.42616 12.7075 3.61819C12.5152 3.81021 12.4071 4.07066 12.4071 4.34222Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
								<path d="M15.8963 1.00002H9.22412C9.07344 1.00094 8.92441 1.03228 8.78619 1.09215C8.64791 1.15203 8.52315 1.23921 8.41947 1.34845L1.29871 8.93557C1.1974 9.0423 1.11882 9.16844 1.06771 9.30639C1.0166 9.44434 0.994026 9.59118 1.00135 9.73808C1.00867 9.88492 1.04574 10.0288 1.11031 10.161C1.17488 10.2931 1.26562 10.4109 1.37704 10.507L8.4978 16.7289C8.71342 16.9165 8.99355 17.0131 9.27909 16.9986C9.56471 16.984 9.83344 16.8593 10.0288 16.6507L16.7009 9.53998C16.893 9.33882 17.0002 9.07138 17 8.79335V2.09507C17 1.95067 16.9714 1.80769 16.9158 1.67437C16.8603 1.54104 16.7789 1.42001 16.6763 1.31823C16.5738 1.21645 16.452 1.13594 16.3181 1.08133C16.1842 1.02672 16.0409 0.999086 15.8963 1.00002Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
							Slevové kupóny
						</a>
						<a class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]" href="/jak-to-funguje">
							<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M1 10.6V17V10.6Z" fill="#ECEDF0"></path>
								<path d="M1 15.9333H12.7333C12.7333 15.3676 12.5085 14.8249 12.1085 14.4249C11.7084 14.0248 11.1657 13.8 10.6 13.8H7.93332C7.93332 13.2342 7.70854 12.6916 7.30848 12.2915C6.90841 11.8914 6.36579 11.6667 5.79999 11.6667H1" fill="#ECEDF0"></path>
								<path d="M5.26666 13.8H7.93332H5.26666Z" fill="#ECEDF0"></path>
								<path d="M5.18133 1H16.0186C16.2815 1.00375 16.5321 1.11127 16.716 1.2991C16.8998 1.48693 17.0019 1.73985 17 2.00267V8.17511C17.0019 8.43794 16.8998 8.69081 16.716 8.87868C16.5321 9.06649 16.2815 9.17401 16.0186 9.17778H5.18133C4.91853 9.17401 4.66788 9.06649 4.48403 8.87868C4.3002 8.69081 4.1981 8.43794 4.2 8.17511V2.00267C4.1981 1.73985 4.3002 1.48693 4.48403 1.2991C4.66788 1.11127 4.91853 1.00375 5.18133 1Z" fill="#ECEDF0"></path>
								<path d="M8.99999 5.08889C8.99999 5.51324 9.16859 5.9202 9.46861 6.22026C9.7687 6.52032 10.1757 6.68889 10.6 6.68889C11.0243 6.68889 11.4313 6.52032 11.7314 6.22026C12.0314 5.9202 12.2 5.51324 12.2 5.08889C12.2 4.66454 12.0314 4.25758 11.7314 3.95752C11.4313 3.65746 11.0243 3.48889 10.6 3.48889C10.1757 3.48889 9.7687 3.65746 9.46861 3.95752C9.16859 4.25758 8.99999 4.66454 8.99999 5.08889Z" fill="#ECEDF0"></path>
								<path d="M6.15555 3.31111H7.22221H6.15555Z" fill="#ECEDF0"></path>
								<path d="M13.9778 6.86667H15.0444H13.9778Z" fill="#ECEDF0"></path>
								<path d="M1 10.6V17M1 15.9333H12.7333C12.7333 15.3676 12.5085 14.8249 12.1085 14.4249C11.7084 14.0248 11.1657 13.8 10.6 13.8H7.93332M7.93332 13.8C7.93332 13.2342 7.70854 12.6916 7.30848 12.2915C6.90841 11.8914 6.36579 11.6667 5.79999 11.6667H1M7.93332 13.8H5.26666M6.15555 3.31111H7.22221M13.9778 6.86667H15.0444M5.18133 1H16.0186C16.2815 1.00375 16.5321 1.11127 16.716 1.2991C16.8998 1.48693 17.0019 1.73985 17 2.00267V8.17511C17.0019 8.43794 16.8998 8.69081 16.716 8.87868C16.5321 9.06649 16.2815 9.17401 16.0186 9.17778H5.18133C4.91853 9.17401 4.66788 9.06649 4.48403 8.87868C4.3002 8.69081 4.1981 8.43794 4.2 8.17511V2.00267C4.1981 1.73985 4.3002 1.48693 4.48403 1.2991C4.66788 1.11127 4.91853 1.00375 5.18133 1ZM8.99999 5.08889C8.99999 5.51324 9.16859 5.9202 9.46861 6.22026C9.7687 6.52032 10.1757 6.68889 10.6 6.68889C11.0243 6.68889 11.4313 6.52032 11.7314 6.22026C12.0314 5.9202 12.2 5.51324 12.2 5.08889C12.2 4.66454 12.0314 4.25758 11.7314 3.95752C11.4313 3.65746 11.0243 3.48889 10.6 3.48889C10.1757 3.48889 9.7687 3.65746 9.46861 3.95752C9.16859 4.25758 8.99999 4.66454 8.99999 5.08889Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>

							Jak získat odměny?
						</a>
					</div>
				</div>

				<a href="/">
					<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
						<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
					</svg>
				</a>

				<div class="hidden md:block h-[65px] w-[1px] bg-light-4 mx-[25px] -my-2.5"></div>

				<a class="hidden md:flex items-center cursor-pointer gap-2  xl:hover:text-primary-orange" href="/obchody">
					<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
						<path d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
						<path d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
						<path d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
					</svg>

					<div class="font-medium text-sm leading-[24.5px]">
						Obchody s odměnami
					</div>
				</a>
			</div>

			<div class="flex items-center shrink-0 gap-6">
				<a class="hidden font-medium hover:underline text-sm leading-[24.5px] md:block" href="/prihlaseni">
					Přihlásit se
				</a>
				<a class="px-7 py-3 rounded-xl bg-orange-gradient text-white font-medium text-sm leading-[24.5px] mr-2 md:mr-0 cursor-pointer xl:hover:bg-orange-gradient-hover" href="/registrace">
					Vytvořit účet
				</a>
			</div>
		</div>
	</div>

</header>

	<div class="container relative">
		<div class="hidden lg:block absolute top-0 left-[-250px] z-20">
			<img src="{$basePath}/new-design/gives-out-bg-star.svg" alt="star">
		</div>

		<div class="lg:hidden absolute top-[-230px] left-[-290px] z-20">
			<img src="{$basePath}/new-design/gives-out-bg-star-mobile.svg" alt="star">
		</div>

		<div class="hidden lg:block absolute top-[-50px] left-[-1200px] z-20">
			<img class="blur-3xl" src="{$basePath}/new-design/gives-out-bg-circle.png" alt="circle">
		</div>

		<div class="flex flex-col lg:flex-row justify-between items-center pt-10 lg:pt-[100px] relative z-50">
			<div class="px-5 lg:px-0">
				<div class="flex items-center text-white lg:text-primary-orange text-lg lg:text-[45px] font-bold leading-[60px] text-nowrap mt-4 lg:mt-0">
					Tipli rozdáva
					<div class="relative flex-shrink-0">
						<img class="hidden lg:block" src="{$basePath}/new-design/gives-out-star-md.svg" alt="donkey">
						<img class="lg:hidden" src="{$basePath}/new-design/gives-out-star-xs.svg" alt="donkey">
						<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-bold text-white">
							<span
								class="text-lg lg:text-[45px] text-white text-nowrap leading-[67px] rounded-md lg:rounded-[13px] bg-orange-gradient px-2 py-1 lg:px-[17px]">20 €
							</span>
						</div>
					</div>
					<div class="lg:hidden text-white text-lg font-bold text-nowrap lg:-mt-[46px]">každý deň!</div>
				</div>
				<div class="hidden lg:block text-white text-[45px] font-bold text-nowrap -mt-[46px] mb-2.5">každý
					deň!</div>

				<div class="w-full max-w-[371px] text-white text-sm lg:text-lg leading-[24.5px] lg:leading-[31.5px]">Každý deň vyberáme šťastný obchod, a tí ktorí ho trafili sa môžu prihlásiť o peňažnú odmenu.</div>
			</div>

			<div class="relative w-full sm:w-auto swiper-lucky-shops-container">
				<div id="swiper-lucky-shops" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur text-white w-full lg:max-w-[455px] lg:ml-auto rounded-2xl pb-[18px] lg:pb-5 pt-5 lg:pt-[25px] shadow-custom relative px-5 lg:px-[30px] mt-10 lg:mt-0">
					<div class="text-sm font-medium lg:text-lg lg:font-bold leading-[24.5px] lg:leading-[31.5px] text-white">
						{_newFront.luckyShops.detail.userLuckyShops}
					</div>

					<div class="swiper swiper-lucky-shops lg:!mr-[-30px]" style="padding: 20px 0 27px 0;">
						<div class="swiper-wrapper">
							{for $i = 1; $i <= 5; $i++}
							<div class="swiper-slide">
								<a
									class="w-[60px] h-[60px] lg:w-[72px]  relative lg:h-[72px] rounded-full flex items-center justify-center border-2 border-primary-orange hover:cursor-pointer">
									<img class="max-w-[45px] flex-shrink-0 blur-[2px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/792258.svg">
									<svg class="absolute bottom-[-12px] right-[-6px]" xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
										<rect width="30" height="30" rx="15" fill="#F72F49"/>
										<path d="M11 20L20 11M20 20L11 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
									</svg>
								</a>
							</div>
							{/for}
						</div>
					</div>

					<div class="h-px bg-white/10 w-full mb-5 mt-[27px]"></div>

					<div class="flex items-center justify-between">
						<div class="text-sm leading-[24.5px] text-white">
							Kontrola šťastných obchodů
						</div>
						<div class="text-sm font-bold leading-[24.5px]">
							🔥 <span class="text-primary-orange">0</span> dní v řadě
						</div>
					</div>

					<div class="flex gap-2 text-sm mt-4">
						<button id="btn-win" class="border border-secondary-green py-1 px-4 rounded-md hover:bg-secondary-green/20">WIN</button>
						<button id="btn-shops" class="border border-primary-orange py-1 px-4 rounded-md hover:bg-primary-orange/20">SHOPS</button>
						<button id="btn-revelation" class="border border-red-500 py-1 px-4 rounded-md hover:bg-red-500/20">REVELATION</button>
						<button id="btn-lose" class="border border-blue-500 py-1 px-4 rounded-md hover:bg-blue-500/20">LOSE</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="bg-lucky-shops mt-[191px] relative">
		<div
			class="is-shops-content absolute top-[-19px] md:top-[31px] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-20 w-full sm:w-auto">
			<img src="{$basePath}/new-design/bg-star-behind-donkey.svg" alt="star">
		</div>

		<div
			class="is-win-content absolute top-[-19px] md:top-[31px] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-20 w-full sm:w-auto">
			<img src="{$basePath}/new-design/bg-star-behind-donkey-win.svg" alt="star">
		</div>

		<div
			class="is-revelation-content absolute top-[-19px] md:top-[31px] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-20 w-full sm:w-auto">
			<img src="{$basePath}/new-design/bg-star-behind-donkey-win.svg" alt="star">
		</div>

		<div
			class="is-lose-content absolute top-[-19px] md:top-[31px] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-20 w-full sm:w-auto">
			<img src="{$basePath}/new-design/bg-star-behind-donkey.svg" alt="star">
		</div>

		<div class="absolute inset-0 -z-10">
			<div class="w-full h-full bg-image"></div>
		</div>

		<div class="relative">
			<img src="{$basePath}/new-design/oslik-lucky-shops-bg.png" alt="oslik"
				 class="is-shops-content is-win-content is-revelation-content absolute top-[-43px] left-1/2 -translate-x-1/2 -translate-y-1/2 z-20">

			<img src="{$basePath}/new-design/oslik-lucky-shops-bg-lose.png" alt="oslik"
				 class="is-lose-content absolute top-[-40px] left-1/2 -translate-x-1/2 -translate-y-1/2 z-20">

			<div class="is-shops-content">
				<div
					class="absolute w-[100px] md:w-auto top-0 left-1/2 -translate-x-[152%] md:-translate-x-[132%] translate-y-[-105px] md:translate-y-[-95px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 font-bold border border-[#FDBB47]"
					style="background:rgba(253, 187,71, 0.10);">
					Zistite, či ste dnes vyhrali
				</div>

				<div
					class="absolute w-[120px] md:w-auto top-0 left-1/2 translate-x-[59%] md:translate-x-[41%] translate-y-[-96px] md:translate-y-[-75px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 font-bold border border-[#FDBB47]"
					style="background:rgba(253, 187,71, 0.10);">
					Odhaľte šťastný obchod
				</div>
			</div>

			<div class="is-win-content">
				<div
					class="py-[6px] absolute w-[116px] md:w-[250px] top-0 left-1/2 -translate-x-[152%] md:-translate-x-[125%] translate-y-[-155px] md:translate-y-[-135px] text-end text-xs md:text-sm text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-[24.5px] font-bold bg-secondary-green/10 border border-secondary-green">
					Ďalší šťastný obchod vyberáme za:

					<div
						class="flex justify-end text-lg md:text-[32px] md:leading-[41px] text-secondary-green tracking-[2px] md:tracking-[5px] font-normal js-count-down">
						<div class="flex flex-col items-center">
							<span class="js-count-down-hour">00</span>
							<span class="hidden md:block text-xs tracking-normal font-light text-white/40"> hodin</span>
						</div>
						<div class="flex flex-col items-center">
							<span>:</span>
						</div>
						<div class="flex flex-col items-center">
							<span class="js-count-down-minute">08</span>
							<span class="hidden md:block text-xs tracking-normal font-light text-white/40"> minut
							</span>
						</div>
						<div class="flex flex-col items-center">
							<span>:</span>
						</div>
						<div class="flex flex-col items-center">
							<span class="js-count-down-second">26</span>
							<span class="hidden md:block text-xs tracking-normal font-light text-white/40"> sekund
							</span>
						</div>
					</div>
				</div>

				<div class="absolute w-[124px] md:w-[220px] top-0 left-1/2 translate-x-[59%] md:translate-x-[31%] translate-y-[-105px] md:translate-y-[-105px] text-xs md:text-sm text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 bg-secondary-green/10 border border-secondary-green">
					Šťastný obchod, ktorý bol naposledy vybraný, je: <span class="font-bold">Notino</span>
				</div>
			</div>

			<div class="is-revelation-content">
				<div
					class="absolute md:w-auto top-0 left-1/2 -translate-x-[152%] md:translate-x-[-170px] translate-y-[-167px] md:translate-y-[-155px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[21px] bg-secondary-green/10 font-bold border border-secondary-green">
					Gratulujeme
				</div>

				<div
					class="flex flex-col md:flex-row items-center md:gap-2 absolute min-w-[120px] md:w-auto top-0 left-1/2 translate-x-[-165px] md:translate-x-[-279px] translate-y-[-128px] md:translate-y-[-110px] text-xs md:text-sm text-white px-2.5 rounded-lg py-2 pb-2.5 leading-[21px] bg-secondary-green/10 font-bold border border-secondary-green">
					Šťastný obchod je
					<div class="flex w-full md:w-auto justify-end">
						<a
							class="min-w-[76px] border border-light-5 bg-white rounded-xl shadow-hover flex items-center justify-center h-[47px]" href="/obchod/eobuv-cz">
							<img class="max-w-[50px] max-h-[22px]" alt="Eobuv.cz"
								 src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/8XGok5-sJOsK47-ghGYmbPiZ68-tirGTt_t-JIFARK0/resize:fit:100:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzc3NTg0LnBuZw.png" loading="lazy">
						</a>
					</div>
				</div>

				<div
					class="w-[110px] md:w-auto absolute md:w-auto top-0 left-1/2 translate-x-[85px] md:translate-x-[73px] translate-y-[-166px] md:translate-y-[-108px] text-xs md:text-sm text-white px-2.5 rounded-lg py-[6px] leading-[19px] md:leading-[21px] bg-secondary-green/10 border border-secondary-green">
					Obchody, ktoré ste mali zvolené: <br> <span class="font-bold">CCC, Temu, Notino</span>
				</div>
			</div>

			<div class="is-lose-content">
				<div
					class="absolute md:w-auto top-0 left-1/2 -translate-x-[137%] md:translate-x-[-126%] translate-y-[-173px] md:translate-y-[-165px] text-xs md:text-base text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 font-bold border border-[#FDBB47] text-nowrap"
					style="background:rgba(253, 187,71, 0.10);"
				>
					Dnes to nevyšlo
				</div>

				<div
					class="flex flex-col md:flex-row items-center absolute gap-2 min-w-[120px] md:w-auto top-0 left-1/2 translate-x-[-133%] md:translate-x-[-114%] translate-y-[-135%] md:translate-y-[-115px] text-xs md:text-sm text-white px-2.5 rounded-lg py-2 pb-2.5 leading-[24.5px] bg-secondary-green/10 font-bold border border-[#FDBB47]"
					style="background:rgba(253, 187,71, 0.10);"
				>
					Šťastný obchod je
					<div class="flex justify-end w-full md:w-auto">
						<a class="min-w-[76px] border border-light-5 bg-white rounded-xl shadow-hover flex items-center justify-center h-[47px]" href="/obchod/eobuv-cz">
							<img class="max-w-[50px] max-h-[22px]" alt="Eobuv.cz"
								 src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/8XGok5-sJOsK47-ghGYmbPiZ68-tirGTt_t-JIFARK0/resize:fit:100:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzc3NTg0LnBuZw.png" loading="lazy">
						</a>
					</div>
				</div>

				<div
					class="absolute w-[120px] md:w-auto top-0 left-1/2 translate-x-[59%] md:translate-x-[41%] translate-y-[-96px] md:translate-y-[-75px] text-xs md:text-sm text-white px-2.5 rounded-lg py-[3px] leading-[19px] md:leading-7 border border-[#FDBB47]"
					style="background:rgba(253, 187,71, 0.10);"
				>
					Obchody, ktoré ste mali zvolené: <br>
					<span class="font-bold">CCC, Temu, Notino</span>
				</div>
			</div>
		</div>

		<div class="is-shops-content pt-[101px] text-center relative px-10 lg:px-0">
			<div class="absolute top-[80%] left-1/2 -translate-x-1/2 -translate-y-1/2 -z-10">
				<img src="{$basePath}/new-design/lucky-shops-cta-star.svg" alt="cta-star">
			</div>
			<button
				class="w-full md:max-w-[372px] mt-5 text-white text-sm lg:text-lg font-bold leading-[31.5px] bg-orange-gradient py-[15px] lg:py-[18px] rounded-xl cursor-pointer hover:bg-orange-gradient-hover relative z-10" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);">
				Odhaliť štastný obchod
				<span class="absolute left-0 top-[-34px]">
					<img src="{$basePath}/new-design/lucky-shops-cta-left-stars.svg" alt="left stars">
				</span>

				<span class="absolute right-0 bottom-[-32px]">
					<img src="{$basePath}/new-design/lucky-shops-cta-right-stars.svg" alt="right stars">
				</span>
			</button>
		</div>

		<div class="container">
			<div class="is-shops-content">
				<div class="h-px bg-white/10 w-full mb-2.5 mt-[62px]"></div>
				<div class="flex flex-col lg:flex-row items-center justify-center gap-2.5 lg:gap-10 text-sm text-white leading-[24.5px] font-bold">
					<div>Celkovo správne tipovali:<span class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">56x</span></div>
					<div>Prihlásení o výhru: <span class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">1 236x</span></div>
				</div>
				<div class="h-px bg-white/10 w-full mt-2.5"></div>
			</div>

			<div class="is-win-content">
				<div class="text-center text-lg md:text-[33px] font-bold leading-[31.5px] md:leading-[49.5px] text-white pt-[73px]">Už zajtra môže
					byť váš obchod víťazný!
				</div>
				<div class="text-center text-sm text-white mt-2 md:mt-5 md:text-lg font-bold leading-[24.5px] md:leading-[31.5px]">Príďte ho	sem
					<span class="text-primary-orange">skontrolovať</span> 😎
				</div>


				<div class="container mt-[50px] px-0 relative">
					<div class="absolute top-[-19px] left-1/2 transform -translate-x-1/2">
						<svg width="124" height="60" viewBox="0 0 124 60" fill="none"
							 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
							<rect class="h-[61px]"  width="124" height="60" fill="url(#pattern0_3_467)"/>
							<defs>
								<pattern id="pattern0_3_467" patternContentUnits="objectBoundingBox" width="1" height="1">
									<use xlink:href="#image0_3_467" transform="scale(0.00403226 0.00833333)"/>
								</pattern>
								<image id="image0_3_467" width="248" height="120" xlink:href="data:image/png;base64,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"/>
							</defs>
						</svg>
						<div
							class="flex items-center gap-[5px] uppercase relative text-white leading-7 font-bold top-[-42px] left-[37px]">
							<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
								<rect width="17" height="17" fill="url(#pattern0_1389_4320)"/>
								<defs>
									<pattern id="pattern0_1389_4320" patternContentUnits="objectBoundingBox" width="1" height="1">
										<use xlink:href="#image0_1389_4320" transform="scale(0.0138889)"/>
									</pattern>
									<image id="image0_1389_4320" width="72" height="72" xlink:href="data:image/png;base64,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"/>
								</defs>
							</svg>
							Tip
						</div>
					</div>
					<div class="pt-[78px] pb-[143px] rounded-[26px]" style="background: linear-gradient(180deg, #FFF 46.7%, #ECEDF0 89.22%)">
						<div
							class="m-auto text-base md:text-lg leading-[31.5px] text-center max-w-[265px] md:max-w-[522px]">
							Máte príležitosť
							<span class="font-bold">zvýšiť svoje šance navýhru</span>
							získaním nového okienka za nákup cez Tipli.
						</div>
						<svg class="m-auto my-3" xmlns="http://www.w3.org/2000/svg" width="70" height="5"
							 viewBox="0 0 70 5"
							 fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M29.4122 0.238338C29.1857 0.219674 28.9556 0.230573 28.7406 0.270138C28.5256 0.309704 28.3317 0.376833 28.1747 0.46607L23.7424 2.98681L19.043 0.504485C18.7622 0.35665 18.3893 0.274534 18.0053 0.276C17.6213 0.277465 17.2573 0.362406 16.9926 0.512324L12.5595 3.03241L7.86172 0.550426C7.58136 0.402167 7.20822 0.319754 6.82397 0.32122C6.43971 0.322687 6.07563 0.407919 5.81139 0.558258L0.385673 3.64432C0.126105 3.7957 -0.0121874 3.99871 0.000844083 4.20926C0.0138756 4.41982 0.177178 4.62094 0.455277 4.76898C0.733376 4.91702 1.10384 5.00004 1.48628 5C1.86872 4.99996 2.23225 4.91687 2.498 4.76878L6.92958 2.24798L11.6291 4.73011C11.9099 4.8779 12.2828 4.95998 12.6668 4.95852C13.0508 4.95705 13.4147 4.87215 13.6794 4.72228L18.1119 2.20215L22.8114 4.68446C23.0922 4.83212 23.4651 4.9141 23.849 4.91259C24.2328 4.91109 24.5967 4.82621 24.8614 4.6764L29.2925 2.15596L33.992 4.63808C34.1305 4.71315 34.2941 4.77267 34.4732 4.8132C34.6524 4.85373 34.8436 4.87447 35.0357 4.87419C35.2278 4.87392 35.4171 4.85264 35.5925 4.81161C35.7679 4.77057 35.926 4.71059 36.0575 4.63514C36.1891 4.55969 36.2915 4.47027 36.3589 4.37206C36.4263 4.27385 36.4573 4.16882 36.4501 4.06304C36.4428 3.95726 36.3976 3.85285 36.3169 3.75584C36.2362 3.65884 36.1217 3.57119 35.98 3.49795L30.2251 0.458241C30.0013 0.339895 29.7167 0.262884 29.4122 0.238338Z" fill="#E1E4E8"/>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M62.9611 0.00886561C62.7346 -0.00979813 62.5045 0.00110077 62.2895 0.0406659C62.0745 0.0802315 61.8806 0.14736 61.7236 0.236598L57.2914 2.75734L52.5919 0.275013C52.3112 0.127178 51.9382 0.0450619 51.5542 0.0465274C51.1702 0.0479932 50.8062 0.132934 50.5416 0.282851L46.1084 2.80294L41.4106 0.320954C41.1303 0.172695 40.7571 0.0902813 40.3729 0.0917481C39.9886 0.0932146 39.6246 0.178447 39.3603 0.328786L33.9346 3.41485C33.675 3.56623 33.5367 3.76924 33.5498 3.97979C33.5628 4.19035 33.7261 4.39146 34.0042 4.5395C34.2823 4.68754 34.6528 4.77056 35.0352 4.77053C35.4176 4.77049 35.7812 4.6874 36.0469 4.53931L40.4785 2.0185L45.178 4.50064C45.4588 4.64843 45.8317 4.73051 46.2157 4.72904C46.5997 4.72758 46.9636 4.64268 47.2283 4.49281L51.6608 1.97267L56.3603 4.45498C56.6412 4.60265 57.014 4.68463 57.3979 4.68312C57.7818 4.68161 58.1456 4.59674 58.4103 4.44693L62.8414 1.92649L67.5409 4.40861C67.6794 4.48368 67.843 4.5432 68.0222 4.58373C68.2013 4.62426 68.3925 4.64499 68.5846 4.64472C68.7768 4.64445 68.966 4.62317 69.1414 4.58213C69.3168 4.5411 69.4749 4.48112 69.6065 4.40567C69.738 4.33022 69.8404 4.24079 69.9078 4.14259C69.9752 4.04438 70.0062 3.93935 69.999 3.83357C69.9918 3.72779 69.9465 3.62338 69.8658 3.52637C69.7851 3.42937 69.6706 3.34171 69.5289 3.26848L63.774 0.228769C63.5502 0.110423 63.2656 0.0334118 62.9611 0.00886561Z" fill="#E1E4E8"/>
						</svg>
						<div
							class="text-sm leading-[24.5px] text-dark-1 max-w-[275px] md:max-w-[665px] m-auto text-center">
							Každým šťastným obchodom si zvyšujete šancu na výhru. Na každý z nich však potrebujete
							jedno okienko. Vyberte si z aktuálne populárnych kupónov a za každý nákup získate jedno okienko.
						</div>
					</div>

					<div class="flex lg:grid lg:grid-cols-3 gap-5 px-2.5 lg:px-[50px] mt-[-100px] overflow-x-auto pb-2.5">
						<div class="min-w-[289px] coupon-item flex flex-col items-center relative">
							<div class="w-full bg-white rounded-t-2xl">
								<div class="absolute right-[-17px] -top-[20px]"></div>
								<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
									<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
										<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
									</svg>
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="h-[122px] flex items-center justify-center" target="_blank">
									<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
								</a>

								<div class="mb-3 md:mb-[18px]">
									<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px] xl:hover:underline" target="_blank">
									<span class="text-primary-orange">Exkluzivně: </span>
									<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
								</a>

								<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

								</svg>
							</div>

							<div class="w-full max-h-[37px]">
								<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="white"></path>
								</svg>
							</div>

							<div class="px-1.5 pb-2 w-full bg-white rounded-b-2xl">
								<button class="flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
									<span class="m-auto js-copy-change-text">TIPLI</span>
									<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
									<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
								</button>
								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank" class="block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
									Použít kupón
								</a>

								<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
									<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0
                text-center text-dark-4 md:justify-start md:h-auto md:items-center">
										<div class="">
											<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
												<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
											</svg>
										</div>
										Platí do 05.01.2025
									</div>

									<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
										Podmínky
										<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
											<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
										</svg>
									</a>
								</div>
							</div>
						</div>
						<div class="min-w-[289px] coupon-item flex flex-col items-center relative">
							<div class="w-full bg-white rounded-t-2xl">
								<div class="absolute right-[-17px] -top-[20px]"></div>
								<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
									<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
										<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
									</svg>
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="h-[122px] flex items-center justify-center" target="_blank">
									<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
								</a>

								<div class="mb-3 md:mb-[18px]">
									<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px] xl:hover:underline" target="_blank">
									<span class="text-primary-orange">Exkluzivně: </span>
									<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
								</a>

								<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

								</svg>
							</div>

							<div class="w-full max-h-[37px]">
								<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="white"></path>
								</svg>
							</div>

							<div class="px-1.5 pb-2 w-full bg-white rounded-b-2xl">
								<button class="flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
									<span class="m-auto js-copy-change-text">TIPLI</span>
									<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
									<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
								</button>
								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank" class="block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
									Použít kupón
								</a>

								<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
									<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0
                text-center text-dark-4 md:justify-start md:h-auto md:items-center">
										<div class="">
											<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
												<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
											</svg>
										</div>
										Platí do 05.01.2025
									</div>

									<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
										Podmínky
										<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
											<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
										</svg>
									</a>
								</div>
							</div>
						</div>
						<div class="min-w-[289px]">
							<div class="coupon-item flex flex-col items-center relative blur-sm cursor-not-allowed">
								<div class="w-full bg-white/50 rounded-t-2xl">
									<div class="absolute right-[-17px] -top-[20px]"></div>
									<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
										<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
											<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
										</svg>
									</div>

									<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku"
									   class="cursor-not-allowed h-[122px] flex items-center justify-center"
									   target="_blank">
										<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
									</a>

									<div class="mb-3 md:mb-[18px]">
										<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
									</div>

									<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku"
									   class="cursor-not-allowed line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px]
" target="_blank">
										<span class="text-primary-orange">Exkluzivně: </span>
										<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
									</a>

									<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
										<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

									</svg>
								</div>

								<div class="w-full">
									<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="#ffffff80" xmlns="http://www.w3.org/2000/svg">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="#ffffff80"></path>
									</svg>
								</div>

								<div class="px-1.5 pb-2 w-full bg-white/50 rounded-b-2xl">
									<button
										class="cursor-not-allowed flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
										<span class="m-auto js-copy-change-text">TIPLI</span>
										<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
										<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
									</button>
									<div class="block h-[56px] lg:mb-2"></div>

									<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
										<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0 text-center text-dark-4 md:justify-start md:h-auto md:items-center">
											<div class="">
												<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
													<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
												</svg>
											</div>
											Platí do 05.01.2025
										</div>

										<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
											Podmínky
											<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
												<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
											</svg>
										</a>
									</div>
								</div>
							</div>

							<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank"
							   style="box-shadow: 6px 6px 13.5px 0px rgba(239a, 127, 26, 0.51);"
							   class="mt-[-112px] lg:mt-[-92px] m-auto max-w-[277px] lg:max-w-[341px] block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
								Použít kupón
							</a>
						</div>
					</div>
				</div>
			</div>

			<div class="is-revelation-content">
				<div
					class="text-center text-lg md:text-[33px] font-bold text-white leading-[31.5px] md:leading-[49.5px] pt-[62px] md:pt-[102px]">
					Predbežný stav výhry:
					<span class="bg-secondary-green px-[11px] py-1 rounded-lg">3,30€</span>
				</div>

				<div class="m-auto text-center text-sm leading-[24.5px] text-white mt-[15px] md:mt-[33px] max-w-[386px]">
					Pokiaľ sa už nikto ďalší o výhru neprihlási, získate túto čiastku. Finálnu čiastku vám oznámime mailom na konci dňa.
				</div>

				<div>
					<div class="h-px bg-white/10 w-full mb-2.5 mt-[30px]"></div>
					<div class="flex flex-col lg:flex-row items-center justify-center gap-2.5 lg:gap-10 text-sm text-white leading-[24.5px] font-bold">
						<div>Celkovo správne tipovali:<span class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">56x</span></div>
						<div>Prihlásení o výhru: <span class="ml-2 bg-secondary-green/10 text-secondary-green p-[5px] rounded-md">1 236x</span></div>
					</div>
					<div class="h-px bg-white/10 w-full mt-2.5"></div>
				</div>
			</div>

			<div class="is-lose-content">
				<div class="text-center text-lg md:text-[33px] font-bold leading-[31.5px] md:leading-[49.5px] text-white pt-[73px]">
					Skúste to <span class="text-primary-orange">zajtra znova!</span>
				</div>
				<div
					class="flex items-center justify-center text-center gap-[13px] text-sm text-white mt-2 md:mt-5 md:text-lg font-bold leading-[24.5px] md:leading-[31.5px]">
					Predlžili ste si aktivitu na: 🔥 3 dni v rade
					<div
						class="flex items-center gap-1.5 text-xs leading-[21px font-bold] py-1.5 px-[9px] rounded-md"
						style="background: rgba(48, 65, 93, 0.50);"
					>
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
							<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M7.92578 7.16797V11.4609" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"/>
						</svg>
						7 dni v rade pridá 1 okienko naviac
					</div>
				</div>


				<div class="container mt-[50px] px-0 relative">
					<div class="absolute top-[-19px] left-1/2 transform -translate-x-1/2">
						<svg width="124" height="60" viewBox="0 0 124 60" fill="none"
							 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
							<rect class="h-[61px]"  width="124" height="60" fill="url(#pattern0_3_467)"/>
							<defs>
								<pattern id="pattern0_3_467" patternContentUnits="objectBoundingBox" width="1" height="1">
									<use xlink:href="#image0_3_467" transform="scale(0.00403226 0.00833333)"/>
								</pattern>
								<image id="image0_3_467" width="248" height="120" xlink:href="data:image/png;base64,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"/>
							</defs>
						</svg>
						<div
							class="flex items-center gap-[5px] uppercase relative text-white leading-7 font-bold top-[-42px] left-[37px]">
							<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
								<rect width="17" height="17" fill="url(#pattern0_1389_4320)"/>
								<defs>
									<pattern id="pattern0_1389_4320" patternContentUnits="objectBoundingBox" width="1" height="1">
										<use xlink:href="#image0_1389_4320" transform="scale(0.0138889)"/>
									</pattern>
									<image id="image0_1389_4320" width="72" height="72" xlink:href="data:image/png;base64,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"/>
								</defs>
							</svg>
							Tip
						</div>
					</div>
					<div class="pt-[78px] pb-[143px] rounded-[26px]" style="background: linear-gradient(180deg, #FFF 46.7%, #ECEDF0 89.22%)">
						<div class="m-auto text-base md:text-lg leading-[31.5px] text-center max-w-[265px] md:max-w-[522px]">
							Máte príležitosť
							<span class="font-bold">zvýšiť svoje šance navýhru</span>
							získaním nového okienka za nákup cez Tipli.
						</div>
						<svg class="m-auto my-3" xmlns="http://www.w3.org/2000/svg" width="70" height="5" viewBox="0 0 70 5" fill="none">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M29.4122 0.238338C29.1857 0.219674 28.9556 0.230573 28.7406 0.270138C28.5256 0.309704 28.3317 0.376833 28.1747 0.46607L23.7424 2.98681L19.043 0.504485C18.7622 0.35665 18.3893 0.274534 18.0053 0.276C17.6213 0.277465 17.2573 0.362406 16.9926 0.512324L12.5595 3.03241L7.86172 0.550426C7.58136 0.402167 7.20822 0.319754 6.82397 0.32122C6.43971 0.322687 6.07563 0.407919 5.81139 0.558258L0.385673 3.64432C0.126105 3.7957 -0.0121874 3.99871 0.000844083 4.20926C0.0138756 4.41982 0.177178 4.62094 0.455277 4.76898C0.733376 4.91702 1.10384 5.00004 1.48628 5C1.86872 4.99996 2.23225 4.91687 2.498 4.76878L6.92958 2.24798L11.6291 4.73011C11.9099 4.8779 12.2828 4.95998 12.6668 4.95852C13.0508 4.95705 13.4147 4.87215 13.6794 4.72228L18.1119 2.20215L22.8114 4.68446C23.0922 4.83212 23.4651 4.9141 23.849 4.91259C24.2328 4.91109 24.5967 4.82621 24.8614 4.6764L29.2925 2.15596L33.992 4.63808C34.1305 4.71315 34.2941 4.77267 34.4732 4.8132C34.6524 4.85373 34.8436 4.87447 35.0357 4.87419C35.2278 4.87392 35.4171 4.85264 35.5925 4.81161C35.7679 4.77057 35.926 4.71059 36.0575 4.63514C36.1891 4.55969 36.2915 4.47027 36.3589 4.37206C36.4263 4.27385 36.4573 4.16882 36.4501 4.06304C36.4428 3.95726 36.3976 3.85285 36.3169 3.75584C36.2362 3.65884 36.1217 3.57119 35.98 3.49795L30.2251 0.458241C30.0013 0.339895 29.7167 0.262884 29.4122 0.238338Z" fill="#E1E4E8"></path>
							<path fill-rule="evenodd" clip-rule="evenodd" d="M62.9611 0.00886561C62.7346 -0.00979813 62.5045 0.00110077 62.2895 0.0406659C62.0745 0.0802315 61.8806 0.14736 61.7236 0.236598L57.2914 2.75734L52.5919 0.275013C52.3112 0.127178 51.9382 0.0450619 51.5542 0.0465274C51.1702 0.0479932 50.8062 0.132934 50.5416 0.282851L46.1084 2.80294L41.4106 0.320954C41.1303 0.172695 40.7571 0.0902813 40.3729 0.0917481C39.9886 0.0932146 39.6246 0.178447 39.3603 0.328786L33.9346 3.41485C33.675 3.56623 33.5367 3.76924 33.5498 3.97979C33.5628 4.19035 33.7261 4.39146 34.0042 4.5395C34.2823 4.68754 34.6528 4.77056 35.0352 4.77053C35.4176 4.77049 35.7812 4.6874 36.0469 4.53931L40.4785 2.0185L45.178 4.50064C45.4588 4.64843 45.8317 4.73051 46.2157 4.72904C46.5997 4.72758 46.9636 4.64268 47.2283 4.49281L51.6608 1.97267L56.3603 4.45498C56.6412 4.60265 57.014 4.68463 57.3979 4.68312C57.7818 4.68161 58.1456 4.59674 58.4103 4.44693L62.8414 1.92649L67.5409 4.40861C67.6794 4.48368 67.843 4.5432 68.0222 4.58373C68.2013 4.62426 68.3925 4.64499 68.5846 4.64472C68.7768 4.64445 68.966 4.62317 69.1414 4.58213C69.3168 4.5411 69.4749 4.48112 69.6065 4.40567C69.738 4.33022 69.8404 4.24079 69.9078 4.14259C69.9752 4.04438 70.0062 3.93935 69.999 3.83357C69.9918 3.72779 69.9465 3.62338 69.8658 3.52637C69.7851 3.42937 69.6706 3.34171 69.5289 3.26848L63.774 0.228769C63.5502 0.110423 63.2656 0.0334118 62.9611 0.00886561Z" fill="#E1E4E8"></path>
						</svg>
						<div class="text-sm leading-[24.5px] text-dark-1 max-w-[275px] md:max-w-[665px] m-auto text-center">
							Každým šťastným obchodom si zvyšujete šancu na výhru. Na každý z nich však potrebujete
							jedno okienko. Vyberte si z aktuálne populárnych kupónov a za každý nákup získate jedno okienko.
						</div>
					</div>

					<div class="flex lg:grid lg:grid-cols-3 gap-5 px-2.5 lg:px-[50px] mt-[-100px] overflow-x-auto pb-2.5">
						<div class="min-w-[289px] coupon-item flex flex-col items-center relative">
							<div class="w-full bg-white rounded-t-2xl">
								<div class="absolute right-[-17px] -top-[20px]"></div>
								<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
									<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
										<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
									</svg>
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="h-[122px] flex items-center justify-center" target="_blank">
									<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
								</a>

								<div class="mb-3 md:mb-[18px]">
									<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px] xl:hover:underline" target="_blank">
									<span class="text-primary-orange">Exkluzivně: </span>
									<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
								</a>

								<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

								</svg>
							</div>

							<div class="w-full max-h-[37px]">
								<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="white"></path>
								</svg>
							</div>

							<div class="px-1.5 pb-2 w-full bg-white rounded-b-2xl">
								<button class="flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
									<span class="m-auto js-copy-change-text">TIPLI</span>
									<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
									<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
								</button>
								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank" class="block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
									Použít kupón
								</a>

								<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
									<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0
                text-center text-dark-4 md:justify-start md:h-auto md:items-center">
										<div class="">
											<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
												<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
											</svg>
										</div>
										Platí do 05.01.2025
									</div>

									<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
										Podmínky
										<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
											<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
										</svg>
									</a>
								</div>
							</div>
						</div>
						<div class="min-w-[289px] coupon-item flex flex-col items-center relative">
							<div class="w-full bg-white rounded-t-2xl">
								<div class="absolute right-[-17px] -top-[20px]"></div>
								<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
									<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
										<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
									</svg>
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="h-[122px] flex items-center justify-center" target="_blank">
									<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
								</a>

								<div class="mb-3 md:mb-[18px]">
									<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
								</div>

								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" class="line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px] xl:hover:underline" target="_blank">
									<span class="text-primary-orange">Exkluzivně: </span>
									<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
								</a>

								<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
									<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
									<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

								</svg>
							</div>

							<div class="w-full max-h-[37px]">
								<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="white"></path>
								</svg>
							</div>

							<div class="px-1.5 pb-2 w-full bg-white rounded-b-2xl">
								<button class="flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
									<span class="m-auto js-copy-change-text">TIPLI</span>
									<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
									<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
								</button>
								<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank" class="block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
									Použít kupón
								</a>

								<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
									<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0
                text-center text-dark-4 md:justify-start md:h-auto md:items-center">
										<div class="">
											<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
												<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
											</svg>
										</div>
										Platí do 05.01.2025
									</div>

									<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
										Podmínky
										<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
											<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
										</svg>
									</a>
								</div>
							</div>
						</div>
						<div class="min-w-[289px]">
							<div class="coupon-item flex flex-col items-center relative blur-sm cursor-not-allowed">
								<div class="w-full bg-white/50 rounded-t-2xl">
									<div class="absolute right-[-17px] -top-[20px]"></div>
									<div class="absolute left-[5px] top-[5px]" title="Exkluzivně">
										<svg xmlns="http://www.w3.org/2000/svg" width="71" height="71" viewBox="0 0 71 71" fill="none">
											<path fill-rule="evenodd" clip-rule="evenodd" d="M71 0L0 71V32C0 16.9151 0 9.37258 4.68629 4.68629C9.37258 0 16.9151 0 32 0H71Z" fill="#FEF3E9"></path>
											<path fill-rule="evenodd" clip-rule="evenodd" d="M22.6636 13.3462C23.2605 12.6228 24.4232 13.1171 24.3287 14.0543L23.6619 20.6371H29.0559C29.2353 20.6371 29.4111 20.6889 29.5625 20.7862C29.7139 20.8835 29.8347 21.0225 29.9109 21.1867C29.987 21.3509 30.0152 21.5336 29.9922 21.7135C29.9693 21.8933 29.8961 22.0628 29.7812 22.2021L20.3364 33.6538C19.7395 34.3772 18.5768 33.8829 18.6713 32.9457L19.3381 26.3629H13.9441C13.7647 26.3629 13.5889 26.3111 13.4375 26.2138C13.2861 26.1165 13.1653 25.9775 13.0891 25.8133C13.013 25.6491 12.9848 25.4664 13.0078 25.2865C13.0307 25.1067 13.1039 24.9372 13.2188 24.7979L22.6636 13.3462ZM15.9606 24.4543H21.0117C21.0645 24.4543 21.1167 24.4655 21.165 24.4871C21.2132 24.5088 21.2565 24.5404 21.2919 24.58C21.3273 24.6195 21.3541 24.6662 21.3706 24.7168C21.3871 24.7675 21.3929 24.8211 21.3876 24.8742L20.8653 30.0322L27.0394 22.5457H21.9883C21.9355 22.5457 21.8833 22.5345 21.835 22.5129C21.7868 22.4912 21.7435 22.4596 21.7081 22.42C21.6727 22.3805 21.6459 22.3338 21.6294 22.2832C21.6129 22.2325 21.6071 22.1789 21.6124 22.1258L22.1347 16.9687L15.9606 24.4543Z" fill="#EF7F1A"></path>
										</svg>
									</div>

									<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku"
									   class="cursor-not-allowed h-[122px] flex items-center justify-center"
									   target="_blank">
										<img src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/koGjpriguRiJyZIP4NFO6Zb8vUvpi-Myh037_S8TowY/resize:fit:320:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9hcnRpY2xlcy1hcnRpY2xlLXByZXZpZXdJbWFnZS8xLnBuZw.png" loading="lazy" alt="AliExpress" class="max-h-[80px] max-w-[112px] md:max-w-[160px]">
									</a>

									<div class="mb-3 md:mb-[18px]">
										<img class="m-auto" src="/new-design/divider-coupon.svg" loading="lazy" alt="divider">
									</div>

									<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku"
									   class="cursor-not-allowed line-clamp-2 min-h-10 md:min-h-14 text-center text-dark-1 leading-[21px] text-xs md:text-base max-w-[236px] w-full mx-auto px-3 md:leading-[28px]
" target="_blank">
										<span class="text-primary-orange">Exkluzivně: </span>
										<strong>Slevový kupon</strong> <strong>3</strong>x <strong>50 Kč</strong> na první objednávku na&nbsp;AliExpress
									</a>

									<svg class="hidden m-auto" id="couponItem-tooltip" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
										<path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<path d="M7.92578 7.16895V11.4619" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
										<circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>

									</svg>
								</div>

								<div class="w-full">
									<svg class="w-full" width="100%" height="39px" viewBox="0 0 285 39" preserveAspectRatio="none" fill="#ffffff80" xmlns="http://www.w3.org/2000/svg">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M0 39C0.00928829 37.6508 0.0634183 36.8876 0.378876 36.3228C0.759676 35.6409 1.88646 34.9468 4.14003 33.5586C8.85493 30.6541 11.9981 25.4441 11.9981 19.5C11.9981 13.5559 8.85494 8.34592 4.14003 5.44143C1.88646 4.05319 0.759676 3.35906 0.378876 2.67723C0.0634184 2.11239 0.00928832 1.34919 7.8465e-09 0H284.996C284.987 1.34919 284.933 2.11239 284.617 2.67723C284.236 3.35906 283.11 4.05319 280.856 5.44144C276.141 8.34592 272.998 13.5559 272.998 19.5C272.998 25.4441 276.141 30.6541 280.856 33.5586L280.856 33.5588C283.11 34.9469 284.237 35.641 284.617 36.3228C284.933 36.8876 284.987 37.6508 284.996 39H0Z" fill="#ffffff80"></path>
									</svg>
								</div>

								<div class="px-1.5 pb-2 w-full bg-white/50 rounded-b-2xl">
									<button
										class="cursor-not-allowed flex py-3 items-center justify-between relative w-full md:py-4 rounded-xl text-xs md:text-sm leading-[28px] border-dashed border border-dark-4 mb-1.5 js-copy-code-button" data-coupon-code="TIPLI" data-coupon-copied="Zkopírováno">
										<span class="m-auto js-copy-change-text">TIPLI</span>
										<img class="absolute right-[15px] js-copy-icon" src="/new-design/copy-icon.svg" alt="copy">
										<img class="absolute right-[15px] hidden js-copy-icon-copied" src="/new-design/copy-icon-copied.svg" alt="copy">
									</button>
									<div class="block h-[56px] lg:mb-2"></div>

									<div class="md:px-1 flex flex-col md:flex-row items-center justify-between text-xs text-dark-4">
										<div class="flex justify-center items-center relative gap-[5px] w-full z-10 h-[20px]  mt-[5px] md:mt-0 text-center text-dark-4 md:justify-start md:h-auto md:items-center">
											<div class="">
												<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
													<path d="M1.62318 9.16748C1.09497 8.13879 0.895313 6.96862 1.05194 5.81945C1.20857 4.67027 1.7137 3.59914 2.4972 2.75482C3.28073 1.91049 4.30366 1.33488 5.42406 1.10792C6.54439 0.880965 7.70659 1.01392 8.74912 1.48833C9.79165 1.96275 10.6629 2.75507 11.2417 3.75525C11.8206 4.75544 12.0784 5.91385 11.9793 7.06962C11.8802 8.22538 11.4291 9.32109 10.6888 10.2047C9.94839 11.0882 8.95546 11.7157 7.84786 12M6.00048 3.0299V7.09183H8.50048" stroke="#ADB3BF" stroke-linecap="round" stroke-linejoin="round"></path>
												</svg>
											</div>
											Platí do 05.01.2025
										</div>

										<a href="/moje-oblibene?openDeal=slevovy-kupon-3x-50-kc-na-prvni-objednavku-1#deal-1" data-ajax-call="js-open-deal-detail" class="ajax flex items-center gap-[5px] leading-[17.5px] mt-[5px] md:mt-0 hover:underline">
											Podmínky
											<svg xmlns="http://www.w3.org/2000/svg" width="7" height="4" viewBox="0 0 7 4" fill="none">
												<path d="M1 0.597656L3.14741 3.05184C3.45881 3.40773 4.01245 3.40773 4.32385 3.05184L6.47126 0.597656" stroke="#ADB3BF"></path>
											</svg>
										</a>
									</div>
								</div>
							</div>

							<a href="/prejit/sleva/slevovy-kupon-3x-50-kc-na-prvni-objednavku" target="_blank"
							   style="box-shadow: 6px 6px 13.5px 0px rgba(239a, 127, 26, 0.51);"
							   class="mt-[-112px] lg:mt-[-92px] m-auto max-w-[277px] lg:max-w-[341px] block text-center w-full py-3.5 relative rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
								Použít kupón
							</a>
						</div>
					</div>
				</div>
			</div>

			<div class="py-20">
				<div class="w-full md:max-w-[385px] mx-auto relative">
					<div class="bg-white w-full rounded-2xl relative z-40">
						<div class="relative bg-[#15243E] max-w-fit mx-auto">
							<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
								<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
									<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
									<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
								</linearGradient>
								<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
							</svg>

							<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
								<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
									<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
									<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
								</linearGradient>
								<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
							</svg>


							<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
								💡 Viete, že ..
							</div>
						</div>

						<div class="pt-[18px] px-[30px] font-bold leading-7 text-center mb-5">
							Nakúpte cez Tipli a spolu s odmenou za nákup získate 1 okienko navyše
						</div>

						<img class="mx-auto"  src="/new-design/widget-default.svg" alt="widget">

						<div class="px-5 pb-5">
							<div class="relative mb-5 -mt-5">
								<div class="absolute inset-y-0 left-0 flex items-center pl-[21px] pointer-events-none">
									<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
									</svg>
								</div>
								<input
									type="text"
									class="w-full pl-[48px] h-[56px] pr-4 py-3 border text-sm border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors duration-200 text-gray-900 placeholder-gray-500"
									placeholder="Vyhľadať obchod"
								>
							</div>
							<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
								Všetky obchody s odmenou  🛒
							</a>
						</div>
					</div>

					<div class="hidden md:block w-[385px] bottom-[-13px] right-[-13px] h-[376px] bg-light-6/40 rounded-2xl absolute z-30"></div>
					<div
						class="hidden md:block w-[385px] bottom-[-22px] right-[21px] h-[376px] blur-[1px] border border-white/20 rounded-2xl absolute z-20"
						style="transform: rotate(-3.207deg);"
					></div>

					<div style="opacity: 0.07" class="hidden md:block absolute whitespace-nowrap -translate-x-1/2 left-1/2 translate-y-1/2 bottom-1/2 text-[160px] font-bold text-[#2F67C2]">Tipli Tipli Tipli</div>

					<div class="hidden md:block absolute w-[506px] h-[178px] whitespace-nowrap blur-[135px] -translate-x-1/2 left-1/2 translate-y-1/2 bottom-[11%] bg-[#2F67C2]"></div>

					<div class="relative w-full max-w-[295px] mx-auto md:hidden">
						<div class="mt-10 text-xs leading-[21px] text-white/70 text-center">Dnes bez výhry. Nevadí.</div>
						<div class="text-lg leading-[31.5px] text-white text-center mb-[17px]">Odmeny bežia ďalej!</div>

						<img class="absolute top-0 left-[10px]" src="/new-design/left-stars.png" alt="stars">
						<img class="absolute top-0 right-[10px]" src="/new-design/right-stars.png" alt="stars">
					</div>

					<div class="p-[2px] border border-secondary-green rounded-lg mx-10 md:hidden">
						<a
							href="#"
							class="relative flex items-center justify-center text-white font-bold text-sm h-[52px] rounded-lg"
							style="background: linear-gradient(139deg, rgba(102, 185, 64, 0.00) 19.67%, rgba(102, 185, 64, 0.30) 82.44%);"
						>
							<span class="mx-auto">Získať odmenu za nákup</span>
							<span class="absolute right-5 flex items-center">
							  <svg class="relative z-30" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
								<path d="M1 11L11 1M10.9984 9.4741L10.9983 1.00032L2.52456 1.00031" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							  </svg>
							  <span class="absolute -top-3 -left-3 w-[36px] h-[36px] bg-secondary-green rounded-full blur-[8px] z-20"></span>
							</span>
						</a>
					</div>

					<div class="text-xs leading-[21px] text-white/70 text-center mt-5 md:hidden">ℹ️  Viac informácií o Tipli rozdáva nájdete nižšie</div>


				</div>
			</div>

			<div class="py-20">
				<div class="text-white text-2xl mb-5">Nové widgety</div>

				<div class="flex flex-col md:flex-row gap-20 mb-10">
					<div>
						<div class="text-white">DEFAULT</div>
						<div class="bg-white w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="pt-[25px] px-[30px] font-bold leading-7 text-center mb-[37px]">
								Nakúpte cez Tipli a spolu s odmenou za nákup získate 1 okienko navyše
							</div>

							<img src="/new-design/widget-default.svg" alt="widget">

							<div class="px-5 pb-5">
								<div class="relative mb-5 -mt-5">
									<div class="absolute inset-y-0 left-0 flex items-center pl-[21px] pointer-events-none">
										<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
										</svg>
									</div>
									<input
										type="text"
										class="w-full pl-[48px] h-[56px] pr-4 py-3 border text-sm border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors duration-200 text-gray-900 placeholder-gray-500"
										placeholder="Vyhľadať obchod"
									>
								</div>
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Všetky obchody s odmenou  🛒
								</a>
							</div>
						</div>
					</div>

					<div>
						<div class="text-white">CASHBACK</div>
						<div class="bg-white w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									🧭  Čo ďalej?
								</div>
							</div>
							<div class="pt-[25px] px-[30px] font-bold leading-7 text-center">
								Získajte 30 € na ruku iba za dokončenie registrácie.
							</div>

							<img class="mx-auto my-[14px]" src="/new-design/widget-cashback.png" alt="widget">

							<div class="px-5 pb-5">
								<div class="text-sm leading-[24.5px] text-dark-2 text-center mb-[15px]">
									Sazka má pre užívateľov Tipli jedinečnú ponuku. <span class="font-bold">Za 3 minúty máte hotovo.</span>
								</div>

								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Vyzdvihnúť 30 € za registráciu
								</a>
							</div>
						</div>
					</div>
				</div>


				<div class="flex flex-col md:flex-row gap-5">
					<div>
						<div class="text-white">Splniteľné akcie</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="pt-[25px] px-[30px] font-bold leading-7 text-center mb-[37px]">
								Stiahnite si aplikáciu do telefónu a už nikdy nezabudnete na odhalenie
							</div>

							<div class="mb-2 relative">
								<img class="mx-auto" src="/new-design/widget-action-1.png" alt="widget">
								<div class="flex items-center gap-2 bg-white w-full max-w-fit whitespace-nowrap p-2 rounded-lg absolute bottom-[25px] left-1/2 -translate-x-1/2">
									<svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 35 35" fill="none">
										<rect width="35" height="35" rx="6" fill="#EF7F1A"/>
										<path d="M17.1087 22.3878C17.421 22.4908 17.7223 22.5441 18.0127 22.5441C18.6429 22.5316 19.2492 22.3009 19.7231 21.8941C19.9665 21.6849 20.1698 21.4221 20.3331 21.1131C20.5473 20.7118 20.6053 19.9873 20.6126 19.2555H24C24 20.9604 23.8076 24.3699 20.0135 25.6413C19.3019 25.8793 18.6155 26 17.9548 26C17.2941 26 16.6113 25.8793 15.9106 25.6413C15.2135 25.4035 14.5707 25.0449 13.9862 24.5582C13.4017 24.0715 12.9259 23.4536 12.5556 22.7006C12.1851 21.9512 12 21.0599 12 20.0335V10H15.4165V14.2689H20.765V17.6466H15.4168V20.0477C15.4168 20.4419 15.4933 20.8006 15.6493 21.1131C15.9422 21.7115 16.4682 22.1705 17.1087 22.3878Z" fill="white"/>
									</svg>
									<div>
										<div class="flex items-center gap-[37px] justify-between">
											<div class="text-sm font-bold">Tipli Rozdává</div>
											<div class="text-[10px] text-dark-4">pred 3 minútami</div>
										</div>
										<div class="text-xs text-dark-2">Je 12:00. Vyhrali ste dnes 20 €?</div>
									</div>
								</div>
							</div>

							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Stiahnuť aplikáciu do telefónu
								</a>
							</div>
						</div>
					</div>

					<div>
						<div class="text-white">Splniteľné akcie</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="pt-[25px] px-[30px] font-bold leading-7 text-center mb-2">
								Aktivujte si rozšírenie do prehliadača a získate okienko navyše!
							</div>

							<div class="bg-white pt-[27px] pb-10 w-[200px] rounded-2xl mx-auto bottom-[-29px] relative rotate-[4.5deg]">
								<img class="mx-auto my-[14px]" src="/new-design/chrome.png" alt="widget">
								<img class="mx-auto" src="/new-design/widget-stars.svg" alt="widget-stars">
								<div class="text-dark-2 text-sm leading-[24.5px] text-center">60 tis. počet užívateľov</div>

								<div class="absolute -top-5 -right-[28px]">
									<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
										<foreignObject x="-6" y="-6" width="84" height="84"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_0_8280_3070_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="6" x="1.25" y="1.25" width="69.5" height="69.5" rx="34.75" fill="#66B940" fill-opacity="0.3" stroke="#66B940" stroke-width="2.5"/>
										<path d="M36.4999 26C37.0016 26 37.4082 26.4067 37.4082 26.9083L37.4081 46.0917C37.4081 46.5933 37.0014 47 36.4997 47C35.9981 47 35.5914 46.5933 35.5914 46.0917L35.5916 26.9083C35.5916 26.4067 35.9983 26 36.4999 26Z" fill="#66B940"/>
										<path d="M47 36.5001C47 35.9984 46.5933 35.5918 46.0917 35.5918L26.9083 35.5919C26.4067 35.5919 26 35.9986 26 36.5003C26 37.0019 26.4067 37.4086 26.9083 37.4086L46.0917 37.4084C46.5933 37.4084 47 37.0017 47 36.5001Z" fill="#66B940"/>
										<foreignObject x="39.1" y="39.1" width="51.8" height="51.8"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5.45px);clip-path:url(#bgblur_1_8280_3070_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="10.9" x="50" y="50" width="30" height="30" rx="15" fill="white" fill-opacity="0.6"/>
										<path d="M67 68.1451C67.6348 68.6404 68.4247 68.9435 69.2637 69C72.1356 69 73.9306 66.0833 72.4946 63.75C71.8282 62.6671 70.5965 62 69.2637 62C66.5989 62 65 65.5 65 65.5C65 65.5 63.4011 69 60.7363 69C57.8644 69 56.0694 66.0833 57.5054 63.75C58.1718 62.6671 59.4035 62 60.7363 62C61.5755 62.0574 62.3653 62.3612 63 62.857" stroke="#646C7C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
										<defs>
											<clipPath id="bgblur_0_8280_3070_clip_path" transform="translate(6 6)"><rect x="1.25" y="1.25" width="69.5" height="69.5" rx="34.75"/>
											</clipPath><clipPath id="bgblur_1_8280_3070_clip_path" transform="translate(-39.1 -39.1)"><rect x="50" y="50" width="30" height="30" rx="15"/>
										</clipPath></defs>
									</svg>
								</div>
							</div>

							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Stiahnuť aplikáciu do telefónu
								</a>
							</div>
						</div>
					</div>

					<div>
						<div class="text-white">Splniteľné akcie</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="pt-[27px] px-[36px] text-sm leading-7 text-center mb-[37px]">
								<span class="font-bold">Získali ste 50 % extra cashback</span> v dnešnom šťastnom obchode! Nakúpte ešte dnes do polnoci a získate vyšší cashback + nové okienko do hry.
							</div>

							<div class="relative mb-[52px] mx-auto">
								<div class="relative w-[126px] h-[77px] mx-auto z-20">
									<div class="flex justify-center items-center bg-white rounded-2xl border w-[126px] h-[77px]">
										<img class="h-10 max-w-20" src="https://www.tipli.cz/upload/images/shops-shop-logo/789033.svg" alt="">
									</div>

									<img class="absolute top-[3px] right-[-68px] z-20" src="/new-design/widget-cashback-3-circle.svg" alt="widget">
								</div>

								<svg class="absolute left-[-30px] bottom-[-73px] z-10" xmlns="http://www.w3.org/2000/svg" width="389" height="131" viewBox="0 0 389 131" fill="none">
									<g opacity="0.29" filter="url(#filter0_f_8280_3087)">
										<path d="M158.859 47H231.238L342 84H47L158.859 47Z" fill="#66B940"/>
									</g>
									<defs>
										<filter id="filter0_f_8280_3087" x="0.599998" y="0.599998" width="387.8" height="129.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
											<feFlood flood-opacity="0" result="BackgroundImageFix"/>
											<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
											<feGaussianBlur stdDeviation="23.2" result="effect1_foregroundBlur_8280_3087"/>
										</filter>
									</defs>
								</svg>
							</div>

							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Ísť nakupovať
								</a>
							</div>
						</div>
					</div>
				</div>


				<div class="flex gap-5 mt-20">
					<div>
						<div class="text-white">Tipli Rozdáva</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="pt-[27px] px-[36px] text-sm leading-7 text-center">
								<span class="font-bold">Skvelá práca!</span>
								Šťastný obchod ste trafili už 7-krát po sebe – a odmenou je extra okienko. Vyberte si nový obchod a zajtra môžete mať ešte väčšiu šancu na výhru.
							</div>

							<div class="relative mb-[27px]">
								<img class="mx-auto" src="/new-design/widget-4-donkey.png" alt="widget">

								<svg class="absolute bottom-[-49px] left-[-10px]" xmlns="http://www.w3.org/2000/svg" width="389" height="131" viewBox="0 0 389 131" fill="none">
									<g opacity="0.29" filter="url(#filter0_f_8280_3112)">
										<path d="M158.859 47H231.238L342 84H47L158.859 47Z" fill="#66B940"/>
									</g>
									<defs>
										<filter id="filter0_f_8280_3112" x="0.599998" y="0.599998" width="387.8" height="129.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
											<feFlood flood-opacity="0" result="BackgroundImageFix"/>
											<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
											<feGaussianBlur stdDeviation="23.2" result="effect1_foregroundBlur_8280_3112"/>
										</filter>
									</defs>
								</svg>
							</div>

							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Nastaviť nové okienko
								</a>
							</div>
						</div>
					</div>
				</div>


				<div class="flex flex-col md:flex-row gap-5 mt-20">
					<div>
						<div class="text-white">Content</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡 Viete, že ..
								</div>
							</div>

							<div class="px-5 mt-2 relative">
								<img class="object-cover rounded-2xl w-full max-h-[180px]" src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?q=80&w=1746&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="">

								<div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-xl flex justify-center items-center w-[97px] h-[55px] shadow-lg bg-white">
									<img class="max-w-[58px] max-h-[38px]" alt="Velký Košík" src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/29xsZaBMNUh-y1RmB5ZCJdN2fQ0fBgfyaN5gakKlmi4/resize:fit:116:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNjcucG5n.png" loading="lazy">
								</div>
							</div>
							<div class="pt-[47px] mb-5 px-[36px] text-sm leading-7 text-center">
								<span class="font-bold">Dekoratívna lampička</span>
								 vhodná ako darček pre všetkých gejmerov práve teraz v akcii "Mega Zľavy" na Zalando
							</div>


							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Ísť nakupovať
								</a>
							</div>
						</div>
					</div>

					<div>
						<div class="text-white">Content</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									📌  TIP DŇA
								</div>
							</div>

							<div class="pt-5 mb-[38px] px-[36px] text-sm leading-7 text-center">
								<span class="font-bold">Až do pondelka 14. apríla</span>
								prebiehajú veľké zľavy na široký sortiment. Ideálny čas na výhodný nákup doplnkov pred Veľkou nocou!
							</div>

							<div class="relative">
								<svg class="absolute top-[-50px]" xmlns="http://www.w3.org/2000/svg" width="363" height="131" viewBox="0 0 363 131" fill="none">
									<g opacity="0.29" filter="url(#filter0_f_8280_3156)">
										<path d="M149 47H215L316 84H47L149 47Z" fill="#EF7F1A"/>
									</g>
									<defs>
										<filter id="filter0_f_8280_3156" x="0.599998" y="0.599998" width="361.8" height="129.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
											<feFlood flood-opacity="0" result="BackgroundImageFix"/>
											<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
											<feGaussianBlur stdDeviation="23.2" result="effect1_foregroundBlur_8280_3156"/>
										</filter>
									</defs>
								</svg>
								<div
									class="flex items-center py-2.5 pl-2.5 pr-[22px] rounded-lg bg-white gap-2.5 mx-[33px] mb-[62px] relative z-30"
									style="box-shadow: 0px 7px 15.2px 0px rgba(0, 0, 0, 0.03);"
								>
									<div class="rounded-xl flex justify-center items-center min-w-[59px] h-[50px] border border-light-5 bg-white">
										<img class="max-w-[38px] max-h-[28px]" alt="Velký Košík" src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/29xsZaBMNUh-y1RmB5ZCJdN2fQ0fBgfyaN5gakKlmi4/resize:fit:116:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNjcucG5n.png" loading="lazy">
									</div>

									<div class="flex items-center w-full justify-between">
										<div class="text-sm leading-[20px] text-dark-1 font-medium">Jarné novinky -20%</div>
										<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
											<path d="M1 12L12 1M11.9982 10.3215L11.9982 1.00035L2.67702 1.00034" stroke="#BDC2CC" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
										</svg>
									</div>
								</div>

								<div class="absolute top-[27px] left-[45px] right-[45px] rounded-md bg-white/70 h-[57px] z-20"></div>

								<div class="absolute top-[41px] left-[57px] right-[57px] rounded-md bg-white/50 h-[57px] z-10"></div>
							</div>



							<div class="px-5 pb-5">
								<a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
									Ísť nakupovať
								</a>
							</div>
						</div>
					</div>
				</div>

				<div class="flex gap-5 mt-20">
					<div>
						<div class="text-white">Reklama</div>
						<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
							<div class="relative bg-[#15243E] max-w-fit mx-auto">
								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
									<linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
								</svg>

								<svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
									<linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
										<stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
										<stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
									</linearGradient>
									<path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
								</svg>


								<div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
									💡📢  REKLAMA
								</div>
							</div>

							<div class="px-5 pb-5 pt-[18px]">
								<img class="rounded-2xl object-cover" src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=1548&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="ads">
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="guide-section" class="mt-10 md:mt-[70px] flex gap-10">
				<div class="w-full">
					<div class="flex gap-[30px] mb-4 text-nowrap overflow-x-auto">
						<div id="tab-1" class="cursor-pointer hover:text-white item-tab flex items-center gap-[9px] text-xs lg:text-sm text-white/50 leading-[24.5px] lg:leading-[24.5px] transition duration-200">
							<svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
								 fill="none">
								<path d="M6.01419 6.38579C6.01419 5.97891 6.13557 5.58117 6.36288 5.24287C6.59019 4.90456 6.91338 4.64088 7.29142 4.48518C7.66946 4.32948 8.08547 4.28873 8.48679 4.36811C8.88811 4.44749 9.25677 4.64343 9.54609 4.93113C9.83553 5.21883 10.0325 5.5854 10.1124 5.98445C10.1922 6.3835 10.1512 6.79714 9.99464 7.17306C9.83806 7.54896 9.5728 7.87025 9.23261 8.0963C8.89241 8.32235 8.4923 8.44299 8.08315 8.44299M6.06208 1.27692C4.76145 1.65525 3.59886 2.39971 2.71434 3.42047C1.82982 4.44123 1.26132 5.69466 1.07727 7.02957C0.643464 10.1777 2.00039 13.5564 6.06704 14.7463C7.85142 15.237 9.7584 15.0102 11.3762 14.1151C12.994 13.22 14.193 11.7283 14.7145 9.96198C15.2359 8.19559 15.0379 6.29618 14.1632 4.67366C12.5833 1.74261 9.18276 0.369116 6.06208 1.27692ZM8.08315 11.7346C7.85462 11.7346 7.66936 11.5504 7.66936 11.3231C7.66936 11.0959 7.85462 10.9116 8.08315 10.9116C8.31167 10.9116 8.49694 11.0959 8.49694 11.3231C8.49694 11.5504 8.31167 11.7346 8.08315 11.7346Z" stroke="currentColor" stroke-linecap="round"/>
							</svg>
							Ako to funguje ?
						</div>
						<div id="tab-2" class="cursor-pointer hover:text-white item-tab flex items-center gap-[9px] text-xs lg:text-sm text-dark-3 leading-[24.5px] lg:leading-[24.5px] transition duration-200">
							<svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
								 fill="none">
								<path d="M5.45455 8.38636L7.07879 9.90909L10.5455 6.72727M1 8C1 9.85653 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14349 15 8 15C9.85653 15 11.637 14.2625 12.9497 12.9497C14.2625 11.637 15 9.85653 15 8C15 6.14349 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85653 1 8 1C6.14349 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14349 1 8Z" stroke="currentColor" stroke-linecap="round"/>
							</svg>
							Podmienky
						</div>
					</div>

					<div id="content-1" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">
								{_newFront.luckyShops.default.tabs.howItWorks.steps.step1.title}
							</div>
							<div class="text-sm leading-[24.5px] text-white">
								{_newFront.luckyShops.default.tabs.howItWorks.steps.step1.text}
							</div>
						</div>

						<div class="h-px bg-white/10 w-full my-5"></div>

						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">
								{_newFront.luckyShops.default.tabs.howItWorks.steps.step2.title}
							</div>
							<div class="text-sm leading-[24.5px] text-white">
								{_newFront.luckyShops.default.tabs.howItWorks.steps.step2.text}
							</div>
						</div>

						<div class="h-px bg-white/10 w-full my-5"></div>

						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.title}</div>
							<div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.step3.text}</div>
						</div>

						<div class="h-px bg-white/10 w-full my-5"></div>

						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.title}</div>
							<div class="text-sm leading-[24.5px] text-white">{_newFront.luckyShops.default.tabs.howItWorks.steps.tip.text}</div>
						</div>
					</div>

					<div id="content-2" class="bg-[#30415D] lg:bg-[#30415D]/20 backdrop-blur px-[30px] py-[25px] rounded-2xl hidden">
						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">
								Podmienky 1
							</div>
							<div class="text-sm leading-[24.5px] text-white">
								Text 1
							</div>
						</div>

						<div class="h-px bg-white/10 w-full my-5"></div>

						<div>
							<div class="text-white font-bold leading-6 mb-[5px]">
								Podmienky 2
							</div>
							<div class="text-sm leading-[24.5px] text-white">
								Text 2
							</div>
						</div>
					</div>
				</div>

				<div id="tab3-container" class="hidden lg:block w-[330px]">
					<div id="tab-3"
						class="cursor-pointer lg:mb-4 item-tab hover:text-white flex items-center gap-[9px] text-xs lg:text-sm text-white/50 lg:text-white leading-[24.5px] lg:leading-[24.5px] transition duration-200">
						<svg class="text-current" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
							 fill="none">
							<path d="M6.01419 6.38579C6.01419 5.97891 6.13557 5.58117 6.36288 5.24287C6.59019 4.90456 6.91338 4.64088 7.29142 4.48518C7.66946 4.32948 8.08547 4.28873 8.48679 4.36811C8.88811 4.44749 9.25677 4.64343 9.54609 4.93113C9.83553 5.21883 10.0325 5.5854 10.1124 5.98445C10.1922 6.3835 10.1512 6.79714 9.99464 7.17306C9.83806 7.54896 9.5728 7.87025 9.23261 8.0963C8.89241 8.32235 8.4923 8.44299 8.08315 8.44299M6.06208 1.27692C4.76145 1.65525 3.59886 2.39971 2.71434 3.42047C1.82982 4.44123 1.26132 5.69466 1.07727 7.02957C0.643464 10.1777 2.00039 13.5564 6.06704 14.7463C7.85142 15.237 9.7584 15.0102 11.3762 14.1151C12.994 13.22 14.193 11.7283 14.7145 9.96198C15.2359 8.19559 15.0379 6.29618 14.1632 4.67366C12.5833 1.74261 9.18276 0.369116 6.06208 1.27692ZM8.08315 11.7346C7.85462 11.7346 7.66936 11.5504 7.66936 11.3231C7.66936 11.0959 7.85462 10.9116 8.08315 10.9116C8.31167 10.9116 8.49694 11.0959 8.49694 11.3231C8.49694 11.5504 8.31167 11.7346 8.08315 11.7346Z" stroke="currentColor" stroke-linecap="round"/>
						</svg>
						História
					</div>

					<div id="content-3" class="hidden lg:block bg-[#30415D] lg:bg-[#30415D]/50 backdrop-blur p-5 pb-3 rounded-2xl">
							<div class="flex justify-between items-center text-white text-sm leading-[24.5px] font-bold uppercase">
								<a href="#" target="_blank">
									TEMU
								</a>
								<div class="font-normal">20.08.2024</div>
							</div>
							<div class="h-px bg-white/10 w-full mt-2.5 mb-[14px]"></div>
							<div class="flex items-center justify-between gap-[15px] w-full">
								<a href="#" target="_blank" class="flex items-center justify-center bg-white min-w-[115px] min-h-[57px] rounded-xl">
									<img class="max-w-[70px] max-h-[40px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789033.svg">
								</a>
								<div class="flex flex-col items-end text-sm text-white text-nowrap z-10">
									<div>Celkovo vyhrali: <span class="text-primary-orange">5x</span></div>
									<div>Prihlásili sa o výhru: <span class="text-primary-orange">1x</span></div>
								</div>
							</div>
							<div class="h-px bg-white/10 w-full mt-[14px] mb-2"></div>
							<div class="flex justify-between items-center">
								<div class="text-sm text-white leading-[21px]">Zvolené obchody:</div>
								<div class="flex gap-1.5">
									<div>
										<a href="#" target="_blank" class="flex items-center justify-center bg-white rounded-full w-12 h-12 border-2 border-primary-blue-dark">
											<img class="min-w-[30px] max-w-[30px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/792258.svg" alt="">
										</a>
									</div>
									<div>
										<a href="#" target="_blank" class="flex items-center justify-center bg-white rounded-full w-12 h-12 border-2 border-primary-blue-dark">
											<img class="min-w-[30px] max-w-[30px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789086.svg" alt="">
										</a>
									</div>
									<div>
										<a href="#" target="_blank" class="flex items-center justify-center bg-white rounded-full w-12 h-12 border-2 border-primary-blue-dark">
											<img class="min-w-[30px] max-w-[30px]" src="https://www.tipli.cz/upload/images/shops-shop-logo/789095.svg" alt="">
										</a>
									</div>
								</div>
							</div>
						</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>

<style>
	.bg-lucky-shops {
		position: relative;
		width: 100%;
		min-height: 1119px;
		height: auto;
		z-index: 20;
	}

	.bg-image {
		background-image: url(/new-design/bg-lucky-shops.svg);
		background-repeat: no-repeat;
		background-size: cover;
		background-position: bottom;
		width: 100%;
		height: 100%;
	}
</style>


<script n:syntax=off>
	// SWIPER LUCKY SHOPS
	const swiperContainer = document.querySelector('.swiper.swiper-lucky-shops');
	const slides = swiperContainer.querySelectorAll('.swiper-slide');
	const slidesCount = slides.length;
	const swiper = new Swiper('.swiper.swiper-lucky-shops', {
		direction: 'horizontal',
		loop: true,
		slidesPerView: slidesCount > 4 ? 4.3 : 4,
	});

	document.addEventListener('DOMContentLoaded', () => {
		const tabs = document.querySelectorAll('.item-tab');
		const defaultTab = tabs[0];
		const defaultContent = document.getElementById('content-1');

		defaultContent.classList.remove('hidden');
		defaultTab.classList.remove('text-white/50');
		defaultTab.classList.add('text-white');

		tabs.forEach((tab, index) => {
			if (tab.id === 'tab-3' && window.innerWidth > 1024) return;

			tab.addEventListener('click', function () {
				document.querySelectorAll('#content-1, #content-2, #content-3').forEach(content =>
				content.classList.add('hidden'));
				document.getElementById(`content-${index + 1}`).classList.remove('hidden');
				tabs.forEach(t => {
					if (t.id === 'tab-3' && window.innerWidth > 1024) return;
					t.classList.remove('text-white');
					t.classList.add('text-white/50');
				});
				tab.classList.remove('text-white/50');
				tab.classList.add('text-white');
			});
		});
	});

	// JS for moving elements on mobile
	function moveElements() {
		const swiperElement = document.getElementById('swiper-lucky-shops');
		const swiperContainer = document.querySelector('.swiper-lucky-shops-container');
		const guideSection = document.getElementById('guide-section');

		const tab3 = document.getElementById('tab-3');
		const tab2 = document.getElementById('tab-2');
		const content3 = document.getElementById('content-3');
		const content2 = document.getElementById('content-2');

		const tab3Container = document.getElementById('tab3-container');

		if (window.innerWidth <= 1024) {
			guideSection.parentNode.insertBefore(swiperElement, guideSection);
			tab2.parentNode.insertBefore(tab3, tab2.nextSibling);
			content2.parentNode.insertBefore(content3, content2.nextSibling);
		} else {
			if (!swiperContainer.contains(swiperElement)) {
				swiperContainer.appendChild(swiperElement);
			}

			if (tab3Container && !tab3Container.contains(tab3)) {
				tab3Container.appendChild(tab3);
			}
			if (tab3Container && !tab3Container.contains(content3)) {
				tab3Container.appendChild(content3);
			}
		}

		swiperElement.style.visibility = 'visible';
		tab3.style.visibility = 'visible';
		content3.style.visibility = 'visible';
	}

	document.getElementById('swiper-lucky-shops').style.visibility = 'hidden';
	document.getElementById('tab-3').style.visibility = 'hidden';
	document.getElementById('content-3').style.visibility = 'hidden';

	window.addEventListener('DOMContentLoaded', moveElements);
	window.addEventListener('resize', moveElements);


	// TOGGLE CONTENT
	document.addEventListener('DOMContentLoaded', () => {
		const btnWin = document.getElementById('btn-win');
		const btnShops = document.getElementById('btn-shops');
		const btnRevelation = document.getElementById('btn-revelation');
		const btnLose = document.getElementById('btn-lose');
		const winContents = document.querySelectorAll('.is-win-content');
		const shopsContents = document.querySelectorAll('.is-shops-content');
		const revelationContents = document.querySelectorAll('.is-revelation-content');
		const loseContents = document.querySelectorAll('.is-lose-content');

		function hideAllContents() {
			winContents.forEach(content => content.classList.add('hidden'));
			shopsContents.forEach(content => content.classList.add('hidden'));
			revelationContents.forEach(content => content.classList.add('hidden'));
			loseContents.forEach(content => content.classList.add('hidden'));
		}

		function showContent(type) {
			hideAllContents();
			if (type === 'win') {
				winContents.forEach(content => content.classList.remove('hidden'));
			} else if (type === 'shops') {
				shopsContents.forEach(content => content.classList.remove('hidden'));
			} else if (type === 'revelation') {
				revelationContents.forEach(content => content.classList.remove('hidden'));
			} else if (type === 'lose') {
				loseContents.forEach(content => content.classList.remove('hidden'));
			}
		}

		// Nastavenie predvoleného zobrazenia
		showContent('revelation');

		btnWin.addEventListener('click', () => showContent('win'));
		btnShops.addEventListener('click', () => showContent('shops'));
		btnRevelation.addEventListener('click', () => showContent('revelation'));
		btnLose.addEventListener('click', () => showContent('lose'));
	});
</script>
