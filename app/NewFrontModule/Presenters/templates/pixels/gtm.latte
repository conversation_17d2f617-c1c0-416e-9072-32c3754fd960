<script>
dataLayer = [{}];

window.dataLayer = window.dataLayer || [];

function gtag() {
	dataLayer.push(arguments);
}
</script>

<script n:if="$user->isLoggedIn()">
    dataLayer.push({
        'userId': {$user->getId()}
    });

	gtag('set', {'user_id': {$user->getId()}});
</script>

<script n:foreach="$events as $event" n:if="$event->type === gtmDataLayer">
    {$event->event|noescape}
</script>

{if $localization->isCzech()}
<!-- Global site tag (gtag.js) - Google Analytics CS -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-5GK3THESKX"></script>
<script>
	gtag('js', new Date());
	gtag('config', 'G-5GK3THESKX');
</script>

<!-- Google Tag Manager CZ -->
<script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NDQDMB');</script>
<!-- End Google Tag Manager -->

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NDQDMB"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

{/if}

{if $localization->isSlovak()}
{*
<!-- Global site tag (gtag.js) - Google Analytics SK -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-QJ59Y7V6JH"></script>
<script>
	gtag('js', new Date());
	gtag('config', 'G-QJ59Y7V6JH');
</script>
*}

    <!-- Google Tag Manager SK -->
    <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-WK7Q8TW');</script>
    <!-- End Google Tag Manager -->

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WK7Q8TW"
                      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

{/if}

{if $localization->isPolish()}
    <!-- Global site tag (gtag.js) - Google Analytics PL -->
    <script src="https://www.googletagmanager.com/gtag/js?id=G-PLME2PRM3X"></script>
    <script>
        gtag('js', new Date());
        gtag('config', 'G-PLME2PRM3X');
    </script>

    <!-- Google Tag Manager PL -->
    <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-MSSQX4T');</script>
    <!-- End Google Tag Manager -->

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MSSQX4T"
                      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

{/if}

{if $localization->isRomanian()}
    <!-- Global site tag (gtag.js) - Google Analytics RO -->
    <script src="https://www.googletagmanager.com/gtag/js?id=G-HDPJKM7N54"></script>
    <script>
        gtag('js', new Date());
        gtag('config', 'G-HDPJKM7N54');
    </script>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-PZ9DRPD');</script>
    <!-- End Google Tag Manager -->
{/if}

{if $localization->isHungarian()}
    <!-- Global site tag (gtag.js) - Google Analytics HU -->
    <script src="https://www.googletagmanager.com/gtag/js?id=G-98YRM5C2GQ"></script>
    <script>
        gtag('js', new Date());
        gtag('config', 'G-98YRM5C2GQ');
    </script>

	<!-- Google Tag Manager HU -->
	<script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
	new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
	j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
	'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-PKPJC22');</script>
	<!-- End Google Tag Manager -->
{/if}

{if $localization->isBulgarian()}
<!-- Global site tag (gtag.js) - Google Analytics BG -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-YYVN0KTQP5"></script>
<script>
    gtag('js', new Date());
    gtag('config', 'G-YYVN0KTQP5');
</script>

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-TZVCMLP6');</script>
<!-- End Google Tag Manager -->
{/if}

{if $localization->isSlovenian()}
<!-- Global site tag (gtag.js) - Google Analytics SI -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-WZZ7W6C11D"></script>
<script>
    gtag('js', new Date());
    gtag('config', 'G-WZZ7W6C11D');
</script>

<!-- Google Tag Manager -->
<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5PBFJQVQ');</script>
<!-- End Google Tag Manager -->
{/if}

{if $localization->isCroatian()}
<!-- Global site tag (gtag.js) - Google Analytics HR -->
<script src="https://www.googletagmanager.com/gtag/js?id=G-N1P4747HG6"></script>
<script>
    gtag('js', new Date());
    gtag('config', 'G-N1P4747HG6');
</script>
<!-- Google Tag Manager -->
<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-TCV9V3XJ');</script>
<!-- End Google Tag Manager -->
{/if}

<script n:if="!empty($googleExperimentVariantResolver->getExperimentVariants())">
	{foreach $googleExperimentVariantResolver->getExperimentVariants() as $experimentId => $experiment}
		{if $experimentId !== 'googleoptimize'}
			{var $variantString = $experimentId . "-" . $experiment->getVariant()}

			gtag('event', 'experience_impression', {
				exp_variant_string: {$variantString}
			});
		{/if}
	{/foreach}
</script>


<script n:foreach="$events as $event" n:if="$event->type === GoogleTagLayer">
    {$event->event|noescape}
</script>

<script>
	function sendGAEvent(eventName, eventParams = {}) {
		if (window.gtag) {
			gtag('event', eventName, eventParams);
		} else {
			console.warn('GA4 tracking is not initialized.');
		}
	}

	// Add event listener to track and send banner click events
	document.addEventListener('DOMContentLoaded', function () {
		var bannerLinks = document.querySelectorAll('a[data-store-name][data-banner-position]')

		bannerLinks.forEach(link => {
			link.addEventListener('click', function (event) {
				event.preventDefault();

				var eventParams = {
					store_name: link.getAttribute('data-store-name'),
					banner_position: link.getAttribute('data-banner-position')
				};
				sendGAEvent('banner_click', eventParams);
				setTimeout(() => {
					window.location.href = link.href;
				}, 200);
			});
		});
	});
</script>
