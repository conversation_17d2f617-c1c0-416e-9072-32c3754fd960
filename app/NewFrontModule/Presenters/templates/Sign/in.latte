{block #robots}noindex,follow{/block}

{block #scripts}
	<script>
		function createForgottenPasswordLink(link) {
			var email = $('#frm-signInControl-form-email').val();

			if (email.length >= 2) {
				return link.href = link.dataset.href + (link.href.includes('?') ? '&' : '?') + 'setEmail=' + email;
			}

			return link.href = link.dataset.href;
		}
	</script>
{/block}

{block content}
<div class="flex flex-col lg:flex-row lg:justify-center w-full px-2.5 lg:px-0">
  <div class="lg:bg-light-6 w-full pt-5 pb-10 lg:pt-[84px]">
    <div class="flex flex-col sm:flex-row justify-center lg:justify-end items-center sm:gap-[148px] px-5 md:px-0 relative lg:mr-[102px]">
      <div class="w-full max-w-[511px] lg:w-[395px] max-w-full">
        <div class="hidden lg:block text-base text-center sm:text-left sm:text-[40px] text-dark-1 sm:text-primary-orange leading-7 sm:leading-[58px] font-bold mb-2 sm:mb-[5px]">
          {_'newFront.sign.in.title'}
        </div>

        <div class="hidden lg:block text-sm sm:text-base text-center sm:text-left leading-[24.5px] sm:leading-7 text-dark-1 mb-5">
          {_'newFront.sign.in.subtitle'}
        </div>

        <form n:name="signInControl-form">
          <div class="form-group" n:foreach="$form->errors as $error">
            <div class="bg-secondary-red text-white my-3 rounded p-3">{$error |noescape}</div>
          </div>

          <div class="w-full relative text-gray-600 focus-within:text-gray-400 mb-2.5 lg:mb-5">
            <label n:name="email" class="hidden lg:block text-xs font-medium text-gray-700 mb-1">{_'newFront.sign.in.form.email'} <span class="text-red-600">* </span></label>
            <span class="lg:hidden absolute left-[11px] flex items-center pl-2 top-[19px]">
               <svg xmlns="http://www.w3.org/2000/svg" width="19" height="13" viewBox="0 0 19 13" fill="none">
                  <path d="M17.7249 1.40337L11.1329 5.99697C10.6648 6.3232 10.0908 6.50008 9.50013 6.50008C8.9095 6.50008 8.33544 6.3232 7.86731 5.99697L1.27455 1.40337M1 2.1L1.00012 10.9C1.00013 11.5075 1.54378 12 2.21441 12H16.7857C17.4563 12 18 11.5075 18 10.9V2.1C18 1.80827 17.8721 1.52847 17.6444 1.32218C17.4167 1.11589 17.1077 1 16.7857 1H2.21429C1.89224 1 1.58338 1.11589 1.35566 1.32218C1.12793 1.52847 1 1.80827 1 2.1Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </span>
            <input n:name="email" class="text-sm text-black bg-white rounded-md pl-[47px] lg:pl-3 focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange block w-full border border-light-4 leading-[24.5px] pb-[11px] pt-3">
          </div>

          <div class="w-full mb-2 md:mb-5 relative text-gray-600 focus-within:text-gray-400">
            <label n:name="password" class="hidden lg:block text-xs font-medium text-gray-700 mb-1">{_'newFront.sign.in.form.password'} <span class="text-red-600">* </span></label>
            <span class="lg:hidden absolute left-[11px] flex items-center pl-2 top-[15px]">
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                  <path d="M3.125 8.4375H11.625M3.125 8.4375C1 8.4375 1 10.5625 1 10.5625V15.875C1 18 3.125 18 3.125 18H11.625C13.75 18 13.75 15.875 13.75 15.875V10.5625C13.75 8.4375 11.625 8.4375 11.625 8.4375M3.125 8.4375V5.25C3.125 4.12283 3.57277 3.04183 4.3698 2.2448C5.16684 1.44777 6.2479 1 7.375 1C8.5021 1 9.58316 1.44777 10.3802 2.2448C11.1772 3.04183 11.625 4.12283 11.625 5.25V8.4375" stroke="black" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </span>
            <input  n:name="password" class="text-sm text-black bg-white rounded-md pl-[47px] lg:pl-3 focus:outline-none focus:border-transparent focus:ring-1 focus:ring-primary-orange block w-full border border-light-4 leading-[24.5px] pb-[11px] pt-3">
          </div>

			<div class="form-group lg:mb-5" n:ifset="$form[recaptcha]">
				<div n:name="recaptcha" style="background-color: rgb(244 244 246 / var(--tw-bg-opacity));"></div>
			</div>

          <div class="text-end">
            <input n:name="submit" value="{_'newFront.sign.in.form.submit'}" class="w-full leading-7 font-bold text-white text-sm sm:text-base bg-orange-gradient py-[14px] px-[54px] rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
          </div>
        </form>

        <div class="flex items-center justify-center py-5">
          <div class="flex-grow border-t border-light-4"></div>
          <div class="px-2 text-dark-2 text-sm leading-[24.5px]">{_'newFront.sign.in.or'}</div>
          <div class="flex-grow border-t border-light-4"></div>
        </div>
        <div class="text-sm text-dark-1 mb-[30px]">
          {control socialLoginButtons "my-signin2"}
        </div>
        <div class="text-center">
          <small>{_'newFront.sign.in.forgottenPasswordLabel'} <a n:href="Sign:forgottenPassword" data-href="{link Sign:forgottenPassword}" onclick="return createForgottenPasswordLink(this)" class="text-primary-orange cursor-pointer hover:underline">{_'newFront.sign.in.forgottenPasswordLink'}</a></small>
        </div>
        <div class="lg:hidden pt-5 bg-pastel-orange-light sm:bg-transparent text-center mx-[-30px] px-[30px] min-h-screen">
          <div class="text-dark-1 leading-7 font-medium mb-2.5">{_'newFront.sign.in.signUpPromo.title'}</div>
          <a n:href="up" class="block w-full rounded-xl text-primary-orange leading-[24.5px] font-bold text-sm border border-primary-orange pt-4 pb-[15px]">{_'newFront.sign.in.signUpPromo.cta'}</a>
        </div>
      </div>
    </div>
  </div>
  <div class="hidden lg:block w-full pt-[84px]">
    <div class="flex flex-col sm:flex-row justify-betweenitems-center sm:gap-[148px] px-5 md:px-0 relative ml-[102px]">
      <div class="sm:w-[395px] max-w-full">
        <div class="text-base text-center sm:text-left sm:text-[40px] text-dark-1 sm:text-primary-orange leading-7 sm:leading-[58px] font-bold mb-2 sm:mb-[5px]">
          {_'newFront.sign.in.signUpPromo.title'}
        </div>
        <div class="text-sm sm:text-base text-center sm:text-left leading-[24.5px] sm:leading-7 text-dark-1 mb-[30px]">
          {_'newFront.sign.in.signUpPromo.text'}
        </div>
        <div class="text-end">
          <a n:href="up" class="block text-center w-full leading-7 font-bold text-white text-sm sm:text-base bg-orange-gradient py-[14px] px-[54px] rounded-xl mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover">
            {_'newFront.sign.in.signUpPromo.cta'}
          </a>
        </div>
        <div class="hidden md:block">
          <img src="{$basePath}/new-design/login-donkey.png" alt="donkey">
        </div>
      </div>
    </div>
  </div>
</div>
