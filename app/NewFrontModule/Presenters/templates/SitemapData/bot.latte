{layout '@layoutBot.latte'}
{contentType application/xml}
{varType tipli\Model\Localization\Entities\Localization $localization}

{block content}
    <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        <sitemap>
            <loc>{link //urls, 'shops'}</loc>
            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>
        </sitemap>
        <sitemap n:if="$isThereLeaflets">
            <loc>{link //urls, 'leaflets'}</loc>
            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>
        </sitemap>
        <sitemap>
            <loc>{link //urls, 'deals'}</loc>
            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>
        </sitemap>
{*        <sitemap>*}
{*            <loc>{link //urls, 'finances'}</loc>*}
{*            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>*}
{*        </sitemap>*}
        <sitemap n:if="$localization->isBulgarian() === false && $localization->isCroatian() === false && $localization->isSlovenian() === false">
            <loc>{link //urls, 'posts'}</loc>
            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>
        </sitemap>
        <sitemap>
            <loc>{link //urls, 'other'}</loc>
            <lastmod>{(new DateTime())|date: '%Y-%m-%d'}</lastmod>
        </sitemap>
    </sitemapindex>
{/block}
