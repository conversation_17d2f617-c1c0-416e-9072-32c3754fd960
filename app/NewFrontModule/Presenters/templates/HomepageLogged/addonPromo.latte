<div class="hidden w-[951px] relative lg:block lg:pb-[100px] m-auto">
    <div class="w-[951px] h-[536px] left-0 top-0 relative bg-zinc-200 rounded-[26px]"></div>
    <div class="w-[913px] h-[468px] left-[19px] top-[49px] absolute bg-white rounded-2xl"></div>
    <div class="w-[490px] h-[85px] left-[233px] top-[276px] absolute">
        <div class="w-[85px] h-[85px] left-0 top-0 absolute">
            <img class="m-auto" src="{$basePath}/new-design/welcome-icons/chrome.svg" alt="chrome" loading="lazy">
        </div>
        <div class="w-[85px] h-[85px] left-[135px] top-0 absolute">
            <img class="m-auto" src="{$basePath}/new-design/welcome-icons/opera.svg" alt="opera" loading="lazy">
        </div>
        <div class="w-[85px] h-[85px] left-[270px] top-0 absolute">
            <img class="m-auto" src="{$basePath}/new-design/welcome-icons/edge.svg" alt="edge" loading="lazy">
        </div>
        <div class="w-[85px] h-[85px] left-[405px] top-0 absolute">
            <img class="m-auto" src="{$basePath}/new-design/welcome-icons/safari.svg" alt="safari" loading="lazy">
        </div>
    </div>
    <div class="left-[296px] top-[141px] absolute text-center text-zinc-950 text-[26px] font-medium leading-[39px]">
        {_newFront.homepageLogged.addonPromo.title}
    </div>
    <div class="w-[456px] left-[249px] top-[192px] absolute text-center">
        {_newFront.homepageLogged.addonPromo.text, ['count' => ($countOfCashbackShops |amount)] |noescape}
    </div>
    <div class="w-[279px] h-14 left-[336px] top-[389px] absolute">
        <a n:href=":NewFront:Static:addon" class="flex items-center justify-center gap-4 p-2 min-w-[279px] h-14 bg-orange-gradient rounded-xl cursor-pointer xl:hover:bg-orange-gradient-hover">
            <img class=""
                 src="{$basePath}/new-design/welcome-icons/download.svg" alt="download" loading="lazy">
            <span class="">
							<span class="text-white font-bold">
                                {_newFront.homepageLogged.addonPromo.button}
                            </span>
						</span>
        </a>
    </div>
    <div class="left-[346px] top-[453px] absolute text-center text-zinc-950 text-sm leading-normal" n:if="$user->getIdentity()->hasDoubleAddonReward()">
        {_newFront.homepageLogged.addonPromo.reward}
    </div>
    <div class="w-[171px] h-[50px] left-[95px] top-[11px] absolute">
        <div class="w-[171px] h-[50px] left-0 top-0 absolute bg-white rounded-[10px]"></div>
        <div class="w-[125px] h-[21px] left-[22px] top-[9px] absolute">
            <div class="flex items-center gap-3 text-center  text-xs font-medium leading-[21px]">
							<span>
								<svg width="9" height="13" viewBox="0 0 9 13" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
									<path id="Vector" fill-rule="evenodd" clip-rule="evenodd"
                                          d="M4.50956 10.1921C4.29174 10.1921 4.06572 10.1488 3.83151 10.0651C3.35113 9.88849 2.95666 9.51558 2.73695 9.02942C2.61995 8.77551 2.56261 8.48408 2.56261 8.16372V6.21286H6.57372V3.46852H2.56239V0H0V8.15223C0 8.98613 0.13884 9.71031 0.416736 10.3192C0.694422 10.931 1.0513 11.4331 1.48967 11.8285C1.92805 12.224 2.41011 12.5154 2.93292 12.7086C3.45847 12.902 3.97057 13 4.46608 13C4.96159 13 5.47642 12.902 6.01015 12.7086C8.8557 11.6755 9 8.90533 9 7.52013H6.45945C6.45399 8.1147 6.41051 8.70332 6.24982 9.02942C6.12736 9.28045 5.97486 9.49395 5.79233 9.66396C5.43693 9.99447 4.98217 10.1819 4.50956 10.1921Z"
                                          fill="#EF7F1A" />
								</svg>
							</span>
                {_newFront.homepageLogged.addonPromo.fakeBrowserMetaTitle}
            </div>
        </div>
    </div>
    <div class="w-12 h-2.5 left-[19px] top-[21px] absolute">
        <div class="w-2.5 h-2.5 left-0 top-0 absolute bg-red-500 rounded-full"></div>
        <div class="w-2.5 h-2.5 left-[19px] top-0 absolute bg-amber-300 rounded-full"></div>
        <div class="w-2.5 h-2.5 left-[38px] top-0 absolute bg-emerald-500 rounded-full"></div>
    </div>
    <div class="w-[878px] h-[39px] left-[33px] top-[64px] absolute">
        <div class="w-[811px] h-[39px] left-0 top-0 absolute">
            <div class="w-[811px] h-[38px] left-0 top-0 absolute bg-gray-200 rounded-[300px]"></div>
            <div class="w-[52px] h-[38px] left-[21px] top-[5px] absolute leading-[28px]">
                {_newFront.homepageLogged.addonPromo.fakeBrowserUrl}
            </div>
        </div>
        <div class="w-[30px] h-[30px] left-[829px] top-[4px] absolute">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                <rect width="30" height="30" rx="15" fill="#EF7F1A" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M15.5096 19.1921C15.2917 19.1921 15.0657 19.1488 14.8315 19.0651C14.3511 18.8885 13.9567 18.5156 13.7369 18.0294C13.6199 17.7755 13.5626 17.4841 13.5626 17.1637V15.2129H17.5737V12.4685H13.5624V9H11V17.1522C11 17.9861 11.1388 18.7103 11.4167 19.3192C11.6944 19.931 12.0513 20.4331 12.4897 20.8285C12.928 21.224 13.4101 21.5154 13.9329 21.7086C14.4585 21.902 14.9706 22 15.4661 22C15.9616 22 16.4764 21.902 17.0102 21.7086C19.8557 20.6755 20 17.9053 20 16.5201H17.4595C17.454 17.1147 17.4105 17.7033 17.2498 18.0294C17.1274 18.2804 16.9749 18.494 16.7923 18.664C16.4369 18.9945 15.9822 19.1819 15.5096 19.1921Z"
                      fill="white" />
            </svg>
        </div>
        <div class="w-1 h-[25px] left-[874px] top-[7px] absolute">
            <div class="w-1 h-1 left-0 top-0 absolute bg-slate-500 rounded-full"></div>
            <div class="w-1 h-1 left-0 top-[7px] absolute bg-slate-500 rounded-full"></div>
            <div class="w-1 h-1 left-0 top-[14px] absolute bg-slate-500 rounded-full"></div>
            <div class="w-1 h-1 left-0 top-[21px] absolute bg-slate-500 rounded-full"></div>
        </div>
    </div>
</div>
