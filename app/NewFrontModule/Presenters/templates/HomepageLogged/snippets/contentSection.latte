{if $block->isBanner()}
	<div class="bg-primary-blue-dark relative z-20" id="{$block->getNavigationLink()}" {if $block->getColor()}style="background-color:{$block->getColor()|noescape};"{/if}>
		<div class="container relative pt-[21px]">
			<div class="text-lg text-white font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] py-[23px] md:py-[38px]">{$block->getName()}</div>
			<div class="swiper-container relative z-20 pb-2 md:pb-[45px]">
				<div class="swiper swiper-actions">
					<div class="swiper-wrapper">
						{foreach $getBlockBanners($block) as $banner}
						<div class="p-2 bg-white rounded-2xl swiper-slide">
							{include '../../../../DealsModule/Presenters/templates/Deals/sliderBannerItem.latte', banner: $banner, bannerPosition: $iterator->counter}
						</div>
						{/foreach}
					</div>
				</div>
				<div class="swiper-pagination"></div>
				<div class="swiper-button-prev swiper-actions-button-prev"></div>
				<div class="swiper-button-next swiper-actions-button-next"></div>
			</div>
		</div>

		<div class="absolute z-10 mt-[-220px] h-[220px] overflow-hidden">
			<img n:if="$isLast" class="w-screen" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
		</div>
	</div>
{elseif $block->isSmallBanner()}
	<div class="bg-primary-blue-dark relative z-20" id="{$block->getNavigationLink()}" {if $block->getColor()}style="background-color:{$block->getColor()|noescape};"{/if}>
		<div class="container relative">
			<div class="text-lg text-white font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] py-[23px] md:py-[38px]">{$block->getName()}</div>

			<div class="swiper-container relative z-20 mb-[112px]">
				<div class="swiper swiper-biggest-sales">
					<div class="swiper-wrapper">
						{foreach $getBlockBanners($block) as $banner}
						{var $shop = $banner->getShop()}
						{var $bannerLocation = $banner->getShop() ? $banner->getShop()->getName() : $banner->getLink()}

						{capture $link}
							{if $user->isLoggedIn() || !$shop}
								{plink :NewFront:Shops:Redirection:banner, "banner" => $banner, "source" => $source}
							{else}
								{plink aqPopup-open!, aqPopup-type => shopRedirection, aqPopup-shopId => $shop->getId()}
							{/if}
						{/capture}
						<div class="p-2 pb-5 bg-white rounded-2xl swiper-slide">
							<div class="relative">
								<img class="rounded-2xl" src="{$banner->getImage() |image:280,140,'exact'}" alt="action">

								<div class="bg-white w-full max-w-[140px] absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rounded-xl py-7">
									<img n:if="$shop" class="m-auto max-w-[70px]" src="{$shop->getCurrentLogo() |image:120,40,'fit'}" alt="">
								</div>
							</div>

							<div class="mt-12 text-dark-1">
								{$banner->getName()}
							</div>

							<img class="m-auto my-5" src="{$basePath}/new-design/hp-icons/smaller-wave.svg" alt="wave">

							<div class="flex justify-center items-center gap-1.5 text-sm text-primary-orange font-medium">
								<a href="{$link |noescape}" {if $user || !$shop}target="_blank" {/if} data-store-name="{$bannerLocation}" data-banner-position="{$iterator->counter}">
									{_'newFront.deals.deal.getSale'}
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 29 30" fill="none" style="display:inline;">
										<path d="M6.6538 22.7944L22.1484 7.2998" stroke="#EF7F1A" stroke-width="3.13087" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
										<path d="M22.1509 20.4301L22.1509 7.30027L9.02104 7.30025" stroke="#EF7F1A" stroke-width="3.13087" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
								</a>
							</div>
						</div>
						{/foreach}
					</div>
				</div>
				<div class="swiper-button-prev swiper-biggest-sales-button-prev"></div>
				<div class="swiper-button-next swiper-biggest-sales-button-next"></div>
			</div>
		</div>

		<div class="absolute z-10 mt-[-220px] h-[220px] overflow-hidden">
			<img n:if="$isLast" class="w-screen" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
		</div>
	</div>
{elseif $block->isShop()}
	<div class="bg-primary-blue-dark relative z-30" id="{$block->getNavigationLink()}" {if $block->getColor()}style="background-color:{$block->getColor()|noescape};"{/if}>
		<div class="relative">
			<section class="container relative z-30" >
				<div class="text-lg text-white font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] py-[23px] md:py-[38px]">{$block->getName()}</div>
				<div class="swiper-container relative mb-[105px]">
					<div class="swiper swiper-top-shops">
						<div class="swiper-wrapper">
							{foreach $getBlockShops($block) as $shop}
								{include "../../../../ShopsModule/Presenters/templates/Shops/shopItem.latte", shop: $shop, class: "swiper-slide"}
							{/foreach}
						</div>
					</div>
					<div class="swiper-button-prev swiper-top-shops-button-prev"></div>
					<div class="swiper-button-next swiper-top-shops-button-next"></div>
				</div>
			</section>
		</div>
		<div class="relative w-full overflow-hidden" style="max-height: 20px;">
			<img n:if="$isLast" class="" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
		</div>
	</div>
{elseif $block->isDeal()}
	<div class="bg-primary-blue-dark relative z-30" id="{$block->getNavigationLink()}" {if $block->getColor()}style="background-color:{$block->getColor()|noescape};"{/if}>
		<div class="relative">
			<section class="container relative z-30">
				<div class="text-lg text-white font-medium leading-[31.5px] md:text-[26px] md:leading-[39px] py-[23px] md:py-[38px]">{$block->getName()}</div>
				<div class="swiper-container relative mb-[105px]">
					<div class="swiper swiper-most-sales">
						<div class="swiper-wrapper">
							{foreach $getBlockDeals($block) as $deal}
								<div class="swiper-slide flex flex-col items-center bg-white rounded-2xl relative">
									{include '../../../../DealsModule/Presenters/templates/Deals/couponItem.latte', coupon: $deal}
								</div>
							{/foreach}
						</div>
					</div>
					<div class="swiper-button-prev swiper-most-sales-button-prev"></div>
					<div class="swiper-button-next swiper-most-sales-button-next"></div>
				</div>
			</section>
		</div>

		<div class="relative w-full overflow-hidden" style="max-height: 50px;">
			<img n:if="$isLast" class="w-full object-cover" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
		</div>
	</div>
{/if}
