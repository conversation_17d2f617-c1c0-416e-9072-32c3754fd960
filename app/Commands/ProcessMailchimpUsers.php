<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Mailchimp\MailchimpUserProcessor;

class ProcessMailchimpUsers extends Job
{
	public function __construct(private MailchimpUserProcessor $mailchimpUserProcessor, private Context $context)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:process-mailchimp-users:run');
	}

	public function start()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 400);

		$log = $this->onStart();

		$countCheck = $this->context->query('SELECT COUNT(user_id) AS c FROM tipli_account_segment_data WHERE has_unsubscribed_newsletters = 1')->fetch()->c;

		if ($countCheck === 0) {
			echo 'Waiting to calculate unsubscribed users';
			$this->onFinish($log, 'Waiting to calculate unsubscribed users');
			return;
		}

		$this->mailchimpUserProcessor->processMailchimpUsers();

		$this->onFinish($log);
	}
}
