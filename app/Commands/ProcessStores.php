<?php

namespace tipli\Commands;

use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\StoreFacade;

class ProcessStores extends Job
{
	/** @var StoreFacade */
	private $storeFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	public function __construct(StoreFacade $storeFacade, LocalizationFacade $localizationFacade)
	{
		parent::__construct();

		$this->storeFacade = $storeFacade;
		$this->localizationFacade = $localizationFacade;
	}

	protected function configure()
	{
		$this->setName('tipli:process-stores:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->processStores();

		$this->onFinish($log);
	}

	private function processStores()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 1200);

		$this->storeFacade->processStores();
	}
}
