<?php

namespace tipli\Commands;

use Nette\Database\Context;
use Nette\Database\Row;
use tipli\Model\Account\UserFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\PartnerOrganizations\PartnerOrganizationFacade;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Transactions\TransactionFacade;

class CreateTwistoPayouts extends Job
{
	/** @var UserFacade */
	private $userFacade;

	/** @var PayoutFacade */
	private $payoutFacade;

	/** @var PartnerOrganizationFacade */
	private $partnerOrganizationFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var TransactionFacade */
	private $transactionFacade;

	/** @var Context */
	private $context;

	public function __construct(UserFacade $userFacade, PayoutFacade $payoutFacade, PartnerOrganizationFacade $partnerOrganizationFacade, LocalizationFacade $localizationFacade, TransactionFacade $transactionFacade, Context $context)
	{
		parent::__construct();

		$this->userFacade = $userFacade;
		$this->payoutFacade = $payoutFacade;
		$this->partnerOrganizationFacade = $partnerOrganizationFacade;
		$this->localizationFacade = $localizationFacade;
		$this->transactionFacade = $transactionFacade;
		$this->context = $context;
	}

	protected function configure()
	{
		parent::configure();

		$this->setName('tipli:create-twisto-payouts:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->createTwistoPayouts();

		$this->onFinish($log);
	}

	private function createTwistoPayouts()
	{
		$data = $this->context->fetchAll('
				SELECT u.localization_id, u.id,
		   (SELECT SUM(t.user_commission_amount)
			FROM tipli_transactions_transaction t
			 WHERE t.user_id=u.id AND t.confirmed_at IS NOT NULL AND t.billable = 1
		   ) AS commission_balance,
		   (SELECT SUM(t.bonus_amount)
			FROM tipli_transactions_transaction t
			 WHERE t.user_id=u.id AND t.confirmed_at IS NOT NULL
		   ) AS bonus_balance
		FROM tipli_account_user u
		INNER JOIN tipli_account_segment_data sd ON (sd.user_id = u.id)
		WHERE u.partner_organization_id IN (26,27,34)
		AND sd.confirmed_balance > 0
		');

		/** @var Row $row */
		foreach ($data as $row) {
			$user = $this->userFacade->find($row->id);
			$confirmedCommissionBalance = $row->commission_balance;
			$confirmedBonusBalance = $row->bonus_balance;

			if (($confirmedCommissionBalance + $confirmedBonusBalance) === 0) {
				continue;
			}

			$this->payoutFacade->createInvoicePayout(
				$user,
				$this->transactionFacade->createCustomPayoutTransaction($user, $confirmedCommissionBalance, $confirmedBonusBalance)
			);

			echo $row->localization_id . ' ## ' . $user->getEmail() . ' ##### ' . $confirmedCommissionBalance . '/' . $confirmedBonusBalance . ' ' . $user->getCurrency();
			echo "\n";

//			break;
		}
	}
}
