<?php

namespace tipli\Commands;

class CalculateUserShopInterests extends Job
{
	public function __construct(
		//		private ShopInterestFacade $shopInterestFacade
	) {
		parent::__construct();
	}

	protected function configure()
	{
		parent::configure();

		$this->setName('tipli:calculate-user-shop-interests:run');
	}

	public function start()
	{
// 		@todo docasne vypnuto
//		$log = $this->onStart();
//
//		$users = $this->shopInterestFacade->findUsersToCalculateShopInterests();
//
//		/** @var User $user */
//		foreach ($users as $user) {
//			echo $user->getId() . "\n";
//			$this->shopInterestFacade->calculateShopInterestsForUser($user);
//			echo "\n";
//		}
//
//		$this->onFinish($log);
	}
}
