<?php

namespace tipli\Commands;

use Contributte\RabbitMQ\Consumer\Consumer;
use Contributte\RabbitMQ\Consumer\ConsumerFactory;
use Nette\Database\Context;
use Nette\Database\IRow;
use tipli\Model\RabbitMq\RabbitMqClient;
use tipli\Model\Reports\MetricFacade;
use tipli\Model\Reports\MetricsQueue;
use tipli\Model\Reports\Producers\MetricsProducer;

class ProcessMetrics extends Job
{
	public const SHOP_RETENTION_TIME_SHIFT = '- 90 days';
	public const PARTNER_SYSTEM_RETENTION_TIME_SHIFT = '- 90 days';
	public const PARTNER_SYSTEM_TYPE_RETENTION_TIME_SHIFT = '- 90 days';

	/** @var MetricFacade */
	private $metricFacade;

	/** @var MetricsProducer */
	private $metricsProducer;

	/** @var Context */
	private $context;

	/** @var MetricsQueue */
	private $metricsQueue;

	/** @var RabbitMqClient */
	public $rabbitMqClient;

	/** @var ConsumerFactory */
	private $consumerFactory;

	public function __construct(
		MetricFacade $metricFacade,
		MetricsProducer $metricsProducer,
		Context $context,
		MetricsQueue $metricsQueue,
		RabbitMqClient $rabbitMqClient,
		ConsumerFactory $consumerFactory
	) {
		parent::__construct();

		$this->metricFacade = $metricFacade;
		$this->metricsProducer = $metricsProducer;
		$this->context = $context;
		$this->metricsQueue = $metricsQueue;
		$this->rabbitMqClient = $rabbitMqClient;
		$this->consumerFactory = $consumerFactory;
	}

	protected function configure()
	{
		$this->setName('tipli:process-metrics:run');
	}

	public function start()
	{
		$log = $this->onStart();

		// save calculated values
//		Debugger::log('save-metrics', 'process-metrics-log');
		echo "save metrics\n";
		$this->saveMetrics();

		// schedule metrics
//		Debugger::log('schedule-metrics', 'process-metrics-log');
		echo "schedule metrics\n";
		$this->scheduleMetrics();

		// schedule history calculation
//		Debugger::log('process-metrics', 'process-metrics-log');
		echo "schedule history calc\n";
		$this->processMetrics();

		$this->onFinish($log);
	}

	private function saveMetrics()
	{
		/** @var Consumer $consumer */
		$consumer = $this->consumerFactory->getConsumer('metrics_save');

		echo "have consumer\n";
		$limit = min(
			$this->rabbitMqClient->getCountOfMessages('metrics_save'),
			4000
		);

		echo "have limit: " . $limit . "\n";

		if ($limit > 0) {
			echo "a\n";
			$consumer->consume(60, $limit);
			echo "b\n";
			$metricsToSave = $this->metricsQueue->getMetricsToSave();
			echo "c\n";
			$this->metricFacade->saveMetricsValues($metricsToSave);
		}
	}

	private function scheduleMetrics()
	{
		/** @var Consumer $consumer */
		$consumer = $this->consumerFactory->getConsumer('metrics_schedule');
		echo "have consumer\n";

		$limit = min(
			$this->rabbitMqClient->getCountOfMessages('metrics_schedule'),
			1000
		);

		echo "have limit: " . $limit . "\n";

		if ($limit > 0) {
			echo "a\n";
			$consumer->consume($limit);
			echo "b\n";
		}
	}

	private function processMetrics(): int
	{
		$metrics = $this->context->query('SELECT id, interval_name FROM tipli_reports_metric_value WHERE refresh_at <= ? LIMIT 5000', new \DateTime())->fetchAll();

		$ids = [];

		/** @var IRow $metric */
		foreach ($metrics as $metric) {
			$this->metricsProducer->scheduleRecalculation($metric->id, $metric->interval_name);

			$ids[] = $metric->id;
		}

		if (count($ids) > 0) {
			$this->context->query('UPDATE tipli_reports_metric_value SET refresh_at = NULL WHERE id IN (?)', $ids);
		}

		return count($metrics);
	}
}
