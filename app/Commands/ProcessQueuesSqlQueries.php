<?php

namespace tipli\Commands;

use tipli\Model\Queues\Entities\SqlQuery;

class ProcessQueuesSqlQueries extends Job
{
	protected function configure()
	{
		$this->setName('tipli:process-queues-sql-queries:run');
		$this->addArgument('routing');
	}

	public function start()
	{
		$log = $this->onStart();

		$routing = $this->getInputArgument('routing');

		/** @var SqlQuery $sqlQuery */
		foreach ($this->queueFacade->findSqlQueriesToProcess($routing, 50) as $sqlQuery) {
			$this->queueFacade->processSqlQuery($sqlQuery);
		}

		$this->onFinish($log);
	}
}
