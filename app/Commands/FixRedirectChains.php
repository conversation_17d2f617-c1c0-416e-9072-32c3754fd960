<?php

namespace tipli\Commands;

use Nette\Localization\Translator;
use Nette\Utils\Strings;
use Nette\Database\Context;
use Nette\Http\Url;
use tipli\Model\Articles\ArticleFacade;
use tipli\Model\Articles\Entities\Article;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;

class FixRedirectChains extends Job
{
	public const TYPE_LEAFLET = 'leaflet';
	public const TYPE_PRODUCT = 'product';
	public const TYPE_ARTICLE = 'article';
	public const TYPE_SHOP = 'shop';
	public const TYPE_TAG = 'tag';

	/** @var Context */
	private $context;

	/** @var LocalizationFacade */
	private $localizationFacade;

	public function __construct(
		Context $context,
		LocalizationFacade $localizationFacade,
		private LeafletFacade $leafletFacade,
		private ShopFacade $shopFacade,
		private ArticleFacade $articleFacade,
		private Translator $translator
	) {
		parent::__construct();

		$this->context = $context;
		$this->localizationFacade = $localizationFacade;
		$this->articleFacade = $articleFacade;
	}

	protected function configure()
	{
		$this->setName('tipli:fix-redirect-chains:run');
		$this->addArgument('type');
	}

	public function start()
	{
		$type = $this->getInputArgument('type');

		if (in_array($type, [self::TYPE_LEAFLET, self::TYPE_PRODUCT, self::TYPE_ARTICLE, self::TYPE_SHOP]) === false) {
			echo "Invalid type\n";
		}

		if ($type === self::TYPE_ARTICLE) {
			echo "Removing article links from contents\n";
			$this->removeArticleLinksFromContents();
		} elseif ($type === self::TYPE_SHOP) {
			echo "Removing shop links from contents\n";
			$this->removeShopLinksFromContents();
		} elseif ($type === self::TYPE_LEAFLET) {
			echo "Removing leaflet links from contents\n";
			$this->removeLeafletLinksFromContents();
		} elseif ($type === self::TYPE_PRODUCT) {
			echo "Removing product links from contents\n";
			$this->removeProductLinksFromContents();
		} elseif ($type === self::TYPE_TAG) {
			echo "Removing tag links from contents\n";
			$this->removeTagLinksFromContents();
		}
	}

	private function fixAdminLinksInContents(): void
	{
		$descriptionBlocksWithAdminLinks = $this->context->query('
			SELECT db.*, s.name as shop_name FROM tipli_shops_shop_description_block db
			INNER JOIN tipli_shops_shop s on s.id = db.shop_id
			WHERE db.description LIKE \'%/admin/%\' ')->fetchAll();

		foreach ($descriptionBlocksWithAdminLinks as $descriptionBlock) {
			$content = $descriptionBlock->description;

			preg_match_all('/<a\s+[^>]*href="[^"]*\/admin\/[^"]*"[^>]*>.*?<\/a>/is', $content, $matches);

			if (!empty($matches[0])) {
				foreach ($matches[0] as $link) {
					$content = str_replace($link, '', $content);
				}

				$this->context->query('
							UPDATE tipli_shops_shop_description_block
							SET description = ?
							WHERE id = ?', $content, $descriptionBlock->id);
			}
		}
	}

	public function fixProductBrandLinks()
	{
		$pageExtensions = $this->context->query('
			SELECT * FROM tipli_seo_page_extension pe
			WHERE pe.top_description LIKE \'%brands%5D=%\' OR pe.bottom_description LIKE \'%brands%5D=\'
		')->fetchAll();

		foreach ($pageExtensions as $pageExtension) {
			foreach ([$pageExtension->top_description, $pageExtension->bottom_description] as $type => $description) {
				preg_match_all('/(https:\/\/www.tipli.cz\/produkty\/.+)"/iU', $description, $matches);

				if (isset($matches[1])) {
					foreach ($matches[1] as $link) {
						$url = new Url($link);

						if ($filters = $url->getQueryParameter('filters')) {
							$brands = $filters['brands'] ?? null;

							if ($brands) {
								$newFilter = [];
								foreach ($brands as $brand) {
									$newFilter['brands'][] = Strings::webalize($brand);
								}

								$url->setQueryParameter('filters', $newFilter);
							}
						}

						$description = str_replace($link, $url->getAbsoluteUrl(), $description);
					}
				}

				$this->context->query('
	                    UPDATE tipli_seo_page_extension
	                    SET ' . ($type === 0 ? 'top_description' : 'bottom_description') . ' = ?
	                    WHERE id = ?
                    ', $description, $pageExtension->id);
			}
		}
	}

	public function removeLeafletLinksFromContents()
	{
		$indexedShops = $this->leafletFacade->getIndexableShops();

		$shopIds = [];
		foreach ($indexedShops as $indexedShop) {
			$shopIds[] = $indexedShop->getShop()->getId();
		}

		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$leafletShopsQuery = $this->shopFacade->createShopsQuery($localization)
				->notIn($shopIds);

			$shops = $this->shopFacade->fetch($leafletShopsQuery);

			$leafletLinks = [];

			/** @var Shop $shop */
			foreach ($shops as $shop) {
				if ($shop->getSlug() !== null) {
					if ($localization->isCzech()) {
						$leafletLinks[] = 'https://www.tipli.cz/letaky/' . $shop->getSlug();
					} elseif ($localization->isSlovak()) {
						$leafletLinks[] = 'https://www.tipli.sk/letaky/' . $shop->getSlug();
					} elseif ($localization->isPolish()) {
						$leafletLinks[] = 'https://www.tipli.pl/gazetki/' . $shop->getSlug();
					} elseif ($localization->isRomanian()) {
						$leafletLinks[] = 'https://www.tipli.ro/cataloage/' . $shop->getSlug();
					} elseif ($localization->isHungarian()) {
						$leafletLinks[] = 'https://www.tiplino.hu/akcios-ujsagok/' . $shop->getSlug();
					}
				}
			}

			foreach ($leafletLinks as $leafletLink) {
				$articles = $this->context->query('
				SELECT * FROM tipli_articles_article a
				WHERE a.content LIKE \'%' . $leafletLink . '%\'
			')->fetchAll();

				foreach ($articles as $article) {
					$content = $article->content;

					$content = $this->removeLinksFromContent($content, $leafletLink);

					$this->context->query('
							UPDATE tipli_articles_article
							SET content = ?
							WHERE id = ?
						', $content, $article->id);
				}
			}

			foreach ($leafletLinks as $leafletLink) {
				$descriptionBlocks = $this->context->query('
				SELECT * FROM tipli_shops_shop_description_block db
				WHERE db.description LIKE \'%' . $leafletLink . '%\'
			')->fetchAll();

				$matches = [];

				foreach ($descriptionBlocks as $descriptionBlock) {
					$description = $descriptionBlock->description;

					$description = $this->removeLinksFromContent($description, $leafletLink);

					$this->context->query('
	                    UPDATE tipli_shops_shop_description_block
	                    SET description = ?
	                    WHERE id = ?
                    ', $description, $descriptionBlock->id);
				}
			}

			foreach ($leafletLinks as $leafletLink) {
				$pageExtensions = $this->context->query('
				SELECT * FROM tipli_seo_page_extension pe
				WHERE pe.top_description LIKE \'%' . $leafletLink . '%\' OR
						pe.middle_description LIKE \'%' . $leafletLink . '%\' OR
						pe.bottom_description LIKE \'%' . $leafletLink . '%\'
			')->fetchAll();

				$matches = [];

				foreach ($pageExtensions as $pageExtension) {
					foreach (['top_description', 'middle_description', 'bottom_description'] as $descriptionType) {
						$description = $pageExtension->{$descriptionType};

						$description = $this->removeLinksFromContent($description, $leafletLink);

						$this->context->query('
								UPDATE tipli_seo_page_extension
								SET ' . $descriptionType . ' = ?
								WHERE id = ?
						', $description, $pageExtension->id);
					}
				}
			}
		}
	}

	public function removeProductLinksFromContents()
	{
		$linksToRemove = [
			'https://www.tipli.cz/cestovni-pojisteni',
			
		]

		$articles = $this->context->query('
			SELECT * FROM tipli_articles_article a
			WHERE a.content LIKE \'%' . $productsLink . '%\'
		')->fetchAll();

		foreach ($articles as $article) {
			preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $article->content, $matches);

			$content = $article->content;

			foreach ($matches[0] as $i => $a) {
				if (!Strings::contains($a, $productsLink)) {
					continue;
				}

				$content = str_replace($a, $matches[2][$i], $content);
			}

			$this->context->query('
	                    UPDATE tipli_articles_article
	                    SET content = ?
	                    WHERE id = ?
                    ', $content, $article->id);
		}

		$descriptionBlocks = $this->context->query('
			SELECT * FROM tipli_shops_shop_description_block db
			WHERE db.description LIKE \'%' . $productsLink . '%\'
		')->fetchAll();

		$matches = [];

		foreach ($descriptionBlocks as $descriptionBlock) {
			$description = $descriptionBlock->description;

			preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $description, $matches);

			foreach ($matches[0] as $i => $a) {
				if (!Strings::contains($a, $productsLink)) {
					bdump('skip: ' . $a);
					continue;
				}

				$description = str_replace($a, $matches[2][$i], $description);

				$this->context->query('
	                    UPDATE tipli_shops_shop_description_block
	                    SET description = ?
	                    WHERE id = ?
                    ', $description, $descriptionBlock->id);
			}
		}
	}

	public function removeTagLinksFromContents()
	{
		$slugsToRemove = ['moda-meska', 'moda-damska', 'moda-dodatki', 'obuwie', 'luksusowa-moda', 'odziez-sportowa', 'biura-podrozy', 'zakwaterowanie', 'bilety-lotnicze', 'wellnes-spa', 'wypozyczalnie-samochodow', 'rozrywka-i-zabawy', 'zagraniczne-podroze', 'sprzet-agd-i-wyposazenie', 'komputery-i-akcesoria', 'telefony-komorkowe', 'sprzet-i-akcesoria-fotograficzne', 'sprzet-rtv-audio-kino-domowe', 'oprogramowanie-i-gry', 'karma-i-akcesoria-dla-zwierzat', 'meble-i-wyposazenie', 'akcesoria-domowe', 'ogrod', 'narzedzia', 'jedzenie-z-dowozem', 'supermarkety', 'restauracje', 'napoje', 'zdrowa-zywnosc', 'kosmetyki', 'perfumy', 'pielegnacja-ciala', 'stomatologia', 'szkla-kontaktowe-i-okulary', 'leki-i-artykuly-zdrowotne', 'witaminy-i-suplementy', 'edukacja', 'bank', 'ubezpieczenia', 'operatorzy-komorkowi', 'uslugi-sprzatajce', 'uslugi-transportowe', 'kwiaciarnia', 'banki', 'sport', 'zabawki', 'ksiazki-muzyka-instrumenty-muzyczne', 'gry', 'atrakcje', 'moda-dziecieca', 'zabawki-dla-dzieci', 'przyszle-i-obecne-mamy', 'zagraniczna-moda', 'zagraniczna-elektronika', 'budownictwo', 'portale-znizkowe', 'prezenty', 'erotyka', 'nocleg', 'transport', 'odziez', 'bielizna', 'sprzet-agd-i-rtv', 'pc-i-telefony-komorkowe', 'inne'];

		foreach ($slugsToRemove as $slug) {
			$slugToRemove = 'sklepy\/' . $slug;
			$articles = $this->context->query('
			SELECT * FROM tipli_articles_article a
			WHERE a.content LIKE \'%' . $slugToRemove . '%\'
		')->fetchAll();

			foreach ($articles as $article) {
				preg_match_all('/<a href=\"[^\"]*\">(.*)<\/a>/iU', $article->content, $matches);
				preg_match_all('/<cta url=".*' . $slugToRemove . '".+<\/cta>/iU', $article->content, $ctaMatches);

				$content = $article->content;

				foreach ($matches[0] as $i => $a) {
					if (!Strings::contains($a, 'sklepy/' . $slug)) {
						continue;
					}

					$content = str_replace($a, $matches[1][$i], $article->content);
				}

				foreach ($ctaMatches[0] as $i => $cta) {
					$content = str_replace($cta, '', $article->content);
				}

				$content = str_replace('url="https://www.tipli.pl/sklepy/' . $slug . '"', '', $content);

				$this->context->query('
	                    UPDATE tipli_articles_article
	                    SET content = ?
	                    WHERE id = ?
                    ', $content, $article->id);
			}

			$descriptionBlocks = $this->context->query('
			SELECT * FROM tipli_shops_shop_description_block db
			WHERE db.description LIKE \'%' . $slugToRemove . '%\'
		')->fetchAll();

			foreach ($descriptionBlocks as $descriptionBlock) {
				preg_match_all('/<a href=\"[^\"]*\">(.*)<\/a>/iU', $descriptionBlock->description, $matches);

				foreach ($matches[0] as $i => $a) {
					if (!Strings::contains($a, 'sklepy/' . $slug)) {
						continue;
					}

					$content = str_replace($a, $matches[1][$i], $descriptionBlock->description);

					$this->context->query('
	                    UPDATE tipli_shops_shop_description_block
	                    SET description = ?
	                    WHERE id = ?
                    ', $content, $descriptionBlock->id);
				}
			}
		}
	}

	public function removeArticleLinksFromContents()
	{
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$articleQuery = $this->articleFacade->createArticlesQuery()
				->withLocalization($localization)
				->onlyInactive();

			$articlesEntities = $this->articleFacade->fetch($articleQuery);

			$articleLinks = [];

			/** @var Article $articleEntity */
			foreach ($articlesEntities as $articleEntity) {
				if ($articleEntity->getSlug() !== null) {
					if ($localization->isCzech()) {
						$articleLinks[] = 'https://www.tipli.cz/clanek/' . $articleEntity->getSlug();
					} elseif ($localization->isSlovak()) {
						$articleLinks[] = 'https://www.tipli.sk/clanok/' . $articleEntity->getSlug();
					} elseif ($localization->isPolish()) {
						$articleLinks[] = 'https://www.tipli.pl/artykul/' . $articleEntity->getSlug();
					} elseif ($localization->isRomanian()) {
						$articleLinks[] = 'https://www.tipli.ro/articol/' . $articleEntity->getSlug();
					} elseif ($localization->isHungarian()) {
						$articleLinks[] = 'https://www.tiplino.hu/cikk/' . $articleEntity->getSlug();
					}
				}
			}

			foreach ($articleLinks as $articleLink) {
				$articles = $this->context->query('
					SELECT * FROM tipli_articles_article a
					WHERE a.content LIKE \'%' . $articleLink . '%\'
				')->fetchAll();

				foreach ($articles as $article) {
					preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $article->content, $matches);

					$content = $article->content;

					foreach ($matches[0] as $i => $a) {
						if (!Strings::contains($a, $articleLink)) {
							continue;
						}

						echo "replacing " . $a . "\n";
						echo "by " . $matches[2][$i] . "\n";

						$content = str_replace($a, $matches[2][$i], $content);
					}

					$this->context->query('
							UPDATE tipli_articles_article
							SET content = ?
							WHERE id = ?
						', $content, $article->id);
				}
			}

			foreach ($articleLinks as $articleLink) {
				$descriptionBlocks = $this->context->query('
				SELECT * FROM tipli_shops_shop_description_block db
				WHERE db.description LIKE \'%' . $articleLink . '%\'
			')->fetchAll();

				$matches = [];

				foreach ($descriptionBlocks as $descriptionBlock) {
					$description = $descriptionBlock->description;

					preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $description, $matches);

					foreach ($matches[0] as $i => $a) {
						if (!Strings::contains($a, $articleLink)) {
							continue;
						}

						$description = str_replace($a, $matches[2][$i], $description);

						echo "replacing " . $a , "\n";
						echo "by " . $matches[2][$i] , "\n";

						$this->context->query('
	                    UPDATE tipli_shops_shop_description_block
	                    SET description = ?
	                    WHERE id = ?
                    ', $description, $descriptionBlock->id);
					}
				}
			}
		}
	}

	public function removeShopLinksFromContents()
	{
		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$activeShops = $this->shopFacade->getActiveAndVisibleShops($localization);

			$shopIds = [];
			foreach ($activeShops->getQuery()->getResult() as $activeShop) {
				$shopIds[] = $activeShop->getId();
			}

			if (empty($shopIds)) {
				continue;
			}

			$nonActiveShops = $this->shopFacade->createShopsQuery($localization)
				->notIn($shopIds);

			$nonActiveShops = $this->shopFacade->fetch($nonActiveShops);

			$shopLinks = [];

			/** @var Shop $shop */
			foreach ($nonActiveShops as $shop) {
				if ($shop->getSlug() !== null) {
					if ($localization->isCzech()) {
						$shopLinks[] = 'https://www.tipli.cz/obchod/' . $shop->getSlug();
					} elseif ($localization->isSlovak()) {
						$shopLinks[] = 'https://www.tipli.sk/obchod/' . $shop->getSlug();
					} elseif ($localization->isPolish()) {
						$shopLinks[] = 'https://www.tipli.pl/sklep/' . $shop->getSlug();
					} elseif ($localization->isRomanian()) {
						$shopLinks[] = 'https://www.tipli.ro/magazin/' . $shop->getSlug();
					} elseif ($localization->isHungarian()) {
						$shopLinks[] = 'https://www.tiplino.hu/webaruhaz/' . $shop->getSlug();
					} elseif ($localization->isBulgarian()) {
						$shopLinks[] = 'https://www.tipli.bg/turgoviyna/' . $shop->getSlug();
					} elseif ($localization->isCroatian()) {
						$shopLinks[] = 'https://www.tipli.hr/trgovina/' . $shop->getSlug();
					} elseif ($localization->isSlovenian()) {
						$shopLinks[] = 'https://www.tipli.si/trgovina/' . $shop->getSlug();
					}
				}
			}

			foreach ($shopLinks as $shopLink) {
				$articles = $this->context->query('
					SELECT * FROM tipli_articles_article a
					WHERE a.content LIKE \'%' . $shopLink . '%\'
				')->fetchAll();

				foreach ($articles as $article) {
					preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $article->content, $matches);

					$content = $article->content;

					foreach ($matches[0] as $i => $a) {
						if (!Strings::contains($a, $shopLink)) {
							continue;
						}

						echo "replacing " . $a . "\n";
						echo "by " . $matches[2][$i] . "\n";

						$content = str_replace($a, $matches[2][$i], $content);
					}

					$this->context->query('
							UPDATE tipli_articles_article
							SET content = ?
							WHERE id = ?
						', $content, $article->id);
				}
			}

			foreach ($shopLinks as $shopLink) {
				$descriptionBlocks = $this->context->query('
				SELECT * FROM tipli_shops_shop_description_block db
				WHERE db.description LIKE \'%' . $shopLink . '%\'
			')->fetchAll();

				$matches = [];

				foreach ($descriptionBlocks as $descriptionBlock) {
					$description = $descriptionBlock->description;

					$description = $this->removeLinksFromContent($description, $shopLink);

					$this->context->query('
	                    UPDATE tipli_shops_shop_description_block
	                    SET description = ?
	                    WHERE id = ?
                    ', $description, $descriptionBlock->id);
				}
			}

			foreach ($shopLinks as $shopLink) {
				$pageExtensions = $this->context->query('
				SELECT * FROM tipli_seo_page_extension pe
				WHERE pe.top_description LIKE \'%' . $shopLink . '%\' OR
						pe.middle_description LIKE \'%' . $shopLink . '%\' OR
						pe.bottom_description LIKE \'%' . $shopLink . '%\'
			')->fetchAll();

				$matches = [];

				foreach ($pageExtensions as $pageExtension) {
					foreach (['top_description', 'middle_description', 'bottom_description'] as $descriptionType) {
						$description = $pageExtension->{$descriptionType};

						$description = $this->removeLinksFromContent($description, $shopLink);

						$this->context->query('
								UPDATE tipli_seo_page_extension
								SET ' . $descriptionType . ' = ?
								WHERE id = ?
						', $description, $pageExtension->id);
					}
				}
			}
		}
	}

	private function removeLinksFromContent(?string $content, ?string $leafletLink): ?string
	{
		if ($content === null || $leafletLink === null) {
			return null;
		}

		preg_match_all('/<a\s.*?(?:href=[\'"](.*?)[\'"]).*?>(.+?)<\/a>/miu', $content, $matches);

		foreach ($matches[0] as $i => $a) {
			if (!Strings::contains($a, $leafletLink)) {
				continue;
			}

			echo "replacing " . $a . "\n";
			echo "by " . $matches[2][$i] . "\n";

			$content = str_replace($a, $matches[2][$i], $content);
		}

		preg_match_all('/<cta\s.*?(?:url=[\'"](.*?)[\'"]).*?><\/cta>/miu', $content, $matches);

		foreach ($matches[0] as $i => $a) {
			if (!Strings::contains($a, $leafletLink)) {
				continue;
			}

			echo "removing CTA  " . $a . "\n";

			$content = str_replace($a, $matches[2][$i], $content);
		}

		preg_match_all('/<picture\s.*?(?:url=[\'"](.*?)[\'"]).*?><\/picture>/miu', $content, $matches);

		foreach ($matches[0] as $i => $a) {
			if (!Strings::contains($a, $leafletLink)) {
				continue;
			}

			echo "removing PICTURE " . $a . "\n";

			$content = str_replace($a, $matches[2][$i], $content);
		}

		return $content;
	}
}
