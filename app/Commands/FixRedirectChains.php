<?php

namespace tipli\Commands;

use Nette\Database\Context;

class FixR<PERSON>irectChains extends Job
{
	private Context $context;

	public function __construct(Context $context)
	{
		parent::__construct();
		$this->context = $context;
	}

	protected function configure()
	{
		$this->setName('tipli:fix-redirect-chains:run');
		$this->addArgument('links', 'Links to remove (comma separated)');
	}

	public function start()
	{
		$linksArgument = $this->getArgument('links');

		if (!$linksArgument) {
			$this->output->writeln('<error>Please provide links to remove as argument</error>');
			$this->output->writeln('<info>Example: php console.php tipli:fix-redirect-chains:run "https://www.tipli.cz/cestovni-pojisteni,https://www.tipli.cz/other-link"</info>');
			return;
		}

		$linksToRemove = array_map('trim', explode(',', $linksArgument));

		$this->output->writeln('<info>Removing links from content...</info>');
		$this->output->writeln('<comment>Links to remove: ' . implode(', ', $linksToRemove) . '</comment>');

		$totalProcessed = 0;

		foreach ($linksToRemove as $link) {
			$processed = $this->removeLinkFromContent($link);
			$totalProcessed += $processed;
			$this->output->writeln("<info>Processed {$processed} records for link: {$link}</info>");
		}

		$this->output->writeln("<success>Total processed records: {$totalProcessed}</success>");
	}

	private function removeLinkFromContent(string $linkToRemove): int
	{
		$processedCount = 0;

		// Process articles
		$processedCount += $this->processArticles($linkToRemove);

		// Process shop description blocks
		$processedCount += $this->processShopDescriptionBlocks($linkToRemove);

		return $processedCount;
	}

	private function processArticles(string $linkToRemove): int
	{
		$articles = $this->context->query('
			SELECT id, content
			FROM tipli_articles_article
			WHERE content LIKE ? AND content IS NOT NULL
		', '%' . $linkToRemove . '%')->fetchAll();

		$processedCount = 0;

		foreach ($articles as $article) {
			$originalContent = $article->content;
			$newContent = $this->removeLinksFromHtmlContent($originalContent, $linkToRemove);

			if ($originalContent !== $newContent) {
				$this->context->query('
					UPDATE tipli_articles_article
					SET content = ?
					WHERE id = ?
				', $newContent, $article->id);

				$processedCount++;
				$this->output->writeln("<comment>Updated article ID: {$article->id}</comment>");
			}
		}

		return $processedCount;
	}

	private function processShopDescriptionBlocks(string $linkToRemove): int
	{
		$descriptionBlocks = $this->context->query('
			SELECT id, description
			FROM tipli_shops_shop_description_block
			WHERE description LIKE ? AND description IS NOT NULL
		', '%' . $linkToRemove . '%')->fetchAll();

		$processedCount = 0;

		foreach ($descriptionBlocks as $block) {
			$originalDescription = $block->description;
			$newDescription = $this->removeLinksFromHtmlContent($originalDescription, $linkToRemove);

			if ($originalDescription !== $newDescription) {
				$this->context->query('
					UPDATE tipli_shops_shop_description_block
					SET description = ?
					WHERE id = ?
				', $newDescription, $block->id);

				$processedCount++;
				$this->output->writeln("<comment>Updated shop description block ID: {$block->id}</comment>");
			}
		}

		return $processedCount;
	}

	private function removeLinksFromHtmlContent(?string $content, string $linkToRemove): ?string
	{
		if (empty($content)) {
			return $content;
		}

		// Escape the link for use in regex
		$escapedLink = preg_quote($linkToRemove, '/');

		// More robust regex patterns for different anchor tag formats
		$patterns = [
			// Standard anchor tags with href containing the link
			'/<a\s+[^>]*href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is',
			// Anchor tags with href as first attribute
			'/<a\s+href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is',
			// Anchor tags with additional attributes before href
			'/<a\s+[^>]*href\s*=\s*["\']([^"\']*' . $escapedLink . '[^"\']*)["\'][^>]*>(.*?)<\/a>/is'
		];

		$modifiedContent = $content;
		$replacementsMade = false;

		foreach ($patterns as $pattern) {
			$modifiedContent = preg_replace_callback($pattern, function($matches) use ($linkToRemove, &$replacementsMade) {
				// Check if the href actually contains our target link
				if (strpos($matches[1], $linkToRemove) !== false) {
					$replacementsMade = true;
					$linkText = trim(strip_tags($matches[2]));

					// Log the replacement for debugging
					$this->output->writeln("<info>Removing link: {$matches[0]}</info>");
					$this->output->writeln("<info>Replacing with text: {$linkText}</info>");

					return $linkText;
				}

				// Return original match if it doesn't contain our target link
				return $matches[0];
			}, $modifiedContent);
		}

		// Additional cleanup for any remaining malformed links
		$modifiedContent = $this->cleanupMalformedLinks($modifiedContent, $linkToRemove);

		return $modifiedContent;
	}

	private function cleanupMalformedLinks(string $content, string $linkToRemove): string
	{
		// Handle cases where links might be malformed or have unusual formatting
		$escapedLink = preg_quote($linkToRemove, '/');

		// Remove any remaining href attributes that contain the link
		$content = preg_replace('/href\s*=\s*["\'][^"\']*' . $escapedLink . '[^"\']*["\']/', '', $content);

		// Clean up any empty anchor tags that might be left
		$content = preg_replace('/<a\s*[^>]*>\s*<\/a>/', '', $content);
		$content = preg_replace('/<a\s*>\s*<\/a>/', '', $content);

		// Remove any standalone anchor tags without content
		$content = preg_replace('/<a\s*[^>]*>\s*/', '', $content);
		$content = preg_replace('/<\/a>/', '', $content);

		return $content;
	}

	private function validateHtmlContent(string $content): bool
	{
		// Basic validation to ensure we haven't broken the HTML structure
		$openTags = substr_count($content, '<a');
		$closeTags = substr_count($content, '</a>');

		// If there are unmatched tags, we might have an issue
		if ($openTags !== $closeTags) {
			$this->output->writeln("<warning>Warning: Unmatched anchor tags detected in content</warning>");
			return false;
		}

		return true;
	}
}
