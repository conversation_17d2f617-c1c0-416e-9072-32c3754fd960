<?php

namespace tipli\Commands;

use tipli\Model\Products\Entities\Category;
use tipli\Model\Products\Entities\CategoryTag;
use tipli\Model\Products\Entities\Product;
use tipli\Model\Products\Entities\ShopProduct;
use tipli\Model\Products\ProductFacade;
use tipli\Model\Tags\Entities\TagRule;
use tipli\Model\Tags\TagFacade;
use tipli\Model\Tags\TagRuleFacade;
use tipli\Model\Tags\TagRuleResolver;

class AssignTagsToProducts extends Job
{
	/** @var ProductFacade */
	private $productFacade;

	/** @var TagFacade */
	private $tagFacade;

	/** @var TagRuleResolver */
	private $tagRuleResolver;

	/** @var TagRuleFacade */
	private $tagRuleFacade;

	public function __construct(ProductFacade $productFacade, TagFacade $tagFacade, TagRuleResolver $tagRuleResolver, TagRuleFacade $tagRuleFacade)
	{
		parent::__construct();

		$this->productFacade = $productFacade;
		$this->tagFacade = $tagFacade;
		$this->tagRuleResolver = $tagRuleResolver;
		$this->tagRuleFacade = $tagRuleFacade;
	}

	protected function configure()
	{
		parent::configure();

		$this->setName('tipli:assign-tags-to-products:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->assignTagsToProducts();
//        $this->assignTagsToProductsByMatchRule();

		$this->onFinish($log);
	}

	private function assignTagsToProducts()
	{
		$shopProducts = $this->productFacade->findShopProductsToAssignTag(50);

		/** @var ShopProduct $shopProduct */
		foreach ($shopProducts as $shopProduct) {
			/** @var Product $product */
			$product = $shopProduct->getProduct();

			/** @var Category $category */
			$category = $shopProduct->getCategory();

			/** @var CategoryTag $categoryTag */
			foreach ($category->getCategoryTags() as $categoryTag) {
				if (!$categoryTag->isApproved()) {
					continue;
				}

				$this->tagFacade->assignTagHierarchyToProduct($categoryTag->getTag(), $product);
			}

			$shopProduct->processCategories();
			$this->productFacade->saveShopProduct($shopProduct);

			$product->scheduleProductCheck();
			$this->productFacade->saveProduct($product);
		}
	}

	private function assignTagsToProductsByMatchRule()
	{
		$productsToSave = [];

		/** @var TagRule $tagRule */
		foreach ($this->tagRuleFacade->findTopLevelTagRules() as $tagRule) {
			$searchTag = null;
			$tag = $tagRule->getTag();
			$localization = $tag->getLocalization();

			if ($tagRule->isMatchAcrossParentTags()) {
				$searchTag = $this->tagFacade->findRootTag($tag);
			}

			$productsByCondition = $this->productFacade->findByCondition($localization, $tagRule->getExpressions(), $tagRule->getColumn(), $tagRule->getCondition(), $tagRule->getRule(), $searchTag);

			foreach ($productsByCondition as $product) {
				if ($tags = $this->tagRuleResolver->resolveTagsForProduct($product)) {
					foreach ($tags as $tag) {
						$product->addProductTag($tag);
					}

					$productsToSave[] = $product;
				}
			}
		}

		if ($productsToSave) {
			$this->productFacade->saveProducts($productsToSave);
		}
	}
}
