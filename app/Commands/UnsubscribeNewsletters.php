<?php

namespace tipli\Commands;

use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;

class UnsubscribeNewsletters extends Job
{
	/** @var UserFacade */
	private $userFacade;

	/** @var EmailSubscriptionManager  */
	private $emailSubscriptionManager;

	public function __construct(UserFacade $userFacade, EmailSubscriptionManager $emailSubscriptionManager)
	{
		parent::__construct();

		$this->userFacade = $userFacade;
		$this->emailSubscriptionManager = $emailSubscriptionManager;
	}

	protected function configure()
	{
		$this->setName('tipli:unsubscribe-newsletters:run');
	}

	public function start()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 180);

		$log = $this->onStart();

		$countOfUnsubscribedUsers = 0;
		$countOfResubscribedUsers = 0;

		// UNSUBSCRIBE INACTIVE USERS
		$usersToUnsubscribeQuery = $this->userFacade->createUsersQuery()
			->createdBetween(
				(new \DateTime())->modify('- 10 years'),
				(new \DateTime())->modify('- 9 months')
			)
			->notWhiteLabeled()
			->withoutDisabledNewslettersDueToInactivity()
			->withSubscribedNewsletters()
			->withoutOpenedAnyEmail()
			->withSubscribedEmails()
			->onlyActive()
		;

		$usersToUnsubscribe = $this->userFacade->fetch($usersToUnsubscribeQuery)->applyPaging(0, 100);

		/** @var User $user */
		foreach ($usersToUnsubscribe as $user) {
			$user->getSegmentData()->disableNewslettersDueToInactivity();
			$this->userFacade->saveUser($user, false);

//			$this->emailSubscriptionManager->unsubscribeContentType(
//				$user,
//				null,
//				SendingPolicy::CONTENT_TYPE_NEWSLETTER,
//				EmailSubscriptionManager::SITE_TIPLI,
//				EmailSubscriptionManager::SOURCE_APPLICATION,
//				'no opened email after 9 months of registration '
//			);

			$countOfUnsubscribedUsers++;
		}

		// SUBSCRIBE ACTIVE USERS
		$usersToSubscribeQuery = $this->userFacade->createUsersQuery()
			->notWhiteLabeled()
			->withDisabledNewslettersDueToInactivity()
			->withUnsubscribedNewsletters()
			->withOpenedAnyEmail()
			->withSubscribedEmails()
			->onlyActive()
		;

		$usersToSubscribe = $this->userFacade->fetch($usersToSubscribeQuery)->applyPaging(0, 100);

		/** @var User $user */
		foreach ($usersToSubscribe as $user) {
			$user->getSegmentData()->enableNewslettersDueToInactivity();
			$this->userFacade->saveUser($user, false);

			$this->emailSubscriptionManager->subscribeContentType(
				$user,
				null,
				SendingPolicy::CONTENT_TYPE_NEWSLETTER,
				EmailSubscriptionManager::SITE_TIPLI,
				EmailSubscriptionManager::SOURCE_APPLICATION,
				'any opened email after 9 months of registration '
			);

			$countOfResubscribedUsers++;
		}

		$this->onFinish($log, 'unsubscribed/resubscribed users: ' . $countOfUnsubscribedUsers . '/' . $countOfResubscribedUsers);
	}
}
