<?php

namespace tipli\Commands;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\Entities\ShopExternalData;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Shops\ShopExternalDataFacade;

class ProcessShopExternalData extends Job
{
	public function __construct(
		private ShopFacade $shopFacade,
		private ShopExternalDataFacade $shopExternalDataFacade
	) {
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:process-shop-external-data:run');
	}

	public function start()
	{
		$log = $this->onStart();

		//$this->generateShopExternalData();

		$this->processSazkaGames();

		//$this->processShops();

		$this->onFinish($log);
	}

	private function processSazkaGames()
	{
		// sazka
		$shop = $this->shopFacade->find(5283);

		$shopExternalData = $this->shopExternalDataFacade->findByShop($shop);

		if ($shopExternalData === null) {
			$shopExternalData = $this->shopExternalDataFacade->createShopExternalData($shop);
		}

		$this->shopExternalDataFacade->processSazkaGames($shopExternalData);
	}

	private function generateShopExternalData(): void
	{
		$shops = $this->shopFacade->findShopsReadyToProcessExternalData(10);

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			try {
				$shopExternalData = $this->shopExternalDataFacade->createShopExternalData($shop);

				$shopExternalData->readyToProcess();

				$this->shopExternalDataFacade->saveShopExternalData($shopExternalData);
			} catch (UniqueConstraintViolationException $e) {
				continue;
			}
		}
	}

	private function processShops(): void
	{
		/** @var ShopExternalData $shopExternalData */
		foreach ($this->shopExternalDataFacade->findShopsReadyToProcess(1) as $shopExternalData) {
			$this->shopExternalDataFacade->processShopExternalData($shopExternalData);
		}
	}
}
