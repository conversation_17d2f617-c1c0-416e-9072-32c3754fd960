<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Transactions\Entities\Transaction;
use Tracy\Debugger;

class Sandbox extends Job
{
	public function __construct(private Context $context)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:sandbox:run');
		$this->addArgument('type');
	}

	public function start()
	{
		echo "Running sandbox command\n";

		$type = $this->getInputArgument('type');

		if ($type === 'lastTransactions') {
			$rows = $this->context->query('SELECT shop_id, MAX(created_at) AS max_created_at FROM tipli_transactions_transaction WHERE created_at >= (NOW() - INTERVAL 2 YEAR) AND type = ? GROUP BY shop_id', Transaction::TYPE_COMMISSION);

			foreach ($rows as $row) {
				echo "setting {$row->shop_id} — {$row->max_created_at}\n";
				$this->context->query('UPDATE tipli_shops_shop_data sd SET sd.last_transaction_at = "' . $row->max_created_at->format('Y-m-d H:i:s') . '" WHERE sd.shop_id = ' . $row->shop_id);
			}
		} else {
			$data = $this->context->query('SHOW FULL PROCESSLIST')->fetchAll();
			$log = [];
			foreach ($data as $row) {
				$log[] = $row;
			}
			Debugger::log(json_encode($log), 'processlist-log');
		}
	}
}
