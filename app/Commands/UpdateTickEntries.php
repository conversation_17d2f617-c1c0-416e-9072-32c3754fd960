<?php

namespace tipli\Commands;

use tipli\Model\Tickspot\TickFacade;
use tipli\Model\Tickspot\TickClient;

class UpdateTickEntries extends Job
{
	/** @var TickFacade */
	private $tickFacade;

	/** @var TickClient */
	private $tickClient;

	public function __construct(TickFacade $tickFacade, TickClient $tickClient)
	{
		parent::__construct();

		$this->tickFacade = $tickFacade;
		$this->tickClient = $tickClient;
	}

	protected function configure()
	{
		$this->setName('tipli:update-tick-entries:run');
	}

	public function start(\DateTime $fromDate = null, \DateTime $toDate = null)
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 240);

//		$log = $this->onStart();

		$fromDate = $fromDate ? : (new \DateTime())->modify('- 2 days')->setTime(0, 0, 0);
		$toDate = $toDate ? : (new \DateTime())->setTime(23, 59, 59);

		$this->tickFacade->synchronizeEntries(
			$this->tickClient->getEntries($fromDate, $toDate),
			$fromDate,
			$toDate
		);

//		$this->onFinish($log);
	}
}
