<?php

namespace tipli\Commands;

use Nette\Neon\Neon;
use Nette\Utils\Finder;
use Nette\Utils\Strings;
use SplFileInfo;

class RemoveUnusedTranslations extends Job
{
	/** @var string $appDir */
	private $appDir;

	/** @var string $localeDir */
	private $localeDir;

	/** @var string $targetContent */
	private $targetContent;

	/**
	 * RemoveUnusedTranslations constructor.
	 * @param string $appDir
	 * @param string $localeDir
	 */
	public function __construct(string $appDir, string $localeDir)
	{
		parent::__construct();

		$this->appDir = $appDir;
		$this->localeDir = $localeDir;
	}

	protected function configure()
	{
		$this->setName('tipli:remove-unused-translations:run');
	}

	public function start()
	{
		ini_set('memory_limit', '2048M');
		ini_set('max_execution_time', 0);

		$log = $this->onStart();

		$totalCounter = 0;
		$targetFiles = Finder::find('*.php', '*.latte', '*.mjml')->from($this->appDir);

		/** @var SplFileInfo $file */
		foreach ($targetFiles as $file) {
			$this->targetContent .= file_get_contents($file->getRealPath());
		}

		print(PHP_EOL);
		print('----------------------------------------------------' . PHP_EOL);
		print('----------------------------------------------------' . PHP_EOL);
		print(PHP_EOL);

		/** @var SplFileInfo $file */
		foreach (Finder::find('*.neon')->from($this->localeDir) as $file) {
			$localCounter = 0;
			$baseKey = Strings::before($file->getBasename(), '.');

			print('> ' . $file->getBasename() . PHP_EOL);

			$data = Neon::decode(file_get_contents($file->getRealPath()));

			foreach ($data as $key => $item) {
				$this->checkTranslation($baseKey . '.' . $key, $item, $data, $localCounter);
			}

			$neon = Neon::encode($data, Neon::BLOCK);

			file_put_contents($file->getRealPath(), $neon);
			$totalCounter += $localCounter;

			print(sprintf('> Počet nalezených nepoužitých překladů: %d', $localCounter) . PHP_EOL . PHP_EOL);
		}

		print(sprintf('Celkový počet nalezených nepoužitých překladů: %d', $totalCounter) . PHP_EOL);

		print(PHP_EOL);
		print('----------------------------------------------------' . PHP_EOL);
		print('----------------------------------------------------' . PHP_EOL);
		print(PHP_EOL);

		$this->onFinish($log);
	}

	/**
	 * @param string $key
	 * @return bool
	 */
	private function isTranslationUsed(string $key)
	{
		return Strings::contains($this->targetContent, $key);
	}

	/**
	 * @param string $key
	 * @param mixed $item
	 * @param mixed $data
	 * @param int $counter
	 */
	private function checkTranslation(string $key, $item, &$data, int &$counter)
	{
		$keysPath = explode('.', $key);
		$subKey = $keysPath[count($keysPath) - 1];

		if (is_numeric($subKey)) {
			return;
		}

		if (!is_array($item)) {
			if (!$this->isTranslationUsed($key)) {
				$keysPath = explode('.', $key);

				// delete first key ($baseKey)
				array_shift($keysPath);

				$node = &$data;
				foreach ($keysPath as $index => $k) {
					if ($index === count($keysPath) - 1) {
						unset($node[$k]);
						array_pop($keysPath);
						$counter++;
					} else {
						$node = &$node[$k];
					}
				}

				for ($i = 1; $i <= count($keysPath); $i++) {
					$node = &$data;
					foreach ($keysPath as $index => $k) {
						if (empty($node[$k])) {
							unset($node[$k]);
							break;
						} elseif (isset($node[$k])) {
							$node = &$node[$k];
						}
					}
				}
			}
		} else {
			foreach ($item as $childKey => $childItem) {
				$this->checkTranslation($key . '.' . $childKey, $childItem, $data, $counter);
			}
		}
	}
}
