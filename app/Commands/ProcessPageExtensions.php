<?php

namespace tipli\Commands;

use GuzzleHttp\Client;
use Nette\Application\LinkGenerator;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use tipli\Model\Seo\Entities\PageExtension;
use tipli\Model\Seo\Entities\PageExtensionKeyword;
use tipli\Model\Seo\Entities\Visit;
use tipli\Model\Seo\SeoFacade;
use tipli\Routers\RouterFactory;

class ProcessPageExtensions extends Job
{
	/** @var SeoFacade */
	private $seoFacade;

	/** @var LinkGenerator */
	private $linkGenerator;

	public function __construct(SeoFacade $seoFacade, LinkGenerator $linkGenerator)
	{
		parent::__construct();

		$this->seoFacade = $seoFacade;
		$this->linkGenerator = $linkGenerator;
	}

	protected function configure()
	{
		$this->setName('tipli:process-page-extensions:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->processPageExtensions();

		$this->onFinish($log);
	}

	private function processPageExtensions()
	{
		ini_set('memory_limit', '2048M');
		ini_set('max_execution_time', 900);

		$pageExtensionsToProcess = $this->seoFacade->findPageExtensionsToProcess(50);
		$traffic = $this->getTraffic($pageExtensionsToProcess);

		/** @var PageExtension $pageExtension */
		foreach ($pageExtensionsToProcess as $pageExtension) {
			$pageExtensionId = $pageExtension->getId();

			if (!$pageExtension->getPageType()) {
				$pageExtension->setPageType($this->seoFacade->detectPageType($pageExtension));

				switch ($pageExtension->getPageType()) {
					case PageExtension::PAGE_TYPE_PRODUCTS:
						$pageExtension->setContentRevisionPivotShift(180);
						break;
					case PageExtension::PAGE_TYPE_SHOP:
						$pageExtension->setContentRevisionPivotShift(183);
						break;
					case PageExtension::PAGE_TYPE_LEAFLETS:
						$pageExtension->setContentRevisionPivotShift(365);
						break;
					case PageExtension::PAGE_TYPE_ARTICLE:
						$pageExtension->setContentRevisionPivotShift(250);
				}
			}

			if ($pageExtension->getPageType() === PageExtension::PAGE_TYPE_SHOP) {
				$this->updateMetaTitle($pageExtension);
			}

			$this->setKeywordPositionAvg($pageExtension);

			$pageExtension->setOrganicLast4Days($traffic->last4Days[$pageExtensionId]);
			$pageExtension->setOrganicLast4DaysPeriod($traffic->last4DaysPeriod[$pageExtensionId]);

			$pageExtension->setOrganicLast14Days($traffic->last14Days[$pageExtensionId]);
			$pageExtension->setOrganicLast14DaysPeriod($traffic->last14DaysPeriod[$pageExtensionId]);

			$pageExtension->setOrganicLastMonth($traffic->lastMonth[$pageExtensionId]);
			$pageExtension->setOrganicLastMonthPeriod($traffic->lastMonthPeriod[$pageExtensionId]);

			$pageExtension->setOrganicLastYear($traffic->lastYear[$pageExtensionId]);
			$pageExtension->setOrganicLastYearPeriod($traffic->lastYearPeriod[$pageExtensionId]);

			$this->checkGrammarMistakes($pageExtension);

			$pageExtension->process();

			$this->seoFacade->savePageExtension($pageExtension);
		}
	}

	private function checkGrammarMistakes(PageExtension $pageExtension)
	{
		$pageExtension->setGrammarMistakes(null);

		$grammarMistakes = [];

		// out space check
/*		$outSpaceCheckPattern = '/[^\s\(\<\>\„\"\'](<[a|strong|i][^>]*>)|(<\/[a|strong|i][^>]*?>)([^\s\.\,\:\;\!\?\“\"\'\)\<]+)/';*/
		$outSpaceCheckPattern = '/[^\s\(\<\>\„\"\'\;](<[a|strong|i][^>]*>)|(<\/[a|strong|i][^>]*?>)([^\s\.\,\:\;\!\&\?\“\"\'\)\<]+)/';

		if ($pageExtension->getTopDescription() !== null && Strings::match($pageExtension->getTopDescription(), $outSpaceCheckPattern)) {
			$grammarMistakes[] = 'chybí mezery mezi tagy (top description)';
		}

		if ($pageExtension->getMiddleDescription() !== null && Strings::match($pageExtension->getMiddleDescription(), $outSpaceCheckPattern)) {
			$grammarMistakes[] = 'chybí mezery mezi tagy (middle description)';
		}

		if ($pageExtension->getBottomDescription() !== null && Strings::match($pageExtension->getBottomDescription(), $outSpaceCheckPattern)) {
			$grammarMistakes[] = 'chybí mezery mezi tagy (bottom description)';
		}

		// inner space check
		$innerSpaceCheckPattern = '/(<[a|strong|i|u][^>]*>)[\s]|[\s](<\/[a|strong|i|u][^>]*?>)/';

		if ($pageExtension->getTopDescription() !== null && Strings::match($pageExtension->getTopDescription(), $innerSpaceCheckPattern)) {
			$grammarMistakes[] = 'mezery uvnitř tagu (top description)';
		}

		if ($pageExtension->getMiddleDescription() !== null && Strings::match($pageExtension->getMiddleDescription(), $innerSpaceCheckPattern)) {
			$grammarMistakes[] = 'mezery uvnitř tagu  (middle description)';
		}

		if ($pageExtension->getBottomDescription() !== null && Strings::match($pageExtension->getBottomDescription(), $innerSpaceCheckPattern)) {
			$grammarMistakes[] = 'mezery uvnitř tagu  (bottom description)';
		}

		if (!empty($grammarMistakes)) {
			$pageExtension->setGrammarMistakes(implode(', ', $grammarMistakes));
		}
	}

	private function getTraffic(array $pageExtensionsToProcess)
	{
		$visitsData = new ArrayHash();
		$visitsData->last4Days = [];
		$visitsData->last4DaysPeriod = [];
		$visitsData->last14Days = [];
		$visitsData->last14DaysPeriod = [];
		$visitsData->lastMonth = [];
		$visitsData->lastMonthPeriod = [];
		$visitsData->lastYear = [];
		$visitsData->lastYearPeriod = [];

		$dateStart = new \DateTime('yesterday');

		$date4DaysStart = clone $dateStart;
		$date4DaysStart->modify('- 3 days');
		$date4DaysPeriodEnd = clone $dateStart;
		$date4DaysPeriodEnd->modify('- 7 days');
		$date4DaysPeriodStart = clone $date4DaysPeriodEnd;
		$date4DaysPeriodStart->modify('- 3 days');

		$date14DaysStart = clone $dateStart;
		$date14DaysStart->modify('- 13 days');
		$date14DaysEnd = $dateStart;
		$date14DaysPeriodStart = clone $date14DaysStart;
		$date14DaysPeriodStart->modify('- 1 month');
		$date14DaysPeriodEnd = clone $date14DaysPeriodStart;
		$date14DaysPeriodEnd->modify('+ 13 days');

		$dateLastMonthStart = clone $dateStart;
		$dateLastMonthStart->modify('-30 days');
		$dateLastMonthPeriodStart = clone $dateLastMonthStart;
		$dateLastMonthPeriodStart->modify('- 1 month');
		$dateLastMonthPeriodEnd = clone $dateLastMonthStart;
		$dateLastMonthPeriodEnd->modify('+ 1 month');

		$dateLastYearStart = clone $dateStart;
		$dateLastYearStart->modify('- 365 days');
		$dateLastYearPeriodStart = clone $dateLastYearStart;
		$dateLastYearPeriodStart->modify('- 365 days');
		$dateLastYearPeriodEnd = clone $dateLastYearStart;

		$visitsData->last4Days = $this->getVisitData($pageExtensionsToProcess, $date4DaysStart, $dateStart);
		$visitsData->last4DaysPeriod = $this->getVisitData($pageExtensionsToProcess, $date4DaysPeriodStart, $date4DaysPeriodEnd);
		$visitsData->last14Days = $this->getVisitData($pageExtensionsToProcess, $date14DaysStart, $date14DaysEnd);
		$visitsData->last14DaysPeriod = $this->getVisitData($pageExtensionsToProcess, $date14DaysPeriodStart, $date14DaysPeriodEnd);
		$visitsData->lastMonth = $this->getVisitData($pageExtensionsToProcess, $dateLastMonthStart, $dateStart);
		$visitsData->lastMonthPeriod = $this->getVisitData($pageExtensionsToProcess, $dateLastMonthPeriodStart, $dateLastMonthPeriodEnd);
		$visitsData->lastYear = $this->getVisitData($pageExtensionsToProcess, $dateLastYearStart, $dateStart);
		$visitsData->lastYearPeriod = $this->getVisitData($pageExtensionsToProcess, $dateLastYearPeriodStart, $dateLastYearPeriodEnd);

		return $visitsData;
	}

	public function getVisitData(array $pageExtensions, \DateTime $dateFrom, \DateTime $dateTo)
	{
		$data = [];

		$qb = $this->seoFacade->getPageExtensions()
			->select('pe.page, pe.id as pageExtensionId, SUM(v.sessions) as sumSessions')
			->leftJoin(Visit::class, 'v', 'WITH', 'v.page = pe.page AND pe.localization = v.localization AND v.channel = :channel AND v.date >= :dateFrom AND v.date <= :dateTo')
			->setParameter('channel', 'Organic Search')
			->setParameter('dateFrom', $dateFrom)
			->setParameter('dateTo', $dateTo)
			->andWhere('pe IN (:pageExtensions)')->setParameter('pageExtensions', $pageExtensions)
			->addOrderBy('v.date', 'DESC')
			->groupBy('pe.id');

		foreach ($qb->getQuery()->getArrayResult() as $visit) {
			$pageExtensionId = $visit['pageExtensionId'];
			$sessions = (int) $visit['sumSessions'];

			if (!isset($data[$pageExtensionId])) {
				$data[$pageExtensionId] = $sessions;
			} else {
				$data[$pageExtensionId] += $sessions;
			}
		}

		return $data;
	}

	public function setKeywordPositionAvg(PageExtension $pageExtension)
	{
		$googlePositionAvg = null;
		$googlePositionLastMonthAvg = null;
		$seznamPositionAvg = null;
		$seznamPositionLastMonthAvg = null;
		$googleSearchesAvg = 0;
		$seznamSearchesAvg = 0;

		if (!$pageExtension->getKeywords()->isEmpty()) {
			$googlePosition = 0;
			$googlePositionLastMonth = 0;
			$seznamPositionLastMonth = 0;
			$countGoogle = 0;
			$seznamPosition = 0;
			$countSeznam = 0;
			$dividendGoogle = 0;
			$dividendSeznam = 0;
			$sumGooglePosition = 0;
			$sumSeznamPosition = 0;
			$countGoogleLastMonth = 0;
			$countSeznamLastMonth = 0;
			$dividendSearchesGoogle = 0;
			$dividendSearchesSeznam = 0;
			$countSearchesGoogle = 0;
			$countSearchesSeznam = 0;
			$countKeywords = $pageExtension->getKeywords()->count();

			/** @var PageExtensionKeyword $keyword */
			foreach ($pageExtension->getKeywords() as $keyword) {
				$kw = $keyword->getKeyword();

				$googlePosition = $kw->getPositionGoogle();
				$googlePositionLastMonth = $kw->getPositionGoogleLastMonth();
				$googleSearches = $kw->getSearchesGoogle();

				$seznamPosition = $kw->getPositionSeznam();
				$seznamPositionLastMonth = $kw->getPositionSeznamLastMonth();
				$seznamSearches = $kw->getSearchesSeznam();

				$sumGooglePosition += $googlePosition;
				$countGoogle += $googlePosition * ($googleSearches ?: 1);
				$countGoogleLastMonth += $googlePositionLastMonth * ($googleSearches ?: 1);
				$dividendGoogle += $googleSearches;

				$sumSeznamPosition += $seznamPosition;
				$countSeznam += $seznamPosition * ($seznamSearches ?: 1);
				$countSeznamLastMonth += $seznamPositionLastMonth * ($seznamSearches ?: 1);
				$dividendSeznam += $seznamSearches;

				$dividendSearchesGoogle += $googlePosition;
				$dividendSearchesSeznam += $seznamPosition;
				$countSearchesGoogle += $googleSearches;
				$countSearchesSeznam += $seznamSearches;
			}

			if ($countKeywords > 1) {
				$googlePositionAvg = 0 !== $dividendGoogle ? round($countGoogle / $dividendGoogle) : $countGoogle;
				$googlePositionLastMonthAvg = 0 !== $dividendGoogle ? round($countGoogleLastMonth / $dividendGoogle) : $countGoogleLastMonth;
				$seznamPositionAvg = 0 !== $dividendSeznam ? round($countSeznam / $dividendSeznam) : $countSeznam;
				$seznamPositionLastMonthAvg = 0 !== $dividendSeznam ? round($countSeznamLastMonth / $dividendSeznam) : $countSeznamLastMonth;

				$googleSearchesAvg = 0 !== $dividendSearchesGoogle ? round($countSearchesGoogle / $dividendSearchesGoogle) : $countSearchesGoogle;
				$seznamSearchesAvg = 0 !== $dividendSearchesSeznam ? round($countSearchesSeznam / $dividendSearchesSeznam) : $countSearchesSeznam;
			} else {
				$googlePositionAvg = $googlePosition;
				$googlePositionLastMonthAvg = $googlePositionLastMonth;
				$seznamPositionAvg = $seznamPosition;
				$seznamPositionLastMonthAvg = $seznamPositionLastMonth;
				$googleSearchesAvg = $countSearchesGoogle;
				$seznamSearchesAvg = $countSearchesSeznam;
			}
		}

		$pageExtension->setGooglePositionAvg($googlePositionAvg);
		$pageExtension->setGooglePositionLastMonthAvg($googlePositionLastMonthAvg);
		$pageExtension->setGooglePositionMonthlyDiff($googlePositionLastMonthAvg ? $googlePositionLastMonthAvg - $googlePositionAvg : null);
		$pageExtension->setGoogleSearchesAvg($googleSearchesAvg);

		$pageExtension->setSeznamPositionAvg($seznamPositionAvg);
		$pageExtension->setSeznamPositionLastMonthAvg($seznamPositionLastMonthAvg);
		$pageExtension->setSeznamPositionMonthlyDiff($seznamPositionLastMonthAvg ? $seznamPositionLastMonthAvg - $seznamPositionAvg : null);
		$pageExtension->setSeznamSearchesAvg($seznamSearchesAvg);
	}

	private function updateMetaTitle(PageExtension $pageExtension)
	{
		if (!$pageExtension->getProcessedAt() || $pageExtension->getProcessedAt()->format('Y-m-d') === (new \DateTime())->format('Y-m-d')) {
			return;
		}

		$shop = $pageExtension->getShop();

		if (!$shop) {
			return;
		}

		$localization = $shop->getLocalization();

		if (!$shop->isVisible()) {
			return;
		}

		$shopDetailUrl = $localization->getBaseUrl() . '/' . RouterFactory::getTranslation('shop', $localization) . '/' . $shop->getSlug();

		try {
			$client = new Client(['verify' => false]);
			$response = $client->request('GET', $shopDetailUrl);

			preg_match('/<title[^>]*>(.*?)<\/title>/', $response->getBody()->getContents(), $matches);

			if (isset($matches[1])) {
				$pageExtension->setCurrentMetaTitle($matches[1]);
				usleep(500000);
			}
		} catch (\Exception $e) {
		}
	}
}
