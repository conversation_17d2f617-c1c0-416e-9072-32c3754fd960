<?php

namespace tipli\Commands;

use tipli\Model\Messages\MailkitEventManager;

class ProcessMailkitEvents extends Job
{
	public function __construct(private MailkitEventManager $mailkitEventManager)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:process-mailkit-events:run');
	}

	public function start()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 400);

		$log = $this->onStart();

		$this->mailkitEventManager->downloadEvents();

		$this->onFinish($log);
	}
}
