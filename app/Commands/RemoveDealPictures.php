<?php

namespace tipli\Commands;

use Nette\Utils\Finder;
use tipli\Model\Deals\DealFacade;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Images\ImageStorage;
use Tracy\Debugger;

class RemoveDealPictures extends Job
{
	private const DIR = __DIR__ . '/../../www/upload/images/deals-deal-picture';

	/** @var ImageStorage */
	private $imageStorage;

	/** @var DealFacade */
	private $dealFacade;

	public function __construct(ImageStorage $imageStorage, DealFacade $dealFacade)
	{
		parent::__construct();

		$this->imageStorage = $imageStorage;
		$this->dealFacade = $dealFacade;
	}

	protected function configure()
	{
		parent::configure();

		$this->setName('tipli:remove-deal-pictures:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->removeDealPictures();

		$this->onFinish($log);
	}

	private function removeDealPictures()
	{
		/*** @var \SplFileInfo  $file */
		foreach (Finder::findFiles('*')->from(self::DIR) as $file) {
			$parseFileName = explode('.', $file->getFilename());
			$imageId = $parseFileName[0];

			/** @var Image|null $image */
			$image = $this->imageStorage->findImage($imageId);

			if ($image === null) {
				Debugger::log($imageId, 'remove-deal-pictures');

				continue;
			}

			$removeDateThreshold = new \DateTime('2021-05-01 00:00:00');
			$dealByImage = $this->dealFacade->findByPicture($image);

			if ($image->getCreatedAt() < $removeDateThreshold && ($dealByImage === null || $dealByImage->getValidTill() < $removeDateThreshold)) {
				$this->imageStorage->deleteImageFiles($image);

				echo $imageId . "\r\n";
			}
		}
	}
}
