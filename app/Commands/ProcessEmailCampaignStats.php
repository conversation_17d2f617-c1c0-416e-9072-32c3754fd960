<?php

namespace tipli\Commands;

use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Messages\MandrillClient;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Utm\Entities\Utm;
use tipli\Model\Utm\UtmFacade;

class ProcessEmailCampaignStats extends Job
{
	/** @var MandrillClient */
	private $mandrillClient;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var MessageFacade */
	private $messageFacade;

	/** @var UtmFacade */
	private $utmFacade;

	/** @var StatisticDataProvider */
	private $statisticDataProvider;

	public function __construct(MandrillClient $mandrillClient, LocalizationFacade $localizationFacade, MessageFacade $messageFacade, UtmFacade $utmFacade, StatisticDataProvider $statisticDataProvider)
	{
		parent::__construct();

		$this->localizationFacade = $localizationFacade;
		$this->messageFacade = $messageFacade;
		$this->mandrillClient = $mandrillClient;
		$this->utmFacade = $utmFacade;
		$this->statisticDataProvider = $statisticDataProvider;
	}

	protected function configure()
	{
		$this->setName('tipli:process-mail-campaign-stats:run');
	}

	public function start()
	{
		ini_set('memory_limit', '2048M');
		ini_set('max_execution_time', 900);

		$log = $this->onStart();
		$currentHour = date('G');

		$this->processMandrill();

		$this->onFinish($log);
	}

	public function matchLocalizationFromCampaignName($campaignName)
	{
		$localization = null;

		preg_match('/(PL|CZ|SK|DE|RO|HU)/', $campaignName, $matchLocalizationPrefix);

		if (isset($matchLocalizationPrefix[0])) {
			$localizationPrefix = Strings::lower($matchLocalizationPrefix[0]);
			$localizationPrefix = str_replace('cz', 'cs', $localizationPrefix);
			$localization = $this->localizationFacade->findOneByLocale($localizationPrefix);
		}

		return $localization;
	}

	public function processMandrill()
	{
		$getAllTimeTags = $this->mandrillClient->getAllTimeTags();
		$dataArray = [];

		foreach ($getAllTimeTags as $allTimeTag) {
			$date = new \DateTime($allTimeTag->time);

			if ($date <= new \DateTime('- 1 day')) {
				continue;
			}

			$sent = $allTimeTag->sent;
			$hard_bounces = $allTimeTag->hard_bounces;
			$soft_bounces = $allTimeTag->soft_bounces;
			$rejects = $allTimeTag->rejects;
			$complaints = $allTimeTag->complaints;
			$unsubs = $allTimeTag->unsubs;
			$opens = $allTimeTag->opens;
			$clicks = $allTimeTag->clicks;
			$unique_opens = $allTimeTag->unique_opens;
			$unique_clicks = $allTimeTag->unique_clicks;

			$item = &$dataArray[$date->format('Y-m-d')][$allTimeTag->tag]; // @phpstan-ignore-line

			$item['sent'] = isset($item['sent']) ? $item['sent'] + $sent : $sent;
			$item['hard_bounces'] = isset($item['hard_bounces']) ? $item['hard_bounces'] + $hard_bounces : $hard_bounces;
			$item['soft_bounces'] = isset($item['soft_bounces']) ? $item['soft_bounces'] + $soft_bounces : $soft_bounces;
			$item['rejects'] = isset($item['rejects']) ? $item['rejects'] + $rejects : $rejects;
			$item['complaints'] = isset($item['complaints']) ? $item['complaints'] + $complaints : $complaints;
			$item['unsubs'] = isset($item['unsubs']) ? $item['unsubs'] + $unsubs : $unsubs;
			$item['opens'] = isset($item['opens']) ? $item['opens'] + $opens : $opens;
			$item['clicks'] = isset($item['clicks']) ? $item['clicks'] + $clicks : $clicks;
			$item['unique_opens'] = isset($item['unique_opens']) ? $item['unique_opens'] + $unique_opens : $unique_opens;
			$item['unique_clicks'] = isset($item['unique_clicks']) ? $item['unique_clicks'] + $unique_clicks : $unique_clicks;
		}

		if (count($dataArray) > 0) {
			foreach ($dataArray as $dateString => $tags) {
				foreach ($tags as $tagKey => $tag) {
					$date = new \DateTime($dateString);
					$transactionData = $this->getUtmTransactionsData($this->utmFacade->findUtmsByCampaign($tagKey), $date, $date);

					$this->messageFacade->createOrUpdateMandrillTag(
						$tagKey,
						$tag['sent'],
						$tag['hard_bounces'],
						$tag['soft_bounces'],
						$tag['rejects'],
						$tag['complaints'],
						$tag['unsubs'],
						$tag['opens'],
						$tag['clicks'],
						$tag['unique_opens'],
						$tag['unique_clicks'],
						$transactionData->turnover,
						$transactionData->countOfActivatedUsers,
						$transactionData->countOfReactivatedUsers,
						$transactionData->countAddonInstalls,
						$date
					);
				}
			}
		}
	}

	public function getUtmTransactionsData(array $utms, $dateFrom, $dateTo)
	{
		$data = new ArrayHash();

		$data->turnover = 0;
		$data->countOfReactivatedUsers = 0;
		$data->countOfActivatedUsers = 0;
		$data->countAddonInstalls = 0;
		$trasnactionUtms = [];

		/** @var Utm $utm */
		foreach ($utms as $utm) {
			$trasnactionUtms[$utm->getId()] = $utm;
			$data->turnover += $this->statisticDataProvider->getTurnoverOfTransactions($dateFrom, $dateTo, null, $utm);
		}

		if ($trasnactionUtms) {
			$data->countOfReactivatedUsers = $this->statisticDataProvider->getCountOfReactivatedUsersByUtmIds($trasnactionUtms, $dateFrom, $dateTo);
			$data->countOfActivatedUsers = $this->statisticDataProvider->getCountOfActivatedUsersByUtmIds($trasnactionUtms, $dateFrom, $dateTo);
			$data->countAddonInstalls = $this->statisticDataProvider->getCountOfAddonInstalls($dateFrom, $dateTo, $trasnactionUtms);
		}

		return $data;
	}
}
