<?php

namespace tipli\Commands;

use tipli\Model\Groups\Entities\Group;
use tipli\Model\Groups\GroupFacade;

class ProcessGroups extends Job
{
	/** @var GroupFacade */
	private $groupFacade;

	public function __construct(GroupFacade $groupFacade)
	{
		parent::__construct();

		$this->groupFacade = $groupFacade;
	}

	protected function configure()
	{
		$this->setName('tipli:process-groups:run');
	}

	public function start()
	{
		ini_set('memory_limit', '2048M');
		ini_set('max_execution_time', 900);

		$log = $this->onStart();

		$this->processGroups();

		$this->onFinish($log);
	}

	private function processGroups()
	{
		$groups = $this->groupFacade->findGroupsToProcess(1);

		/** @var Group $group */
		foreach ($groups as $group) {
			$this->groupFacade->processGroup($group);
		}
	}
}
