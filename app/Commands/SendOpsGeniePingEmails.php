<?php

namespace tipli\Commands;

use tipli\Model\Messages\Entities\Email;
use tipli\Model\Messages\MessageFacade;

class SendOpsGeniePingEmails extends Job
{
	/** @var MessageFacade */
	private $messageFacade;

	public function __construct(MessageFacade $messageFacade)
	{
		parent::__construct();

		$this->messageFacade = $messageFacade;
	}

	protected function configure()
	{
		$this->setName('tipli:send-opsgenie-ping-emails:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->messageFacade->sendTextEmail(
			'<EMAIL>',
			'ping',
			'ping',
			'opsgenie-ping',
			'opsgenie-ping',
			'<EMAIL>',
			Email::PRIORITY_SEND_ALWAYS,
			new \DateTime(),
			false
		);

		$this->messageFacade->sendTextEmail(
			'<EMAIL>',
			'ping',
			'ping',
			'opsgenie-ping',
			'opsgenie-ping',
			'<EMAIL>',
			Email::PRIORITY_SEND_ALWAYS,
			new \DateTime(),
			false
		);

		$this->onFinish($log);
	}
}
