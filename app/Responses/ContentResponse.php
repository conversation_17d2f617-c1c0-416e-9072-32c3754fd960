<?php

namespace tipli\Responses;

use Nette\Application\Response;

class ContentResponse implements Response
{
	use \Nette\SmartObject;

	/** @var string */
	private $contentType;

	/** @var string */
	private $name;

	/**
	 * @param mixed $fileContent file content
	 * @param string $name imposed file name
	 * @param boolean $forDownload is file for download?
	 * @param string $contentType MIME content type
	 */
	public function __construct(private $fileContent, $name = null, protected $forDownload = false, $contentType = null)
	{
		$this->name = $name ? $name : 'file';
		$this->contentType = $contentType ? $contentType : 'application/octet-stream';
	}

	/**
	 * Returns the file name.
	 *
	 * @return string
	 */
	final public function getName()
	{
		return $this->name;
	}

	/**
	 * Returns the MIME content type of a downloaded file.
	 *
	 * @return string
	 */
	final public function getContentType()
	{
		return $this->contentType;
	}

	/**
	 * Sends content to output
	 *
	 * @param \Nette\Http\IRequest $httpRequest
	 * @param \Nette\Http\IResponse $httpResponse
	 */
	public function send(\Nette\Http\IRequest $httpRequest, \Nette\Http\IResponse $httpResponse): void
	{
		$httpResponse->setContentType($this->contentType);
		$httpResponse->setHeader('Content-Disposition', ($this->forDownload ? 'attachment; ' : '') . 'filename="' . $this->name . '"');
		$httpResponse->setHeader('Content-Length', strlen($this->fileContent));

		echo $this->fileContent;
	}
}
