var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Welsh = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>w", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON> Sul",
            "<PERSON><PERSON><PERSON>lu<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON> Merch<PERSON>",
            "<PERSON><PERSON><PERSON> Iau",
            "<PERSON><PERSON><PERSON>",
            "Dydd Sadwrn",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Chwe<PERSON>",
            "<PERSON>w",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON>",
            "Gorf<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Tach",
            "Rhag",
        ],
        longhand: [
            "<PERSON><PERSON>r",
            "<PERSON>we<PERSON><PERSON>r",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "Mehe<PERSON>",
            "Gorffennaf",
            "Aws<PERSON>",
            "Medi",
            "<PERSON>yd<PERSON>f",
            "<PERSON>ch<PERSON><PERSON>",
            "Rhag<PERSON><PERSON>",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function (nth) {
        if (nth === 1)
            return "af";
        if (nth === 2)
            return "ail";
        if (nth === 3 || nth === 4)
            return "ydd";
        if (nth === 5 || nth === 6)
            return "ed";
        if ((nth >= 7 && nth <= 10) ||
            nth == 12 ||
            nth == 15 ||
            nth == 18 ||
            nth == 20)
            return "fed";
        if (nth == 11 ||
            nth == 13 ||
            nth == 14 ||
            nth == 16 ||
            nth == 17 ||
            nth == 19)
            return "eg";
        if (nth >= 21 && nth <= 39)
            return "ain";
        return "";
    },
    time_24hr: true,
};
fp.l10ns.cy = Welsh;
export default fp.l10ns;
