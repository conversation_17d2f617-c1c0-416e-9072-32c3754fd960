//
// Header Menu
//




// Build desktop menu
@include kt-menu-hor-build-layout(kt-get($kt-header-config, menu, desktop));
@include kt-menu-hor-build-skin(kt-get($kt-header-config, menu, desktop), default);

// Build mobile menu
@include kt-menu-ver-build-layout(kt-get($kt-header-config, menu, tablet-and-mobile));
@include kt-menu-ver-build-skin(kt-get($kt-header-config, menu, tablet-and-mobile), default);

// Header menu mobile offcanvas
@include kt-offcanvas-build(kt-header-menu-wrapper, tablet-and-mobile, kt-get($kt-header-config, menu, offcanvas-mobile));

@include kt-desktop {
	.kt-header-menu {
		margin-left: kt-get($kt-page-padding, desktop);
		
		.kt-menu__nav {
			> .kt-menu__item {
				> .kt-menu__link {
					border-radius: 3px;
					padding: 0.75rem 1.1rem;

					.kt-menu__link-text {
						font-size: 1.075rem;
						font-family: kt-get($kt-font-families, heading);
					}
				}

				> .kt-menu__submenu {
					> .kt-menu__subnav {
						border-top-left-radius: 0;
						border-top-right-radius: 0;
					}
				}
			}
		}
	}
}

@include kt-media-range(lg, 1400px) {
	.kt-header-menu {
		margin-left: 10px;

		.kt-menu__nav {
			> .kt-menu__item {
				padding-left: 1px;
				padding-right: 1px;

				> .kt-menu__link {
					padding-left: 0.35rem;
					padding-right: 0.35rem;

					.kt-menu__link-text {
						font-size: 0.85rem;
					}
				}
			}
		}
	}
}