/*
$kt-media-breakpoints: (
    // Small screen / phone
    sm: 576px,
    
    // Medium screen / tablet
    md: 768px,
    
    // Large screen / desktop
    lg: 1024px,
    
    // Extra large screen / wide desktop
    xl: 1200px,

    // Extra large screen / wide desktop
    xxl: 1600px
)!default;
*/
.kt-blog-grid .kt-blog-grid__head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center; }
  .kt-blog-grid .kt-blog-grid__head .kt-blog-grid__thumb-link {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1; }
    .kt-blog-grid .kt-blog-grid__head .kt-blog-grid__thumb-link .kt-blog-grid__image {
      width: 100%;
      height: 100%; }

.kt-blog-grid .kt-blog-grid__body .kt-blog-grid__date {
  color: #918ea7;
  margin-bottom: 0.75rem;
  font-weight: 400; }

.kt-blog-grid .kt-blog-grid__body .kt-blog-grid__title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.25rem;
  line-height: 2.25rem; }

.kt-blog-grid .kt-blog-grid__body .kt-blog-grid__content {
  font-size: 1.1rem;
  font-weight: 400;
  color: #918ea7;
  margin-bottom: 1.25rem; }

.kt-blog-grid .kt-blog-grid__body .kt-blog-grid__link {
  color: #5578eb;
  font-weight: 500;
  font-size: 1.1rem;
  -webkit-transition: all 0.3s;
  transition: all 0.3s; }
  .kt-blog-grid .kt-blog-grid__body .kt-blog-grid__link:hover, .kt-blog-grid .kt-blog-grid__body .kt-blog-grid__link:focus {
    color: #173fc3; }
