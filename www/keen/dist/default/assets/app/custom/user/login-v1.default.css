/*
$kt-media-breakpoints: (
    // Small screen / phone
    sm: 576px,
    
    // Medium screen / tablet
    md: 768px,
    
    // Large screen / desktop
    lg: 1024px,
    
    // Extra large screen / wide desktop
    xl: 1200px,

    // Extra large screen / wide desktop
    xxl: 1600px
)!default;
*/
body {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 0; }

.kt-login-v1 {
  padding: 0; }
  .kt-login-v1 .kt-login-v1__head {
    padding: 3rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap; }
    .kt-login-v1 .kt-login-v1__head .kt-login-v1__logo > a {
      display: inline-block; }
    .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center; }
      .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup > .kt-login-v1__heading {
        margin-bottom: 0;
        color: rgba(255, 255, 255, 0.5);
        font-size: 1.1rem;
        font-weight: 500; }
      .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup > a {
        font-size: 1.1rem;
        margin-left: 0.5rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        -webkit-transition: color 0.3s ease;
        transition: color 0.3s ease; }
        .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup > a:hover {
          color: #fff;
          -webkit-transition: color 0.3s ease;
          transition: color 0.3s ease; }
  .kt-login-v1 .kt-login-v1__body {
    padding: 3rem;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__section {
      width: 50%;
      -webkit-box-pack: end;
      -ms-flex-pack: end;
      justify-content: flex-end;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex; }
      .kt-login-v1 .kt-login-v1__body .kt-login-v1__section .kt-login-v1__info {
        width: 390px;
        margin-right: 7rem; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__section .kt-login-v1__info > .kt-login-v1__intro {
          color: #fff;
          font-size: 1.9rem;
          font-weight: 500;
          margin-bottom: 1.5rem;
          line-height: 1.75; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__section .kt-login-v1__info > p {
          color: rgba(255, 255, 255, 0.5);
          font-weight: 500;
          font-size: 1.1rem; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__seaprator {
      background-color: rgba(255, 255, 255, 0.1);
      height: 450px;
      width: 1px;
      margin: auto; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper {
      width: 50%; }
      .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container {
        max-width: 390px;
        width: 100%;
        margin-left: 7rem; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__title {
          text-align: center;
          color: #fff;
          padding-bottom: 2rem;
          font-size: 1.8rem;
          font-weight: 500; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__form .form-control {
          border-radius: 40px;
          padding: 1.75rem 2.2rem;
          margin-top: 1.3rem;
          border-color: rgba(255, 255, 255, 0.1);
          background-color: transparent;
          color: #fff; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__form .form-control::-moz-placeholder {
            color: rgba(255, 255, 255, 0.6);
            opacity: 1; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__form .form-control:-ms-input-placeholder {
            color: rgba(255, 255, 255, 0.6); }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__form .form-control::-webkit-input-placeholder {
            color: rgba(255, 255, 255, 0.6); }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__actions {
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-pack: justify;
          -ms-flex-pack: justify;
          justify-content: space-between;
          -webkit-box-align: center;
          -ms-flex-align: center;
          align-items: center;
          width: 100%; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__actions .kt-login-v1__forgot {
            display: inline-block;
            color: rgba(255, 255, 255, 0.6);
            -webkit-transition: color 0.3s ease;
            transition: color 0.3s ease; }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__actions .kt-login-v1__forgot:hover {
              color: #fff;
              -webkit-transition: color 0.3s ease;
              transition: color 0.3s ease; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__actions .btn {
            padding: 0.85rem 3.5rem;
            border-radius: 60px;
            color: #fff;
            background-color: #ff146c;
            font-size: 1.1rem; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider {
          margin: 3rem 0; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider .kt-divider > span:not(:first-child):not(:last-child) {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
            font-size: 1.1rem; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider .kt-divider > span {
            background: none; }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider .kt-divider > span:first-child {
              border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider .kt-divider > span:last-child {
              border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options {
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -webkit-box-pack: justify;
          -ms-flex-pack: justify;
          justify-content: space-between; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn {
            border-radius: 60px;
            background-color: #1F2375;
            font-weight: 500;
            -webkit-box-flex: 1;
            -ms-flex: 1;
            flex: 1;
            padding: 0.85rem 0;
            font-size: 1rem;
            color: #fff !important; }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn i {
              font-size: 1.1rem;
              color: #fff; }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn:not(:first-child):not(:last-child) {
              margin: 0 2rem; }
            .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn:hover {
              color: #fff;
              background-color: #1F2164; }
  .kt-login-v1 .kt-login-v1__footer {
    padding: 3rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }
    .kt-login-v1 .kt-login-v1__footer .kt-login-v1__menu {
      -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
      order: 2; }
      .kt-login-v1 .kt-login-v1__footer .kt-login-v1__menu > a {
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
        font-size: 0.93rem;
        -webkit-transition: color 0.3s ease;
        transition: color 0.3s ease; }
        .kt-login-v1 .kt-login-v1__footer .kt-login-v1__menu > a:not(:first-child):not(:last-child) {
          margin: 0 2rem; }
        .kt-login-v1 .kt-login-v1__footer .kt-login-v1__menu > a:hover {
          color: #fff;
          -webkit-transition: color 0.3s ease;
          transition: color 0.3s ease; }
    .kt-login-v1 .kt-login-v1__footer .kt-login-v1__copyright > a {
      font-weight: 500;
      font-size: 1rem;
      color: rgba(255, 255, 255, 0.7);
      -webkit-transition: color 0.3s ease;
      transition: color 0.3s ease; }
      .kt-login-v1 .kt-login-v1__footer .kt-login-v1__copyright > a:hover {
        color: #fff;
        -webkit-transition: color 0.3s ease;
        transition: color 0.3s ease; }

@media (max-width: 1024px) {
  body {
    background-size: initial; }
  .kt-login-v1 .kt-login-v1__head {
    padding: 2rem 1.5rem 1.5rem 1.5rem; }
    .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup {
      margin-top: 0.5rem; }
      .kt-login-v1 .kt-login-v1__head .kt-login-v1__signup > .kt-login-v1__heading {
        padding-left: 0; }
  .kt-login-v1 .kt-login-v1__body {
    padding: 3rem 1.5rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__section {
      padding-right: 0;
      width: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      margin-top: 3rem; }
      .kt-login-v1 .kt-login-v1__body .kt-login-v1__section .kt-login-v1__info {
        width: 380px;
        max-width: 100%;
        text-align: center;
        margin-right: 0; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__seaprator {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      height: 0px;
      width: 100%;
      max-width: 500px;
      margin: 2rem auto; }
    .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper {
      padding-left: 0;
      width: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center; }
      .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container {
        margin-left: 0; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__title {
          padding-top: 1rem; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__divider {
          margin: 2rem 0; }
        .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options {
          margin-top: 3rem; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn i {
            padding: 0.4rem 0.3rem 0.4rem 0; }
          .kt-login-v1 .kt-login-v1__body .kt-login-v1__wrapper .kt-login-v1__container .kt-login-v1__options .btn:not(:first-child):not(:last-child) {
            margin: 0 1rem; }
  .kt-login-v1 .kt-login-v1__footer {
    padding: 1.5rem; }
    .kt-login-v1 .kt-login-v1__footer .kt-login-v1__menu > a {
      padding-right: 0.5rem; }
    .kt-login-v1 .kt-login-v1__footer .kt-login-v1__copyright > a:not(:first-child):not(:last-child) {
      padding: 0 1rem; } }
