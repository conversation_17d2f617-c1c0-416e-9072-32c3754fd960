@charset "UTF-8";
/*
$kt-media-breakpoints: (
    // Small screen / phone
    sm: 576px,
    
    // Medium screen / tablet
    md: 768px,
    
    // Large screen / desktop
    lg: 1024px,
    
    // Extra large screen / wide desktop
    xl: 1200px,

    // Extra large screen / wide desktop
    xxl: 1600px
)!default;
*/
.kt-pricing-v1 {
  margin: 2rem 0;
  padding: 0 2rem;
  border-right: 1px solid #ebebf5; }
  @media (max-width: 1399px) {
    .kt-pricing-v1 {
      border-right: 0;
      margin: 0 2rem;
      padding: 2rem 0;
      border-bottom: 1px solid #ebebf5; }
      div[class*='col-']:nth-child(2n) .kt-pricing-v1 {
        border-right-width: 0; } }
  .kt-pricing-v1--last {
    border-right: 0; }
    @media (max-width: 1024px) {
      .kt-pricing-v1--last {
        border-bottom: 0; } }
  .kt-pricing-v1 .kt-pricing-v1__header .kt-iconbox__icon {
    margin-bottom: 3rem; }
  .kt-pricing-v1 .kt-pricing-v1__header .kt-iconbox__title {
    font-size: 1.7rem;
    font-weight: 600;
    margin-bottom: 0; }
  .kt-pricing-v1 .kt-pricing-v1__body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center; }
    .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      margin-bottom: 1.25rem; }
      .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels .kt-pricing-v1__labels-item {
        color: #9b98af;
        font-size: 1.15rem;
        font-weight: 500; }
        .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels .kt-pricing-v1__labels-item:after {
          content: '•';
          -ms-flex-item-align: center;
          align-self: center;
          margin: 0 1rem;
          color: #d3d1dc; }
        .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels .kt-pricing-v1__labels-item:first-child {
          margin-left: 0; }
        .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels .kt-pricing-v1__labels-item:last-child {
          padding-right: 0; }
          .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__labels .kt-pricing-v1__labels-item:last-child:after {
            content: none; }
    .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__content {
      color: #b2afc6;
      text-align: center;
      padding: 0 2rem;
      margin-bottom: 2rem; }
    .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__price {
      font-weight: 700;
      font-size: 4rem;
      text-align: center;
      margin-bottom: 2rem;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex; }
      .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__price .kt-pricing-v1__price-currency {
        color: #cccce3;
        font-size: 1.75rem;
        font-weight: 300;
        -ms-flex-item-align: top;
        align-self: top;
        margin-top: 1rem;
        margin-left: 0.4rem; }
    .kt-pricing-v1 .kt-pricing-v1__body .kt-pricing-v1__button {
      margin-bottom: 2.75rem; }
  .kt-pricing-v1.kt-pricing-v1--brand .kt-pricing-v1__header .kt-iconbox__icon {
    color: #385aeb; }
  .kt-pricing-v1.kt-pricing-v1--brand .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #95a7f4; }
  .kt-pricing-v1.kt-pricing-v1--brand .kt-pricing-v1__button .btn {
    background-color: #385aeb;
    color: #ffffff;
    border-color: #385aeb; }
    .kt-pricing-v1.kt-pricing-v1--brand .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--brand .kt-pricing-v1__button .btn:focus {
      background-color: #163bda; }
  .kt-pricing-v1.kt-pricing-v1--metal .kt-pricing-v1__header .kt-iconbox__icon {
    color: #d3dae6; }
  .kt-pricing-v1.kt-pricing-v1--metal .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: white; }
  .kt-pricing-v1.kt-pricing-v1--metal .kt-pricing-v1__button .btn {
    background-color: #d3dae6;
    color: #586272;
    border-color: #d3dae6; }
    .kt-pricing-v1.kt-pricing-v1--metal .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--metal .kt-pricing-v1__button .btn:focus {
      background-color: #b2bfd4; }
  .kt-pricing-v1.kt-pricing-v1--light .kt-pricing-v1__header .kt-iconbox__icon {
    color: #ffffff; }
  .kt-pricing-v1.kt-pricing-v1--light .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: white; }
  .kt-pricing-v1.kt-pricing-v1--light .kt-pricing-v1__button .btn {
    background-color: #ffffff;
    color: #282a3c;
    border-color: #ffffff; }
    .kt-pricing-v1.kt-pricing-v1--light .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--light .kt-pricing-v1__button .btn:focus {
      background-color: #e6e6e6; }
  .kt-pricing-v1.kt-pricing-v1--dark .kt-pricing-v1__header .kt-iconbox__icon {
    color: #645ca1; }
  .kt-pricing-v1.kt-pricing-v1--dark .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #a19dc7; }
  .kt-pricing-v1.kt-pricing-v1--dark .kt-pricing-v1__button .btn {
    background-color: #645ca1;
    color: #ffffff;
    border-color: #645ca1; }
    .kt-pricing-v1.kt-pricing-v1--dark .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--dark .kt-pricing-v1__button .btn:focus {
      background-color: #504a80; }
  .kt-pricing-v1.kt-pricing-v1--accent .kt-pricing-v1__header .kt-iconbox__icon {
    color: #00c5dc; }
  .kt-pricing-v1.kt-pricing-v1--accent .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #43ebff; }
  .kt-pricing-v1.kt-pricing-v1--accent .kt-pricing-v1__button .btn {
    background-color: #00c5dc;
    color: #ffffff;
    border-color: #00c5dc; }
    .kt-pricing-v1.kt-pricing-v1--accent .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--accent .kt-pricing-v1__button .btn:focus {
      background-color: #0097a9; }
  .kt-pricing-v1.kt-pricing-v1--focus .kt-pricing-v1__header .kt-iconbox__icon {
    color: #9816f4; }
  .kt-pricing-v1.kt-pricing-v1--focus .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #c377f9; }
  .kt-pricing-v1.kt-pricing-v1--focus .kt-pricing-v1__button .btn {
    background-color: #9816f4;
    color: #ffffff;
    border-color: #9816f4; }
    .kt-pricing-v1.kt-pricing-v1--focus .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--focus .kt-pricing-v1__button .btn:focus {
      background-color: #7c0acd; }
  .kt-pricing-v1.kt-pricing-v1--primary .kt-pricing-v1__header .kt-iconbox__icon {
    color: #5867dd; }
  .kt-pricing-v1.kt-pricing-v1--primary .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #adb4ee; }
  .kt-pricing-v1.kt-pricing-v1--primary .kt-pricing-v1__button .btn {
    background-color: #5867dd;
    color: #ffffff;
    border-color: #5867dd; }
    .kt-pricing-v1.kt-pricing-v1--primary .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--primary .kt-pricing-v1__button .btn:focus {
      background-color: #2e40d4; }
  .kt-pricing-v1.kt-pricing-v1--success .kt-pricing-v1__header .kt-iconbox__icon {
    color: #1dc9b7; }
  .kt-pricing-v1.kt-pricing-v1--success .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #63e9db; }
  .kt-pricing-v1.kt-pricing-v1--success .kt-pricing-v1__button .btn {
    background-color: #1dc9b7;
    color: #ffffff;
    border-color: #1dc9b7; }
    .kt-pricing-v1.kt-pricing-v1--success .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--success .kt-pricing-v1__button .btn:focus {
      background-color: #179c8e; }
  .kt-pricing-v1.kt-pricing-v1--info .kt-pricing-v1__header .kt-iconbox__icon {
    color: #5578eb; }
  .kt-pricing-v1.kt-pricing-v1--info .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #b0c0f6; }
  .kt-pricing-v1.kt-pricing-v1--info .kt-pricing-v1__button .btn {
    background-color: #5578eb;
    color: #ffffff;
    border-color: #5578eb; }
    .kt-pricing-v1.kt-pricing-v1--info .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--info .kt-pricing-v1__button .btn:focus {
      background-color: #2754e6; }
  .kt-pricing-v1.kt-pricing-v1--warning .kt-pricing-v1__header .kt-iconbox__icon {
    color: #ffb822; }
  .kt-pricing-v1.kt-pricing-v1--warning .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #ffd988; }
  .kt-pricing-v1.kt-pricing-v1--warning .kt-pricing-v1__button .btn {
    background-color: #ffb822;
    color: #111111;
    border-color: #ffb822; }
    .kt-pricing-v1.kt-pricing-v1--warning .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--warning .kt-pricing-v1__button .btn:focus {
      background-color: #eea200; }
  .kt-pricing-v1.kt-pricing-v1--danger .kt-pricing-v1__header .kt-iconbox__icon {
    color: #fd397a; }
  .kt-pricing-v1.kt-pricing-v1--danger .kt-pricing-v1__price .kt-pricing-v1__price-currency {
    color: #fe9ebe; }
  .kt-pricing-v1.kt-pricing-v1--danger .kt-pricing-v1__button .btn {
    background-color: #fd397a;
    color: #ffffff;
    border-color: #fd397a; }
    .kt-pricing-v1.kt-pricing-v1--danger .kt-pricing-v1__button .btn:hover, .kt-pricing-v1.kt-pricing-v1--danger .kt-pricing-v1__button .btn:focus {
      background-color: #fc0758; }
