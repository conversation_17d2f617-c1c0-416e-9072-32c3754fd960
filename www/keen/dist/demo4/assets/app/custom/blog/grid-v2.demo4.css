/*
$kt-media-breakpoints: (
    // Small screen / phone
    sm: 576px,
    
    // Medium screen / tablet
    md: 768px,
    
    // Large screen / desktop
    lg: 1024px,
    
    // Extra large screen / wide desktop
    xl: 1200px,

    // Extra large screen / wide desktop
    xxl: 1600px
)!default;
*/
.kt-blog-grid-v2 {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  min-height: 33rem;
  padding: 3.5rem;
  position: relative; }
  .kt-blog-grid-v2:before {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.3;
    z-index: 0; }
  .kt-blog-grid-v2 .kt-blog-grid-v2__tag {
    background-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    font-weight: 600;
    padding: 0.5rem 0.7rem;
    margin: 0 auto auto 0;
    border-radius: 5px;
    -ms-flex-item-align: start;
    align-self: flex-start;
    position: relative;
    z-index: 1; }
  .kt-blog-grid-v2 .kt-blog-grid-v2__body {
    position: relative;
    z-index: 1; }
    .kt-blog-grid-v2 .kt-blog-grid-v2__body .kt-blog-grid-v2__date {
      color: #ffffff;
      margin-bottom: 1.5rem;
      font-weight: 400; }
    .kt-blog-grid-v2 .kt-blog-grid-v2__body .kt-blog-grid-v2__link {
      color: #ffffff; }
      .kt-blog-grid-v2 .kt-blog-grid-v2__body .kt-blog-grid-v2__link .kt-blog-grid-v2__title {
        font-weight: 700;
        margin: 0; }
      .kt-blog-grid-v2 .kt-blog-grid-v2__body .kt-blog-grid-v2__link:hover .kt-blog-grid-v2__title, .kt-blog-grid-v2 .kt-blog-grid-v2__body .kt-blog-grid-v2__link:focus .kt-blog-grid-v2__title {
        text-decoration: underline; }
