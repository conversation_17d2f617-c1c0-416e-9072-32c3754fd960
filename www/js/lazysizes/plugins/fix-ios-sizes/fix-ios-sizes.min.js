/*! lazysizes - v5.2.2 */

!function(e,t){var r=function(){t(e.lazySizes),e.removeEventListener("lazyunveilread",r,!0)};t=t.bind(null,e,e.document),"object"==typeof module&&module.exports?t(require("lazysizes")):"function"==typeof define&&define.amd?define(["lazysizes"],t):e.lazySizes?r():e.addEventListener("lazyunveilread",r,!0)}(window,function(e,c,l){"use strict";var d,o=l.cfg,t=c.createElement("img");!("srcset"in t)||"sizes"in t||e.HTMLPictureElement||(d=/^picture$/i,c.addEventListener("lazybeforeunveil",function(e){var t,r,i,n,s,a,u;e.detail.instance==l&&!e.defaultPrevented&&!o.noIOSFix&&(t=e.target)&&(i=t.getAttribute(o.srcsetAttr))&&(r=t.parentNode)&&((s=d.test(r.nodeName||""))||(n=t.getAttribute("sizes")||t.getAttribute(o.sizesAttr)))&&(a=s?r:c.createElement("picture"),t._lazyImgSrc||Object.defineProperty(t,"_lazyImgSrc",{value:c.createElement("source"),writable:!0}),u=t._lazyImgSrc,n&&u.setAttribute("sizes",n),u.setAttribute(o.srcsetAttr,i),t.setAttribute("data-pfsrcset",i),t.removeAttribute(o.srcsetAttr),s||(r.insertBefore(a,t),a.appendChild(t)),a.insertBefore(u,t))}))});