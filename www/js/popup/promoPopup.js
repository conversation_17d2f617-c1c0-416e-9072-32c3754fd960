// Promo popup

var promoPopup = {};

promoPopup.loader = {
  ready: function(fn) {
    if (document.readyState == "complete") {
      return fn();
    }

    if (window.addEventListener) {
      window.addEventListener("load", fn, false);
    } else if (window.attachEvent) {
      window.attachEvent("onload", fn);
    } else {
      window.onload = fn;
    }
  }
};

promoPopup.loader.ready(function() {

  // Close popup - on click close button
  var close = document.getElementsByClassName("promo-popup__close");
  var popupBody = document.getElementsByClassName("promo-popup");
  var popupBg = document.getElementsByClassName("promo-popup-bg");
  var promoPop = readCookie("promoPop");

  // Click on Close button
  close[0].addEventListener("click", function() {
    popupBody[0].classList.remove("show");
    popupBg[0].classList.remove("show");
    createCookie("promoPop", "hide", 1);
  });

  // Click on Background
  popupBg[0].addEventListener("click", function() {
    popupBody[0].classList.remove("show");
    popupBg[0].classList.remove("show");
    createCookie("promoPop", "hide", 1);
  });

});

function createCookie(name, value, days) {
  var expires;

  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toGMTString();
  } else {
    expires = "";
  }
  document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(value) + expires + "; path=/";
}

function readCookie(name) {
  var nameEQ = encodeURIComponent(name) + "=";
  var ca = document.cookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
  }
  return null;
}

function eraseCookie(name) {
  createCookie(name, "", -1);
}
