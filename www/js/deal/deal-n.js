// Deal n modal

var deal_n = {};

deal_n.loader = {
    ready: function(fn) {
        if (document.readyState == "complete") {
            return fn();
        }

        if (window.addEventListener) {
            window.addEventListener("load", fn, false);
        } else if (window.attachEvent) {
            window.attachEvent("onload", fn);
        } else {
            window.onload = fn;
        }
    }
};

deal_n.loader.ready(function() {

	$(document).on("click", function(event) {
		if (event.target.className == "deal-category__dropdown-label") {
			$('.deal-category__dropdown').toggleClass("--show");
		} else {
			$('.deal-category__dropdown').removeClass("--show");
		}
	});

	$(".js-deals-n-shop-prev").on("click", function(event) {
		$(this).hide();
		$(".deal-n-shop").removeClass("deal-n-shop--second");
		$(".js-deals-n-shop-next").show();
	});

	$(".js-deals-n-shop-next").on("click", function(event) {
		$(this).hide();
		$(".deal-n-shop").addClass("deal-n-shop--second");
		$(".js-deals-n-shop-prev").show();
	});

});

