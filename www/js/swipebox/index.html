<!DOCTYPE HTML>
<html lang="en-US">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
	<meta name="description" content="A touchable jQuery lightbox plugin for desktop, mobile and tablet" />
	<meta property="og:site_name" content="Swipebox" />
	<meta property="og:url" content="http://brutaldesign.github.com/swipebox/" />
	<meta property="og:image" content="http://swipebox.csag.co/images/swipe250.jpg" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="Swipebox | A touchable jQuery lightbox" />
	<meta property="og:description" content="Swipebox is a jQuery lightbox plugin for desktop, mobile and tablet">
	<meta itemprop="name" content="Swipebox | A touchable jQuery lightbox">
	<meta itemprop="image" content="http://swipebox.csag.co/images/swipe250.jpg">
	<meta itemprop="description" content="Swipebox is a jQuery lightbox plugin for desktop, mobile and tablet">
	<link href='http://fonts.googleapis.com/css?family=Source+Sans+Pro:400,700|Merriweather:400,700&subset=latin,latin-ext' rel='stylesheet' type='text/css'>
	<link rel="stylesheet" href="demo/normalize.css">
	<link rel="stylesheet" href="demo/bagpakk.min.css">
	<link rel="stylesheet" href="demo/style.css">
	<link rel="stylesheet" href="src/css/swipebox.css">
	<title>Swipebox | A touchable jQuery lightbox</title>
	<!-- share buttons -->
	<script type="text/javascript">(function(doc, script) {
		var js, 
		fjs = doc.getElementsByTagName(script)[0],
		add = function(url, id) {
		if (doc.getElementById(id)) {return;}
		js = doc.createElement(script);
		js.src = url;
		id && (js.id = id);
		fjs.parentNode.insertBefore(js, fjs);
		};add("//connect.facebook.net/en_US/all.js#xfbml=1", "facebook-jssdk");
		add("//platform.twitter.com/widgets.js", "twitter-wjs");
		}(document, "script"));
	</script>
	<!-- end share buttons -->
</head>
<body>
	<div id="fb-root"></div>
	<div id="fork-this">
		<a href="https://github.com/brutaldesign/swipebox"></a>
	</div>

	<header class="container">
		<div class="wrap small-width">
			<div class="lead text-center">
				<h1>Swipebox.</h1>
				<p class="tagline">A touchable jQuery lightbox</p>
			</div>
			
			<div id="share" class="text-center">
				<div id="twitter">
					<a href="https://twitter.com/share" class="twitter-share-button" data-url="http://brutaldesign.github.com/swipebox" data-text="Swipebox | A touchable jQuery lightbox" data-via="brutaldesign">Tweet</a>
				</div>
				<div id="fb">
					<div class="fb-like" data-href="http://brutaldesign.github.com/swipebox/" data-send="false" data-layout="button_count" data-width="110" data-show-faces="false" data-colorscheme="light" data-action="like"></div>
				</div>
			</div>
			<ul>
				<li><a href="#what">What is Swipebox ?</a>
					<ul>
						<li><a href="#features">Main Features</a></li>
						<li><a href="#compatibility">Compatibility</a></li>
					</ul>
				</li>
				<li><a href="#try">Try it</a></li>
				<li><a href="#basic">Basic Usage</a></li>
				<li><a href="#advanced">Advanced</a>
					<ul>
						<li><a href="#rel">Gallery</a></li>
						<li><a href="#video">Video support</a></li>
						<li><a href="#dynamic">Load slides dynamically</a></li>
						<li><a href="#open">Check open state</a></li>
						<li><a href="#options">Options</a></li>
					</ul>
				</li>
				<li><a href="#download">Download</a></li>
			</ul>
		</div>
	</header>

	<section id="exemple" class="container">
		<div class="wrap small-width">
			<div id="try"></div>
			<ul id="box-container">
				<li class="box">
					<a href="http://swipebox.csag.co/images/image-1.jpg" class="swipebox" title="Fog">
						<img src="http://swipebox.csag.co/images/image-1.jpg" alt="image">
					</a>
				</li>
				<li class="box">
					<a href="http://swipebox.csag.co/images/image-2.jpg" class="swipebox" title="City">
						<img src="http://swipebox.csag.co/images/image-2.jpg" alt="image">
					</a>
				</li>
				<li class="box">
					<a href="http://swipebox.csag.co/images/image-3.jpg" class="swipebox" title="Street">
						<img src="http://swipebox.csag.co/images/image-3.jpg" alt="image">
					</a>
				</li>
				<li class="box">
					<a href="http://swipebox.csag.co/images/image-4.jpg" class="swipebox" title="Mustache Guy">
						<img src="http://swipebox.csag.co/images/image-4.jpg" alt="image">
					</a>
				</li>
			</ul>
		</div>
	</section>

	<section  id="presentation" class="container">
		<div class="wrap small-width">
			<div id="what"></div>
			<h2>What is Swipebox ?</h2>
			<p>Swipebox is a jQuery "lightbox" plugin for desktop, mobile and tablet.</p>		
			
			<div id="features"></div>
			<h3>Main Features</h3>
			<ol>
				<li>Swipe gestures for mobile</li>
				<li>Keyboard Navigation for desktop</li>
				<li>CSS transitions with jQuery fallback</li>
				<li>Retina support for UI icons</li>
				<li>Easy CSS customization</li>
			</ol>
			
			<div id="compatibility"></div>
			<h3>Compatibility</h3>
			<p>Chrome, Safari, Firefox, Opera, IE9+, IOS4+, Android, windows phone.</p>
		</div>
	</section>

	<section id="usage" class="container">
		<div class="wrap small-width">
			<div id="basic"></div>
			<h2>Basic Usage</h2>
			<h3>Javascript</h3>
			
			<p>Include jquery and the swipebox script in your <code>head</code> tags or right before your <code>body</code> closing tag.</p>
			<pre>&lt;script src="lib/jquery-2.0.3.js"&gt;&lt;/script&gt;
&lt;script src="src/js/jquery.swipebox.js"&gt;&lt;/script&gt;</pre>

			<h3>CSS</h3>
			<p>Include the swipebox CSS style in your <code>head</code> tags.</p>
<pre>&lt;link rel="stylesheet" href="src/css/swipebox.css"&gt;</pre>
		<h3>HTML</h3>
		<p>Use a specific class for your links and use the title attribute as caption.</p>
<pre>&lt;a href="big/image.jpg" class="swipebox" title="My Caption"&gt;
&lt;img src="small/image.jpg" alt="image"&gt;
&lt;/a></pre>
		<h3>Fire the plugin</h3>
		<p>Bind the swipebox behaviour on every link with the "swipebox" class.</p>
<pre>&lt;script type="text/javascript"&gt;
;( function( $ ) {

	$( '.swipebox' ).swipebox();

} )( jQuery );
&lt;/script&gt;
</pre>
		</div>
	</section>
	<section id="advanced-usage" class="container">
		<div class="wrap small-width">
			<div id="advanced"></div>
			<h2>Advanced</h2>
			
			<div id="rel"></div>
			<h3>Gallery</h3>
			<p>You can add a <code>rel</code> attribute to your links to seperate your galleries.</p>
			<pre>
<span>&lt;!-- Gallery 1 --&gt;</span>
&lt;a rel="gallery-1" href="big/image1.jpg" class="swipebox"&gt;
	&lt;img src="small/image1.jpg" alt="image"&gt;
&lt;/a&gt;
&lt;a rel="gallery-1" href="big/image2.jpg" class="swipebox"&gt;
	&lt;img src="small/image2.jpg" alt="image"&gt;
&lt;/a&gt;
<span>&lt;!-- Gallery 2 --&gt;</span>
&lt;a rel="gallery-2" href="big/image3.jpg" class="swipebox"&gt;
	&lt;img src="small/image3.jpg" alt="image"&gt;
&lt;/a&gt;
&lt;a rel="gallery-2" href="big/image4.jpg" class="swipebox"&gt;
	&lt;img src="small/image4.jpg" alt="image"&gt;
&lt;/a&gt;</pre>
			
			<div id="video"></div>
			<h3>Video support</h3>
			<p>Simply paste a youtube or vimeo video URL in your href attribute. The script will automatically check if it's a youtube or vimeo URL and open the video in the swipebox.</p>
			<p>
				<a class="swipebox-video" href="http://vimeo.com/29193046">My Video</a>
			</p>
			<pre>&lt;a class="swipebox-video" rel="vimeo" href="http://vimeo.com/29193046"&gt;My Videos&lt;/a&gt;</pre>
			
			<div id="dynamic"></div>
			<h3>Load slides dynamically</h3>
			<p>You can set your gallery dynamically by passing an array object to the swipebox.</p>
			<p><a id="gallery" href="#">View gallery</a></p>
			
			<pre>
$( '#gallery' ).click( function( e ) {
	e.preventDefault();
	$.swipebox( [
		{ href:'big/image1.jpg', title:'My Caption' }, 
		{ href:'big/image2.jpg', title:'My Second Caption' }
	] );
} );</pre>
				<div id="open"></div>
				<h3>Check open state</h3>
<pre>if ( $.swipebox.isOpen ) {
	// do stuff
}</pre>
			
				<div id="options"></div>
				<h3>Options</h3>
<pre>
&lt;script type="text/javascript"&gt;
;( function( $ ) {

	$( '.swipebox' ).swipebox( {
		useCSS : true, <span>// false will force the use of jQuery for animations</span>
		useSVG : true, <span>// false to force the use of png for buttons</span>
		initialIndexOnArray : 0, <span>// which image index to init when a array is passed</span>
		hideCloseButtonOnMobile : false, <span>// true will hide the close button on mobile devices</span>
		removeBarsOnMobile : true, <span>// false will show top bar on mobile devices</span>
		hideBarsDelay : 3000, <span>// delay before hiding bars on desktop</span>
		videoMaxWidth : 1140, <span>// videos max width</span>
		beforeOpen: function() {}, <span>// called before opening</span>
		afterOpen: null, <span>// called after opening</span>
		afterClose: function() {}, <span>// called after closing</span>
		loopAtEnd: false <span>// true will return to the first image after the last image is reached</span>
	} );

} )( jQuery );
&lt;/script&gt;</pre>
		</div>
	</section>

	<section id="action" class="container text-center">
		<div class="wrap small-width">
			<div id="download"></div>
			<p><a class="button-big" href="https://github.com/brutaldesign/swipebox/archive/master.zip">Download</a> <a class="button-big" href="https://github.com/brutaldesign/swipebox">View Source</a></p>
		</div>
	</section>

	<footer class="text-center">
		<div class="wrap small-width">
			<a href="http://csag.co" target="_blank">csag.co</a> | <a href="http://wpwlf.co" target="_blank">wpwlf.co</a>
		</div>
	</footer>
	<script src="lib/ios-orientationchange-fix.js"></script>
	<script src="lib/jquery-2.1.0.min.js"></script>
	<script src="src/js/jquery.swipebox.js"></script>
	<script type="text/javascript">
	$( document ).ready(function() {

			/* Basic Gallery */
			$( '.swipebox' ).swipebox();
			
			/* Video */
			$( '.swipebox-video' ).swipebox();

			/* Dynamic Gallery */
			$( '#gallery' ).click( function( e ) {
				e.preventDefault();
				$.swipebox( [
					{ href : 'http://swipebox.csag.co/mages/image-1.jpg', title : 'My Caption' },
					{ href : 'http://swipebox.csag.co/images/image-2.jpg', title : 'My Second Caption' }
				] );
			} );

      });
	</script>
</body>
	
</html>
