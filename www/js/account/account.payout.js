/* Review Step 1 FrontEnd function  */

function accountPayoutFunctions() {
	var click = false;

    $('.payout__footer-submit').on( "click", function(e) {
		if (click == true) {
			e.preventDefault();
			console.log("click");
		} else {
			e.preventDefault();
			var spinner = document.createElement('div');
			spinner.setAttribute("id", "ajax-spinner");
			document.body.appendChild(spinner);

			spinner.style.display = 'block';
			click = true;

			$(this).parents("form").submit();

			console.log("submit");
		}
    });

}

/**
* Functions after document ready
*/

$().ready(function() {

    //
    accountPayoutFunctions();

});


