window.addEventListener('DOMContentLoaded', function () {
	const { autocomplete, getAlgoliaResults } =
		window['@algolia/autocomplete-js']
	const bodyElement = document.querySelector('body')
	const locale = bodyElement.getAttribute('data-locale')
	const basePath = bodyElement.getAttribute('data-basePath')
	const searchFormUrl = bodyElement.getAttribute('data-search-url')
	const autocompleteElm = document.getElementById('autocomplete')
	const apiUrl = basePath + '/api/v1/reward/'
	let items = []
	let containerElement = '#autocomplete-mobile'

	if (autocompleteElm) {
		const inputPlaceholder =
			autocompleteElm.getAttribute('data-placeholder')

		if (window.innerWidth >= 768) {
			containerElement = '#autocomplete'

			document
				.getElementById('autocomplete')
				.addEventListener('click', function (event) {
					// Zabránění šíření události na document
					event.stopPropagation()

					// Při kliknutí na autocomplete se zobrazí dropdown
					document
						.getElementById('autocomplete-desktop-dropdown')
						.classList.remove('hidden')
					document
						.getElementById('autocomplete-desktop-bg')
						.classList.remove('hidden')

					// Přidáme event listener na document pro detekci kliknutí mimo dropdown
					document.addEventListener(
						'click',
						function (event) {
							var dropdown = document.getElementById(
								'autocomplete-desktop-dropdown'
							)
							var dropdownInner = document.getElementById(
								'autocomplete-desktop-dropdown-inner'
							)

							// Pokud kliknutí není na autocomplete nebo na dropdown, skryjeme dropdown
							if (
								!dropdownInner.contains(event.target) &&
								event.target.id !== 'autocomplete'
							) {
								dropdown.classList.add('hidden')
								document
									.getElementById('autocomplete-desktop-bg')
									.classList.add('hidden')
							}
						},
						{ once: true }
					) // Tento listener se spustí pouze jednou a pak se odstraní
				})
		}

		const searchClient = algoliasearch(
			'D6VZ0E6F5F',
			'********************************'
		)

		const indexName = locale + '_shops'

		function updateElementsWithCashback() {
			items.forEach((item) => {
				const rewardElement = document.getElementById(item.slug)
				if (rewardElement) {
					rewardElement.innerHTML = `${item.cashback}`
				}
			})
		}

		function fetchAndStoreCashback(slug) {
			fetchCashback(slug).then((cashback) => {
				// Najdi odpovídající položku v poli a aktualizuj cashback
				const existingItem = items.find((item) => item.slug === slug)
				if (existingItem) {
					existingItem.cashback = cashback
				} else {
					// Pokud položka ještě neexistuje, přidej ji
					items.push({ slug, cashback })
				}
				// Aktualizuj všechny elementy po získání cashbacku
				updateElementsWithCashback()
			})
		}

		function processItems(newItem) {
			if (!items.some((item) => item.slug === newItem.slug)) {
				// Přidej položku a načti cashback
				items.push({ slug: newItem.slug, cashback: '' })
				fetchAndStoreCashback(newItem.slug)

				//console.log(items)
			}
		}

		// ziskej hodnoty rewardu
		async function fetchCashback(slug) {
			try {
				const response = await fetch(`${apiUrl}${slug}`)
				if (response.ok) {
					const data = await response.json()
					//console.log(data)
					return data || ''
				} else {
					console.log(`Failed to fetch cashback for ${slug}`)
					return ''
				}
			} catch (error) {
				console.log(`Error fetching cashback for ${slug}:`, error)
				return ''
			}
		}

		autocomplete({
			container: containerElement,
			placeholder: inputPlaceholder,
			insights: true,
			getSources({ query }) {
				return [
					{
						sourceId: 'products',
						getItems() {
							return getAlgoliaResults({
								searchClient,
								queries: [
									{
										indexName: indexName,
										query,
										params: {
											hitsPerPage: 8,
											attributesToSnippet: [
												'name:10',
												'description:35',
											],
											snippetEllipsisText: '…',
										},
									},
								],
							})
						},
						templates: {
							item({ item, components, html }) {
								processItems(item)

								return html`
									<a
										href="${basePath}/${item.destination_url}"
										class="flex gap-4 items-center py-2 px-5 md:py-4 xl:hover:bg-green-gradient"
									>
										<div
											class="flex justify-center items-center flex-shrink-0 w-[97px] h-[55px] border border-light-5 rounded-xl md:border-0 md:max-h-[30px] md:w-[75px]"
										>
											<img
												src="${item.logo}"
												alt="${item.name}"
												class="max-w-[58px] md:max-w-[75px] max-h-[30px] h-auto"
											/>
										</div>
										<div
											class="flex flex-col md:flex-row w-full"
										>
											<div>
												<span
													class="text-gray-500 text-sm font-normal leading-normal"
												>
													${components.Highlight({
														hit: item,
														attribute: 'name',
													})}
												</span>
											</div>
											<div
												id="${item.slug}"
												class="search-reward h-7 text-sm md:ml-auto"
											></div>
										</div>
									</a>
								`
							},
						},
					},
				]
			},
		})

		// Click submit Algolia form
		function submitForm() {
			var inputValue = document.querySelector('.aa-Input').value
			var fullUrl = searchFormUrl + '/' + encodeURIComponent(inputValue)

			var form = document.querySelector('.aa-Form')
			form.action = fullUrl
			form.submit()
		}

		document
			.querySelector('.aa-SubmitButton')
			.addEventListener('click', function (event) {
				event.preventDefault() // Zabráníme defaultnímu odeslání formuláře
				submitForm()
			})

		document
			.querySelector('.aa-Input')
			.addEventListener('keypress', function (event) {
				if (event.key === 'Enter') {
					event.preventDefault() // Zabráníme defaultnímu odeslání formuláře
					submitForm()
				}
			})
	}
})
