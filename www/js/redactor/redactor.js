$R('.redactor', {
	lang: 'cs',
	focus: true,
	toolbarFixedTopOffset: 60,
	autoparseLinks: false,
	minHeight: '280px',
	buttons: [
		'undo',
		'redo',
		'format',
		'bold',
		'italic',
		'ul',
		'ol',
		'link',
		'html',
		'image',
	],
	plugins: ['widget', 'tipliPlugin', 'table', 'video'],
	imageUpload: '/api/v1/image/admin-redactor-upload',
	spellcheck:
		parseInt($('meta[name=current-localization-id]').attr('content')) === 1,
})

$R(".redactor-disabled", {
	lang: "en",
	focus: false,
	toolbar: false,
	readOnly: true,
	toolbarFixedTopOffset: 60,
	autoparseLinks: false,
	minHeight: "250px",
	buttons: [],
});