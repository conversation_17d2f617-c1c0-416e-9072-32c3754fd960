// Flash message
window.hideFlashMessage = function (event) {
	const flashMessageElement = event.target.closest('.js-flash-message')
	if (flashMessageElement) {
		flashMessageElement.classList.add('hidden')
	}
}

var flashMessage = document.querySelector('.js-flash-message')
if (flashMessage) {
	setTimeout(function () {
		flashMessage.classList.add('hidden')
	}, 2000)
}

// Shops menu
const showShopsElement = document.querySelector('.js-show-shops')
const shopsMenuElement = document.querySelector('.js-shops-menu')
const shopsMenuElementInner = document.querySelector('.js-shops-menu-inner')

if (showShopsElement && shopsMenuElement) {
	showShopsElement.addEventListener('mouseenter', function () {
		shopsMenuElement.classList.remove('hidden')
	})

	shopsMenuElementInner.addEventListener('mouseleave', function () {
		shopsMenuElement.classList.add('hidden')
	})
}

// User dropdown
const showUserDropdownElement = document.querySelectorAll(
	'.js-show-user-dropdown'
)
const userDropdownElement = document.querySelector('.js-user-dropdown')
const userDropdownElementDown = document.querySelector('.js-dropdown-down')
const userDropdownElementUp = document.querySelector('.js-dropdown-up')
const headerElement = document.getElementById('header')

var userDropdownElementShow = false

if (showUserDropdownElement && userDropdownElement) {
	showUserDropdownElement.forEach(function (button) {
		button.addEventListener('click', function () {
			toggleUserDropdown()
		})
	})

	userDropdownElement.addEventListener('click', function () {
		toggleUserDropdown()
	})

	function toggleUserDropdown() {
		if (userDropdownElementShow) {
			userDropdownElement.classList.add('hidden')
			userDropdownElementShow = false
			userDropdownElementDown.classList.remove('hidden')
			userDropdownElementUp.classList.add('hidden')
		} else {
			userDropdownElement.classList.remove('hidden')
			userDropdownElementShow = true
			userDropdownElementDown.classList.add('hidden')
			userDropdownElementUp.classList.remove('hidden')
		}
	}
}

function toggleSidebar() {
	const sidebar = document.getElementById('sidebar')
	const hamburgerIcon = document.getElementById('hamburgerIcon')
	const closeIcon = document.getElementById('closeIcon')
	const overlay = document.querySelector('.overlay')
	const dropdownMenu = document.getElementById('dropdown-autocomplete-mobile')

	sidebar.classList.toggle('open')
	overlay.classList.toggle('hidden')

	if (sidebar.classList.contains('open')) {
		hamburgerIcon.classList.add('hidden')
		closeIcon.classList.remove('hidden')

		if (!dropdownMenu.classList.contains('hidden')) {
			dropdownMenu.classList.add('hidden')
			document.getElementById('svg-icon').classList.remove('hidden')
		}

		document.body.style.overflow = 'hidden'
	} else {
		hamburgerIcon.classList.remove('hidden')
		closeIcon.classList.add('hidden')

		document.body.style.overflow = ''
	}
}

const overlayElm = document.querySelector('.overlay')
if (overlayElm) {
	overlayElm.addEventListener('click', function () {
		const sidebar = document.getElementById('sidebar')
		const overlay = document.querySelector('.overlay')

		sidebar.classList.remove('open')
		overlay.classList.add('hidden')
		document.getElementById('hamburgerIcon').classList.remove('hidden')
		document.getElementById('closeIcon').classList.add('hidden')
	})
}

// Mobile menu button toggle
const mobileMenu = document.getElementById('mobileMenu')
if (mobileMenu) {
	mobileMenu.addEventListener('click', toggleSidebar)
}

document.addEventListener(
	'click',
	function (event) {
		const sidebar = document.getElementById('sidebar')
		const dropdownMenu = document.getElementById(
			'dropdown-autocomplete-mobile'
		)
		const userDropdownElement = document.querySelector('.js-user-dropdown')
		const showUserDropdownElement = document.querySelectorAll(
			'.js-show-user-dropdown'
		)

		let isClickInsideDropdownToggle = false

		showUserDropdownElement.forEach(function (button) {
			if (button.contains(event.target)) {
				isClickInsideDropdownToggle = true
			}
		})

		if (
			!sidebar.contains(event.target) &&
			!mobileMenu.contains(event.target)
		) {
			sidebar.classList.remove('open')
			document.querySelector('.overlay').classList.add('hidden')
			document.getElementById('hamburgerIcon').classList.remove('hidden')
			document.getElementById('closeIcon').classList.add('hidden')
		}

		if (
			!dropdownMenu.contains(event.target) &&
			!svgElm.contains(event.target)
		) {
			dropdownMenu.classList.add('hidden')
			document.getElementById('svg-icon').classList.remove('hidden')
		}

		if (
			userDropdownElement &&
			!userDropdownElement.contains(event.target) &&
			!isClickInsideDropdownToggle
		) {
			userDropdownElement.classList.add('hidden')
			userDropdownElementShow = false

			if (userDropdownElementDown) {
				document
					.querySelector('.js-dropdown-down')
					.classList.remove('hidden')
			}

			if (userDropdownElementUp) {
				document
					.querySelector('.js-dropdown-up')
					.classList.add('hidden')
			}
		}
	},
	true
)

const sections = [
	{ header: 'stores-header', content: 'stores', arrow: 'stores-arrow' },
	{
		header: 'about-tipli-header',
		content: 'about-tipli',
		arrow: 'about-tipli-arrow',
	},
	{
		header: 'tipli-countries-header',
		content: 'tipli-countries',
		arrow: 'tipli-countries-arrow',
	},
	{
		header: 'more-about-tipli-header',
		content: 'more-about-tipli',
		arrow: 'more-about-tipli-arrow',
	},
	{ header: 'help-header', content: 'help', arrow: 'help-arrow' },
]

sections.forEach((section) => {
	const header = document.getElementById(section.header)
	const content = document.getElementById(section.content)
	const arrow = document.getElementById(section.arrow)

	if (header) {
		header.addEventListener('click', () => {
			content.classList.toggle('hidden')
			arrow.classList.toggle('rotate-180')
		})
	}
})

// Toggle detail coupon
function toggleDetails(id) {
	var element = document.getElementById(id)
	var ellipseBottom = document.getElementById(
		'ellipse-bottom-' + id.split('-')[2]
	)

	if (element.classList.contains('hidden')) {
		element.classList.remove('hidden')
		ellipseBottom.classList.add('hidden')
	} else {
		element.classList.add('hidden')
		ellipseBottom.classList.remove('hidden')
	}
}

// Copy coupon code
var copyButtons = document.querySelectorAll('.js-copy-code-button')

if (copyButtons.length > 0) {
	// Přidání event listeneru pro každé tlačítko
	copyButtons.forEach(function (button) {
		button.addEventListener('click', function () {
			// Získání hodnoty data atributů
			var couponCode = button.getAttribute('data-coupon-code')
			var couponCopiedText = button.getAttribute('data-coupon-copied')

			// Vytvoření dočasného textarea elementu pro zkopírování textu
			var tempTextarea = document.createElement('textarea')
			tempTextarea.value = couponCode
			document.body.appendChild(tempTextarea)

			// Zkopírování obsahu do schránky
			tempTextarea.select()
			document.execCommand('copy')

			// Odstranění dočasného textarea elementu
			document.body.removeChild(tempTextarea)

			// Najděte element s třídou 'js-copy-change-text' uvnitř tlačítka
			var textElement = button.querySelector('.js-copy-change-text')
			if (textElement) {
				// Uložení původního textu
				var originalText = textElement.textContent

				// Změna textu na hodnotu z data-coupon-copied
				textElement.textContent = couponCopiedText

				// Přidání vizuálních tříd
				button.classList.add(
					'border-secondary-green',
					'text-secondary-green',
					'font-medium',
					'bg-pastel-green-light'
				)

				// Změna ikonky
				button.querySelector('.js-copy-icon').classList.add('hidden')
				button
					.querySelector('.js-copy-icon-copied')
					.classList.remove('hidden')

				// Po 1 sekundě vrátíme původní text a odstraníme třídy
				setTimeout(function () {
					textElement.textContent = originalText
					button.classList.remove(
						'border-secondary-green',
						'text-secondary-green',
						'font-medium',
						'bg-pastel-green-light'
					)

					// Změna ikonky
					button
						.querySelector('.js-copy-icon')
						.classList.remove('hidden')
					button
						.querySelector('.js-copy-icon-copied')
						.classList.add('hidden')
				}, 1000)
			}
		})
	})
}

// Image lazy load
var lazyloadImages

if ('IntersectionObserver' in window) {
	lazyloadImages = document.querySelectorAll('.lazy')
	var imageObserver = new IntersectionObserver(function (entries, observer) {
		entries.forEach(function (entry) {
			if (entry.isIntersecting) {
				var image = entry.target
				image.classList.remove('lazy')
				imageObserver.unobserve(image)
			}
		})
	})

	lazyloadImages.forEach(function (image) {
		imageObserver.observe(image)
	})
} else {
	var lazyloadThrottleTimeout
	lazyloadImages = document.querySelectorAll('.lazy')

	function lazyload() {
		if (lazyloadThrottleTimeout) {
			clearTimeout(lazyloadThrottleTimeout)
		}

		lazyloadThrottleTimeout = setTimeout(function () {
			var scrollTop = window.pageYOffset
			lazyloadImages.forEach(function (img) {
				if (img.offsetTop < window.innerHeight + scrollTop) {
					img.src = img.dataset.src
					img.classList.remove('lazy')
				}
			})
			if (lazyloadImages.length == 0) {
				document.removeEventListener('scroll', lazyload)
				window.removeEventListener('resize', lazyload)
				window.removeEventListener('orientationChange', lazyload)
			}
		}, 20)
	}

	document.addEventListener('scroll', lazyload)
	window.addEventListener('resize', lazyload)
	window.addEventListener('orientationChange', lazyload)
}

// avif support
let i = new Image()
i.onload = i.onerror = (_) => {
	document.body.classList.add(i.height > 0 ? 'avif' : 'no-avif')
}
i.src =
	'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A='

// Mobile search
// Mobile search / Autocomplete
const svgElm = document.getElementById('svg-container')
const closeElm = document.getElementById('close-icon')

if (svgElm) {
	svgElm.addEventListener('click', function () {
		var svgIcon = document.getElementById('svg-icon')
		var dropdownMenu = document.getElementById(
			'dropdown-autocomplete-mobile'
		)
		var sidebar = document.getElementById('sidebar')

		if (sidebar.classList.contains('open')) {
			toggleSidebar()
		}

		if (dropdownMenu.classList.contains('hidden')) {
			dropdownMenu.classList.remove('hidden')
			svgIcon.classList.add('hidden')
		}
	})
}

if (closeElm) {
	closeElm.addEventListener('click', function () {
		var svgIcon = document.getElementById('svg-icon')
		var dropdownMenu = document.getElementById(
			'dropdown-autocomplete-mobile'
		)
		dropdownMenu.classList.add('hidden')
		svgIcon.classList.remove('hidden')
	})
}

// Nette init
$(function () {
	$.nette.init()
})

// Recaptcha
function loadScript(url, callback) {
	var script = document.createElement('script')
	script.type = 'text/javascript'
	script.src = url

	// Po načtení skriptu zavoláme zadanou callback funkci
	script.onload = callback

	// Vložíme skript do záhlaví (head) stránky
	document.head.appendChild(script)
}

// Definice funkce recaptchaCallback
function recaptchaCallback() {
	document.querySelectorAll("[name='recaptcha']").forEach(function (element) {
		var id = 'recaptcha' + Math.random().toString(36).substring(7)
		element.setAttribute('id', id)
		grecaptcha.render(id, {
			sitekey: '6LcUF98ZAAAAAP2vYiKpDneqsqX_w_t1SWpKNjnY',
			callback: function () {
				console.log('recaptcha callback')
			},
		})
	})
}

// Funkce pro načtení skriptu
function loadScriptOnUserInteraction() {
	if (
		document.querySelectorAll("[name='recaptcha']").length > 0 &&
		!window.recaptchaLoaded
	) {
		var scriptUrl =
			'https://www.google.com/recaptcha/api.js?onload=recaptchaCallback&render=explicit'

		loadScript(scriptUrl, function () {
			console.log('Skript načten.')
			window.recaptchaLoaded = true // Označení, že recaptcha byla načtena
		})

		console.log('recaptcha in DOM')

		// Odebrání posluchačů po načtení skriptu
		removeEventListeners()
	} else {
		console.log('NO recaptcha')
	}
}

// Funkce pro odstranění posluchačů událostí
function removeEventListeners() {
	document.removeEventListener('click', loadScriptOnUserInteraction)
	document.removeEventListener('scroll', loadScriptOnUserInteraction)
}

// Přidání posluchačů událostí pro kliknutí a posouvání stránky
document.addEventListener('click', loadScriptOnUserInteraction, { once: true })
document.addEventListener('scroll', loadScriptOnUserInteraction, { once: true })

// Tipli timer desktop and mobile
document.addEventListener('DOMContentLoaded', function () {
	// Desktop
	const timeElement = document.querySelector('.js-menu-time')

	if (timeElement) {
		let timeParts = timeElement.textContent.trim().split(':')
		let totalSeconds =
			parseInt(timeParts[0]) * 3600 +
			parseInt(timeParts[1]) * 60 +
			parseInt(timeParts[2])

		function updateTime() {
			if (totalSeconds > 0) {
				totalSeconds--

				let hours = Math.floor(totalSeconds / 3600)
				let minutes = Math.floor((totalSeconds % 3600) / 60)
				let seconds = totalSeconds % 60

				timeElement.textContent = `${String(hours).padStart(
					2,
					'0'
				)}:${String(minutes).padStart(2, '0')}:${String(
					seconds
				).padStart(2, '0')}`
			} else {
				clearInterval(countdown)
			}
		}

		const countdown = setInterval(updateTime, 1000)
	}

	// Mobile
	const timeElementMob = document.querySelector('.js-mobile-time')

	if (timeElementMob) {
		let timeParts = timeElementMob.textContent.trim().split(':')
		let totalSeconds =
			parseInt(timeParts[0]) * 3600 +
			parseInt(timeParts[1]) * 60 +
			parseInt(timeParts[2])

		function updateTime() {
			if (totalSeconds > 0) {
				totalSeconds--

				let hours = Math.floor(totalSeconds / 3600)
				let minutes = Math.floor((totalSeconds % 3600) / 60)
				let seconds = totalSeconds % 60

				timeElementMob.textContent = `${String(hours).padStart(
					2,
					'0'
				)}:${String(minutes).padStart(2, '0')}:${String(
					seconds
				).padStart(2, '0')}`
			} else {
				clearInterval(countdownMob)
			}
		}

		const countdownMob = setInterval(updateTime, 1000)
	}

	// Support email
	document.querySelectorAll('.support-email').forEach((el) => {
		const email = el.getAttribute('data-email')
		if (email) {
			const button = document.createElement('button')
			button.type = 'button'
			button.textContent = email
			button.setAttribute('data-google-interstitial', 'false')
			button.className = 'underline xl:hover:no-underline'
			button.onclick = () => {
				window.location.href = `mailto:${email}`
			}
			el.innerHTML = '' // smažeme obsah
			el.appendChild(button) // přidáme nový button
		}
	})
})
