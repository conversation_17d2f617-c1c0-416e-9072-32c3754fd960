function convertDate(dateString){
    var p = dateString.split(/\D/g)
    return [p[2],p[1],p[0] ].join(".")
}

var today = new Date();
var dd = today.getDate();
var mm = today.getMonth()+1; //January is 0!
var yyyy = today.getFullYear();

if (dd < 10) {
    dd = '0' + dd;
}

if (mm < 10) {
    mm = '0' + mm;
}

$( document ).ready(function() {
	var isAdmin = $("body").data("isadmin");
    var client = algoliasearch("D6VZ0E6F5F", "********************************");
    var input = $('input[name="searchQuery"]');
    var locale = input.data("locale");
	//var category = client.initIndex(locale + '_tags');
	//var sale = client.initIndex(locale + '_sales');
    var shop = client.initIndex(locale + '_shops');
	var deal = client.initIndex(locale + '_deals');
	var fallbacks = client.initIndex(locale + '_fallbacks');
    var elm = "#frm-searchControl-form-searchQuery";
    var iteration = 0;
    var items = [];
    var searchBtn = input.data("btnlabel");
	var saleLabel = input.data("salelabel");

	autocomplete(elm, { autoselect : false , debug : true , templates: {
		dropdownMenu:
		'<div class="container aa-container--deal">' +
		'<span class="search-close"></span>' +
		'<div class="column"><div class="aa-dataset-fallbacks"></div><div class="aa-dataset-shop"></div></div>' +
		'<div class="column"><div class="aa-dataset-deal"></div></div>' +
		'<div class="aa-dataset-category"></div>' +
		'<div class="aa-dataset-banner">' +
		'<div class="hot-offer__clone"> ' +
		'</div>' +
		'</div>' +
		'</div>'
		}},
		[{
			source: autocomplete.sources.hits(fallbacks, { hitsPerPage: 3 }),
			displayKey: 'name',
			name: 'fallbacks',
			templates: {
				header: '',
				suggestion: function(content) {

					res = '';
					res += '<a href="' + $("body").data("basepath") + '/' + content.destination_url + '" class="query query-categories" data-hit="event" data-category="search" data-action="click" data-label="fallback">';
					res += '<i class="fa fa-info" aria-hidden="true"></i><i class="fa fa-arrow-circle-right" aria-hidden="true"></i>'
					res += '<span class="query-right">';
					res += '<span class="title-name">';
					res += content.name;
					res += '</span>';
					res += '<p>' + content.description + '</p>';
					res += '</span>'
					res += '</a>';

					return res;
				}
			}
		},
		{
			source: autocomplete.sources.hits(shop, { hitsPerPage: 5 }),
			displayKey: 'name',
			name: 'shop',
			templates: {
				header: '',
				suggestion: function(content) {
					var url = $("body").data("basepath") + "/api/v1/reward/" + content.slug;

					var cashback = false;

					if (content.cashback_allowed == undefined) {
						cashback = true // Pokud neni nastavena je true
					} else {
						cashback = content.cashback_allowed;
					}

					if (items[content.slug] == undefined) {
						// Not in array
						items.push(content.slug);
					}

					res = '';
					res += '<div class="wrapper">';
					res += '<a href="' + $("body").data("basepath") + '/' +  content.destination_url + '" class="query" data-hit="event" data-category="search" data-action="click" data-label="shop">';
					res += '<span class="query-left"><img src="' + content.logo + '"/></span>';
					res += '<span class="query-right">';
					res += '<span class="title-name">';
					res += content._highlightResult.name.value;
					res += '<span id="'+ content.slug + '">'

					if (cashback == true) {
						// In slug array
						if (items[content.slug] != undefined) {
						res += items[content.slug];
						} else {
						res += '<span class="rectangle-bounce"><div class="rect rect1"></div><div class="rect rect2"></div><div class="rect rect3"></div><div class="rect rect4"></div><div class="rect rect5"></div></span>';
						}
					}

					res += '</span>';
					res += '</span>';
					res += '</span>'
					res += '</a>';

					if (cashback == true) {
						res += '<a href="' + $("body").data("basepath") + '/' +  content.destination_url +'?shortcut=1" class="btn search-btn" target="_blank"><span class="text"> ' + searchBtn + '</span></a>';
					}

					res += '</div>';

					return res;
				}
			}
		},
		{
			source: autocomplete.sources.hits(deal, { hitsPerPage: 5 }),
			displayKey: 'name',
			name: 'deal',
			templates: {
				header: '',
				suggestion: function(content) {
					res = '';

					res += '<div class="wrapper">';

					if(content.detail_url_shop) {
						res += '<a href="' + $("body").data("basepath") + '/' + content.detail_url_shop + '" class="query" data-hit="event" data-category="search" data-action="click" data-label="deal">';
					} else {
						res += '<a href="' + $("body").data("basepath") + '/' + content.detail_url_homepage + '" class="query" data-hit="event" data-category="search" data-action="click" data-label="deal">';
					}

					res += '<span class="query-left"><img src="' + content.shop_logo_path + '"/></span>';
					res += '<span class="query-right">';
					res += '<span class="title-name d-flex align-items-center">';
					res += '<span class="pr-5">';
					res += '<span class="tag mr-2">'+ content.typeLabel +'</span>';
					res += content.shop_name + ': ';
					res += content._highlightResult.name.value;
					res += '</span>';
					res += '<span class="reward flex-shrink-0"><span class="_upTo"></span> <span class="_value">' + (content.label ? content.label : '') + '</span></span>';
					res += '</span>';
					res += '</span>'
					res += '</a>';
					res += '</div>';

					return res;

				}
			}
		}
	]).on('autocomplete:selected', function(event, suggestion, dataset) {
		event.preventDefault();
	}).on('autocomplete:shown', function() {
		cashbackValue();
		//console.log("show");
	});

    document.getElementsByClassName("aa-dropdown-menu")[0].style.top = ""

    var firstDown = true;
    var urlKey = "";
    var urlLink = "";

    $(document).on("click", '#frm-searchControl-form input', function (event) {
        initStartSearch();
    });

    $(document).on("keydown", '#frm-searchControl-form input', function (event) {
        var aa_suggestion = $(".aa-suggestion");
        var aa_suggestion_category = $(".aa-dataset-category .aa-suggestion");
        var aa_suggestion_shop = $(".aa-dataset-shop .aa-suggestion");
        var aa_suggestion_article = $(".aa-dataset-article .aa-suggestion");
        var aa_suggestion_cursor = $(".aa-suggestion.aa-cursor");
        var search_input = $('input[name="searchQuery"]');
        var new_search_title = $(".aa-suggestion.aa-cursor .query-right .title-name").text();
        var keyCode = event.keyCode || event.which;

        var height = $(window).height() - $("#primary-navbar").height();
        var minH = $('.aa-dataset-banner').height();

        $(".hot-offer__clone").html("");
		$(".start-search .hot-offer__wrapper").clone().appendTo(".hot-offer__clone");

        if ($(".algolia-autocomplete .aa-dropdown-menu:visible").length > 0) {
		  //console.log("visible");
			if (keyCode == 40) { //down pressed

                if (firstDown == true) {
                    aa_suggestion.removeClass("aa-cursor");
                    aa_suggestion.removeAttr("aria-selected");
                    aa_suggestion_shop.eq(0).addClass("aa-cursor");
                    aa_suggestion_shop.eq(0).attr("aria-selected", "true");
                    new_search_title = $(".aa-suggestion.aa-cursor .query-right .title-name").text();
                    search_input.val(new_search_title);
                    firstDown = false;
                }
            } else if (keyCode == 8) { //backspace pressed
                firstDown = true;
                iteration = 0;
            }
            search_input.attr("aria-activedescendant", $(".aa-suggestion.aa-cursor").attr("id"));
        }

        if (height > minH) {
          $('.aa-dropdown-menu').css({"height":height});
        }
    });

  $(document).on("click", '.search-close', function (event) {
    $('input[name="searchQuery"]').val("");
    $('input[name="searchQuery"]').blur();
    $('.aa-dropdown-menu').hide();
    $(".navbar__left").removeClass("mobile");
  });

  $(document).on("click", '.search-btn', function (event) {
    $('input[name="searchQuery"]').val("");
    $('input[name="searchQuery"]').blur();

    setTimeout(function() {
        $('input[name="searchQuery"]').val("");
    }, 100);
  });

  $(document).on("click", '.navbar', function (event) {
      if (!(event.target.id == "frm-searchControl-form-searchQuery" || event.target.className == "navbar__submit")) {
          $('input[name="searchQuery"]').val("");
          $('input[name="searchQuery"]').blur();
          $('.aa-dropdown-menu').hide();
        }
  });

  $(document).on("keydown", function (event) {
	var keyCode = event.keyCode || event.which;

    if (keyCode == 27) { //esc press
      $('input[name="searchQuery"]').val("");
      $('input[name="searchQuery"]').blur();
      $('.aa-dropdown-menu').hide();
    }
  });

  var current = 0;
  var basePath = $("body").data("basepath");
  var itemsValue = [];

  function cashbackValue() {
	//check to make sure there are more requests to make

	if (current < items.length) {
		var url = basePath + "/api/v1/reward/" + items[current];
		var index = current;
		//console.log(url);

		//make the AJAX request with the given info from the array of objects
		$.ajax({
			url      : url,
			success  : function (data) {
				//once a successful response has been received,
				//no HTTP error or timeout reached,
				//run the callback for this request

				if (index < items.length) {
					itemsValue[index] = data.reward;
					$("#" + items[index]).html("<span class=\"reward\">" + itemsValue[index] + "</span>");

					//console.log(items[index][0]);
					//console.log(items[index][1]);
					//console.log("---");
				}
			},
			complete : function () {

				//increment the `current` counter
				//and recursively call our do_ajax() function again.
				current++;
				cashbackValue();
			}
		});
	} else {
		//console.log("END");

		for (i = 0; i < items.length; i++) {
			if (itemsValue[i] != undefined) {
				$("#" + items[i]).html("<span class=\"reward\">" + itemsValue[i] + "</span>");
			}
		}
	}

	//console.log("---");
	//console.log(items);
	//console.log(itemsValue);
	//console.log("---");
  }

});
