.hub-header {
  position: relative;
  display: block;
  overflow: hidden;
  height: 430px;
}

.hub-header img {
  position: relative;
  display: block;
  width: auto;
  min-width: 100%;
  min-height: 430px;
  left: 50%;
  margin-left: -960px;
}

@media only screen and (min-width: 1920px) {
  .hub-header img {
    left: auto;
    margin: auto;
  }
}

.hub-header__content {
  position: absolute;
  display: block;
  width: 80%;
  margin: auto;
  left: 10%;
  text-align: center;
  top: 50%;
  transform: translate(0, -50%);
}

.hub-header__title {
  position: relative;
  display: block;
  font-weight: 700;
  margin-top: 0;
  font-size: 28px;
}

@media (min-width: 768px) {
  .hub-header__title {
    font-size: 4.2rem;
  }
}

.hub-header__text {
  position: relative;
  display: block;
  font-weight: 300;
  font-size: 18px;
  margin-top: 10px;
}

.hub__title {
  position: relative;
  display: block;
  font-weight: 700;
  font-size: 30px;
  text-align: center;
  margin-bottom: 0;
  margin-top: 45px;
}

.hub__text {
  position: relative;
  display: block;
  font-weight: 300;
  font-size: 15px;
  text-align: center;
  margin: auto;
  margin-top: 10px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .hub__text {
    max-width: 75%;
  }
}

.hub-content {
  position: relative;
  display: block;
  background-color: #f2f1f1;
}

.hub-content--hot-offer {
  padding-top: 40px;
  padding-bottom: 40px;
}

.hub-grid {
  position: relative;
  display: block;
  margin: 40px 0;
}

.grid-item {
  position: relative;
  display: block;
  margin-bottom: 20px;
  z-index: 888;
  background-color: #fff;
  border: 1px solid #fff;
}

.grid-item:hover {
  border: 1px solid #66b940;
}

.grid-item--2 {
  width: 96%;
}

@media (min-width: 768px) {
  .grid-item--2 {
    width: calc((95% / 5) * 2);
  }
}

.grid-item--2-5 {
  width: 96%;
}

@media (min-width: 768px) {
  .grid-item--2-5 {
    width: calc((96% / 5) * 2.5);
  }
}

.grid-item--5-5 {
  width: 96%;
}

@media (min-width: 768px) {
  .grid-item--5-5 {
    width: calc((98% / 5) * 5);
  }
}

.grid-item__image {
  position: relative;
  display: block;
  height: 100px;
  margin: auto;
}

@media (min-width: 1024px) {
  .grid-item__image {
    height: 159px;
  }
}

.grid-item img {
  position: relative;
  display: block;
  max-width: 100%;
  max-height: 100%;
  margin: auto;
}

.grid-item__image--black-white {
  filter: url("data:image/svg+xml;utf8,&lt;svg xmlns='http://www.w3.org/2000/svg'&gt;&lt;filter id='grayscale'&gt;&lt;feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/&gt;&lt;/filter&gt;&lt;/svg&gt;#grayscale");
  /* Firefox 10+, Firefox on Android */
  filter: gray;
  /* IE6-9 */
  -webkit-filter: grayscale(100%);
  /* Chrome 19+, Safari 6+, Safari 6+ iOS */
}

.grid-item img.unveil {
  max-width: 100px;
}

.hub-item__logo {
  position: absolute;
  display: block;
  max-width: 100px;
  padding: 10px;
  background-color: #fff;
  top: 0;
  left: 0;
}

.hub-item__logo img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.hub-item__content {
  position: relative;
  display: block;
  width: 100%;
  height: auto;
  padding: 15px 15px;
  background-color: #fff;
}

.hub-item__content:before, .hub-item__content:after {
  content: ' ';
  display: table;
}

.hub-item__content:after {
  clear: both;
}

.hub-item__title {
  position: relative;
  display: block;
  font-weight: 700;
  color: #66b940;
  font-size: 12px;
  line-height: 1.25;
  margin-bottom: 0px;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

@media (min-width: 1024px) {
  .hub-item__title {
    font-size: 15px;
  }
}

.hub-item__text {
  position: relative;
  display: block;
  font-weight: 400;
  color: #000;
  font-size: 12px;
  margin-bottom: 14px;
  line-height: 1.25;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

@media (min-width: 1024px) {
  .hub-item__text {
    font-size: 15px;
  }
}

.hub-item__button {
  position: relative;
  display: inline-block;
  font-weight: 700;
  color: #000;
  background-color: #fff;
  font-size: 12px;
  line-height: 30px;
  text-transform: uppercase;
  text-align: center;
  padding: 0 20px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.grid-item--shop {
  background-color: transparent;
  border: 1px solid transparent;
}

.grid-item--shop .shop-card {
  width: 100%;
  margin: 0;
}

.grid-item--shop .shop-card__image {
  height: 80px;
}

.grid-item--shop .shop-card__image img {
  max-width: 100%;
  width: auto;
  max-height: 50%;
}

.hub-v2 {
  margin-bottom: 100px;
  background-color: #fff;
}

.hub-v2 .container {
  padding-left: 0;
  padding-right: 0;
}

.hub-v2 .hub-item__content {
  padding: 0;
  padding-top: 6px;
}

.hub-v2 .grid-item {
  margin: 0;
  margin-bottom: 20px;
  padding-left: 8px;
  padding-right: 8px;
  border: 0;
}

.hub-v2 .grid-item:hover {
  border: 0;
}

.hub-v2 .grid-item:hover .grid-item__image:before {
  opacity: 1;
}

.hub-v2 .grid-item:hover img {
  transform: scale(1.12);
}

.hub-v2 .grid-item--5-5 {
  flex: 0 0 100%;
}

.hub-v2 img,
.hub-v2 .grid-item img {
  position: relative;
  display: block;
  width: 100%;
  max-width: none;
}

.hub-v2 .grid-item__image {
  height: auto;
  border-radius: 4px;
  overflow: hidden;
}

.hub-v2 .grid-item__image:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 888;
  backface-visibility: hidden;
}

.hub-v2 .grid-item__image img {
  transition: opacity 0.35s, transform 0.35s;
  transform: scale(1);
  backface-visibility: hidden;
}

.hub-v2 .grid-item:hover .hub-item__text {
  text-decoration: underline;
}

.hub-v2 .hub-item__text {
  margin-bottom: 0;
  font-size: 16px;
  width: 90%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.hub-v2 .hub-item__title {
  position: absolute;
  bottom: 35px;
  z-index: 9;
  background: rgba(0, 0, 0, 0.5);
  display: inline-block;
  width: auto;
  color: #fff;
  padding: 10px 20px;
}

@media (min-width: 768px) {
  .hub-v2 .row {
    margin-left: -8px;
    margin-right: -8px;
  }
}

.hub-v2 .col-xs-1, .hub-v2 .col-sm-1, .hub-v2 .col-md-1, .hub-v2 .col-lg-1, .hub-v2 .col-xs-2, .hub-v2 .col-sm-2, .hub-v2 .col-md-2, .hub-v2 .col-lg-2, .hub-v2 .col-xs-3, .hub-v2 .col-sm-3, .hub-v2 .col-md-3, .hub-v2 .col-lg-3, .hub-v2 .col-xs-4, .hub-v2 .col-sm-4, .hub-v2 .col-md-4, .hub-v2 .col-lg-4, .hub-v2 .col-xs-5, .hub-v2 .col-sm-5, .hub-v2 .col-md-5, .hub-v2 .col-lg-5, .hub-v2 .col-xs-6, .hub-v2 .col-sm-6, .hub-v2 .col-md-6, .hub-v2 .col-lg-6, .hub-v2 .col-xs-7, .hub-v2 .col-sm-7, .hub-v2 .col-md-7, .hub-v2 .col-lg-7, .hub-v2 .col-xs-8, .hub-v2 .col-sm-8, .hub-v2 .col-md-8, .hub-v2 .col-lg-8, .hub-v2 .col-xs-9, .hub-v2 .col-sm-9, .hub-v2 .col-md-9, .hub-v2 .col-lg-9, .hub-v2 .col-xs-10, .hub-v2 .col-sm-10, .hub-v2 .col-md-10, .hub-v2 .col-lg-10, .hub-v2 .col-xs-11, .hub-v2 .col-sm-11, .hub-v2 .col-md-11, .hub-v2 .col-lg-11, .hub-v2 .col-xs-12, .hub-v2 .col-sm-12, .hub-v2 .col-md-12, .hub-v2 .col-lg-12 {
  padding-left: 8px;
  padding-right: 8px;
}

.hub-v2 .col-md-eighth {
  position: relative;
  display: block;
  float: left;
  padding-left: 8px;
  padding-right: 8px;
}

@media (min-width: 768px) {
  .hub-v2 .col-md-eighth {
    width: 25%;
  }
}

@media (min-width: 1024px) {
  .hub-v2 .col-md-eighth {
    width: 12.5%;
  }
}

.hub-v2 .col-md-fifth {
  position: relative;
  display: block;
  float: left;
  padding-left: 8px;
  padding-right: 8px;
}

@media (min-width: 768px) {
  .hub-v2 .col-md-fifth {
    width: 50%;
  }
}

@media (min-width: 1024px) {
  .hub-v2 .col-md-fifth {
    width: 20%;
  }
}

.hub-v2 .grid-item--shop img {
  width: auto;
  max-width: 90%;
  max-height: 50px;
}
