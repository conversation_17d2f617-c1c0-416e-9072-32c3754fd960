@charset "UTF-8";
#ajax-spinner {
  position: fixed;
  display: none;
  top: 50%;
  left: 50%;
  width: 52px;
  height: 52px;
  margin-left: -26px;
  padding: 10px;
  background-image: url("../../images/spinner-deal.gif");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: #fff;
  z-index: 99999;
  border: 3px solid #e5e5e5;
}

.deal-wrapper {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical {
    margin-right: -1rem;
  }
}

.deal-wrapper--vertical .deal-item {
  box-shadow: none;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item {
    flex: 0 1 calc((100% - 3.099rem) / 3);
    width: calc((100% - 3rem) / 3);
    margin-right: 1rem;
    flex-direction: column;
  }
}

@media (min-width: 1180px) {
  .deal-wrapper--vertical .deal-item {
    flex: 0 1 calc((100% - 4.099rem) / 4);
    width: calc((100% - 4rem) / 4);
  }
}

.deal-wrapper--vertical .deal-item__aside {
  width: 100%;
  border-right: 0;
  padding-bottom: 26px;
}

@media (min-width: 375px) {
  .deal-wrapper--vertical .deal-item__aside {
    flex: 0 0 138px;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__aside {
    flex: 0 0 165px;
    height: 165px;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__top-logo {
    display: flex;
  }
}

.deal-wrapper--vertical .deal-item__image-wrapper {
  overflow: hidden;
}

@media (min-width: 375px) {
  .deal-wrapper--vertical .deal-item__image {
    max-width: 100%;
    height: auto;
    max-height: none;
  }
}

.deal-wrapper--vertical .deal-item__content {
  padding: 1rem;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__content {
    padding: 0.75rem;
    background-color: #fafafa;
    border-top: 1px solid #e5e5e5;
    overflow: initial;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__text-content {
    padding-left: 0rem;
  }
}

@media (min-width: 1024px) {
  .deal-wrapper--vertical .deal-item__show-more--short {
    text-align: justify;
  }
}

.deal-wrapper--vertical .deal-item__title {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__title-link {
    height: 35px;
    font-size: 14px;
    white-space: normal;
  }
}

.deal-wrapper--vertical .deal-item__title-link.fw-regular {
  font-weight: 400;
}

.deal-wrapper--vertical .deal-item__title {
  flex-direction: column;
  align-items: flex-start;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__tag {
    top: -1px;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__text {
    height: 35px;
    font-size: 12px;
  }
}

.text--overflow-white {
  overflow: hidden;
}

.deal-wrapper--vertical .deal-item__title-link:after,
.deal-wrapper--vertical .deal-item__text:after,
.text--overflow-white:after {
  content: " ";
  position: absolute;
  top: 15px;
  right: 0;
  width: 3em;
  height: 20px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), #fafafa 50%, #fafafa);
}

.deal-wrapper--vertical .deal-item__row--bottom {
  flex-wrap: wrap;
  flex-direction: column;
}

.deal-wrapper--vertical .deal-item__reward {
  flex: 0 0 auto;
  font-size: 12px;
}

.deal-wrapper--vertical .deal-item__reward--small {
  display: inline-block;
}

.deal-wrapper--vertical .deal-item__reward--medium {
  font-size: 16px;
}

.deal-wrapper--vertical .deal-item__button {
  margin-top: 0.5rem;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__button {
    width: 100%;
  }
}

.deal-wrapper--vertical .deal-item__button-text:before {
  display: none;
}

.deal-wrapper--vertical .deal-item__button-code:before {
  position: absolute;
  display: block;
  content: "";
  background: #de5c13;
  width: 22px;
  height: 5px;
  left: -12px;
  bottom: 0;
  transform: skew(0deg, 0);
  z-index: 9;
}

.deal-wrapper--vertical
.deal-item__button:hover
.deal-item__button-text:before {
  right: -18px;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__code {
    width: 100%;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item__code {
    margin-top: 0.5rem;
  }
}

@media (min-width: 768px) {
  .deal-wrapper--5 .deal-item {
    flex: 0 1 calc((100% - 3.099rem) / 3);
    width: calc((100% - 3rem) / 3);
    margin-right: 1rem;
    margin-bottom: 1rem;
  }
}

@media (min-width: 1180px) {
  .deal-wrapper--5 .deal-item {
    flex: 0 1 calc((100% - 5.099rem) / 5);
    width: calc((100% - 5rem) / 5);
    flex-direction: column;
  }
}

.deal-wrapper--smaller-logo .deal-item__top-logo {
  align-self: flex-end;
}

@media (min-width: 768px) {
  .deal-wrapper--smaller-logo .deal-item__top-logo {
    width: 80px;
  }
}

.deal-wrapper--smaller-logo .deal-item__top-logo.mb-2 {
  margin-bottom: 0.5rem;
}

.deal-wrapper--smaller-logo .deal-item__top-logo .deal-item__logo-image {
  max-width: 64px;
}

.deal-wrapper--smaller-logo .deal-item__image {
  max-width: 170px;
  max-height: 200px;
}

@media (min-width: 768px) {
  .deal-wrapper--smaller-logo .deal-item__image {
    max-width: 200px;
  }
}

.deal-wrapper--button-on-hover .deal-item__row--bottom {
  display: none;
}

@media (min-width: 1180px) {
  .deal-wrapper--button-on-hover .deal-item:hover .deal-item__row--bottom {
    display: block;
    position: absolute;
    bottom: -55px;
    width: 100%;
    z-index: 9;
    background: #fff;
    left: 0;
    padding: 1rem;
    box-shadow: 0 10px 6px 1px rgba(0, 0, 0, 0.14);
  }
}

.deal-wrapper--product {
  margin-right: 0;
}

.deal-item {
  position: relative;
  display: flex;
  align-items: stretch;
  flex-direction: column;
  background-color: #fff;
  margin-bottom: 0px;
  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.1);
}

@media (min-width: 375px) {
  .deal-item {
    flex: 0 0 calc(50% - 0.099rem);
    min-width: 0;
    /* Flexbox - white space no wrap bugfix */
  }
}

@media (min-width: 768px) {
  .deal-item {
    width: 100%;
    flex: 0 0 100%;
    margin-bottom: 1rem;
    flex-direction: row;
  }
}

.deal-item__aside {
  position: relative;
  display: flex;
  height: 165px;
  align-items: flex-start;
  justify-content: center;
  flex: 0 0 165px;
  background-color: #fff;
}

@media (min-width: 375px) {
  .deal-item__aside {
    height: 138px;
  }
}

@media (min-width: 768px) {
  .deal-item__aside {
    width: 165px;
    border-right: 1px solid #e5e5e5;
    align-items: center;
  }
}

.deal-item__aside--green {
  background-color: #3cab92;
}

.deal-item__image-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  max-height: 165px;
  align-items: center;
  justify-content: center;
}

.deal-item__image {
  position: relative;
  display: block;
  max-width: 100%;
  max-height: 165px;
  height: auto;
}

.deal-item__deal-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.deal-item__deal-text {
  font-weight: 700;
  color: #fff;
  text-align: center;
  margin-bottom: 0;
  padding: 1rem 1rem;
}

.deal-item__deal-text--small {
  font-size: 18px;
}

.deal-item__deal-text--medium {
  font-size: 24px;
}

.deal-item__deal-text--large {
  font-size: 30px;
}

.deal-item__deal-text--big {
  font-size: 30px;
}

@media (min-width: 768px) {
  .deal-item__deal-text--big {
    font-size: 26px;
  }
}

@media (min-width: 1180px) {
  .deal-item__deal-text--big {
    font-size: 32px;
  }
}

.deal-item__medium-icon {
  display: block;
  width: 100%;
  height: 24px;
  margin-bottom: 0.5rem;
}

.deal-item__logo {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65px;
  height: 32px;
  background-color: #fff;
  margin-bottom: 0.5em;
}

@media (min-width: 768px) {
  .deal-item__logo {
    margin-bottom: 0;
  }
}

.deal-item__logo--center {
  position: relative;
  bottom: 0;
  left: 0;
}

.deal-item__logo--medium {
  width: 100px;
}

.deal-item__logo-image {
  position: relative;
  display: block;
  max-width: 80px;
  max-height: 35px;
  height: auto;
}

.deal-item__content {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  padding: 6px 6px;
  background-color: #fafafa;
  border-top: 1px solid #e5e5e5;
}

@media (min-width: 768px) {
  .deal-item__content {
    background-color: #fff;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
}

@media (min-width: 768px) {
  .deal-item__text-content {
    padding-left: 0.5rem;
  }
}

.deal-item__row {
  position: relative;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

@media (min-width: 768px) {
  .deal-item__row {
    flex-direction: row;
    align-items: center;
  }
}

.deal-item__row--mb {
  margin-bottom: 0.5rem;
}

.deal-item__row--mb-3 {
  margin-bottom: 1rem;
}

.deal-item__row--direction-column {
  flex-direction: column;
}

.deal-item__row--direction-row {
  flex-direction: row;
}

.deal-item__row--bottom {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .deal-item__row--bottom {
    flex-wrap: nowrap;
  }
}

.deal-item__row--vertical:after {
  top: 0;
}

.deal-item__row--vertical .deal-item__cashback {
  flex: 0 0 auto;
  font-size: 12px;
}

.deal-item__row--vertical .deal-item__reward {
  margin-right: 0.5rem;
}

.deal-item__small {
  position: relative;
  display: flex;
  align-items: center;
  font-weight: 400;
  color: #8f8f8f;
  margin-bottom: 0;
  font-size: 10px;
}

@media (min-width: 768px) {
  .deal-item__small {
    font-size: 12px;
    margin-right: 0.5rem;
  }
}

.deal-item__small-icon {
  position: relative;
  top: 0px;
  width: 12px;
  height: 12px;
  color: #8f8f8f;
  fill: #8f8f8f;
  margin-right: 0.25rem;
}

.deal-item__divider {
  display: none;
  font-weight: 400;
  color: #8f8f8f;
  font-size: 13px;
  margin-bottom: 0;
  margin-right: 1em;
}

@media (min-width: 768px) {
  .deal-item__divider {
    display: block;
  }
}

.deal-item__title {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  text-align: left;
  margin-bottom: 0.1em;
}

@media (min-width: 768px) {
  .deal-item__title {
    flex-direction: row;
    align-items: center;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
  }
}

.deal-item__title-link {
  position: relative;
  display: block;
  font-weight: 700;
  color: #000;
  font-size: 12px;
  line-height: 1.25;
  align-items: center;
  overflow: hidden;
}

@media (min-width: 375px) {
  .deal-item__title-link {
    height: 30px;
  }
}

@media (min-width: 768px) {
  .deal-item__title-link {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: auto;
    height: auto;
    max-width: 100%;
    font-size: 18px;
  }
}

a.deal-item__title-link {
  cursor: pointer;
}

a.deal-item__title-link:hover {
  text-decoration: underline;
}

.deal-item__tag {
  position: relative;
  display: inline-block;
  color: #fff;
  font-size: 10px;
  font-weight: 700;
  padding: .25rem .5rem;
  background: #ee7836;
  border-radius: 4px;
}

@media (min-width: 768px) {
  .deal-item__tag {
    top: -2px;
    margin-right: 0.25rem;
  }
}

.deal-item__text {
  position: relative;
  display: block;
  width: 100%;
  height: 30px;
  font-weight: 400;
  font-size: 10px;
  color: #656565;
  line-height: 1.5;
  margin-bottom: 4px;
  overflow: hidden;
}

@media (min-width: 768px) {
  .deal-item__text {
    font-size: 12px;
    height: auto;
  }
}

.deal-item__link {
  font-weight: 400;
  text-decoration: underline;
}

.deal-item__link:hover {
  text-decoration: none;
}

.deal-item__show-more--full {
  display: none;
}

.deal-item__show-more--short {
  display: inline-block;
  max-width: 510px;
}

@media (min-width: 1024px) {
  .deal-item__show-more--short {
    text-align: justify;
  }
}

.deal-item__text-icon {
  width: 10px;
  height: 10px;
  fill: #8f8f8f;
  color: #8f8f8f;
  cursor: pointer;
}

.deal-item__free-shipping-icon {
  position: relative;
  width: 14px;
  height: 11px;
  top: 1px;
}

.deal-item__reward {
  font-weight: 700;
  font-size: 14px;
  color: #999;
}

@media (min-width: 768px) {
  .deal-item__reward {
    margin-right: 0.75rem;
    margin-bottom: 0;
  }
}

.deal-item__new-logo-no-reward .deal-item__new-logo {
  border-bottom: 1px solid #F2F1F1;
  border-radius: 8px 8px;
}

.deal-item__reward--small {
  display: inline-block;
  font-weight: 400;
  color: #999;
  font-size: 12px;
  text-decoration: line-through;
}

@media (min-width: 768px) {
  .deal-item__reward--small {
    display: block;
  }
}

.deal-item__reward--medium {
  font-weight: 700;
  color: #ee7836;
  font-size: 16px;
}

@media (min-width: 768px) {
  .deal-item__reward--medium {
    font-size: 20px;
  }
}

.deal-item__pool {
  position: relative;
  display: flex;
  width: 116px;
  height: 36px;
  align-items: stretch;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  background-color: #fff;
}

@media (min-width: 768px) {
  .deal-item__pool {
    margin-right: 1.5em;
  }
}

.deal-item__pool--active {
  background-color: #f2f1f1;
}

.deal-item__pool-count {
  position: relative;
  display: flex;
  width: 52px;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #a3a3a3;
  font-size: 12px;
  background-color: #fff;
  border-right: 1px solid #e5e5e5;
  border-left: 1px solid #e5e5e5;
}

.deal-item__pool-count--green {
  color: #fff;
  background-color: #26a446;
}

.deal-item__pool-count--red {
  color: #fff;
  background-color: #cd3d3d;
}

.deal-item__pool-button {
  position: relative;
  display: flex;
  width: 38px;
  align-items: center;
  justify-content: center;
}

.deal-item__pool-button:hover,
.deal-item__comment:hover {
  background-color: #eaeaea;
}

.deal-item__pool-button-icon {
  width: 15px;
  height: 14px;
  color: #a3a3a3;
}

.deal-item__user-click {
  font-weight: 400;
  font-size: 12px;
  color: #bdbdbd;
}

.deal-item__icon-user {
  width: 10px;
  height: 10px;
  margin-right: 0.25rem;
  fill: #bdbdbd;
}

.deal-item__comment {
  position: relative;
  display: flex;
  width: 36px;
  height: 36px;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  order: 1;
  margin-left: auto;
  margin-top: 1em;
}

@media (min-width: 768px) {
  .deal-item__comment {
    order: 0;
    margin-top: 0;
    margin-left: 0;
    margin-right: 1.5em;
  }
}

[data-upnum]:after {
  content: attr(data-upnum);
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -10px;
  right: -10px;
  width: 18px;
  height: 18px;
  padding: 3px 3px;
  border-radius: 50%;
  background-color: #cd3d3d;
  font-weight: 700;
  color: #fff;
  font-size: 8px;
  padding-top: 4px;
}

.deal-item__comment-icon {
  width: 19px;
  height: 17px;
  color: #6c6c6c;
}

.deal-item__button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 38px;
  color: #fff;
  font-weight: 700;
  padding: 1rem 0.75rem;
  font-size: 12px;
  border-radius: 5px;
  background-color: #ee7836;
  border: 1px solid #ee7836;
  margin: auto;
  margin-top: 0.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .deal-item__button {
    width: 160px;
    margin: 0;
    margin-top: 0;
    font-size: 14px;
    margin-left: auto;
  }
}

.deal-item__button:hover {
  color: #fff;
  background-color: #de5c13;
}

.deal-item__button-text {
  position: relative;
  display: block;
  width: auto;
  margin-right: 11px;
  text-align: left;
  z-index: 12;
  transition: all 250ms;
}

.deal-item__button-code {
  position: absolute;
  display: inline-block;
  width: auto;
  width: 32px;
  height: 100%;
  top: 0;
  right: 0;
  padding-right: 8px;
  text-align: right;
  color: #3d3d3d;
  font-size: 12px;
  line-height: 39px;
  background-color: #fff;
  border-radius: 0 5px 5px 0;
  transition: all 250ms;
}

.deal-item__button-code:before {
  position: absolute;
  display: block;
  content: "";
  background: #de5c13;
  width: 22px;
  height: 11px;
  z-index: 12;
  right: 20px;
  bottom: 0px;
  transform: translate(34deg, 0);
  transform: skew(34deg, 0);
  transition: all 250ms;
}

.deal-item__button-corner {
  position: absolute;
  display: block;
  width: 46px;
  height: 38px;
  right: -1px;
  top: 7px;
  z-index: 13;
  margin-top: 2px;
  backface-visibility: hidden;
  background-color: #f29865;
  background: linear-gradient(38deg, #f29865 0, #f29865 49%, rgba(202, 235, 137, 0) 50%, rgba(255, 255, 255, 0) 100%);
  background-position: 0 6px;
  background-repeat: no-repeat;
  transition: all 250ms;
  transform: rotateZ(22deg) translate(0, -10px) scaleX(0.9);
}

.deal-item__button:hover .deal-item__button-code:before {
  right: 24px;
}

.deal-item__button:hover .deal-item__button-code {
  width: 39px;
}

.deal-item__button:hover .deal-item__button-corner {
  right: 5px;
}

.deal-item--bg-gradient-1 {
  background-image: linear-gradient(to left top, #ee7836, #e25a67, #b65488);
}

.deal-item--bg-gradient-2 {
  background-image: linear-gradient(to left top, #79f1a4, #0e5cad);
}

.deal-item--bg-gradient-3 {
  background-image: linear-gradient(to left top, #fdec1c, #ff5b00);
}

.deal-item--bg-gradient-4 {
  background-image: linear-gradient(to left top, #ffc576, #fe6a67, #f01717);
}

.deal-item--bg-gradient-5 {
  background-image: linear-gradient(to left top, #0dc6b4, #12ec9d);
}

.deal-item--bg-gradient-orange {
  background-image: linear-gradient(to left top, #de5c13, #ee7836);
  background-image: linear-gradient(to left top, #f2f1f1, #fff);
}

.deal-item--bg-gradient-orange .deal-item__deal-text {
  color: #ee7836;
}

.deal-item--bg-gradient-green {
  background-image: linear-gradient(to left top, #519333, #83c963);
  background-image: linear-gradient(to left top, #f2f1f1, #fff);
}

.deal-item--bg-gradient-green .deal-item__deal-text {
  color: #66b940;
}

.deal-item--bg-gradient-red {
  background-image: linear-gradient(to left top, #cc0e00, #ff4133);
  background-image: linear-gradient(to left top, #f2f1f1, #fff);
}

.deal-item--bg-gradient-red .deal-item__deal-text {
  color: #ff1100;
}

.deal-item--bg-gradient-blue {
  background-image: linear-gradient(to left top, #0286c2, #2ebcfc);
  background-image: linear-gradient(to left top, #f2f1f1, #fff);
}

.deal-item--bg-gradient-blue .deal-item__deal-text {
  color: #03a9f4;
}

.deal-item--bg-gradient-grey {
  background-image: linear-gradient(to left top, #6f6f6f, #a2a2a2);
  background-image: linear-gradient(to left top, #f2f1f1, #fff);
}

.deal-item--bg-gradient-grey .deal-item__deal-text {
  color: #888;
}

.deal-item__top {
  position: absolute;
  display: flex;
  width: 100%;
  top: 0;
  padding: 1rem;
}

.deal-item__code {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 38px;
  border-radius: 5px;
  border: 1px solid #919191;
  border-style: dashed;
  font-weight: 700;
  font-size: 11px;
  margin-left: auto;
  margin-top: 0.5rem;
  padding: 1rem 0.75rem;
  padding-right: 30px;
  cursor: pointer;
}

@media (min-width: 768px) {
  .deal-item__code {
    width: 110px;
    margin-top: 0;
  }
}

.deal-item__code:hover {
  border-color: #ee7836;
}

.deal-item__code span {
  position: relative;
  top: 1px;
}

.deal-item__code--copy {
  border-color: green;
  color: green;
  text-transform: uppercase;
  cursor: initial;
}

.deal-item__code--copy:before {
  position: absolute;
  content: attr(data-copy-complete);
  width: calc(100% - 35px);
  font-size: 10px;
  background-color: #fafafa;
  overflow: hidden;
  z-index: 9;
}

.deal-item__code--copy:hover {
  border-color: green !important;
}

.deal-item__code--copy svg {
  color: green;
}

.deal-item__code-icon {
  position: absolute;
  width: 30px;
  height: 38px;
  top: -1px;
  right: 0;
  padding: 0.75rem;
  color: #ee7836;
  cursor: pointer;
  border: 0;
  background: transparent;
}

.deal-item__code-icon svg {
  width: 100%;
  height: 100%;
}

@media (min-width: 768px) {
  .deal-item__row--code-button .deal-item__button {
    font-size: 12px;
  }
}

.deal-item__cashback {
  display: block;
  font-weight: 700;
  font-size: 10px;
  color: #008005;
  margin-right: 0;
}

@media (min-width: 768px) {
  .deal-item__cashback {
    font-size: 90%;
  }
}

.deal-item__cashback-icon {
  width: 10px;
  height: 10px;
  fill: #656565;
}

.deal-item__edit {
  position: absolute;
  display: flex;
  top: -5px;
  right: -5px;
  padding: 1rem;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  z-index: 9;
}

.deal-item__edit:hover {
  background-color: #ee7836;
}

.deal-item__edit:hover .deal-item__edit-icon {
  color: #fff;
}

.deal-item__edit-icon {
  position: relative;
  display: block;
  width: 15px;
  height: 15px;
  color: #999;
}

.deal-item__pool--vertical {
  margin-left: auto;
}

.deal-item__pool--vertical .deal-item__pool {
  width: auto;
  height: 32px;
  border: 0;
  margin-right: 0;
  background-color: transparent;
}

.deal-item__pool--vertical .deal-item__pool-button {
  width: 28px;
}

.deal-item__pool--vertical .deal-item__pool-button--up {
  order: 1;
}

.deal-item__pool--vertical .deal-item__pool-count {
  width: 28px;
  border: 0;
  background-color: transparent;
}

.deal-item__pool--vertical .deal-item__pool-count--red {
  color: #cd3d3d;
}

.deal-item__pool--vertical .deal-item__pool-count--green {
  color: #26a446;
}

.deal-item__pool--vertical .deal-item__pool--active {
  background-color: transparent;
}

.deal-item__pool--vertical .deal-item__pool--active svg {
  color: #cd3d3d;
}

.deal-item__pool--vertical
.deal-item__pool--active.deal-item__pool-button--up
svg {
  color: #26a446;
}

.deal-item__top-logo {
  position: relative;
  display: flex;
  flex: 0 0 50px;
  width: 100%;
  margin-top: -40px;
  margin-right: 0;
  margin-bottom: 1rem;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}

@media (min-width: 768px) {
  .deal-item__top-logo {
    display: none;
  }
}

.deal-item__top-logo .deal-item__logo-image {
  max-width: 75px;
  height: auto;
  max-height: 35px;
  margin: auto;
}

@media (max-width: 767px) {
  .deal-item--horizontal-xs-hide {
    display: none;
  }
}

@media (min-width: 768px) {
  .deal-item--horizontal-only-xs-show {
    display: none !important;
  }
}

.deal-item--horizontal-no-hover:hover {
  color: #999;
}

.deal-item__image-wrapper--coupon {
  background-image: url("../../images/deal/tag.svg");
  background-size: 80% 80%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__image-wrapper--free-shipping {
  background-image: url("../../images/deal/doprava.svg");
  background-size: 90% 90%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__image-wrapper--percentage {
  background-image: url("../../images/deal/badge.svg");
  background-size: 80% 80%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__image-wrapper--money {
  background-image: url("../../images/deal/penize.svg");
  background-size: 80% 80%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__image-wrapper--tip {
  background-image: url("../../images/deal/tip.svg");
  background-size: 70% 70%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__image-wrapper--gift {
  background-image: url("../../images/deal/gift.svg");
  background-size: 70% 70%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.deal-item__price-down {
  position: relative;
  display: inline-flex;
  align-items: baseline;
  color: #999;
  font-size: 12px;
  font-weight: 700;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .deal-item__price-down {
    font-size: 14px;
  }
}

.deal-item__discount {
  position: relative;
  width: 14px;
  height: 11px;
  top: 1px;
  margin-right: 0.25rem;
}

@media (min-width: 768px) {
  .not-vertical-layout .deal-item__price-down {
    display: none;
  }
}

.deal-item--product {
  border: 0;
  border-right: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}

@media (min-width: 768px) {
  .deal-wrapper--vertical .deal-item--product {
    flex: 0 1 calc(100% / 3);
    width: calc(100% / 3);
    margin-right: 0;
    margin-bottom: 0;
    flex-direction: column;
  }
}

@media (min-width: 1180px) {
  .deal-wrapper--vertical .deal-item--product {
    flex: 0 1 calc(100% / 4);
    width: calc(100% / 4);
  }
}

@media (min-width: 768px) {
  .deal-wrapper--5 .deal-item--product {
    flex: 0 1 calc(100% / 3);
    width: calc(100% / 3);
    margin-right: 0;
    margin-bottom: 0;
  }
}

@media (min-width: 1180px) {
  .deal-wrapper--5 .deal-item--product {
    flex: 0 1 calc(100% / 5);
    width: calc(100% / 5);
    flex-direction: column;
  }
}

@media (min-width: 768px) {
  .deal-item--product .deal-item__row--product {
    margin-top: -20px;
  }
}

.deal-item--product .deal-item__content {
  background-color: #fff;
  border-top: 0;
  padding-bottom: 1rem;
}

.deal-item--product .deal-item__image-wrapper {
  max-height: 220px;
}

@media (min-width: 768px) {
  .deal-item--product .deal-item__image-wrapper {
    max-height: 260px;
  }
}

.deal-item--product .deal-item__aside {
  flex: 0 0 240px;
  height: 240px;
}

@media (min-width: 768px) {
  .deal-item--product .deal-item__aside {
    flex: 0 0 260px;
    height: 260px;
  }
}

.product-tag__wrapper {
  position: absolute;
  display: flex;
  top: 1rem;
  right: 0;
  flex-direction: column;
  align-items: flex-end;
}

.product-tag__item {
  display: inline-flex;
  padding: 0.5rem;
  font-weight: 700;
  background-color: grey;
  color: #fff;
  font-size: 12px;
  margin-bottom: 0.25rem;
}

.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.danger {
  background-color: #f8d7da;
  background-color: #f44336;
  border-color: #f5c6cb;
}

.warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}

.deal-item__new {
  width: 100%;
  background: #FFF;
  border-radius: 15px;
  width: 100%;
  border: 1px solid #D9D9D9;
  margin-bottom: 12px;
}

.deal-item__new-wrapper {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  padding: 12px;
}

.deal-item__new-wrapper-flex {
  display: flex;
  gap: 16px;
  width: 100%;
}

.deal-item__divider-new {
  margin-top: -12px;
  margin-bottom: -12px;
}

.deal-item__new-logo {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  min-width: 185px;
  border-radius: 8px;
  border: 1px solid #D9D9D9;
  height: 100%;
}

.deal-item__new-logo img {
  margin-top: auto;
  margin-bottom: auto;
  max-width: 100px;
  height: auto;
}

.deal-item__new-sale {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 34px;
  color: #5E9B3B;
  border-top: none;
  font-size: 16px;
  border-radius: 0 0 8px 8px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
  background: #DEFDD7;
  padding: 0;
  width: 100%;
}

.deal-item__new-coupon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 34px;
  color: #EC7700;
  border-top: none;
  font-size: 16px;
  border-radius: 0 0 8px 8px;
  font-weight: 700;
  line-height: 24px;
  background: #FEF5F0;
  padding: 0;
  width: 100%;
}

.deal-item__new-description-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
}

.deal-item__new-description {
  font-size: 20px;
  font-weight: 400;
  line-height: 27.5px;
  margin-bottom: 4px;
}

.deal-item__new-description-sale {
  display: inline-block;
  color: #5E9B3B;
  border: 1px solid #5E9B3B;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  margin-bottom: 4px;
}

.deal-item__new-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.deal-item__new-cta .btn-copy-coupon {
  background: #fff;
  color: #182B4A;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 230px;
  padding: 17px 16px;
  border-radius: 8px;
  border: 1px dashed #182B4A;
}

.deal-item__new-cta .btn-copy-coupon:hover {
  border: 1px dashed #ee7836;
}

.deal-item__new-cta .btn-get-coupon:hover {
  color: #fff;
  background-color: #de5c13;
}

.btn-get-coupon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 230px;
  padding: 17px 32px;
  border-radius: 8px;
  background: #ee7836;
  border: 1px solid #ee7836;
  color: #FFF;
  font-size: 16px;
  font-weight: 600;
  margin-top: auto;
}

.btn-get-coupon svg {
  margin-left: 12px;
}

.deal-item_new-conditions-wrapper {
  display: flex;
  justify-content: space-between;
  color: #949494;
  font-size: 12px;
  font-weight: 400;
  line-height: 24px;
}

.deal-item_new-conditions,
.deal-item_new-until {
  display: flex;
  align-items: center;
}

.deal-item_new-conditions {
  color: #949494;
  gap: 5px;
}

.deal-item_new-until {
  gap: 5px;
}

.deal-item_new-conditions-wrapper-mobile {
  display: none;
}

.claim-coupon__container {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  margin-top: auto;
}

.claim-coupon__text {
  display: flex;
  align-items: center;
  width: 180px;
  padding: 17px 32px;
  border-radius: 8px 0 0 8px;
  background: #ee7836;
  border: 1px solid #ee7836;
  color: #FFF;
  font-size: 16px;
  font-weight: 600;
  margin-top: auto;
}

.claim-coupon__container:hover .claim-coupon__text {
  background-color: #de5c13;
}

.claim-coupon__code {
  z-index: 2;
  padding: 13px 16px 13px 0;
  background: #EDEDED;
  border: 1px solid #EDEDED;
  width: 50px;
  color: #182B4A;
  border-radius: 0 8px 8px 0;
  font-size: 23px;
  font-weight: 400;
  direction: rtl;
  overflow: hidden;
}

.claim-coupon__svg1 {
  right: 49.8px;
  position: absolute;
  z-index: 3;
}

.claim-coupon__svg2 {
  position: absolute;
  right: 16px;
  z-index: 1;
}

.deal-modal__wrapper {
  display: none;
  justify-content: center;
  padding: 0 12px;
  align-items: center;
  position: fixed;
  z-index: 50;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(84, 86, 90, 0.77);
}

.deal-modal__content {
  background-color: #FFF;
  border-radius: 15px;
  padding: 64px 108px 24px 108px;
  width: 855px;
  max-width: 100%;
  text-align: center;
  position: relative;
}

.deal-modal__content .close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  position: absolute;
  right: 20px;
  top: 20px;
}

.deal-modal__content .close:hover {
  cursor: pointer;
}

.deal-modal__content .deal-modal__logo-container {
  margin-bottom: 24px;
}

.deal-modal__content .deal-modal__title {
  color: #182B4A;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  margin-bottom: 24px;
}

.deal-modal__content .deal-modal__subtitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #182B4A;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 48px;
}

.deal-modal__content .deal-modal__subtitle:hover {
  cursor: pointer;
}

.deal-modal__content .deal-modal__coupon {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 15px;
  border: 1px solid #EC7700;
  padding: 14px 11px;
  margin-bottom: 24px;
}

.deal-modal__content .deal-modal__coupon .deal-modal__coupon-code {
  color: #EC7700;
  text-align: center;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 48px;
  margin: 0 auto;
}

.deal-modal__content .deal-modal__coupon .deal-modal__copy-btn {
  width: 186px;
  background: #EC7700;
  max-width: 100%;
  padding: 17px 24px;
}

.deal-modal__content .deal-modal__coupon .deal-modal__copy-btn:hover {
  background: #d36a00;
  border: 1px solid #d36a00;
  color: #FFF;
}

.deal-modal__content .deal-modal__conditions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: #AEAEAE;
  font-size: 14px;
  font-weight: 400;
}

.deal-modal__content .deal-modal__conditions:hover {
  cursor: pointer;
}

@media (max-width: 767px) {
  .deal-item__new-wrapper {
    flex-direction: column;
    align-items: normal;
    gap: 0;
    padding: 25px 22px;
  }
  .deal-item__divider-new {
    display: none;
  }
  .deal-item__new-logo {
    min-height: 150px;
  }
  .deal-item__new-cta {
    margin-top: 24px;
  }
  .deal-item__new-cta .btn-copy-coupon {
    width: 100%;
  }
  .btn-get-coupon {
    width: 100%;
  }
  .deal-item__new-description {
    font-size: 18px;
    line-height: 24.8px;
  }
  .deal-item__new-description-sale {
    font-size: 14px;
    font-weight: 400;
  }
  .claim-coupon__container {
    width: 100%;
  }
  .claim-coupon__text {
    width: 100%;
  }
  .claim-coupon__svg1 {
    right: 49.8px;
  }
}

@media (max-width: 500px) {
  .deal-item__new {
    margin-bottom: 15px;
  }
  .deal-item_new-conditions-wrapper {
    display: none;
  }
  .deal-item__new-cta {
    gap: 0;
  }
  .claim-coupon__container {
    margin-top: 12px;
  }
  .btn-get-coupon {
    margin-top: 12px;
  }
  .deal-item_new-conditions-wrapper-mobile {
    display: flex;
    margin-top: 12px;
    justify-content: space-between;
    color: #949494;
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
  }
  .deal-item__new-wrapper-flex {
    flex-direction: column;
    gap: 12px;
  }
  .deal-item__new-description {
    margin-bottom: 12px;
    line-height: 32px;
  }
  .deal-item__mobile-flex {
    display: flex;
  }
  .deal-item__new-logo {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
    width: 100%;
    min-height: 54px;
    border: 1px solid #EBEBEB;
  }
  .deal-item__new-logo img {
    margin-right: auto;
    margin-left: auto;
  }
  .deal-item__new-coupon {
    height: 52px;
    width: 45%;
    border-radius: 0 8px 8px 0;
    text-align: center;
  }
  .deal-item__new-sale {
    height: 52px;
    width: 45%;
    border-radius: 0 8px 8px 0;
    text-align: center;
  }
  .deal-item__new-cta .deal-item__code {
    width: 100%;
    height: 50px;
    font-size: 14px;
  }
  .deal-item__new-cta .deal-item__code-icon {
    height: 50px;
  }
  .deal-modal__content {
    padding: 24px;
  }
}

@media (max-width: 500px) {
  .deal-modal__content .deal-modal__coupon .deal-modal__coupon-code {
    font-size: 24px;
  }
  .deal-modal__content .deal-modal__subtitle {
    gap: 0px;
  }
  .deal-modal__content .deal-modal__subtitle svg {
    display: none;
  }
}

@media (max-width: 420px) {
  .deal-modal__content .deal-modal__coupon {
    flex-direction: column;
  }
  .deal-modal__content .deal-modal__coupon .deal-modal__copy-btn {
    width: 100%;
  }
}

.deal-detail__bg {
  position: fixed;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 9999;
  overflow-y: scroll;
  transition: opacity 0.25s linear;
  padding: 1rem;
}

@media (min-width: 768px) {
  .deal-detail__bg {
    padding: 4rem;
  }
}

.deal-detail__bg:not(.js-visible) {
  opacity: 0;
}

.deal-detail__bg.js-hide {
  display: none;
}

.deal-detail {
  position: relative;
  display: flex;
  width: 840px;
  min-height: 250px;
  max-height: 100%;
  box-shadow: 0px 0px 30px -10px rgba(0, 0, 0, 0.9);
  background-color: #fff;
  border-radius: 16px;
  margin: auto;
  transition: transform 0.5s ease-out;
  transform: translate(0, -100px);
  flex-direction: column;
}

@media (min-width: 768px) {
  .deal-detail {
    flex-direction: row;
  }
}

.deal-detail__bg.js-visible .deal-detail {
  transform: none;
}

.deal-detail__aside {
  position: relative;
  display: block;
  background-color: #fff;
  padding: 2rem;
  border-radius: 16px;
}

@media (min-width: 768px) {
  .deal-detail__aside {
    flex: 0 0 205px;
    border-right: 1px solid #e5e5e5;
    border-radius: 16px 0 0 16px;
  }
}

.deal-detail__content {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  padding: 2rem;
  padding-bottom: 110px;
  overflow: auto;
}

.deal-detail__close {
  position: absolute;
  display: block;
  top: 10px;
  right: 0px;
  width: 36px;
  height: 36px;
  padding: 0.9rem;
  background: #fff;
  border-radius: 50%;
  color: #000;
  z-index: 99;
  cursor: pointer;
}

@media (min-width: 768px) {
  .deal-detail__close {
    top: -10px;
    right: -10px;
  }
}

.deal-detail__close:hover {
  background-color: #e5e5e5;
}

.deal-detail__top {
  position: absolute;
  display: flex;
  width: 100%;
  top: 0;
  left: 0;
  padding: 1.5rem;
  z-index: 9;
}

.deal-detail__user-click {
  font-weight: 400;
  font-size: 12px;
  color: #bdbdbd;
}

.deal-detail__user-icon {
  width: 10px;
  height: 10px;
  margin-right: 0.25rem;
  fill: #bdbdbd;
}

.deal-detail .deal-item__image-wrapper {
  background-image: none;
}

.deal-detail .deal-item__deal-text {
  padding: 1rem 2rem;
}

.deal-detail__image-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  max-height: 165px;
  align-items: center;
  justify-content: center;
  margin: auto;
}

.deal-detail__image {
  position: relative;
  display: block;
  max-width: 100%;
  max-height: 135px;
  height: auto;
}

.deal-detail__logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 40px;
  background-color: #fff;
  margin: auto;
}

.deal-detail__logo-image {
  position: relative;
  display: block;
  max-width: 80px;
  max-height: 35px;
  height: auto;
}

.deal-detail__title {
  position: relative;
  display: flex;
  width: 100%;
  text-align: left;
  margin-bottom: 0.1em;
}

.deal-detail__title-link {
  position: relative;
  display: block;
  font-weight: 700;
  color: #000;
  font-size: 16px;
  line-height: 1.25;
}

@media (min-width: 768px) {
  .deal-detail__title-link {
    font-size: 22px;
  }
}

.deal-detail__title-link:hover {
  text-decoration: underline;
}

.deal-detail__small-title {
  font-weight: 700;
  color: #000;
  font-size: 14px;
}

@media (min-width: 768px) {
  .deal-detail__small-title {
    font-size: 16px;
  }
}

.deal-detail__text {
  position: relative;
  display: block;
  width: 100%;
  font-weight: 400;
  font-size: 12px;
  color: #656565;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .deal-detail__text {
    font-size: 14px;
  }
}

.deal-detail__link {
  font-weight: 700;
}

.deal-detail__link:hover {
  text-decoration: underline;
}

.deal-detail__cashback {
  display: block;
  font-weight: 700;
  font-size: 90%;
  color: #008005;
  margin-right: 0;
}

.deal-detail__cashback-icon {
  width: 10px;
  height: 10px;
  fill: #656565;
}

.deal-detail__reward {
  display: flex;
  align-items: baseline;
  font-weight: 700;
  color: #999;
  font-size: 21px;
  margin-bottom: 0.5em;
}

@media (min-width: 768px) {
  .deal-detail__reward {
    font-size: 24px;
    margin-left: 0;
    margin-bottom: 0;
  }
}

.deal-detail__reward--line-through {
  display: block;
  font-weight: 400;
  color: #999;
  font-size: 12px;
  text-decoration: line-through;
}

.deal-detail__reward--s {
  font-size: 14px;
}

.deal-detail__reward--m {
  font-size: 16px;
}

.deal-detail__reward--medium {
  font-weight: 700;
}

.deal-detail__small {
  position: relative;
  display: flex;
  align-items: center;
  font-weight: 400;
  color: #8f8f8f;
  font-size: 13px;
}

.deal-detail__small-icon {
  position: relative;
  top: 0px;
  width: 12px;
  height: 12px;
  color: #8f8f8f;
  fill: #8f8f8f;
  margin-right: 0.25rem;
}

.deal-detail__free-shipping-icon {
  position: relative;
  width: 16px;
  height: 12px;
}

.deal-detail__bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  right: 0;
  padding: 2rem;
  background-color: #fff;
  border-radius: 0 0 16px 16px;
  border-top: 1px solid #e5e5e5;
  box-shadow: 0px -10px 10px -10px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .deal-detail__bottom {
    width: calc(100% - 205px);
    border-radius: 0 0 16px 0;
  }
}

.deal-detail__code {
  position: relative;
  display: block;
  width: 100%;
  min-width: 130px;
  border-radius: 5px;
  border: 1px solid #919191;
  border-style: dashed;
  padding: 1rem 0.75rem;
  margin-top: 0.25rem;
  font-weight: 700;
  font-size: 11px;
  padding-right: 45px;
  cursor: pointer;
}

.deal-detail__code:hover {
  border-color: #ee7836;
}

.deal-detail__code--copy {
  border-color: green;
  color: green;
  text-transform: uppercase;
  cursor: initial;
}

.deal-detail__code--copy:before {
  position: absolute;
  content: attr(data-copy-complete);
  background-color: #fafafa;
}

.deal-detail__code--copy:hover {
  border-color: green;
}

.deal-detail__code--copy svg {
  color: green;
}

.deal-detail__code-icon {
  position: absolute;
  width: 35px;
  height: 35px;
  top: -1px;
  right: 0;
  padding: 0.75rem;
  color: #ee7836;
  cursor: pointer;
  border: 0;
  background: transparent;
}

.deal-detail__code-icon svg {
  width: 100%;
  height: 100%;
}

.deal-detail__button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: #fff;
  font-size: 14px;
  padding: 12px 20px;
  border-radius: 5px;
  background-color: #ee7836;
  border: 1px solid #ee7836;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .deal-detail__button {
    margin-right: 0;
  }
}

.deal-detail__button:hover {
  color: #fff;
  background-color: #de5c13;
}

.addon-promo {
  display: none;
  align-items: center;
  padding: 2rem;
  padding-left: 1rem;
  background: #f2f1f1;
  border-radius: 16px;
  margin-bottom: 2rem;
  margin-top: 2rem;
}

@media (min-width: 1180px) {
  .addon-promo {
    display: flex;
  }
}

.addon-promo__image-wrapper {
  flex: 0 0 180px;
}

.addon-promo__content {
  padding-left: 1rem;
}

.addon-promo__content h2 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 1rem;
}

.addon-promo__content p {
  line-height: 1.5;
  margin-bottom: 2rem;
}

.addon-promo__button {
  position: relative;
  display: inline-block;
  max-width: 100%;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  padding: 1.5rem 2rem;
  background-color: #66b940;
  border-radius: 8px;
}

.addon-promo__button:hover {
  color: #fff;
  background-color: #519333;
}

.deal-modal {
  position: fixed;
  display: none;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  overflow-x: hidden;
  overflow-y: auto;
  transition: opacity .15s linear;
}

.deal-modal:not(.deal-modal--show) {
  opacity: 0;
}

.deal-modal--show {
  display: block;
}

.deal-modal--open {
  overflow: hidden;
}

.deal-modal__close {
  position: absolute;
  width: 20px;
  height: 20px;
  top: -30px;
  right: 0px;
  color: #fff;
  cursor: pointer;
  transition: transform 0.5s;
}

@media (min-width: 768px) {
  .deal-modal__close {
    top: -25px;
    right: -25px;
  }
}

.deal-modal__close:hover {
  transform: rotate(90deg);
}

.deal-modal__inner {
  position: relative;
  width: 90%;
  max-width: 640px;
  background-color: #fff;
  margin: 6rem auto;
  text-align: center;
}

.deal-modal__header {
  padding: 1.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .deal-modal__header {
    padding: 2rem;
  }
}

h3.deal-modal__title {
  position: relative;
  display: block;
  text-align: center;
  margin-bottom: 0;
  display: inline-block;
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
}

@media (min-width: 768px) {
  h3.deal-modal__title {
    font-size: 18px;
  }
}

.deal-modal__title strong {
  font-weight: 700;
}

.deal-modal__title--orange {
  color: #ee7836;
}

.deal-modal__text {
  position: relative;
  display: block;
  text-align: center;
  line-height: 1.25;
  font-weight: 400;
  font-size: 10px;
  margin: auto;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .deal-modal__text {
    width: 90%;
    font-size: 12px;
  }
}

.deal-modal__text--mt {
  margin-top: 0.5em;
}

.deal-modal__text--mb {
  margin-bottom: 0.5em;
}

.deal-modal__copy-code {
  position: relative;
  display: inline-block;
  max-width: 90%;
  margin: auto;
  border: 1px dashed #ee7836;
  background-color: #fffaf1;
  padding: 1.5rem 1.5rem;
}

.deal-modal__row {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
}

@media (min-width: 768px) {
  .deal-modal__row {
    flex-direction: row;
  }
}

.deal-modal__code {
  position: relative;
  display: block;
  font-weight: 700;
  font-size: 12px;
  margin-bottom: 1rem;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

@media (min-width: 768px) {
  .deal-modal__code {
    font-size: 16px;
    margin-right: 1.5rem;
    margin-bottom: 0;
  }
}

.deal-modal__button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  background-color: #66b940;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
  font-size: 12px;
}

@media (min-width: 768px) {
  .deal-modal__button {
    font-size: 14px;
  }
}

.deal-modal__button:hover {
  background-color: #519333;
}

.deal-modal__link-wrapper {
  position: relative;
  display: block;
  margin-top: 1rem;
  text-align: center;
}

@media (min-width: 768px) {
  .deal-modal__link-wrapper {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }
}

.deal-modal__link {
  font-weight: 400;
  color: #66b940;
  font-size: 12px;
  text-decoration: underline;
}

@media (min-width: 768px) {
  .deal-modal__link {
    font-size: 16px;
  }
}

.deal-modal__link:hover {
  text-decoration: none;
}

.deal-modal__vote {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.deal-modal__vote-text {
  text-align: left;
  font-size: 11px;
  margin-bottom: 0;
  margin-right: 1rem;
}

@media (min-width: 768px) {
  .deal-modal__vote-text {
    font-size: 14px;
    margin-right: 2rem;
  }
}

.deal-category {
  position: relative;
  display: flex;
  flex: 1 1 calc(33% - 3rem);
  font-weight: 400;
  color: #888888;
  font-size: 14px;
  border-bottom: 1px solid #888888;
  padding: 1rem 1rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 1rem;
  align-items: center;
  min-height: 53px;
}

@media (min-width: 768px) {
  .deal-category {
    flex: 1 1 auto;
    margin-left: 1rem;
    margin-right: 0;
    margin-bottom: 0;
    min-height: 0px;
  }
}

.deal-category:hover,
.deal-category.active {
  color: #ee7836;
  border-color: #ee7836;
}

.deal-category__dropdown {
  position: absolute;
  display: none;
  width: 100%;
  height: auto;
  left: 0;
  top: 36px;
  z-index: 9;
  border: 1px solid #e5e5e5;
  z-index: 99;
}

.deal-category__dropdown.--show {
  display: block;
}

.deal-category__dropdown-label {
  position: relative;
  display: block;
  min-width: 180px;
  text-align: center;
  cursor: pointer;
}

.deal-category__dropdown-label:after {
  position: absolute;
  display: block;
  content: "↓";
  right: -12px;
  top: 1px;
  font-size: 12px;
}

.deal-category__dropdown-item {
  position: relative;
  display: block;
  font-weight: 400;
  color: #888;
  font-size: 14px;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 2rem;
  background-color: #fff;
}

.deal-category__dropdown-item:hover {
  background-color: #fafafa;
}

.aq-popup a {
  color: #ee7836;
}

.aq-popup a.color-black {
  color: #000;
}

.aq-popup-bg {
  position: fixed;
  display: block;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  overflow-y: scroll;
  transition: opacity 0.25s linear;
}

.aq-popup-bg:not(.js-visible) {
  opacity: 0;
}

.aq-popup-bg.js-hide {
  display: none;
}

.aq-popup__close {
  position: absolute;
  display: block;
  top: 10px;
  right: 0px;
  color: #fff;
  width: 20px;
  height: 20px;
  z-index: 99;
  cursor: pointer;
}

@media (min-width: 768px) {
  .aq-popup__close {
    top: 15px;
    right: -30px;
  }
}

.aq-popup__close:hover {
  opacity: 0.7;
}

.aq-popup {
  position: relative;
  display: block;
  width: 615px;
  max-width: 90%;
  height: auto;
  margin: auto;
  padding-top: 40px;
  padding-bottom: 30px;
  z-index: 99999;
  transition: transform 0.5s ease-out;
  transform: translate(0, -100px);
}

@media (min-width: 768px) {
  .aq-popup {
    padding-top: 30px;
  }
}

.aq-popup-bg.js-visible .aq-popup {
  transform: none;
}

.aq-popup__content {
  border-radius: 15px;
  overflow: hidden;
}

.aq-popup__bg-grey {
  background-color: #f1f1f1;
}

.aq-popup__bg-white {
  background-color: #fff;
}

.aq-popup__top {
  padding-top: 40px;
  padding-bottom: 30px;
}

.aq-popup__bottom {
  padding-top: 30px;
  padding-bottom: 30px;
}

.aq-popup__background {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.aq-popup__logo-wrapper {
  position: absolute;
  display: flex;
  width: 80px;
  height: 80px;
  top: 71px;
  left: 50%;
  margin-left: -40px;
  padding: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  box-shadow: 0 11px 32px rgba(0, 0, 0, 0.19);
  background-color: #fff;
}

@media (min-width: 768px) {
  .aq-popup__logo-wrapper {
    width: 136px;
    height: 136px;
    margin-left: -68px;
  }
}

.aq-popup__logo-wrapper--relative {
  position: relative;
  top: 0;
  left: 0;
  margin: 2rem auto;
  margin-top: 0;
}

.aq-popup__logo-wrapper--relative.mb-4 {
  margin-bottom: 3rem;
}

.aq-popup__logo {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.aq-popup__title {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 400;
  font-size: 20px;
  line-height: 1;
  margin-bottom: 0px;
}

@media (min-width: 768px) {
  .aq-popup__title {
    font-size: 26px;
  }
}

.aq-popup__title.fw-bold {
  font-weight: 700;
}

.aq-popup__text {
  position: relative;
  display: block;
  width: 90%;
  margin: auto;
  text-align: center;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.25;
  margin-bottom: 0;
  margin-top: 10px;
}

@media (min-width: 768px) {
  .aq-popup__text {
    font-size: 20px;
    margin-top: 20px;
  }
}

.aq-popup__or {
  position: relative;
  display: block;
  font-weight: 400;
  color: #b3b3b3;
  font-size: 12px;
  text-align: center;
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .aq-popup__or {
    font-size: 14px;
  }
}

.aq-popup-form {
  position: relative;
  display: block;
  width: 80%;
  margin: auto;
}

.aq-popup-form .error li {
  text-align: center;
  border-radius: 6px;
  font-size: 11px;
}

@media (min-width: 768px) {
  .aq-popup-form .error li {
    font-size: 14px;
  }
}

.aq-popup-form__label {
  position: relative;
  display: block;
  font-weight: 400;
  color: #555;
  font-size: 14px;
  text-align: left;
}

.aq-popup-form__input {
  position: relative;
  display: block;
  width: 100%;
  border: 1px solid #dedede;
  border-radius: 6px;
  background-color: #fff;
  padding: 12px;
  font-weight: 400;
  color: #7f7f7f;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .aq-popup-form__input {
    font-size: 14px;
  }
}

.aq-popup-form__submit {
  position: relative;
  display: block;
  width: 100%;
  border-radius: 6px;
  background-color: #ef7b3b;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-size: 15px;
  padding: 12px 20px;
  margin-top: 10px;
  border: 0;
  -webkit-appearance: none;
}

@media (min-width: 768px) {
  .aq-popup-form__submit {
    font-size: 16px;
  }
}

.aq-popup-form__submit:hover {
  background-color: #e45d13;
}

.aq-popup-form__condition {
  position: relative;
  display: block;
  font-weight: 400;
  color: #9e9e9e;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  margin: 1rem 0;
  margin-bottom: 0;
}

.aq-popup-form__condition a {
  color: #9e9e9e;
  text-decoration: underline;
}

.aq-popup-form__condition a:hover {
  text-decoration: none;
}

.form-error {
  position: relative;
  display: block;
  padding: 10px;
  background-color: #eb0132;
  border: 1px solid #eb0132;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(8, 8, 8, 0.1);
  margin-top: 0px;
  margin-bottom: 1rem;
  font-weight: 400;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

.aq-popup-form .form-error a {
  color: #fff;
  text-decoration: underline;
}

.aq-popup-form .form-error a:hover {
  text-decoration: none;
}

.sign__social-login {
  text-align: center;
}

.g-signin2 {
  height: 40px;
  margin: auto;
  margin-top: 10px;
}

.g-signin2:before {
  position: absolute;
  content: '';
  display: block;
  left: 50%;
  margin-left: -125px;
  width: 250px;
  height: 40px;
  background-color: #c8c8c8;
  border-radius: 4px;
}

.g-signin2.show:before {
  display: none;
}

.g-signin2 > div {
  display: none;
  margin: auto;
  text-align: left;
  border-radius: 4px;
  box-shadow: none;
}

.g-signin2 .abcRioButtonContentWrapper {
  border: 0 !important;
}

.g-signin2 .abcRioButtonContents {
  font-size: 16px !important;
  font-weight: 400;
  margin-left: 2px;
}

.g-signin2 .abcRioButtonIcon {
  width: 24px;
  padding: 4px !important;
  margin: 7px 8px;
  border-radius: 4px !important;
}

.g-signin2 .abcRioButtonIconImage svg {
  width: 15px !important;
  margin: auto;
}

.g-signin2 .abcRioButtonBlue {
  background-color: #4285f4;
  border: none;
  color: #fff;
}

.g_id_signin {
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.aq-popup__big {
  font-size: 28px;
}

@media (min-width: 768px) {
  .aq-popup__big {
    font-size: 42px;
  }
}

.aq-popup--medium .aq-popup__top {
  padding: 2rem;
  padding-top: 4rem;
}

.aq-popup--medium .aq-popup__top--no-logo {
  padding-top: 2rem;
}

.aq-popup--medium .aq-popup__bottom {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.aq-popup--medium .aq-popup__title {
  font-size: 20px;
}

@media (min-width: 768px) {
  .aq-popup--medium .aq-popup__title {
    font-size: 32px;
  }
}

.aq-popup--medium .aq-popup__text {
  color: #a1a1a1;
}

.aq-popup__button {
  display: block;
  width: 80%;
  color: #fff !important;
  font-weight: 600;
  margin: auto;
}

.aq-popup__button.btn-grey {
  color: grey !important;
}

.aq-popup__button.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.aq-popup__button.w-auto {
  width: auto;
}

.aq-popup__steps {
  display: flex;
  text-align: center;
  flex-direction: column;
}

@media (min-width: 768px) {
  .aq-popup__steps {
    width: 85%;
    flex-direction: row;
    margin: auto;
  }
}

.aq-popup__step {
  display: flex;
  align-items: center;
  text-align: left;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .aq-popup__step {
    display: block;
    text-align: center;
    margin-bottom: 0;
  }
}

.aq-popup__number {
  position: relative;
  display: flex;
  width: 36px;
  flex: 0 0 36px;
  height: 36px;
  align-items: center;
  justify-content: center;
  border: 1px dashed #999;
  margin: auto;
  font-weight: 700;
  font-size: 14px;
  color: #999;
  border-radius: 50%;
  margin-left: 0;
  margin-right: 1rem;
}

@media (min-width: 768px) {
  .aq-popup__number {
    margin: auto;
    margin-bottom: 1rem;
  }
}

.aq-popup__number.color-green {
  color: #66b940;
  border-color: #66b940;
}

.aq-popup__header {
  padding-top: 1rem;
  padding-bottom: 2rem;
}

.aq-popup__bottom-msg {
  font-weight: 700;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 0;
  padding: 1.5rem;
  border-radius: 0 0 15px 15px;
  background-color: #ca2c25;
}

.aq-popup__bottom-msg--info {
  font-weight: 400;
  font-size: 12px;
  color: #856404;
  background-color: #fff3cd;
}

.aq-popup__bottom-msg--info a {
  color: #856404;
  text-decoration: underline;
}

.aq-popup--medium .sign__social-login {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-height: 100%;
}

.aq-popup--medium .fb_iframe_widget {
  margin: 10px !important;
}

.aq-popup--medium .g-signin2 {
  margin: 10px;
}

.aq-popup--medium .g-signin2:before {
  display: none;
}

.aq-popup--medium .appleid-signin {
  display: none;
}

.aq-popup-form--password {
  margin-top: 6rem;
}

.aq-popup-form--password .aq-popup__close {
  display: none;
}

.aq-popup-form--password .aq-popup__text {
  margin-top: 1rem;
}

.aq-popup-form--password .aq-popup__bottom {
  padding-bottom: 4rem;
  background-color: #fff;
}

.aq-popup-form--password .aq-popup__bottom:before {
  position: absolute;
  display: block;
  content: '';
  width: 80px;
  height: 172px;
  right: 0px;
  bottom: 80px;
  background-image: url("../images/../../images/homepage/oslikHeader.png");
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

@media (min-width: 768px) {
  .aq-popup-form--password .aq-popup__bottom:before {
    width: 122px;
    height: 204px;
  }
}

.aq-popup__center-content {
  width: 85%;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .aq-popup-form--what-now.aq-popup__top:before {
    position: absolute;
    display: block;
    content: '';
    width: 100px;
    height: 172px;
    right: 0px;
    bottom: 30px;
    background-image: url("../images/../../images/homepage/oslikHeader.png");
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
  }
}

.aq-popup__redirect-icon {
  width: 40px;
  height: 40px;
  color: #66b940;
}

.aq-popup__c-message {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1.25;
  font-weight: 400;
  color: #a1a1a1;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .aq-popup__c-message {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .aq-popup__c-message {
    align-items: center;
  }
}

@media (min-width: 1180px) {
  .aq-popup__c-message {
    align-items: baseline;
  }
}

.aq-popup__c-message-value {
  position: relative;
  display: inline-block;
  font-weight: 900;
  flex: 0 0 auto;
  font-size: 26px;
  line-height: 1.25;
  color: #66b940;
  background: #fff;
  border: 2px dashed #66b940;
  box-sizing: border-box;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  margin: 1rem 1.6rem;
}

@media (min-width: 768px) {
  .aq-popup__c-message-value {
    margin: 0 1.6rem;
  }
}

.aq-popup__c-message-value small {
  font-size: 14px;
}

.aq-popup--condition {
  display: flex;
  align-items: center;
}

.aq-popup__icon {
  position: relative;
  display: block;
  width: 40px;
  height: 40px;
  margin: auto;
  margin-bottom: 2rem;
  color: #888;
}

.aq-popup__title--medium.aq-popup__title {
  font-size: 24px;
  line-height: 1.25;
}

.aq-popup__title--medium .aq-popup__big {
  font-size: 28px;
}

.aq-popup-form--nexit .aq-popup__top {
  padding-top: 3rem;
}

.aq-popup-form--nexit .aq-popup__logo-wrapper {
  width: 130px;
  height: 70px;
}

@media (min-width: 768px) {
  .aq-popup-form--nexit .aq-popup__logo-wrapper {
    width: 130px;
    height: 70px;
  }
}

.aq-popup-form--nexit .sign__social-login {
  flex-direction: column;
  align-items: center;
}

.aq-popup-form--nexit .fb_iframe_widget {
  margin: 0;
  margin-bottom: 10px;
}

.aq-popup-form--nexit .g-signin2 {
  margin: 0;
}

.aq-popup-form--nexit .aq-popup-form__input {
  padding: 10px;
  border: 1px solid #555;
}

.aq-popup-form--nexit .aq-popup-form__submit {
  padding: 14px 20px;
  box-shadow: 0 6px 5px -3px #989898;
}

.aq-popup-form--nexit .aq-popup-form {
  max-width: 320px;
  margin: auto;
}

@media (min-width: 768px) {
  .aq-popup-form--nexit .aq-popup-form {
    font-size: 16px;
  }
}

.aq-popup-survey__textarea {
  position: relative;
  display: block;
  width: 80%;
  height: 70px;
  margin: auto;
  border: 1px solid #e9e9e9;
  border-radius: 9px;
  background-color: #fff;
  padding: 1rem;
  margin-bottom: 2rem;
  text-align: center;
  resize: horizontal;
}

.aq-popup__button.btn-orange-inverse {
  color: #ee7836 !important;
  background-color: transparent;
}

.aq-popup__button.btn-orange-inverse:hover {
  color: #fff !important;
  background-color: #ee7836;
}

.aq-popup__button.btn-green-inverse {
  color: #66b940 !important;
  background-color: transparent;
}

.aq-popup__button.btn-green-inverse:hover {
  color: #fff !important;
  background-color: #66b940;
}

.aq-popup__button.btn-red-inverse {
  color: #ff5656 !important;
  background-color: transparent;
}

.aq-popup__button.btn-red-inverse:hover {
  color: #fff !important;
  background-color: #ff5656;
}

.aq-popup-form--survey .aq-popup__button {
  width: 90%;
  padding: 12px;
}

@media (min-width: 1180px) {
  .aq-popup--warning--mobile {
    display: none;
  }
}

.aq-popup--warning--desktop {
  display: none;
}

@media (min-width: 1180px) {
  .aq-popup--warning--desktop {
    display: block;
  }
}

.aq-popup-form__small {
  position: relative;
  display: block;
  font-weight: 400;
  color: #9e9e9e;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  margin: 1rem 0;
  margin-bottom: 0;
}

.aq-popup-form__small a {
  color: #9e9e9e;
  text-decoration: underline;
}

.aq-popup-form__small a:hover {
  text-decoration: none;
}

.aq-popup__c-message-show-more {
  cursor: pointer;
}

.aq-popup__c-message-show-more:hover .aq-popup__c-message-info-msg {
  display: block;
}

.aq-popup__c-message-info-msg {
  position: absolute;
  display: none;
  width: 320px;
  bottom: -90px;
  left: 50%;
  margin-left: -160px;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  padding: 1rem 0.75rem;
  background: #66b940;
  border-radius: 8px;
  line-height: 1.5;
  text-align: center;
  z-index: 99;
}

@media (min-width: 768px) {
  .aq-popup__c-message-info-msg {
    width: 340px;
    margin-left: -170px;
    padding: 1rem 1.5rem;
  }
}

.hu .aq-popup__c-message,
.bg .aq-popup__c-message,
.hr .aq-popup__c-message {
  font-size: 16px;
}

.carousel {
  margin-bottom: 0;
}

.carousel__wrapper {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-top: 2rem;
  margin-bottom: 1rem;
  flex-direction: column;
}

@media (min-width: 1024px) {
  .carousel__wrapper {
    flex-direction: row;
  }
}

.carousel__content {
  position: relative;
  display: block;
  width: 100%;
  height: calc((100vw - 30px) * 0.66);
  flex: 1 1 auto;
  margin: auto;
}

@media (min-width: 768px) {
  .carousel__content {
    height: calc((100vw - 30px) * 0.29);
  }
}

@media (min-width: 1024px) {
  .carousel__content {
    width: calc(100% - 315px);
    height: auto;
    margin-right: 15px;
  }
}

@media (min-width: 1180px) {
  .carousel__content {
    height: 250px;
  }
}

.carousel__sidebar {
  position: relative;
  display: none;
}

@media (min-width: 1024px) {
  .carousel__sidebar {
    display: block;
    flex: 0 0 300px;
  }
}

.carousel__item {
  display: none;
}

.carousel__item a {
  cursor: pointer;
}

.carousel .slick-list {
  border-radius: 11px;
}

.carousel .slick-prev,
.carousel .slick-next {
  width: 150px;
  height: 100%;
  z-index: 9;
}

@media (min-width: 1180px) {
  .carousel .slick-prev,
  .carousel .slick-next {
    opacity: 0;
  }
}

.carousel .slick-prev {
  left: 0px;
}

.carousel .slick-next {
  right: 0px;
}

.carousel .slick-prev:before,
.carousel .slick-next:before {
  position: relative;
  display: flex;
  content: '';
  width: 34px;
  height: 34px;
  align-items: center;
  justify-content: center;
  margin: auto;
  background-image: url("../../images/carousel/arrow-left.svg");
  background-size: cover;
  background-position: 50% 50%;
  margin-left: 3rem;
}

.carousel .slick-next:before {
  background-image: url("../../images/carousel/arrow-right.svg");
  margin-left: auto;
  margin-right: 3rem;
}

.carousel .slick-prev:hover,
.carousel .slick-next:hover {
  background: rgba(0, 0, 0, 0.5);
  background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
  border-radius: 10px;
}

@media (min-width: 1180px) {
  .carousel .slick-prev:hover,
  .carousel .slick-next:hover {
    opacity: 1;
  }
}

.carousel .slick-prev:hover {
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);
}

.carousel__image {
  margin: auto;
}

@media (min-width: 768px) {
  .slick-slide img.carousel__image--s {
    display: none;
  }
}

.slick-slide img.carousel__image--m {
  display: none;
}

@media (min-width: 768px) {
  .slick-slide img.carousel__image--m {
    display: block;
  }
}

.carousel .slick-dots {
  position: relative;
  bottom: 0px;
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .carousel .slick-dots {
    position: absolute;
    width: auto;
    bottom: 10px;
    margin-top: 0;
    left: 0;
    right: 0;
  }
}

.carousel .slick-dots li {
  width: 12px;
  height: 12px;
}

.carousel .slick-dots li button {
  width: 12px;
  height: 12px;
  padding: 0;
  border-radius: 50%;
  background: white;
}

.carousel .slick-dots li button:before {
  content: '';
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 3px;
  opacity: 1;
}

.carousel .slick-dots li.slick-active button:before {
  background: #5b5b5b;
  opacity: 1;
}

.slick-slide > div {
  display: flex;
}

.carousel .slick-slide img {
  width: 100%;
  height: auto;
  border-radius: 11px;
  border: 1px solid #dedede;
}

.carousel__form {
  position: relative;
  display: block;
  border: 1px solid #dedede;
  border-radius: 11px;
  background-color: #fff;
  box-shadow: 0 11px 32px rgba(0, 0, 0, 0.19);
  overflow: hidden;
  padding-top: 19px;
  padding-bottom: 19px;
}

.carousel__sidebar-cta-box {
  position: relative;
  display: block;
  border: 1px solid #dedede;
  border-radius: 11px;
  background-color: #fff;
  padding: 2rem 1.75rem;
  background-image: url(../../images/homepage/background.webp);
  background-size: cover;
}

.carousel__sidebar-cta-box-donkey {
  position: absolute;
  display: block;
  width: 200px;
  height: 200px;
  bottom: 0;
  right: -160px;
  background-image: url(../../images/deal/oslik.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.carousel__sidebar-title {
  position: relative;
  text-align: center;
  font-weight: 400;
  font-size: 23px;
  margin-bottom: 1.5rem;
}

.carousel__sidebar .homepage-header__icon-title {
  font-size: 15px;
}

.carousel__sidebar-button {
  position: relative;
  display: block;
  width: 100%;
  border-radius: 6px;
  background-color: #ef7b3b;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-size: 18px;
  padding: 14px 20px;
  border: 0;
  z-index: 99;
}

.carousel__action {
  height: 196px;
}

@media (min-width: 1024px) {
  .carousel__action {
    height: calc(((100vw - 30px) - 315px) * 0.289);
  }
}

@media (min-width: 1180px) {
  .carousel__action {
    height: calc((1180px - 315px) * 0.279);
  }
}

@media (min-width: 1200px) {
  .carousel__action {
    height: 250px;
  }
}

@media (min-width: 1024px) {
  .carousel--sidebar-action .carousel__content {
    width: calc(100% - 315px);
    height: auto;
    margin-right: 0;
  }
}

@media (min-width: 1180px) {
  .carousel--sidebar-action .carousel__content {
    height: 250px;
    overflow: hidden;
  }
}

@media (min-width: 1024px) {
  .carousel--sidebar-action .carousel__sidebar {
    display: block;
    flex: 0 0 315px;
  }
}

.carousel--sidebar-action .carousel .slick-list,
.carousel--sidebar-action .carousel .slick-slide img {
  border-radius: 11px 11px;
}

@media (min-width: 1024px) {
  .carousel--sidebar-action .carousel .slick-list,
  .carousel--sidebar-action .carousel .slick-slide img {
    border-radius: 11px 0 0 11px;
  }
}

.carousel--sidebar-action .border-box {
  border-radius: 0px 11px 11px 0;
  border-left: 0;
}

.carousel--sidebar-action .carousel .slick-next:hover {
  border-radius: 0;
}

@media (min-width: 1024px) {
  .carousel--sidebar-action .carousel__content .slick-dots {
    display: none !important;
  }
}

.slick-track {
  display: flex !important;
}

.carousel-reasons {
  position: relative;
  display: block;
  padding: 0;
  margin-top: 1rem;
}

.carousel-reasons__item {
  position: relative;
  display: flex;
  align-items: center;
  font-weight: 400;
  line-height: 1.25;
  margin-bottom: 10px;
  font-size: 14px;
}

.carousel-reasons__number {
  display: flex;
  flex: 0 0 30px;
  width: 30px;
  height: 30px;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  background: #66b940;
  font-weight: 700;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  font-size: 14px;
}

.instructions__bg {
  position: relative;
  display: block;
  padding: 1rem;
  padding-top: 1rem;
  background-color: #fff;
  border: 1px solid #dedede;
  animation: 1s fadeIn;
  animation-fill-mode: forwards;
  visibility: hidden;
  float: none !important;
}

@media (min-width: 375px) {
  .instructions__bg {
    padding: 1rem 1.5rem;
  }
}

@media (min-width: 768px) {
  .instructions__bg {
    height: 213px;
  }
}

@media (min-width: 1024px) {
  .instructions__bg {
    height: 196px;
  }
}

@media (min-width: 1180px) {
  .instructions__bg {
    height: 250px;
  }
}

@keyframes fadeIn {
  99% {
    visibility: hidden;
  }
  100% {
    visibility: visible;
  }
}

.instructions__wrapper {
  position: relative;
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.instructions {
  position: relative;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

@media (min-width: 768px) {
  .instructions {
    flex-direction: row;
    align-items: center;
    padding: 1rem 0;
  }
}

.instructions__item {
  position: relative;
  display: flex;
  width: 100%;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .instructions__item {
    display: block;
    flex: 0 0 33%;
    margin-bottom: 0;
  }
}

.instructions__item:last-child {
  margin-bottom: 0;
}

.instructions__item:before {
  position: absolute;
  display: none;
  content: '';
  top: 0;
  right: -40px;
  width: 80px;
  height: 110px;
}

.instructions__item:last-child:before {
  display: none;
}

.carousel--sidebar-action img.instructions__image {
  position: relative;
  display: block;
  width: 20px;
  height: 20px;
  margin: auto;
  border: 0;
  border-radius: 0 !important;
  margin-left: 0;
  margin-right: 1rem;
}

@media (min-width: 375px) {
  .carousel--sidebar-action img.instructions__image {
    width: 45px;
    height: 34px;
  }
}

@media (min-width: 768px) {
  .carousel--sidebar-action img.instructions__image {
    width: 55px;
    height: 40px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1rem;
  }
}

@media (min-width: 1180px) {
  .carousel--sidebar-action img.instructions__image {
    height: 55px;
  }
}

.instructions__title {
  position: relative;
  display: block;
  text-align: left;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 0;
}

@media (min-width: 375px) {
  .instructions__title {
    font-size: 14px;
  }
}

@media (min-width: 768px) {
  .instructions__title {
    text-align: center;
    margin-bottom: 1rem;
  }
}

@media (min-width: 1180px) {
  .instructions__title {
    font-size: 16px;
  }
}

.instructions__text {
  position: relative;
  display: block;
  text-align: left;
  font-size: 10px;
  font-weight: 400;
  margin: auto;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .instructions__text {
    font-size: 12px;
    text-align: center;
  }
}

@media (min-width: 1180px) {
  .instructions__text {
    max-width: 80%;
    min-height: 54px;
  }
}

.instructions__button-wrapper {
  display: block;
  width: 100%;
  text-align: center;
  margin-top: 0.5rem;
}

@media (min-width: 1024px) {
  .instructions__button-wrapper {
    margin-top: 1rem;
  }
}

.instructions__button {
  position: relative;
  display: inline-flex;
  height: 20px;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 11px;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  background-color: #ee7836;
  border-radius: 40px;
}

@media (min-width: 375px) {
  .instructions__button {
    height: 24px;
  }
}

@media (min-width: 768px) {
  .instructions__button {
    font-size: 13px;
    height: 30px;
  }
}

.instructions__button:hover {
  color: #fff;
  background-color: #de5c13;
}
