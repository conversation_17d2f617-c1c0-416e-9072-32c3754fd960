* {
  box-sizing: border-box;
}

*:before,
*:after {
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  padding: 0;
}

h3 {
  margin-top: 0;
}

.aq-popup a {
  color: #ee7836;
}

.aq-popup a.color-black {
  color: #000;
}

.aq-popup-bg {
  position: fixed;
  display: block;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  overflow-y: scroll;
  transition: opacity 0.25s linear;
}

.aq-popup-bg:not(.js-visible) {
  opacity: 0;
}

.aq-popup-bg.js-hide {
  display: none;
}

.aq-popup__close {
  position: absolute;
  display: block;
  top: 10px;
  right: 0px;
  color: #fff;
  width: 20px;
  height: 20px;
  z-index: 99;
  cursor: pointer;
}

@media (min-width: 768px) {
  .aq-popup__close {
    top: 15px;
    right: -30px;
  }
}

.aq-popup__close:hover {
  opacity: 0.7;
}

.aq-popup {
  position: relative;
  display: block;
  width: 615px;
  max-width: 90%;
  height: auto;
  margin: auto;
  padding-top: 40px;
  padding-bottom: 30px;
  z-index: 99999;
  transition: transform 0.5s ease-out;
  transform: translate(0, -100px);
}

@media (min-width: 768px) {
  .aq-popup {
    padding-top: 30px;
  }
}

.aq-popup-bg.js-visible .aq-popup {
  transform: none;
}

.aq-popup__content {
  border-radius: 15px;
  overflow: hidden;
}

.aq-popup__bg-grey {
  background-color: #f1f1f1;
}

.aq-popup__bg-white {
  background-color: #fff;
}

.aq-popup__top {
  padding-top: 40px;
  padding-bottom: 30px;
}

.aq-popup__bottom {
  padding-top: 30px;
  padding-bottom: 30px;
}

.aq-popup__background {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.aq-popup__logo-wrapper {
  position: absolute;
  display: flex;
  width: 80px;
  height: 80px;
  top: 71px;
  left: 50%;
  margin-left: -40px;
  padding: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  box-shadow: 0 11px 32px rgba(0, 0, 0, 0.19);
  background-color: #fff;
}

@media (min-width: 768px) {
  .aq-popup__logo-wrapper {
    width: 136px;
    height: 136px;
    margin-left: -68px;
  }
}

.aq-popup__logo-wrapper--relative {
  position: relative;
  top: 0;
  left: 0;
  margin: 2rem auto;
  margin-top: 0;
}

.aq-popup__logo-wrapper--relative.mb-4 {
  margin-bottom: 3rem;
}

.aq-popup__logo {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.aq-popup__title {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 400;
  font-size: 20px;
  line-height: 1;
  margin-bottom: 0px;
}

@media (min-width: 768px) {
  .aq-popup__title {
    font-size: 26px;
  }
}

.aq-popup__title.fw-bold {
  font-weight: 700;
}

.aq-popup__text {
  position: relative;
  display: block;
  width: 90%;
  margin: auto;
  text-align: center;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.25;
  margin-bottom: 0;
  margin-top: 10px;
}

@media (min-width: 768px) {
  .aq-popup__text {
    font-size: 20px;
    margin-top: 20px;
  }
}

.aq-popup__or {
  position: relative;
  display: block;
  font-weight: 400;
  color: #b3b3b3;
  font-size: 12px;
  text-align: center;
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .aq-popup__or {
    font-size: 14px;
  }
}

.aq-popup-form {
  position: relative;
  display: block;
  width: 80%;
  margin: auto;
}

.aq-popup-form .error li {
  text-align: center;
  border-radius: 6px;
  font-size: 11px;
}

@media (min-width: 768px) {
  .aq-popup-form .error li {
    font-size: 14px;
  }
}

.aq-popup-form__label {
  position: relative;
  display: block;
  font-weight: 400;
  color: #555;
  font-size: 14px;
  text-align: left;
}

.aq-popup-form__input {
  position: relative;
  display: block;
  width: 100%;
  border: 1px solid #dedede;
  border-radius: 6px;
  background-color: #fff;
  padding: 12px;
  font-weight: 400;
  color: #7f7f7f;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .aq-popup-form__input {
    font-size: 14px;
  }
}

.aq-popup-form__submit {
  position: relative;
  display: block;
  width: 100%;
  border-radius: 6px;
  background-color: #ef7b3b;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-size: 15px;
  padding: 12px 20px;
  margin-top: 10px;
  border: 0;
  -webkit-appearance: none;
}

@media (min-width: 768px) {
  .aq-popup-form__submit {
    font-size: 16px;
  }
}

.aq-popup-form__submit:hover {
  background-color: #e45d13;
}

.aq-popup-form__condition {
  position: relative;
  display: block;
  font-weight: 400;
  color: #9e9e9e;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  margin: 1rem 0;
  margin-bottom: 0;
}

.aq-popup-form__condition a {
  color: #9e9e9e;
  text-decoration: underline;
}

.aq-popup-form__condition a:hover {
  text-decoration: none;
}

.form-error {
  position: relative;
  display: block;
  padding: 10px;
  background-color: #eb0132;
  border: 1px solid #eb0132;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(8, 8, 8, 0.1);
  margin-top: 0px;
  margin-bottom: 1rem;
  font-weight: 400;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

.aq-popup-form .form-error a {
  color: #fff;
  text-decoration: underline;
}

.aq-popup-form .form-error a:hover {
  text-decoration: none;
}

.sign__social-login {
  text-align: center;
}

.g-signin2 {
  height: 40px;
  margin: auto;
  margin-top: 10px;
}

.g-signin2:before {
  position: absolute;
  content: '';
  display: block;
  left: 50%;
  margin-left: -125px;
  width: 250px;
  height: 40px;
  background-color: #c8c8c8;
  border-radius: 4px;
}

.g-signin2.show:before {
  display: none;
}

.g-signin2 > div {
  display: none;
  margin: auto;
  text-align: left;
  border-radius: 4px;
  box-shadow: none;
}

.g-signin2 .abcRioButtonContentWrapper {
  border: 0 !important;
}

.g-signin2 .abcRioButtonContents {
  font-size: 16px !important;
  font-weight: 400;
  margin-left: 2px;
}

.g-signin2 .abcRioButtonIcon {
  width: 24px;
  padding: 4px !important;
  margin: 7px 8px;
  border-radius: 4px !important;
}

.g-signin2 .abcRioButtonIconImage svg {
  width: 15px !important;
  margin: auto;
}

.g-signin2 .abcRioButtonBlue {
  background-color: #4285f4;
  border: none;
  color: #fff;
}

.g_id_signin {
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.aq-popup__big {
  font-size: 28px;
}

@media (min-width: 768px) {
  .aq-popup__big {
    font-size: 42px;
  }
}

.aq-popup--medium .aq-popup__top {
  padding: 2rem;
  padding-top: 4rem;
}

.aq-popup--medium .aq-popup__top--no-logo {
  padding-top: 2rem;
}

.aq-popup--medium .aq-popup__bottom {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.aq-popup--medium .aq-popup__title {
  font-size: 20px;
}

@media (min-width: 768px) {
  .aq-popup--medium .aq-popup__title {
    font-size: 32px;
  }
}

.aq-popup--medium .aq-popup__text {
  color: #a1a1a1;
}

.aq-popup__button {
  display: block;
  width: 80%;
  color: #fff !important;
  font-weight: 600;
  margin: auto;
}

.aq-popup__button.btn-grey {
  color: grey !important;
}

.aq-popup__button.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.aq-popup__button.w-auto {
  width: auto;
}

.aq-popup__steps {
  display: flex;
  text-align: center;
  flex-direction: column;
}

@media (min-width: 768px) {
  .aq-popup__steps {
    width: 85%;
    flex-direction: row;
    margin: auto;
  }
}

.aq-popup__step {
  display: flex;
  align-items: center;
  text-align: left;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .aq-popup__step {
    display: block;
    text-align: center;
    margin-bottom: 0;
  }
}

.aq-popup__number {
  position: relative;
  display: flex;
  width: 36px;
  flex: 0 0 36px;
  height: 36px;
  align-items: center;
  justify-content: center;
  border: 1px dashed #999;
  margin: auto;
  font-weight: 700;
  font-size: 14px;
  color: #999;
  border-radius: 50%;
  margin-left: 0;
  margin-right: 1rem;
}

@media (min-width: 768px) {
  .aq-popup__number {
    margin: auto;
    margin-bottom: 1rem;
  }
}

.aq-popup__number.color-green {
  color: #66b940;
  border-color: #66b940;
}

.aq-popup__header {
  padding-top: 1rem;
  padding-bottom: 2rem;
}

.aq-popup__bottom-msg {
  font-weight: 700;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 0;
  padding: 1.5rem;
  border-radius: 0 0 15px 15px;
  background-color: #ca2c25;
}

.aq-popup__bottom-msg--info {
  font-weight: 400;
  font-size: 12px;
  color: #856404;
  background-color: #fff3cd;
}

.aq-popup__bottom-msg--info a {
  color: #856404;
  text-decoration: underline;
}

.aq-popup--medium .sign__social-login {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-height: 100%;
}

.aq-popup--medium .fb_iframe_widget {
  margin: 10px !important;
}

.aq-popup--medium .g-signin2 {
  margin: 10px;
}

.aq-popup--medium .g-signin2:before {
  display: none;
}

.aq-popup--medium .appleid-signin {
  display: none;
}

.aq-popup-form--password {
  margin-top: 6rem;
}

.aq-popup-form--password .aq-popup__close {
  display: none;
}

.aq-popup-form--password .aq-popup__text {
  margin-top: 1rem;
}

.aq-popup-form--password .aq-popup__bottom {
  padding-bottom: 4rem;
  background-color: #fff;
}

.aq-popup-form--password .aq-popup__bottom:before {
  position: absolute;
  display: block;
  content: '';
  width: 80px;
  height: 172px;
  right: 0px;
  bottom: 80px;
  background-image: url("../images/../../images/homepage/oslikHeader.png");
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

@media (min-width: 768px) {
  .aq-popup-form--password .aq-popup__bottom:before {
    width: 122px;
    height: 204px;
  }
}

.aq-popup__center-content {
  width: 85%;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .aq-popup-form--what-now.aq-popup__top:before {
    position: absolute;
    display: block;
    content: '';
    width: 100px;
    height: 172px;
    right: 0px;
    bottom: 30px;
    background-image: url("../images/../../images/homepage/oslikHeader.png");
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
  }
}

.aq-popup__redirect-icon {
  width: 40px;
  height: 40px;
  color: #66b940;
}

.aq-popup__c-message {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1.25;
  font-weight: 400;
  color: #a1a1a1;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .aq-popup__c-message {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .aq-popup__c-message {
    align-items: center;
  }
}

@media (min-width: 1180px) {
  .aq-popup__c-message {
    align-items: baseline;
  }
}

.aq-popup__c-message-value {
  position: relative;
  display: inline-block;
  font-weight: 900;
  flex: 0 0 auto;
  font-size: 26px;
  line-height: 1.25;
  color: #66b940;
  background: #fff;
  border: 2px dashed #66b940;
  box-sizing: border-box;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  margin: 1rem 1.6rem;
}

@media (min-width: 768px) {
  .aq-popup__c-message-value {
    margin: 0 1.6rem;
  }
}

.aq-popup__c-message-value small {
  font-size: 14px;
}

.aq-popup--condition {
  display: flex;
  align-items: center;
}

.aq-popup__icon {
  position: relative;
  display: block;
  width: 40px;
  height: 40px;
  margin: auto;
  margin-bottom: 2rem;
  color: #888;
}

.aq-popup__title--medium.aq-popup__title {
  font-size: 24px;
  line-height: 1.25;
}

.aq-popup__title--medium .aq-popup__big {
  font-size: 28px;
}

.aq-popup-form--nexit .aq-popup__top {
  padding-top: 3rem;
}

.aq-popup-form--nexit .aq-popup__logo-wrapper {
  width: 130px;
  height: 70px;
}

@media (min-width: 768px) {
  .aq-popup-form--nexit .aq-popup__logo-wrapper {
    width: 130px;
    height: 70px;
  }
}

.aq-popup-form--nexit .sign__social-login {
  flex-direction: column;
  align-items: center;
}

.aq-popup-form--nexit .fb_iframe_widget {
  margin: 0;
  margin-bottom: 10px;
}

.aq-popup-form--nexit .g-signin2 {
  margin: 0;
}

.aq-popup-form--nexit .aq-popup-form__input {
  padding: 10px;
  border: 1px solid #555;
}

.aq-popup-form--nexit .aq-popup-form__submit {
  padding: 14px 20px;
  box-shadow: 0 6px 5px -3px #989898;
}

.aq-popup-form--nexit .aq-popup-form {
  max-width: 320px;
  margin: auto;
}

@media (min-width: 768px) {
  .aq-popup-form--nexit .aq-popup-form {
    font-size: 16px;
  }
}

.aq-popup-survey__textarea {
  position: relative;
  display: block;
  width: 80%;
  height: 70px;
  margin: auto;
  border: 1px solid #e9e9e9;
  border-radius: 9px;
  background-color: #fff;
  padding: 1rem;
  margin-bottom: 2rem;
  text-align: center;
  resize: horizontal;
}

.aq-popup__button.btn-orange-inverse {
  color: #ee7836 !important;
  background-color: transparent;
}

.aq-popup__button.btn-orange-inverse:hover {
  color: #fff !important;
  background-color: #ee7836;
}

.aq-popup__button.btn-green-inverse {
  color: #66b940 !important;
  background-color: transparent;
}

.aq-popup__button.btn-green-inverse:hover {
  color: #fff !important;
  background-color: #66b940;
}

.aq-popup__button.btn-red-inverse {
  color: #ff5656 !important;
  background-color: transparent;
}

.aq-popup__button.btn-red-inverse:hover {
  color: #fff !important;
  background-color: #ff5656;
}

.aq-popup-form--survey .aq-popup__button {
  width: 90%;
  padding: 12px;
}

@media (min-width: 1180px) {
  .aq-popup--warning--mobile {
    display: none;
  }
}

.aq-popup--warning--desktop {
  display: none;
}

@media (min-width: 1180px) {
  .aq-popup--warning--desktop {
    display: block;
  }
}

.aq-popup-form__small {
  position: relative;
  display: block;
  font-weight: 400;
  color: #9e9e9e;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  margin: 1rem 0;
  margin-bottom: 0;
}

.aq-popup-form__small a {
  color: #9e9e9e;
  text-decoration: underline;
}

.aq-popup-form__small a:hover {
  text-decoration: none;
}

.aq-popup__c-message-show-more {
  cursor: pointer;
}

.aq-popup__c-message-show-more:hover .aq-popup__c-message-info-msg {
  display: block;
}

.aq-popup__c-message-info-msg {
  position: absolute;
  display: none;
  width: 320px;
  bottom: -90px;
  left: 50%;
  margin-left: -160px;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  padding: 1rem 0.75rem;
  background: #66b940;
  border-radius: 8px;
  line-height: 1.5;
  text-align: center;
  z-index: 99;
}

@media (min-width: 768px) {
  .aq-popup__c-message-info-msg {
    width: 340px;
    margin-left: -170px;
    padding: 1rem 1.5rem;
  }
}

.hu .aq-popup__c-message,
.bg .aq-popup__c-message,
.hr .aq-popup__c-message {
  font-size: 16px;
}

.redirect-new {
  background-color: #ffff;
}

.redirect-new__bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
  flex-direction: column;
}

@media (min-width: 768px) {
  .redirect-new__bottom {
    flex-direction: row;
  }
}

a.redirect-new__no-reward {
  position: relative;
  display: block;
  text-align: center;
  font-size: 12px;
  color: grey;
  text-decoration: none;
  margin-top: 1rem;
}

@media (min-width: 768px) {
  a.redirect-new__no-reward {
    margin-top: 0;
  }
}

a.redirect-new__no-reward:hover {
  text-decoration: underline;
}

.redirect-new__text {
  font-size: 12px;
  color: grey;
  margin: 0;
}

.redirect-new__text a {
  color: grey;
}

.aq-popup {
  padding-top: 1rem;
}

.aq-popup a {
  color: #5e5e5e;
}

.aq-popup__content {
  border: 1px solid #f1f1f1;
}

.aq-popup__logo {
  margin: auto;
  margin-top: 2rem;
}

.aq-popup--medium .aq-popup__bottom {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.aq-popup--medium .aq-popup__top {
  padding: 1rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  .aq-popup--medium .aq-popup__top {
    padding: 2rem;
    padding-top: 1rem;
  }
}

.aq-popup--medium .aq-popup__title {
  font-size: 16px;
  line-height: 1.25;
}

@media (min-width: 768px) {
  .aq-popup--medium .aq-popup__title {
    font-size: 30px;
  }
}

.aq-popup-form__label {
  margin-bottom: 0.5rem;
}

.btn {
  position: relative;
  display: inline-block;
  font-weight: 400;
  font-size: 15px;
  border: 2px solid;
  max-width: 100%;
  padding: 12px 20px;
  text-align: center;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
}

.btn-grey {
  background: #f1f1f1;
  border-color: grey;
  color: grey;
}

.btn-orange {
  background: #ee7836;
  border-color: #ee7836;
  color: #fff;
}

.btn-orange:hover {
  background: #de5c13;
  border-color: #de5c13;
  color: #fff;
}

.aq-popup__button {
  display: block;
  width: 80%;
  color: #fff !important;
  font-weight: 600;
  margin: auto;
}

.aq-popup__button.btn-grey {
  color: grey !important;
}

.aq-popup__button.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.aq-popup__button.w-auto {
  width: auto;
}

.d-flex {
  display: flex;
}

.px-4 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.hide {
  display: none;
}
