.leaflet {
  position: relative;
  display: block;
  padding-bottom: 50px;
  background-color: #f2f1f1;
}

.leaflet__title {
  position: relative;
  display: block;
  font-weight: 400;
  font-size: 2.6rem;
  line-height: 1.25;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .leaflet__title {
    font-size: 3.3rem;
  }
}

.leaflet__title--grey {
  font-size: 28px;
  color: #373737;
}

.leaflet__title-tag {
  position: relative;
  display: inline-block;
  top: -4px;
  padding: 3px 10px;
  border-radius: 5px;
  background-color: rgba(200, 200, 200, 0.25);
  font-weight: 400;
  color: #373737;
  font-size: 10px;
  margin-left: 4px;
}

.leaflet__title-link,
.leaflet__title-link--normal {
  font-weight: 700;
  color: #ee7836;
  text-decoration: underline;
}

.leaflet__title-link:hover,
.leaflet__title-link--normal:hover {
  text-decoration: none;
}

.leaflet__title-link--logo {
  position: relative;
  padding-left: 45px;
}

.leaflet__title-link--logo .leaflet__title-link-logo {
  position: absolute;
  display: block;
  width: 40px;
  max-height: 40px;
  height: auto;
  padding: 0px;
  border: 5px solid #fff;
  top: 3px;
  left: 0;
}

.leaflet__sub-title {
  position: relative;
  display: inline-block;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.25;
  text-align: left;
  z-index: 9;
  margin-bottom: 5px;
}

@media (min-width: 768px) {
  .leaflet__sub-title {
    font-size: 20px;
  }
}

.leaflet__sub-title a {
  font-weight: 400;
  color: #373737;
  font-size: 16px;
  line-height: 1.25;
  text-decoration: none;
}

@media (min-width: 768px) {
  .leaflet__sub-title a {
    font-size: 20px;
  }
}

.leaflet__sub-title a:hover {
  text-decoration: underline;
}

.leaflet__title-wrapper {
  position: relative;
  display: block;
  margin-top: 20px;
  margin-bottom: 10px;
}

.leaflet__title-wrapper:before, .leaflet__title-wrapper:after {
  content: ' ';
  display: table;
}

.leaflet__title-wrapper:after {
  clear: both;
}

.leaflet__title-wrapper--no-top {
  margin-top: 0;
}

.leaflet__title-wrapper--mt40 {
  margin-top: 40px;
}

.leaflet__title-subscribe {
  position: relative;
  display: inline-block;
  padding: 4px 10px;
  border-radius: 5px;
  background-color: #66b940;
  font-weight: 400;
  color: #fff;
  font-size: 12px;
  float: right;
}

.leaflet__title-subscribe:hover {
  color: #fff;
  background-color: #519333;
}

.leaflet__text {
  position: relative;
  display: block;
  font-weight: 300;
  font-size: 14px;
  text-align: center;
}

@media (min-width: 1180px) {
  .leaflet__text {
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    font-size: 16px;
  }
}

.leaflet__small {
  position: relative;
  display: inline-block;
  font-weight: 300;
  color: grey;
  font-size: 21px;
  margin: 0 10px;
}

.leaflet__line {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
  z-index: 9;
  text-align: left;
}

.leaflet__line:before, .leaflet__line:after {
  content: ' ';
  display: table;
}

.leaflet__line:after {
  clear: both;
}

.leaflet__back {
  position: relative;
  display: inline-block;
  width: 100%;
  font-weight: 400;
  color: #ee7836;
  text-align: center;
  text-decoration: underline;
  margin-right: 10px;
}

@media (min-width: 768px) {
  .leaflet__back {
    width: auto;
  }
}

.leaflet__back:hover {
  text-decoration: none;
}

.leaflet__back .fa {
  margin-right: 5px;
}

.leaflet__next {
  position: relative;
  display: inline-block;
  width: 100%;
  font-weight: 400;
  color: #ee7836;
  text-align: center;
  text-decoration: underline;
  margin-left: 10px;
}

@media (min-width: 768px) {
  .leaflet__next {
    width: auto;
  }
}

.leaflet__next:hover {
  text-decoration: none;
}

.leaflet__next .fa {
  margin-left: 5px;
}

.leaflet__link {
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: #373737;
  text-decoration: underline;
}

.leaflet__link:hover {
  color: #373737;
  text-decoration: none;
}

.leaflet__wrapper {
  position: relative;
  display: flex;
  z-index: 9;
  flex-direction: column;
}

@media (min-width: 1180px) {
  .leaflet__wrapper {
    flex-direction: row;
  }
}

.leaflet__content {
  position: relative;
  display: block;
  width: 100%;
  flex: 1 1 auto;
}

.leaflet__sidebar {
  position: relative;
  display: block;
  margin-top: 40px;
  text-align: center;
}

@media (min-width: 1024px) {
  .leaflet__sidebar {
    flex: 0 0 300px;
    margin: auto;
    margin-top: 0;
    margin-left: 30px;
  }
}

@media (min-width: 1024px) {
  .leaflet__sidebar--detail {
    flex: 1 1 auto;
    margin-left: auto;
  }
}

@media (min-width: 1180px) {
  .leaflet__sidebar--detail {
    flex: 0 0 300px;
    margin-left: 30px;
  }
}

.leaflet__content-ads {
  position: relative;
  display: block;
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.leaflet__content-ads img {
  position: relative;
  display: inline-block;
}

.leaflet__content-ads--inner-margin ins {
  margin: 0 10px;
}

.leaflet__ads-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  justify-content: center;
  overflow: hidden;
  margin-top: 0;
  margin-bottom: 24px;
}

@media (min-width: 768px) {
  .leaflet__ads-wrapper {
    justify-content: space-between;
  }
}

.leaflet__detail {
  position: relative;
  display: block;
  margin-bottom: 20px;
}

.leaflet__detail-header {
  position: relative;
  display: block;
  display: flex;
  align-items: center;
  z-index: 9;
  margin-top: 20px;
  flex-direction: column;
}

@media (min-width: 768px) {
  .leaflet__detail-header {
    margin-top: 40px;
    flex-direction: row;
  }
}

.leaflet__detail-header-content {
  position: relative;
  display: block;
  flex: 1 1 auto;
}

.leaflet__detail-header-side {
  position: relative;
  display: block;
  order: -1;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .leaflet__detail-header-side {
    flex: 0 0 160px;
    order: 0;
    margin-bottom: 0;
  }
}

.leaflet__detail-header-logo {
  position: relative;
  display: block;
  margin: auto;
  max-height: 60px;
  max-width: 80px;
}

.leaflet__detail-header .page-header__title,
.leaflet__detail-header .page-header__text,
.leaflet__detail-header .leaflet__date {
  text-align: center;
}

@media (min-width: 768px) {
  .leaflet__detail-header .page-header__title,
  .leaflet__detail-header .page-header__text,
  .leaflet__detail-header .leaflet__date {
    text-align: left;
  }
}

.leaflet__detail-header .page-header__title {
  margin-top: 0px;
}

.leaflet__detail--mTop {
  margin-top: 24px;
}

.leaflet__detail--noBottom {
  margin-bottom: 0;
}

.leaflet__paginator {
  margin: 8px 0 !important;
}

.leaflet__paginator .paginator-wrapper {
  width: 100%;
  justify-content: center;
}

.leaflet__paginator .paginator-w {
  position: relative;
}

.leaflet__detail img {
  position: relative;
  display: block;
  margin: auto;
  max-width: 100%;
}

.leaflet__date {
  display: block;
  font-weight: 700;
  color: #000000;
  font-size: 14px;
}

@media (min-width: 768px) {
  .leaflet__date {
    display: inline-block;
    font-size: 28px;
  }
}

.leaflet__content-text {
  position: relative;
  display: block;
}

.leaflet__content-text:before, .leaflet__content-text:after {
  content: ' ';
  display: table;
}

.leaflet__content-text:after {
  clear: both;
}

.leaflet__content-text p {
  position: relative;
  display: inline-block;
  font-weight: 400;
}

.leaflet__content-text strong {
  position: relative;
  display: inline-block;
  font-weight: 700;
}

.leaflet__content-text--left {
  float: left;
}

.leaflet__content-text--right {
  float: right;
}

.leaflet-preview {
  position: relative;
  width: 100%;
  max-width: 820px;
  margin: auto;
  overflow: hidden;
}

.leaflet-preview__ads {
  position: absolute;
  top: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.leaflet-preview.zoomed .zoom {
  display: block;
  z-index: 9;
}

.leaflet-preview img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.leaflet-preview .zoom {
  position: absolute;
  display: none;
  width: auto;
  max-width: none;
  top: 0;
  left: 0;
}

.leaflet-preview__link {
  position: relative;
  display: block;
  cursor: zoom-in;
}

.leaflet-adsbygoogle {
  margin: 20px 0;
}

.leaflet-adsbygoogle-square--tablet {
  display: none;
}

@media (min-width: 768px) {
  .leaflet-adsbygoogle-square--tablet {
    display: inline-block;
    width: 250px;
    height: 250px;
  }
}

.leaflet__sub-title.mb1 {
  margin-bottom: 1em;
}

.leaflet__detail-header--mobile-row {
  flex-direction: row;
  text-align: left;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.leaflet__detail-header--mobile-row .page-header__title {
  margin-bottom: 0.5rem;
}

.leaflet__detail-header--mobile-row .page-header__title,
.leaflet__detail-header--mobile-row .page-header__text,
.leaflet__detail-header--mobile-row .leaflet__date {
  text-align: left;
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-side {
  margin-bottom: 0;
  margin-right: 1rem;
}

@media (min-width: 768px) {
  .leaflet__detail-header--mobile-row .leaflet__detail-header-side {
    display: flex;
    justify-content: flex-end;
    margin-right: 0;
  }
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-logo {
  margin: 0;
}

.lf-n-layout {
  background-color: #fff;
}

.lf-n-layout .adsbygoogle {
  width: 100%;
}

.lf-n-layout .leaflet__ads-wrapper {
  margin-bottom: 1rem;
}

.lf-n-layout .leaflet__paginator.paginator-w {
  margin: 1rem !important;
}

.lf-n-layout .container {
  display: flex;
  flex-direction: column;
}

@media (min-width: 1024px) {
  .lf-n-layout .container {
    width: 995px;
    flex-direction: row;
  }
}

@media (min-width: 1200px) {
  .lf-n-layout .container {
    width: 1170px;
  }
}

@media (min-width: 1510px) {
  .lf-n-layout .container {
    width: 1470px;
	max-width: none;
  }
}

.lf-n-layout .leaflet__content {
  display: flex;
  flex-direction: column;
}

@media (min-width: 1200px) {
  .lf-n-layout .leaflet__content {
    flex-direction: row;
    width: calc(100% - 315px);
  }
}

.lf-n-layout .leaflet__aside {
  position: relative;
  display: block;
}

@media (min-width: 1200px) {
  .lf-n-layout .leaflet__aside {
    display: block;
    flex: 0 0 195px;
    width: 195px;
    order: -1;
    margin: 0;
    margin-right: 15px;
    padding-top: 2rem;
  }
}

@media (min-width: 1510px) {
  .lf-n-layout .leaflet__aside {
    flex: 0 0 270px;
    width: 270px;
  }
}

.lf-n-layout .leaflet__sidebar {
  margin-top: 0;
}

@media (min-width: 1024px) {
  .lf-n-layout .leaflet__sidebar {
    margin-left: 15px;
    flex: 0 0 160px;
    width: 160px;
    padding-top: 2rem;
  }
}

@media (min-width: 1200px) {
  .lf-n-layout .leaflet__sidebar {
    flex: 0 0 300px;
    width: 300px;
  }
}

.lf-n-layout .leaflet__detail-header {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  .lf-n-layout .leaflet__detail-header {
    margin-top: 2rem;
  }
}

.lf-n-layout .page-header__title {
  font-size: 16px;
  font-weight: 400;
}

@media (min-width: 768px) {
  .lf-n-layout .page-header__title {
    font-size: 24px;
  }
}

.lf-n-layout .page-header__text {
  margin-bottom: 0;
  font-size: 12px;
}

@media (min-width: 768px) {
  .lf-n-layout .page-header__text {
    font-size: 14px;
  }
}

.lf-n-layout .leaflet__date {
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
}

@media (min-width: 768px) {
  .lf-n-layout .leaflet__date {
    font-size: 28px;
  }
}

.lf__box {
  position: relative;
  display: block;
  text-align: left;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .lf__box {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  .lf__box-lg-border {
    border: 0;
    padding: 0rem;
  }
}

@media (min-width: 1200px) {
  .lf__box-lg-border {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

.lf__box-title {
  font-size: 14px;
  margin-bottom: 1rem;
}

@media (min-width: 1200px) {
  .lf__box-title {
    font-size: 18px;
  }
}

.lf__box-item {
  position: relative;
  display: flex;
  text-align: center;
}

@media (min-width: 1200px) {
  .lf__box-item {
    text-align: left;
  }
}

.lf__box-item:last-child {
  margin-bottom: 0;
}

.lf__box-image-wrapper {
  position: relative;
  display: block;
  flex: 0 0 60px;
  height: auto;
}

.lf__box-image--medium {
  flex: 0 0 100px;
}

.lf-n-layout .paginator-w .btn {
  color: #373737;
  background-color: transparent;
  border: 0px;
}

@media (min-width: 1200px) {
  .lf-n-layout .paginator-w .btn:hover {
    background-color: #ddd;
  }
}

.lf-n-layout .paginator-w ul li a {
  border: 1px solid #d9d9d9;
}

.lf-n-layout .paginator-w ul li.disabled {
  margin-right: 5px;
}

.lf-n-layout .leaflet-preview {
  max-width: 870px;
}

.lf-n-layout .leaflet-list-compact__item {
  border-color: #d9d9d9;
}

.lf-n-layout .leaflet__back,
.lf-n-layout .leaflet__next {
  color: #878787;
}

.lf-n-layout--header {
  position: relative;
}

.lf-n-layout--header .navbar-secondary {
  position: relative;
  top: 0;
}

.lf-n-layout--main {
  padding-top: 0;
}

.leaflet-search__wrapper {
  position: relative;
  display: none;
  margin-bottom: 40px;
}

@media (min-width: 1180px) {
  .leaflet-search__wrapper {
    max-width: 90%;
  }
}

.leaflet-search__wrapper--show {
  display: block;
}

.leaflet-search:before {
  position: absolute;
  display: block;
  content: "\f002";
  font-family: FontAwesome;
  color: #adadad;
  top: 16px;
  left: 16px;
  z-index: 9;
}

.leaflet-search {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
}

@media (min-width: 768px) {
  .leaflet-search {
    flex-direction: row;
  }
}

.leaflet-search__input {
  position: relative;
  display: block;
  flex: 1 1 auto;
  width: 100%;
  height: 45px;
  font-weight: 400;
  font-size: 12px;
  color: #000;
  margin: 0;
  padding: 5px 12px;
  border-radius: 5px;
  border: 1px solid #ddd;
  background-color: #fff;
  outline: none;
  margin-bottom: 10px;
  text-align: center;
  padding-left: 44px;
}

@media (min-width: 768px) {
  .leaflet-search__input {
    font-size: 15px;
    margin-bottom: 0px;
    text-align: left;
    padding-right: 40px;
  }
}

.leaflet-search__icon-wrapper {
  position: relative;
  display: block;
  width: 100%;
  height: 45px;
  padding: 0px 20px;
  background-color: #66b940;
  border-radius: 5px;
}

@media (min-width: 768px) {
  .leaflet-search__icon-wrapper {
    border-radius: 0 5px 5px 0;
    flex: 0 0 140px;
  }
}

.leaflet-search__icon {
  position: absolute;
  display: none;
  color: #fff;
  line-height: 45px;
  z-index: 9;
}

@media (min-width: 768px) {
  .leaflet-search__icon {
    display: block;
  }
}

.leaflet-search__icon-wrapper input[type="submit"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  border: 0;
  background-color: transparent;
  font-weight: 400;
  font-size: 12px;
  color: #fff;
  text-align: center;
}

@media (min-width: 768px) {
  .leaflet-search__icon-wrapper input[type="submit"] {
    font-size: 15px;
    padding-left: 43px;
    text-align: left;
  }
}

.leaflet-search__icon-wrapper:hover {
  color: #fff;
  background-color: #519333;
}

.leaflet-result {
  position: relative;
  display: block;
}

.leaflet-result .algolia-autocomplete {
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
}

.leaflet-result .aa-dropdown-menu {
  position: relative !important;
  display: block !important;
  top: 0 !important;
  border: 0 !important;
  min-width: 0 !important;
  background-color: transparent;
}

.leaflet-result .aa-suggestion {
  position: relative;
  display: inline-block;
  float: left;
}

.leaflet-result .aa-suggestion:before, .leaflet-result .aa-suggestion:after {
  content: ' ';
  display: table;
}

.leaflet-result .aa-suggestion:after {
  clear: both;
}

.leaflet-result .aa-suggestion {
  width: calc((100% - 20px) / 2);
  margin-right: 20px;
}

@media (min-width: 768px) {
  .leaflet-result .aa-suggestion {
    width: calc((100% - 60px) / 4);
  }
}

.leaflet-result .aa-dropdown-menu .aa-dataset-shop .aa-suggestion:nth-child(2n) {
  margin-right: 0;
}

@media (min-width: 768px) {
  .leaflet-result .aa-dropdown-menu .aa-dataset-shop .aa-suggestion:nth-child(2n) {
    margin-right: 20px;
  }
}

.leaflet-result .aa-dropdown-menu .aa-dataset-shop .aa-suggestion:nth-child(4n),
.leaflet-result .aa-dropdown-menu .aa-dataset-region .aa-suggestion:nth-child(4n) {
  margin-right: 0;
}

.leaflet-result .leaflet-list__item {
  width: 100%;
  margin-right: 0;
}

.leaflet-search__query-list {
  position: relative;
  display: block;
  text-align: left;
  margin-top: 10px;
  margin-bottom: 20px;
  padding-left: 10px;
  padding-right: 10px;
}

.leaflet-search__query-list:before, .leaflet-search__query-list:after {
  content: ' ';
  display: table;
}

.leaflet-search__query-list:after {
  clear: both;
}

.leaflet-search__query-list-item {
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: #888888;
  text-decoration: underline;
  padding-left: 12px;
  margin-bottom: 10px;
}

.leaflet-search__query-list-item:hover {
  text-decoration: none;
}

.leaflet-search__query-list-item:before {
  position: absolute;
  content: "\f041";
  font-family: FontAwesome;
  left: 0;
  top: 3px;
}

.leaflet-search__or {
  position: relative;
  display: block;
  font-weight: 300;
  font-size: 12px;
  padding: 0 10px;
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  .leaflet-search__or {
    margin-bottom: 0;
  }
}

.leaflet-search__button {
  position: relative;
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  height: 45px;
  color: #ee7836;
  background-color: #fff;
  font-weight: 600;
  color: #ee7836;
  font-size: 14px;
  text-align: center;
  padding: 0 10px;
  border: 0;
  border: 1px solid #ee7836;
  border-radius: 5px;
  padding-left: 45px;
  text-align: left;
}

.leaflet-search__button:hover {
  color: #fff;
  background-color: #ee7836;
}

.leaflet-search__button:hover:before {
  color: #fff;
}

.leaflet-search__button:before {
  position: absolute;
  display: block;
  content: "\f278";
  font-family: FontAwesome;
  color: #ee7836;
  top: 50%;
  margin-top: -7px;
  left: 16px;
  z-index: 9;
}

.leaflet-list__wrapper {
  position: relative;
  display: block;
  z-index: 9;
}

.leaflet-list__wrapper:before, .leaflet-list__wrapper:after {
  content: ' ';
  display: table;
}

.leaflet-list__wrapper:after {
  clear: both;
}

.leaflet-list__wrapper--margin {
  margin-top: 40px;
}

.leaflet-list__wrapper--right {
  text-align: right;
}

.leaflet-list__item {
  position: relative;
  display: block;
  width: calc((100% - 20px) / 2);
  margin-right: 20px;
  margin-bottom: 20px;
  float: left;
  border: 1px solid #fff;
}

@media (min-width: 768px) {
  .leaflet-list__item {
    width: calc((100% - 40px) / 3);
  }
}

.leaflet-list__item:hover {
  border: 1px solid #66b940;
}

.leaflet-list__item:nth-child(2n) {
  margin-right: 0;
}

@media (min-width: 768px) {
  .leaflet-list__item:nth-child(2n) {
    margin-right: 20px;
  }
}

@media (min-width: 768px) {
  .leaflet-list__item:nth-child(3n) {
    margin-right: 0;
  }
}

.leaflet-list__image {
  position: relative;
  display: block;
  height: 130px;
  background-color: #fff;
  overflow: hidden;
}

@media (min-width: 768px) {
  .leaflet-list__image {
    height: 220px;
  }
}

@media (min-width: 1024px) {
  .leaflet-list__image {
    height: 260px;
  }
}

.leaflet-list__image img {
  position: relative;
  display: block;
  width: 100%;
}

.leaflet-list__content {
  position: relative;
  display: block;
  padding-top: 9px;
  padding-bottom: 8px;
  background: #fff;
  text-align: center;
}

.leaflet-list__item-logo {
  position: absolute;
  display: flex;
  align-items: center;
  width: 60px;
  height: 50px;
  top: -45px;
  left: 50%;
  margin-left: -30px;
  background: #fff;
}

.leaflet-list__item-logo-image {
  position: relative;
  display: block;
  width: 100%;
}

.leaflet-list__item-logo img {
  position: relative;
  display: block;
  max-width: 45px;
  max-height: 35px;
  margin: auto;
}

.leaflet-list__item-title {
  position: relative;
  display: block;
  font-weight: 700;
  color: #000;
  font-size: 14px;
  text-align: center;
  width: 90%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  margin: auto;
  padding-top: 0px;
  margin-bottom: 5px;
}

.leaflet-list__item-small {
  position: relative;
  display: block;
  margin: auto;
  font-weight: 300;
  color: grey;
  font-size: 12px;
}

.leaflet-list__item-small strong {
  font-weight: 400;
  color: #000;
}

.leaflet-list__more-link {
  position: relative;
  display: inline-block;
  text-decoration: none;
  border: 1px solid;
  margin-top: 10px;
  padding: 3px 10px;
  border-radius: 5px;
  font-size: 12px;
}

.leaflet-list__more-link:hover {
  text-decoration: none;
  background-color: #ee7836;
  color: #fff;
}

.leaflet-list__item-corner {
  position: absolute;
  display: inline-block;
  top: 10px;
  right: 0;
  padding: 3px 40px;
  background-color: #ee7836;
  z-index: 9;
  max-width: 90%;
}

.leaflet-list__item-corner--red {
  background-color: #f44336;
}

.leaflet-list__item-corner--green {
  background-color: #66b940;
}

.leaflet-list__item-corner span {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 600;
  color: #fff;
  font-size: 14px;
  line-height: 21px;
}

.leaflet-list__wrapper--expire .leaflet-list__image {
  opacity: 0.4;
}

.leaflet-list__wrapper--5 {
  width: 100%;
  margin-right: 0;
  float: none;
}

.leaflet-list__wrapper--5 .leaflet-list__item {
  width: calc((100% - 80px) / 5);
}

.leaflet-list__wrapper--5 .leaflet-list__item:nth-child(4n) {
  margin-right: 20px;
}

.leaflet-list__wrapper--5 .leaflet-list__item:nth-child(5n) {
  margin-right: 0px;
}

.leaflet-list-compact__wrapper {
  position: relative;
  display: block;
  z-index: 9;
  margin-bottom: -20px;
}

.leaflet-list-compact__wrapper:before, .leaflet-list-compact__wrapper:after {
  content: ' ';
  display: table;
}

.leaflet-list-compact__wrapper:after {
  clear: both;
}

.leaflet-list-compact__item {
  position: relative;
  display: block;
  width: calc((100% - 20px) / 2);
  margin-right: 20px;
  margin-bottom: 20px;
  float: left;
  border: 1px solid #fff;
}

@media (min-width: 768px) {
  .leaflet-list-compact__item {
    width: calc((100% - 100px) / 6);
  }
}

.leaflet-list-compact__item:hover {
  border: 1px solid #66b940;
}

.leaflet-list-compact__item:nth-child(2n) {
  margin-right: 0;
}

@media (min-width: 768px) {
  .leaflet-list-compact__item:nth-child(2n) {
    margin-right: 20px;
  }
}

@media (min-width: 768px) {
  .leaflet-list-compact__item:nth-child(6n) {
    margin-right: 0;
  }
}

.leaflet-list-compact__content {
  position: relative;
  display: block;
  padding-top: 0px;
  padding-bottom: 10px;
  background: #fff;
  text-align: center;
}

.leaflet-list-compact__item-logo {
  position: relative;
  display: table;
  table-layout: fixed;
  width: 100%;
  height: 100px;
  background: #fff;
}

.leaflet-list-compact__item-logo-cell {
  position: relative;
  display: table-cell;
  width: 100%;
  vertical-align: middle;
}

.leaflet-list-compact__item-logo img {
  position: relative;
  display: block;
  max-width: 60px;
  max-height: 60px;
  margin: auto;
}

.leaflet-list-compact__item-title {
  position: relative;
  display: block;
  margin: auto;
  font-weight: 700;
  color: #000;
  font-size: 12px;
  text-align: center;
  width: 90%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.leaflet-list-compact__wrapper--search .leaflet-list-compact__item {
  position: relative;
  display: block;
  width: 100%;
  margin-right: 0;
  float: none;
}

.leaflet-category {
  position: relative;
  display: block;
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px;
}

.leaflet-category:before, .leaflet-category:after {
  content: ' ';
  display: table;
}

.leaflet-category:after {
  clear: both;
}

.leaflet-category__title {
  position: relative;
  display: inline-block;
  font-weight: 700;
  font-size: 12px;
  margin-right: 5px;
}

.leaflet-category__item {
  position: relative;
  display: inline-block;
  padding: 3px 10px;
  border-radius: 5px;
  background-color: rgba(200, 200, 200, 0.25);
  font-weight: 400;
  color: #373737;
  font-size: 12px;
}

.leaflet-category__item:hover {
  background-color: #de5c13;
  color: #fff;
}

.leaflet-location {
  position: relative;
  display: none;
  background: #e7e7e7;
  padding: 30px;
  margin-bottom: 20px;
}

.leaflet-location--show {
  display: block;
}

.leaflet-location__title {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 400;
  font-size: 21px;
}

.leaflet-location__search-wrapper {
  position: relative;
  display: block;
  margin-top: 20px;
  margin-bottom: 20px;
}

.leaflet-location__or {
  position: relative;
  display: block;
  text-align: center;
  margin-bottom: 20px;
}

.leaflet-location__or:before {
  position: absolute;
  display: block;
  content: "";
  width: 80%;
  height: 1px;
  margin: auto;
  top: 10px;
  left: 10%;
  background-color: #888;
}

.leaflet-location__or-text {
  position: relative;
  display: inline-block;
  background: #e7e7e7;
  padding: 0 30px;
}

.leaflet-location__search {
  position: relative;
  display: block;
}

@media (min-width: 768px) {
  .leaflet-location__search {
    width: 450px;
    margin: auto;
  }
}

.leaflet-location__input {
  position: relative;
  display: block;
  width: 100%;
  height: 45px;
  font-weight: 400;
  font-size: 12px;
  color: #373737;
  margin: 0;
  padding: 5px 12px;
  padding-right: 40px;
  border-radius: 5px;
  border: 1px solid #ddd;
  background-color: #fff;
  outline: none;
}

@media (min-width: 768px) {
  .leaflet-location__input {
    font-size: 15px;
  }
}

.leaflet-location__icon {
  position: absolute;
  display: block;
  z-index: 9;
  top: 0;
  right: 0;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  color: #6b6b6b;
}

.leaflet-location__icon input[type="submit"] {
  position: absolute;
  top: 0;
  right: 0;
  width: 34px;
  height: 45px;
  padding: 0;
  background-color: transparent;
  border: 0;
  z-index: 99;
}

.leaflet-location__icon:hover {
  color: #66b940;
}

.leaflet-location__form {
  position: relative;
  display: block;
}

.leaflet-location__wrapper {
  position: relative;
  display: block;
}

.leaflet-location__wrapper:before, .leaflet-location__wrapper:after {
  content: ' ';
  display: table;
}

.leaflet-location__wrapper:after {
  clear: both;
}

.leaflet-location__wrapper--center {
  text-align: center;
}

.leaflet-location__row {
  position: relative;
  display: block;
  float: left;
  width: calc(100% / 3);
  padding: 0 8%;
}

.leaflet-location__button {
  position: relative;
  display: inline-block;
  font-weight: 400;
  font-size: 14px;
  color: #fff;
  line-height: 18px;
  border-radius: 5px;
  padding: 10px 20px;
  background: #66b940;
  border-color: #66b940;
  margin: 0 10px;
  margin-top: 20px;
}

.leaflet-location__button:hover {
  background: #519333;
  color: #fff;
}

.leaflet-location__button--grey {
  background: #888888;
}

.leaflet-location__button--grey:hover {
  background: #6f6f6f;
}

.leaflet-location__form input[type="radio"] {
  display: none;
}

.leaflet-location__form input[type="radio"] + label {
  position: relative;
  display: block;
  padding-left: 34px;
  background-image: url("../../images/checkbox.png");
  background-size: 24px 24px;
  background-position: 0px 50%;
  background-repeat: no-repeat;
  line-height: 24px;
  cursor: pointer;
}

.leaflet-location__form input[type="radio"]:checked + label {
  background-image: url("../../images/checkbox-active.png");
  background-size: 24px 24px;
  background-position: 0px 50%;
  background-repeat: no-repeat;
}

.leaflet-branch__wrapper {
  position: relative;
  display: block;
}

.leaflet-branch {
  position: relative;
  display: block;
  width: 100%;
  height: 40px;
  table-layout: fixed;
  margin-bottom: 5px;
  background-color: #fff;
  padding: 0 20px;
  padding-left: 10px;
}

@media (min-width: 768px) {
  .leaflet-branch {
    display: table;
  }
}

.leaflet-branch__image {
  position: relative;
  display: block;
  width: 100%;
  height: 40px;
  vertical-align: middle;
}

@media (min-width: 768px) {
  .leaflet-branch__image {
    display: table-cell;
    width: 30px;
  }
}

.leaflet-branch__image img {
  position: relative;
  display: block;
  margin: auto;
  max-width: 30px;
  max-height: 30px;
}

.leaflet-branch__content {
  position: relative;
  display: block;
  width: 100%;
  vertical-align: middle;
  padding-left: 10px;
  padding-right: 10px;
  font-weight: 600;
  color: #888888;
  font-size: 10px;
  text-decoration: none;
  text-align: center;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .leaflet-branch__content {
    display: table-cell;
    font-size: 14px;
    text-align: left;
    margin-bottom: 0;
  }
}

.leaflet-branch__content a {
  position: relative;
  display: block;
  font-weight: 600;
  color: #000;
  font-size: 10px;
  text-align: center;
}

@media (min-width: 768px) {
  .leaflet-branch__content a {
    font-size: 14px;
    text-align: left;
  }
}

.leaflet-branch__content small {
  position: relative;
  display: inline-block;
  font-weight: 400;
  color: #888888;
  font-size: 10px;
}

.leaflet-branch__link {
  position: relative;
  display: block;
  width: 100%;
  vertical-align: middle;
  text-align: center;
  font-weight: 400;
  color: #888888;
  font-size: 10px;
}

@media (min-width: 768px) {
  .leaflet-branch__link {
    display: table-cell;
    width: 120px;
    font-size: 12px;
    text-align: right;
  }
}

.leaflet-branch__link:hover {
  color: #ee7836;
}

.leaflet-branch__link .fa {
  color: #ddd;
  margin-left: 5px;
}

.leaflet-branch__more-link {
  position: relative;
  display: inline-block;
  float: right;
  font-weight: 400;
  color: #ee7836;
  font-size: 12px;
  text-decoration: none;
  margin-top: 5px;
}

.leaflet-branch__more-link:hover {
  text-decoration: underline;
}

.leaflet-box {
  position: relative;
  display: block;
  padding: 20px;
  background-color: #fff;
  margin-bottom: 20px;
}

.leaflet-box__title {
  position: relative;
  display: block;
  font-weight: 600;
  font-size: 18px;
  color: #000;
  text-align: left;
  margin-bottom: 9px;
}

.leaflet-box__text {
  position: relative;
  display: block;
  font-weight: 400;
  font-size: 14px;
  color: #888;
  text-align: left;
  margin-bottom: 14px;
}

.leaflet-box__text:last-child {
  margin-bottom: 0;
}

.leaflet-box__text a {
  font-weight: 400;
  color: #ee7836;
  text-decoration: underline;
}

.leaflet-box__text a:hover {
  color: #ee7836;
  text-decoration: none;
}

.leaflet-box__map {
  position: relative;
  display: block;
  margin-top: -20px;
  margin-bottom: 20px;
}

.leaflet-box__map iframe {
  position: relative;
  display: block;
  width: 100%;
}

.leaflet-box__link {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 400;
  color: #ee7836;
  text-decoration: underline;
  margin-bottom: 20px;
}

.leaflet-box__link:hover {
  text-decoration: none;
}

@media only screen and (min-width: 1510px) {
  .leaflet-layout .container {
    width: 1500px;
  }
}

.leaflet-layout .leaflet__sidebar {
  margin-left: 20px;
  padding-top: 30px;
}

.leaflet-layout__aside {
  display: block;
}

@media only screen and (min-width: 1510px) {
  .leaflet-layout__aside {
    flex: 0 0 300px;
    order: -1;
    padding-top: 30px;
    padding-right: 20px;
  }
}

.leaflet__wrapper-inner {
  flex-direction: column;
}

@media only screen and (min-width: 1510px) {
  .leaflet__wrapper-inner {
    flex-direction: row;
  }
}

.article-horizontal {
  position: relative;
  display: flex;
  background: #fff;
  padding: 1em;
  flex-direction: column;
}

@media (min-width: 768px) {
  .article-horizontal {
    flex-direction: row;
  }
}

.article-horizontal__image {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 0.5em;
}

@media (min-width: 768px) {
  .article-horizontal__image {
    width: 150px;
    flex: 0 0 150px;
    margin-bottom: 0;
  }
}

.article-horizontal__image img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
  max-width: auto;
}

.article-horizontal__content {
  position: relative;
  display: block;
  width: 100%;
  flex: 1 1 auto;
}

@media (min-width: 768px) {
  .article-horizontal__content {
    padding-left: 1em;
  }
}

.article-horizontal__title {
  font-size: 18px;
  margin-bottom: 0.5em;
}

.article-horizontal__title:hover {
  text-decoration: underline;
}

.article-horizontal__text {
  position: relative;
  display: block;
  font-weight: 400;
  font-size: 14px;
}
