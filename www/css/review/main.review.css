.review-list {
  position: relative;
  display: block;
  padding-top: 20px;
  padding-bottom: 40px;
  background-color: #fff;
  z-index: 9;
}

.review-list__wrapper {
  margin-top: 35px;
}

.review-list__item {
  position: relative;
  display: block;
  padding-left: 70px;
}

.review-list__user {
  position: absolute;
  display: block;
  top: 8px;
  left: 20px;
  font-size: 32px;
  color: #c6c6c6;
}

.review-list__user-image {
  position: absolute;
  display: block;
  width: 59px;
  height: 59px;
  top: 0px;
  left: 0px;
  overflow: hidden;
  border-radius: 50%;
}

.review-list__user-image img {
  position: relative;
  display: block;
  max-width: 100%;
  overflow: hidden;
  border-radius: 50%;
  border: 2px solid #fff;
}

.review-list__bubble {
  position: relative;
  display: block;
  padding: 15px 15px;
  margin-bottom: 30px;
  background-color: #f7f7f7;
}

@media (min-width: 768px) {
  .review-list__bubble {
    padding: 17px 33px;
  }
}

.review-list__bubble--with-image {
  padding-top: 100px;
}

@media (min-width: 768px) {
  .review-list__bubble--with-image {
    padding: 17px 33px;
    padding-left: 22%;
    min-height: 120px;
  }
}

.review-list__logo {
  position: absolute;
  display: block;
  width: 100%;
  height: 80px;
  left: 0;
  top: 0;
  background: #fff;
  border: 3px solid #f7f7f7;
}

@media (min-width: 768px) {
  .review-list__logo {
    width: 20%;
    height: 100%;
  }
}

.review-list__logo--image-wrapper {
  position: relative;
  display: block;
  top: 50%;
  transform: translate(0, -50%);
}

.review-list__logo img {
  position: relative;
  display: block;
  margin: auto;
  width: auto;
  max-width: 90%;
  max-height: 40px;
}

.review-list__line {
  position: relative;
  display: block;
  margin-bottom: 7px;
}

.review-list__line:before, .review-list__line:after {
  content: ' ';
  display: table;
}

.review-list__line:after {
  clear: both;
}

.review-list__name {
  position: relative;
  display: inline-block;
  margin-right: 10px;
  font-weight: 700;
  color: #000;
  font-size: 16px;
}

.review-list__date {
  position: relative;
  display: inline-block;
  font-weight: 300;
  color: #000;
  font-size: 12px;
}

.review-list__text {
  position: relative;
  display: block;
  font-weight: 300;
  color: #000;
  font-size: 13px;
  line-height: 1.5;
}

.review-list__star-wrapper {
  position: relative;
  display: block;
  width: 100%;
  text-align: left;
  margin: 0px 0;
}

@media (min-width: 768px) {
  .review-list__star-wrapper {
    display: inline-block;
    width: auto;
    float: right;
  }
}

.review-list__star-wrapper .fa {
  position: relative;
  display: inline-block;
  color: #e0bb26;
  font-size: 16px;
  margin: 0 3px;
}

.review-list-box {
  position: relative;
  display: none;
  border: 1px solid #dcdcdc;
  background-color: #f9f9f9;
  box-shadow: 0 2px 14px rgba(0, 0, 0, 0.1);
  margin-top: 105px;
}

@media (min-width: 1024px) {
  .review-list-box {
    display: block;
  }
}

.review-list-box--page {
  margin-top: 65px;
}

.review-list-box__logo {
  position: absolute;
  display: block;
  width: 100%;
  height: 60px;
  left: 0;
  top: -60px;
  background: #fff;
}

.review-list-box__logo img {
  position: relative;
  display: block;
  margin: auto;
  max-width: 70%;
  max-height: 80%;
  top: 50%;
  transform: translate(0, -50%);
}

.review-list-box__info {
  position: relative;
  display: block;
  padding: 15px;
  background-color: #3c3c3c;
}

.review-list-box__title {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-size: 24px;
}

.review-list-box__title--small {
  font-size: 20px;
}

.review-list-box__star-wrapper {
  position: relative;
  display: block;
  text-align: center;
  margin: 10px 0;
}

.review-list-box__star {
  position: relative;
  display: inline-block;
  color: #e0bb26;
  font-size: 18px;
  margin: 0 1px;
}

.review-list-box__value {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 700;
  color: #fff;
  font-size: 21px;
}

.review-list-box__small {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 400;
  color: #fff;
  font-size: 13px;
  margin-bottom: 0;
}

.review-list-box__content {
  position: relative;
  display: block;
  padding: 10px;
}

.review-list-box__link {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 600;
  color: #000;
  font-size: 15px;
}

.review-list-box__add-review {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 600;
  color: #5ca739;
  font-size: 15px;
}

.review-list-box__button {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 600;
  padding: 10px 20px;
  color: #fff;
  font-size: 15px;
  margin-top: 10px;
  margin-bottom: 0px;
  background-color: #66b940;
  border-radius: 5px;
  cursor: pointer;
}

.review-list-box__button:hover {
  color: #000;
  opacity: 0.8;
}

.review-list-box__button--orange {
  background-color: #ee7836;
  margin-top: 20px;
  color: #fff;
}

.review-list-box__button--orange:hover {
  color: #fff;
}

.review-list-box__button--no-top {
  margin-top: 0;
}
