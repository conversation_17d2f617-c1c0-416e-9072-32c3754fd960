.interaction-wrapper {
  position: fixed;
  height: 0px;
  bottom: 20px;
  right: 20px;
  z-index: 9;
  transition: all 0.5s ease;
}

.interaction-hide {
  display: none;
}

.interaction {
  position: relative;
  display: flex;
  align-items: center;
  top: 0;
  width: 280px;
  height: 86px;
  border: 1px solid #d7d7d7;
  border-radius: 15px;
  background-color: #fff;
  box-shadow: -1px 5px 30px rgba(0, 0, 0, 0.15);
  padding: 20px;
  margin-top: 10px;
  opacity: 0;
  background-image: url("../../images/interaction-bg.png");
  background-size: 82px 76px;
  background-position: 105% 25px;
  background-repeat: no-repeat;
  transition: all 1.5s ease;
}

@media (min-width: 768px) {
  .interaction {
    width: 350px;
  }
}

.interaction__close {
  position: absolute;
  display: block;
  width: 12px;
  height: 11px;
  top: 12px;
  right: 12px;
  cursor: pointer;
  background-image: url("../../images/interaction-close.png");
  background-size: 12px 11px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.interaction__close:hover {
  opacity: 0.5;
}

.interaction__image {
  position: relative;
  display: flex;
  flex: 0 0 100px;
  align-items: center;
}

.interaction__image--icon {
  height: 40px;
  background-image: url("../../images/interaction-bag.png");
  background-size: 37px 40px;
  background-position: 22% 50%;
  background-repeat: no-repeat;
}

.interaction__image img {
  position: relative;
  display: block;
  max-width: 70%;
}

.interaction__content {
  position: relative;
  display: block;
  flex: 1 1 auto;
  align-items: center;
  font-weight: 400;
  color: #212121;
  font-size: 15px;
  line-height: 1.5;
  text-align: left;
  margin-bottom: 0;
}

.interaction__content strong {
  font-weight: 700;
  color: #212121;
}

.ns-hide {
  animation-direction: reverse;
}

.ns-show {
  animation-name: animation;
  animation-duration: 2s;
  animation-timing-function: linear;
  opacity: 1;
}

/* Generated with Bounce.js. Edit at http://goo.gl/akZHSq */

@keyframes animation {
  0% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1000, 0, 0, 1);
  }
  1.3% {
    transform: matrix3d(1.83, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 790.068, 0, 0, 1);
  }
  2.55% {
    transform: matrix3d(2.015, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 609.326, 0, 0, 1);
  }
  4.1% {
    transform: matrix3d(1.864, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 419.708, 0, 0, 1);
  }
  5.71% {
    transform: matrix3d(1.583, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 265.321, 0, 0, 1);
  }
  8.11% {
    transform: matrix3d(1.234, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 105.491, 0, 0, 1);
  }
  8.81% {
    transform: matrix3d(1.166, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 72.799, 0, 0, 1);
  }
  11.96% {
    transform: matrix3d(1.01, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -16.084, 0, 0, 1);
  }
  12.11% {
    transform: matrix3d(1.007, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -18.434, 0, 0, 1);
  }
  15.07% {
    transform: matrix3d(0.985, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -42.205, 0, 0, 1);
  }
  16.12% {
    transform: matrix3d(0.986, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -43.356, 0, 0, 1);
  }
  27.23% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -7.839, 0, 0, 1);
  }
  27.58% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -7.069, 0, 0, 1);
  }
  38.34% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1.037, 0, 0, 1);
  }
  40.09% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.97, 0, 0, 1);
  }
  50% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.159, 0, 0, 1);
  }
  60.56% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.025, 0, 0, 1);
  }
  82.78% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.001, 0, 0, 1);
  }
  100% {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
  }
}
