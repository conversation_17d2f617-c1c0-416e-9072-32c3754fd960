.howItWorks-header {
  position: relative;
  display: block;
  border-bottom: 110px solid #ee7836;
  padding-top: 35px;
  background-color: #fff;
}

.howItWorks-header__iframe {
  position: relative;
  display: block;
  width: 280px;
  height: 174px;
  margin: auto;
  margin-bottom: -38px;
  background-image: url("../../../images//howItWorks/header-bg.png");
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

@media (min-width: 768px) {
  .howItWorks-header__iframe {
    width: 660px;
    height: 411px;
  }
}

@media (min-width: 1024px) {
  .howItWorks-header__iframe {
    width: 691px;
    height: 430px;
  }
}

.howItWorks-header__iframe iframe {
  max-width: 100%;
  height: 175px;
}

@media (min-width: 768px) {
  .howItWorks-header__iframe iframe {
    height: 431px;
  }
}

.howitworks-header__steps {
  margin-top: 55px;
}

.howitworks-header__image-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.howitworks-header__image {
  position: relative;
  display: block;
  width: 120px;
  max-width: 100%;
  height: auto;
  margin: auto;
}

@media (min-width: 1024px) {
  .howitworks-header__image {
    width: 229px;
  }
}

.howitworks-header__content {
  position: relative;
  display: block;
  text-align: center;
  margin-top: 25px;
}

h2.howitworks-header__title {
  position: relative;
  display: block;
  font-weight: 600;
  color: #262626;
  font-size: 16px;
  line-height: 1.25;
  text-transform: uppercase;
  margin-bottom: 8px;
}

p.howitworks-header__text {
  position: relative;
  display: block;
  font-weight: 300;
  color: #262626;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 0;
}

p.howitworks-header__text a {
  color: #ee7836;
}

p.howitworks-header__text a:hover {
  text-decoration: none;
}

.howitworks-content {
  position: relative;
  display: block;
  width: 100%;
  margin: auto;
  margin-top: 40px;
}

@media (min-width: 768px) {
  .howitworks-content {
    margin-top: 80px;
  }
}

@media (min-width: 1024px) {
  .howitworks-content {
    width: 76%;
  }
}

.howitworks-content__item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  flex-direction: column;
}

@media (min-width: 768px) {
  .howitworks-content__item {
    flex-direction: row;
  }
}

.howitworks-content__image-wrapper {
  position: relative;
  display: block;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .howitworks-content__image-wrapper {
    margin-bottom: 0;
  }
}

.howitworks-content__image {
  position: relative;
  display: block;
  width: 160px;
}

@media (min-width: 768px) {
  .howitworks-content__image {
    width: 229px;
  }
}

.howitworks-content__image-value {
  position: absolute;
  display: flex;
  justify-content: center;
  flex-direction: column;
  bottom: 0;
  right: 0;
  width: 70px;
  height: 70px;
  font-weight: 900;
  color: #fff;
  font-size: 16px;
  text-align: center;
}

@media (min-width: 768px) {
  .howitworks-content__image-value {
    width: 100px;
    height: 100px;
    font-size: 20px;
  }
}

.howitworks-content__image-value small {
  position: relative;
  display: block;
  font-weight: 400;
  color: #fff;
  font-size: 10px;
  text-transform: none;
}

@media (min-width: 768px) {
  .howitworks-content__image-value small {
    font-size: 14px;
  }
}

small.howitworks-content__image-value--small {
  font-size: 8px;
}

@media (min-width: 768px) {
  small.howitworks-content__image-value--small {
    font-size: 11px;
  }
}

.howitworks-content__content {
  position: relative;
  display: block;
  text-align: center;
}

@media (min-width: 768px) {
  .howitworks-content__content {
    padding-left: 21px;
    text-align: left;
  }
}

.howitworks-content__title {
  position: relative;
  display: block;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .howitworks-content__title {
    font-size: 16px;
  }
}

.howitworks-content__text {
  position: relative;
  display: block;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .howitworks-content__text {
    font-size: 14px;
  }
}

.howitworks-content__small {
  position: relative;
  display: block;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  font-style: italic;
}

@media (min-width: 768px) {
  .howitworks-content__small {
    font-size: 14px;
  }
}

.howitworks-header-n {
  position: relative;
  display: block;
  padding-top: 3rem;
  padding-bottom: 3rem;
  background: linear-gradient(132deg, #de5c13 0%, #f29865 82%, #de5c13 100%);
}

.howitworks-header-n__wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 768px) {
  .howitworks-header-n__wrapper {
    flex-direction: row;
  }
}

.howitworks-header-n__content {
  position: relative;
  display: block;
  color: #fff;
  text-align: center;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .howitworks-header-n__content {
    flex: 0 0 350px;
    text-align: left;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .howitworks-header-n__content {
    flex: 0 0 490px;
  }
}

@media (min-width: 1180px) {
  .howitworks-header-n__content {
    flex: 0 0 550px;
  }
}

.howitworks-header-n__sidebar {
  flex: 1 1 auto;
  margin-left: auto;
}

.howitworks-header-n__iframe {
  position: relative;
  display: flex;
}

.howitworks-header-n__iframe iframe {
  position: relative;
  max-width: 100%;
  margin-left: auto;
}

.howitworks-header-n__content a {
  color: #fff;
  text-decoration: underline;
}

.howitworks-header-n__content a:hover {
  text-decoration: none;
}

.howitworks-content-n {
  width: 100%;
}

@media (min-width: 768px) {
  .howitworks-content-n {
    margin-top: 40px;
  }
}

@media (min-width: 1024px) {
  .howitworks-content-n {
    display: flex;
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .howitworks-content-n .howitworks-content__item {
    flex-direction: column;
    flex: 1 1 33%;
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .howitworks-content-n .howitworks-content__content {
    text-align: center;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .howitworks-content-n .howitworks-content__title {
    margin-top: 2rem;
  }
}

.howitworks-header-n__wrapper--noSidebar {
  justify-content: center;
}

@media (min-width: 768px) {
  .howitworks-header-n__wrapper--noSidebar .howitworks-header-n__content {
    text-align: center;
  }
}

@media (min-width: 1180px) {
  .howitworks-header-n__wrapper--noSidebar .howitworks-header-n__content {
    flex: 0 0 700px;
  }
}

.review-bubble {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.review-bubble--grey .review-bubble__msg {
  background-color: #f9f9f9;
}

.review-bubble--mt {
  margin-top: 40px;
}

.review-bubble--grey .review-bubble__msg:before {
  background-image: url("../../../images/../images/homepage/grey-light.png");
  background-size: 15px 29px;
  background-position: 0px 0px;
  background-repeat: no-repeat;
}

.review-bubble__msg {
  position: relative;
  display: block;
  width: 70%;
  padding: 15px 20px;
  margin-right: 60px;
  margin-bottom: 60px;
  border-radius: 15px;
  background-color: #fff;
  font-weight: 400;
  color: #000;
  font-size: 12px;
  text-align: justify;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .review-bubble__msg {
    flex: 1 1 35%;
    max-width: 450px;
    margin-right: 85px;
  }
}

@media (min-width: 1024px) {
  .review-bubble__msg {
    font-size: 15px;
    padding: 25px 30px;
  }
}

.review-bubble__msg:before {
  position: absolute;
  display: block;
  content: "";
  width: 15px;
  height: 29px;
  right: -1px;
  bottom: -7px;
  background-image: url("../../../images/../images/homepage/white.png");
  background-size: 15px 29px;
  background-position: 0px 0px;
  background-repeat: no-repeat;
}

.review-bubble__msg-image {
  position: absolute;
  display: block;
  width: 40px;
  height: 40px;
  left: auto;
  right: -50px;
  bottom: -7px;
  border-radius: 50%;
}

@media (min-width: 768px) {
  .review-bubble__msg-image {
    width: 53px;
    height: 53px;
    right: -61px;
  }
}

.review-bubble__msg-name {
  position: absolute;
  display: block;
  left: auto;
  right: -80px;
  bottom: -30px;
  font-weight: 400;
  font-size: 13px;
  color: #000;
  text-align: center;
  width: 90%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 90px;
}

.howitworks--bg {
  min-height: 380px;
  background-image: url("../../../images//howItWorks/green-bg.jpg");
  background-size: cover;
  background-position: 50% 0%;
  background-repeat: no-repeat;
}

.howitworks, .landing-page {
  position: relative;
  display: block;
}

.howitworks h1, .landing-page h1, .howitworks h2, .landing-page h2 {
  margin-top: 60px;
  font-size: 2.45rem;
  margin-bottom: 13px;
  color: #66b940;
}

.howitworks h2, .landing-page h2 {
  margin-top: 0;
}

.howitworks h3, .landing-page h3 {
  font-weight: 600;
  color: #65b83f;
  font-size: 21px;
}

.howitworks p, .landing-page p {
  font-weight: 300;
  color: #464646;
  font-size: 15px;
}

.howitworks .how-work, .landing-page .how-work {
  padding-top: 17px;
  padding-bottom: 12px;
  text-align: center;
}

.howitworks .how-work h2, .landing-page .how-work h2 {
  font-weight: 700;
  font-size: 25px;
}

.howitworks .how-work strong, .landing-page .how-work strong {
  font-size: 14px;
}

.howitworks .how-work ul li p, .landing-page .how-work ul li p {
  font-size: 14px;
}

.howitworks .how-work .btn, .landing-page .how-work .btn {
  margin-top: 20px;
  margin-bottom: 40px;
  min-width: 240px;
  max-width: 90%;
  padding: 15px;
}

.howitworks .top-shop-w, .landing-page .top-shop-w {
  background-color: #f1f1f1;
}

.howitworks .top-shop-w h2, .landing-page .top-shop-w h2 {
  padding-top: 57px;
  font-size: 2.2rem;
  margin-bottom: 31px;
  color: #66b940;
}

.howitworks .top-shop-w .btn-w, .landing-page .top-shop-w .btn-w {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 40px;
}

.howitworks .top-shop-w .btn-w .btn-lg, .landing-page .top-shop-w .btn-w .btn-lg {
  font-size: 16px;
}

.howitworks .top-shop-w .top-shop, .landing-page .top-shop-w .top-shop {
  position: relative;
  width: 100%;
}

.howitworks .top-shop-w .top-shop .top-shop-content, .landing-page .top-shop-w .top-shop .top-shop-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid #c8c8c8;
  text-align: center;
}

.howitworks .top-shop-w .top-shop .top-shop-content .table-cell, .landing-page .top-shop-w .top-shop .top-shop-content .table-cell {
  vertical-align: middle;
}

.howitworks .top-shop-w .top-shop .top-shop-content .logo, .landing-page .top-shop-w .top-shop .top-shop-content .logo {
  max-width: 80%;
  max-height: 80%;
  margin: auto;
  padding: 10px 0;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back, .landing-page .top-shop-w .top-shop .top-shop-content .back {
  color: #000;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share {
  margin-top: 15px;
  color: #717171;
  /* text color */
  font-weight: 400;
  font-size: 18px;
  line-height: 23px;
  text-align: center;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share:before, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share:before, .howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share:after, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share:after {
  content: ' ';
  display: table;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share:after, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share:after {
  clear: both;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share ._upTo, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share ._upTo {
  display: inline-block;
  vertical-align: bottom;
  line-height: 33px;
  top: 4px;
  position: relative;
  margin-right: 4px;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share ._suffix, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share ._suffix {
  color: #727272;
  font-size: 15px;
  line-height: 16px;
  display: inline-block;
  width: 100%;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share ._value, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share ._value, .howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share ._symbol, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share ._symbol {
  color: #66b940;
  font-size: 24px;
  line-height: 33px;
  font-weight: 600;
  display: inline-block;
  margin-left: 4px;
}

.howitworks .top-shop-w .top-shop .top-shop-content .back .reward-share ._value, .landing-page .top-shop-w .top-shop .top-shop-content .back .reward-share ._value {
  margin-left: 0;
}

.howitworks .step-w, .landing-page .step-w {
  text-align: center;
}

.howitworks .step-w h2, .landing-page .step-w h2 {
  margin-top: 74px;
  font-size: 2.3rem;
  margin-bottom: 7px;
  text-align: left;
}

.howitworks .step-w p, .landing-page .step-w p {
  font-size: 17px;
  text-align: left;
  margin-bottom: 30px;
}

.howitworks .step-w .step, .landing-page .step-w .step {
  position: relative;
  display: block;
  table-layout: fixed;
  width: 100%;
  height: auto;
  margin-bottom: 20px;
  background-color: #f7f7f7;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step, .landing-page .step-w .step {
    display: table;
    height: 190px;
  }
}

.howitworks .step-w .step .left, .landing-page .step-w .step .left, .howitworks .step-w .step .center, .landing-page .step-w .step .center, .howitworks .step-w .step .right, .landing-page .step-w .step .right {
  position: relative;
  vertical-align: middle;
  display: block;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .left, .landing-page .step-w .step .left, .howitworks .step-w .step .center, .landing-page .step-w .step .center, .howitworks .step-w .step .right, .landing-page .step-w .step .right {
    display: table-cell;
  }
}

.howitworks .step-w .step .left, .landing-page .step-w .step .left {
  width: 100%;
  margin-bottom: 20px;
  padding: 20px;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .left, .landing-page .step-w .step .left {
    width: 144px;
    padding: 0;
    margin-bottom: 0;
  }
}

@media screen and (min-width: 1024px) {
  .howitworks .step-w .step .left, .landing-page .step-w .step .left {
    width: 194px;
  }
}

.howitworks .step-w .step .left .icon, .landing-page .step-w .step .left .icon {
  position: relative;
  display: block;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: auto;
  border: 2px solid #66b940;
}

.howitworks .step-w .step .left .icon i, .landing-page .step-w .step .left .icon i {
  text-align: center;
  color: #66b940;
  font-size: 32px;
  position: absolute;
  display: block;
  top: 50%;
  left: 50%;
  padding-top: 0;
  margin-top: -16px;
  margin-left: -16px;
}

.howitworks .step-w .step .center, .landing-page .step-w .step .center {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .center, .landing-page .step-w .step .center {
    padding-right: 40px;
    margin-bottom: 0;
    text-align: left;
  }
}

.howitworks .step-w .step .center .title, .landing-page .step-w .step .center .title {
  display: block;
  font-weight: 400;
  font-size: 23px;
  margin-bottom: 7px;
}

.howitworks .step-w .step .center p, .landing-page .step-w .step .center p {
  display: block;
  font-size: 14px;
  text-align: center;
  width: 90%;
  margin: auto;
  margin-bottom: 0;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .center p, .landing-page .step-w .step .center p {
    width: 100%;
    text-align: left;
  }
}

.howitworks .step-w .step .right, .landing-page .step-w .step .right {
  width: 100%;
  padding: 20px;
  background-color: #66b940;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .right, .landing-page .step-w .step .right {
    padding: 0;
    width: 261px;
  }
}

.howitworks .step-w .step .right .title, .landing-page .step-w .step .right .title {
  display: block;
  font-weight: 300;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  font-size: 16px;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .right .title, .landing-page .step-w .step .right .title {
    font-size: 18px;
  }
}

.howitworks .step-w .step .right .value, .landing-page .step-w .step .right .value {
  display: block;
  font-weight: 700;
  color: #fff;
  text-align: center;
  font-size: 30px;
}

@media screen and (min-width: 768px) {
  .howitworks .step-w .step .right .value, .landing-page .step-w .step .right .value {
    font-size: 42px;
  }
}

.howitworks .step-w .btn, .landing-page .step-w .btn {
  margin-top: 26px;
  margin-bottom: 120px;
  width: 240px;
  padding: 15px;
  background-color: #ee7836;
  border-color: #ee7836;
}

.howitworks .step-w .btn:hover, .landing-page .step-w .btn:hover {
  background-color: #de5c13;
  border-color: #ee7836;
}

.howitworks .two-column, .landing-page .two-column {
  position: relative;
  display: block;
  background-color: #f7f7f7;
  padding: 64px 0;
}

.howitworks .two-column:before, .landing-page .two-column:before, .howitworks .two-column:after, .landing-page .two-column:after {
  content: ' ';
  display: table;
}

.howitworks .two-column:after, .landing-page .two-column:after {
  clear: both;
}

.howitworks .two-column .column, .landing-page .two-column .column {
  position: relative;
  display: block;
  float: left;
  width: 100%;
  margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
  .howitworks .two-column .column, .landing-page .two-column .column {
    width: 50%;
    padding-right: 30px;
    margin-bottom: 0;
  }
  .howitworks .two-column .column:last-child, .landing-page .two-column .column:last-child {
    padding-left: 30px;
    padding-right: 0;
  }
}

.howitworks .faq-w h2, .landing-page .faq-w h2 {
  padding-top: 30px;
  margin-bottom: 20px;
  text-align: center;
}

@media screen and (min-width: 768px) {
  .howitworks .faq-w h2, .landing-page .faq-w h2 {
    padding-top: 60px;
    text-align: left;
  }
}

.howitworks .guarantee-w, .landing-page .guarantee-w {
  position: relative;
  display: block;
  width: 100%;
  height: auto;
  background-image: url("../../../images/../../../images/panel-garance.jpg");
  background-repeat: no-repeat;
  background-size: 1920px 100%;
  background-position: 50% 50%;
  margin-top: 80px;
  padding: 30px 0;
}

@media screen and (min-width: 768px) {
  .howitworks .guarantee-w, .landing-page .guarantee-w {
    background-size: 1920px 216px;
    height: 216px;
  }
}

.howitworks .guarantee-w h2, .landing-page .guarantee-w h2 {
  position: relative;
  display: block;
  font-weight: 600;
  color: #fff;
  text-align: center;
  line-height: 1.5;
  font-size: 20px;
  padding-top: 15px;
  margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
  .howitworks .guarantee-w h2, .landing-page .guarantee-w h2 {
    font-size: 40px;
    padding-top: 0;
  }
}

.howitworks .guarantee-w p, .landing-page .guarantee-w p {
  position: relative;
  display: block;
  font-weight: 300;
  color: #fff;
  font-size: 15px;
  text-align: center;
  line-height: 1.5;
  margin: auto;
  margin-bottom: 0;
  width: 90%;
}

@media screen and (min-width: 768px) {
  .howitworks .guarantee-w p, .landing-page .guarantee-w p {
    margin-top: 2px;
    width: 74%;
  }
}

.howitworks .testimonial-w, .landing-page .testimonial-w {
  position: relative;
  display: block;
  text-align: center;
  padding-bottom: 49px;
}

.howitworks .testimonial-w h2, .landing-page .testimonial-w h2 {
  position: relative;
  display: block;
  font-size: 22px;
  padding-top: 49px;
  margin-bottom: 30px;
  text-align: center;
}

@media screen and (min-width: 768px) {
  .howitworks .testimonial-w h2, .landing-page .testimonial-w h2 {
    text-align: left;
  }
}

.howitworks .testimonial-w .wrapper, .landing-page .testimonial-w .wrapper {
  position: relative;
  display: block;
}

.howitworks .testimonial-w .wrapper:before, .landing-page .testimonial-w .wrapper:before, .howitworks .testimonial-w .wrapper:after, .landing-page .testimonial-w .wrapper:after {
  content: ' ';
  display: table;
}

.howitworks .testimonial-w .wrapper:after, .landing-page .testimonial-w .wrapper:after {
  clear: both;
}

.howitworks .testimonial-w .wrapper .column, .landing-page .testimonial-w .wrapper .column {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 20px;
  min-height: 150px;
  float: left;
  background-color: #f7f7f7;
  padding: 14px 19px;
  margin-left: 0;
}

@media screen and (min-width: 768px) {
  .howitworks .testimonial-w .wrapper .column, .landing-page .testimonial-w .wrapper .column {
    width: calc(50% - 20px);
    margin-bottom: 0;
  }
  .howitworks .testimonial-w .wrapper .column:last-child, .landing-page .testimonial-w .wrapper .column:last-child {
    margin-left: 40px;
  }
}

.howitworks .testimonial-w .wrapper .column .name, .landing-page .testimonial-w .wrapper .column .name {
  position: relative;
  display: block;
  text-align: left;
  background-image: url("../../../images/../../../images/upperscope.png");
  background-repeat: no-repeat;
  background-size: 20px 17px;
  background-position: 0% 50%;
  font-weight: 600;
  color: #313131;
  padding-left: 24px;
  font-size: 20px;
}

.howitworks .testimonial-w .wrapper .column p, .landing-page .testimonial-w .wrapper .column p {
  font-weight: 300;
  color: #7d7d7d;
  line-height: 19px;
  font-size: 14px;
  margin-top: 5px;
  text-align: left;
}

.howitworks .testimonial-w .btn, .landing-page .testimonial-w .btn {
  position: relative;
  display: inline-block;
  margin: auto;
  margin-top: 56px;
  margin-bottom: 74px;
  font-size: 14px;
  width: 230px;
  padding: 10px;
}

.howitworks .start-save, .landing-page .start-save {
  position: relative;
  display: block;
  width: 100%;
  height: 258px;
  background-color: #65b93f;
  text-align: center;
}

.howitworks .start-save h2, .landing-page .start-save h2 {
  color: #fff;
  padding-top: 77px;
  margin-bottom: 22px;
  font-size: 23px;
}

@media screen and (min-width: 768px) {
  .howitworks .start-save h2, .landing-page .start-save h2 {
    font-size: 32px;
  }
}

.howitworks .start-save .btn, .landing-page .start-save .btn {
  width: 220px;
  padding: 11px;
  color: #fff;
}

.howitworks .start-save .btn:hover, .landing-page .start-save .btn:hover {
  color: #66b940;
  background-color: #fff;
}

.landing-page h2, .landing-page .testimonial-w h2, .landing-page .top-shop-w h2, .landing-page .step-w h2, .landing-page .example h2 {
  text-align: center;
  font-size: 3.2rem;
  margin-bottom: 4.2rem;
}

.landing-page .guarantee-w {
  margin-top: 0;
}

.landing-page .step-w p {
  text-align: center;
  margin-bottom: 5rem;
}

.landing-page .step-w .btn, .landing-page .top-shop-w .btn-w .btn-lg, .landing-page .start-save .btn {
  max-width: 90%;
  min-width: 280px;
  width: auto;
  font-size: 1.5rem;
  color: #fff;
  padding: 10px 30px;
}

@media screen and (min-width: 768px) {
  .landing-page .step-w .btn, .landing-page .top-shop-w .btn-w .btn-lg, .landing-page .start-save .btn {
    font-size: 2.2rem;
  }
}

.landing-page .wrapper {
  position: relative;
  display: block;
}

.landing-page .wrapper:before, .landing-page .wrapper:after {
  content: ' ';
  display: table;
}

.landing-page .wrapper:after {
  clear: both;
}

.landing-page .wrapper .column {
  position: relative;
  display: block;
  float: left;
  width: 50%;
}

.landing-page .wrapper img {
  position: relative;
  display: block;
  max-width: 100%;
  margin: auto;
}

.landing-page .logo-w {
  position: relative;
  display: table;
  table-layout: fixed;
  width: 100%;
  margin: auto;
  margin-left: 0;
  margin-bottom: 2rem;
}

.landing-page .logo-w .logo {
  position: relative;
  display: none;
  width: 250px;
  vertical-align: middle;
}

@media screen and (min-width: 768px) {
  .landing-page .logo-w .logo {
    display: table-cell;
  }
}

.landing-page .logo-w .logo img {
  position: relative;
  display: block;
  margin: auto;
  margin-left: 0;
  max-width: 90%;
  max-height: 90%;
}

.landing-page .logo-w h1 {
  position: relative;
  display: table-cell;
  vertical-align: middle;
}

.landing-page .two-column {
  background-color: #FFF;
}

.landing-page .two-column-text {
  position: relative;
  display: block;
  margin-top: 1.5rem;
  margin-bottom: 3.5rem;
}

.landing-page .two-column-text:before, .landing-page .two-column-text:after {
  content: ' ';
  display: table;
}

.landing-page .two-column-text:after {
  clear: both;
}

.landing-page .two-column-text h1 {
  font-size: 3rem;
  margin-top: 1rem;
}

.landing-page .two-column-text h1 strong {
  color: #519333;
}

@media screen and (min-width: 768px) {
  .landing-page .two-column-text h1 {
    font-size: 2.2rem;
  }
}

@media screen and (min-width: 1024px) {
  .landing-page .two-column-text h1 {
    font-size: 3rem;
  }
}

@media screen and (min-width: 1200px) {
  .landing-page .two-column-text h1 {
    font-size: 4rem;
  }
}

.landing-page .two-column-text p {
  font-size: 1.8rem;
  width: 95%;
  line-height: 1.7;
}

.landing-page .two-column-text ul {
  font-size: 1.8rem;
  width: 95%;
  line-height: 1.7;
  padding-left: 0;
  padding-left: 20px;
}

.landing-page .two-column-text ul li {
  margin-bottom: 1rem;
}

.landing-page .two-column-text ul li a {
  text-decoration: underline;
}

.landing-page .two-column-text ul li a:hover {
  text-decoration: none;
}

.landing-page .two-column-text .column {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 2rem;
}

.landing-page .two-column-text .column:before, .landing-page .two-column-text .column:after {
  content: ' ';
  display: table;
}

.landing-page .two-column-text .column:after {
  clear: both;
}

@media screen and (min-width: 768px) {
  .landing-page .two-column-text .column {
    width: 72%;
    margin-bottom: 0;
  }
  .landing-page .two-column-text .column:last-child {
    width: 28%;
  }
}

.landing-page .two-column-text .column .right {
  float: right;
}

.landing-page .two-column-text .column .product-card {
  display: block;
  float: left;
  border: 1px solid lightgray;
  border-radius: 5px;
  padding: 0px;
  margin: 10px;
  margin-top: 0;
  margin-bottom: 3rem;
  margin-right: 3rem;
  margin-left: 0;
}

.landing-page .two-column-text .column .product-card img {
  padding: 10px;
}

.landing-page .two-column-text .column .product-card.product-card-right {
  width: 100%;
  margin-bottom: 0;
  border-bottom: 0;
  border-radius: 10px 10px 0 0;
}

.landing-page .two-column-text .column .product-card.product-card-right img {
  display: block;
  margin: auto;
  width: 250px;
}

.landing-page .two-column-text .column .product-card.product-card-right .example span.green {
  background-color: #ee7836;
  right: 0;
  left: auto;
  font-weight: 600;
}

.landing-page .two-column-text .column .product-card.product-card-right .example span {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 35%;
  background-color: lightgray;
  padding: 5px;
  font-size: 1.8rem;
}

.landing-page .two-column-text .column .product-card.product-card-right .example span .small {
  position: relative;
  width: 100%;
  font-size: 14px;
  display: block;
  text-align: center;
  padding: 0;
  background-color: transparent;
}

.landing-page .two-column-text .column .example {
  position: relative;
  display: block;
  background-color: lightgray;
  padding: 0px;
}

.landing-page .two-column-text .column .example span {
  position: relative;
  display: block;
  text-align: center;
  font-weight: 300;
  font-size: 1.8rem;
  padding: 10px;
}

.landing-page .two-column-text .column .example span.green {
  font-weight: 400;
  color: #fff;
  font-size: 2.5rem;
  padding: 15px;
  background-color: #66b940;
}

.landing-page .time-title {
  font-size: 1.8rem !important;
  margin-top: 1.5rem;
  margin-bottom: 1rem !important;
}

.landing-page .time {
  position: relative;
  display: block;
  vertical-align: middle;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 20px;
  width: 100%;
  margin: 0;
}

.landing-page .time:before, .landing-page .time:after {
  content: ' ';
  display: table;
}

.landing-page .time:after {
  clear: both;
}

.landing-page .time .unit {
  position: relative;
  display: inline-block;
  width: 50px;
  background-color: white;
  margin: 0 5px;
  padding: 8px 0px;
  border: 1px solid #66b940;
}

.landing-page .time .unit .value {
  display: block;
  text-align: center;
  font-weight: 400;
  color: #373737;
  font-size: 22px;
}

.landing-page .time .unit .title {
  display: none;
  text-align: center;
  font-weight: 400;
  color: #373737;
  font-size: 14px;
}

.landing-page .divider {
  color: #fff;
}

.landing-page .form {
  position: relative;
  display: block;
  background-color: #66b940;
  border-radius: 5px;
  border-radius: 0px 0px 10px 10px;
  padding: 20px 20px;
  padding-top: 1.5rem;
  width: 100%;
  float: right;
}

.landing-page .form small {
  display: block;
  text-align: center;
  font-weight: 400;
  font-size: 1.6rem;
  color: #fff;
  margin-top: 2rem;
  border-top: 1px solid #fff;
  padding-top: 2.5rem;
  margin-top: 3rem;
}

.landing-page .form h2 {
  position: relative;
  display: block;
  color: #fff;
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 0rem;
  line-height: 1.5;
  font-weight: 400;
}

.landing-page .form h2 strong {
  font-weight: 700;
}

.landing-page .form p {
  position: relative;
  display: block;
  color: #fff;
  font-size: 1.5rem;
  text-align: center;
  width: 100%;
  margin: auto;
  line-height: 1.5;
}

.landing-page .form .btn-facebook {
  padding-left: 55px;
  text-align: center;
}

.landing-page .form .btn-facebook .fa-label {
  margin-left: 0;
  font-size: 1.5rem;
}

.landing-page .form .btn-facebook .fa-facebook {
  line-height: 45px !important;
  width: 42px;
  font-size: 1.1em;
}

.landing-page .form .or {
  position: relative;
  display: inline-block;
  width: 100%;
  text-align: center;
  line-height: 30px;
  font-size: 11px;
  color: #fff;
  margin: 0px 0;
}

.landing-page .form form {
  margin-top: 1.5rem;
}

.landing-page .form input {
  position: relative;
  display: block;
  width: 100%;
  border: 1px solid #b0b0b0;
  border-radius: 5px;
  background-color: #fff;
  padding: 16px 20px;
  height: 55px;
  text-align: center;
  font-size: 16px;
  margin-bottom: 10px;
}

.landing-page .form input[type="submit"] {
  margin-bottom: 0;
  background-color: #ee7836;
  font-weight: 700;
  color: #fff;
  font-size: 16px;
  line-height: 1;
}

.landing-page .form input[type="submit"]:hover {
  background-color: #de5c13;
}

.landing-page .example h2 {
  margin-top: 74px;
}

.landing-page .example p {
  font-size: 17px;
  text-align: center;
  margin-bottom: 5rem;
}

.landing-page .example .table {
  position: relative;
  display: block;
  table-layout: fixed;
  width: 100%;
  height: auto;
  margin-bottom: 20px;
  background-color: #f7f7f7;
}

@media screen and (min-width: 768px) {
  .landing-page .example .table {
    display: table;
    height: 190px;
  }
}

.landing-page .example .table .table-cell {
  position: relative;
  vertical-align: middle;
  display: block;
}

@media screen and (min-width: 768px) {
  .landing-page .example .table .table-cell {
    display: table-cell;
  }
}

.landing-page .example .table .table-cell:nth-child(1) {
  background-color: grey;
}

.landing-page .example .table .table-cell:nth-child(2) {
  background-color: #ee7836;
}

.landing-page .example .table .table-cell:nth-child(3) {
  background-color: #66b940;
}

.landing-page .example .table .title {
  display: block;
  font-weight: 300;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  font-size: 16px;
}

@media screen and (min-width: 768px) {
  .landing-page .example .table .title {
    font-size: 18px;
  }
}

.landing-page .example .table .value {
  display: block;
  font-weight: 700;
  color: #fff;
  text-align: center;
  font-size: 30px;
}

@media screen and (min-width: 768px) {
  .landing-page .example .table .value {
    font-size: 42px;
  }
}

.landing-page ul.step-number {
  position: relative;
  display: block;
  padding-left: 0;
  margin-bottom: 80px;
  padding-left: 0;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.landing-page ul.step-number li {
  position: relative;
  display: block;
  padding-left: 50px;
  margin-bottom: 1.5rem;
}

.landing-page ul.step-number li:nth-child(2) span {
  color: rgba(101, 184, 63, 0.7);
}

.landing-page ul.step-number li:nth-child(3) span {
  color: #65b83f;
}

.landing-page ul.step-number li span {
  position: absolute;
  display: block;
  width: 33px;
  top: 8px;
  left: 0;
  font-weight: 700;
  color: rgba(101, 184, 63, 0.5);
  font-size: 40px;
  line-height: 1;
  border-right: 1px solid #bfbfbf;
}

.landing-page ul.step-number li strong {
  display: block;
  font-weight: 700;
  color: #333;
}

.landing-page ul.step-number li p {
  font-weight: 300;
  color: #363636;
  font-size: 14px;
  margin-top: 0px;
  margin-bottom: 0;
}

.landing-page ul.step-number li p strong {
  display: inline-block;
}

.tier-stats-w {
  position: relative;
  display: block;
  background-color: #66b940;
  padding-bottom: 2.2rem;
}

.tier-stats-w .tab {
  position: relative;
  display: block;
}

.tier-stats-w h2 {
  color: #fff;
  margin-bottom: 0;
  margin-top: 4.2rem;
  text-align: center;
}

.tier-stats-w p {
  color: #fff;
  margin-top: 1rem;
  margin-bottom: 0rem;
  font-size: 1.8rem;
  text-align: center;
}

.tier-stats-w ul {
  position: relative;
  display: block;
  width: 100%;
  margin: auto;
  padding: 0;
  padding-top: 24px;
}

.tier-stats-w ul:before, .tier-stats-w ul:after {
  content: ' ';
  display: table;
}

.tier-stats-w ul:after {
  clear: both;
}

.tier-stats-w ul li {
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  width: 100%;
  float: left;
  padding-top: 130px;
  padding-bottom: 21px;
  margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
  .tier-stats-w ul li {
    width: 33.33333%;
  }
}

.tier-stats-w ul li i {
  position: absolute;
  display: block;
  top: 0;
  left: 50%;
  margin-left: -60px;
  line-height: 120px;
  font-size: 50px;
  width: 120px;
  height: 120px;
  color: #fff;
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
}

.tier-stats-w ul li i:before {
  color: #66b940;
}

.tier-stats-w ul li strong {
  display: inline-block;
  width: 100%;
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  text-align: center;
}

.tier-stats-w ul li p {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin-top: 0;
  text-align: center;
}

.tier-review-w {
  background-color: #f7f7f7;
}

.tier-review-w h2 {
  color: #66b940;
  margin-bottom: 0;
  margin-top: 4.2rem;
  text-align: center;
}

.tier-review-w ul {
  position: relative;
  display: block;
  width: 100%;
  margin: auto;
  padding: 0;
  padding-top: 47px;
}

.tier-review-w ul:before, .tier-review-w ul:after {
  content: ' ';
  display: table;
}

.tier-review-w ul:after {
  clear: both;
}

.tier-review-w ul li {
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  width: 100%;
  float: left;
  padding-top: 120px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 21px;
  margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
  .tier-review-w ul li {
    width: 33.33333%;
  }
}

.tier-review-w ul li .photo {
  position: absolute;
  display: block;
  top: 0;
  left: 50%;
  margin-left: -50px;
  width: 100px;
  height: 100px;
  color: #fff;
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
  border: 6px solid #ddd;
}

.tier-review-w ul li .photo:before {
  position: absolute;
  display: block;
  content: "";
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 15px solid #ddd;
  bottom: -17px;
  left: 50%;
  margin-left: -10px;
}

.tier-review-w ul li .photo img {
  max-width: 100%;
  border-radius: 50%;
}

.tier-review-w ul li strong {
  display: inline-block;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.tier-review-w ul li p {
  display: block;
  font-size: 14px;
  font-weight: 300;
  text-align: center;
}

.tier-review-w ul li .star-w {
  position: relative;
  display: block;
  text-align: center;
}

.tier-review-w ul li .star-w i {
  display: inline-block;
  text-align: center;
  font-size: 12px;
  color: #dddddd;
}

.tier-review-w ul li .star-w i.active {
  color: #ee7836;
}
