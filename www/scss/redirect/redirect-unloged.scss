@import "../vars";
@import "../tools";

@import "../staticPage/staticPage/static-page-content";
@import '../StaticPage/howItWorks/howItWorks-header';
@import '../components/social-login';

body {
    font-family: sans-serif;
    -webkit-font-smoothing: antialiased;
    @include light();
    margin: 0;
}

.redirect-unloged {
  position: relative;
  display: block;
  background-color: #f2f1f1;

  @include breakpoint($tablet) {
    min-height: 1024px;
  }

  @include breakpoint($tablet--land) {
    height: auto;
    min-height: 0;
  }
}

.redirect-unloged * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.redirect-unloged *:before, .redirect-unloged *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.redirect-unloged__container {
  position: relative;
  display: block;
  margin: auto;
  width: 90%;
  padding-bottom: 40px;

  @include breakpoint($tablet--land) {
    width: 943px;
  }

  @include clearfix;
}

.redirect-unloged__box {
  position: relative;
  display: block;
  margin-top: 25px;
  padding-bottom: 30px;
  background-color: #fff;
}

.redirect-unloged__logo {
  position: relative;
  display: block;
  padding-top: 15px;
  padding-bottom: 15px;

}

.redirect-unloged__logo img {
  position: relative;
  display: block;
  margin: auto;
  width: 200px;
  max-width: 90%;
}

.redirect-unloged__title-wrapper {
  position: relative;
  display: block;
  border-top: 2px solid #f1f1f1;
  border-bottom: 2px solid #f1f1f1;
  padding: 0 10px;
}

.redirect-unloged__title {
  position: relative;
  display: block;
  @include bold;
  color: #212121;
  font-size: 16px;
  text-align: center;
  margin-top: 20px;

  @include breakpoint($tablet) {
    font-size: 20px;
  }

  @include breakpoint($tablet--land) {
    font-size: 26px;
  }
}

.redirect-unloged__title strong {
    color: #ed7735;
}

.redirect-unloged__sub-title {
  position: relative;
  display: block;
  @include regular;
  color: #212121;
  font-size: 12px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 20px;

  @include breakpoint($tablet) {
    font-size: 16px;
  }

  @include breakpoint($tablet--land) {
    font-size: 23px;
  }
}

.redirect-unloged__sub-title strong {
  @include bold;
}

.redirect-unloged__wrapper {
  position: relative;
  display: block;
  width: 90%;
  margin: auto;
  border-bottom: 2px solid #f1f1f1;
  padding-top: 15px;
  padding-bottom: 15px;
  @include clearfix;

  @include breakpoint($tablet) {
    padding-top: 45px;
    padding-bottom: 45px;
  }

  @include breakpoint($tablet--land) {
    width: 72%;
  }
}

.redirect-unloged__column {
  position: relative;
  display: block;
  float: left;
  width: 100%;

  @include breakpoint($tablet) {
    width: 49%;
  }
}

.redirect-unloged__column:last-child {
  display: block;
  padding-top: 5%;

  @include breakpoint($tablet) {
    width: 51%;
    padding-left: 5%;
    padding-top: 0;
  }
}

.redirect-unloged__form {
  position: relative;
  display: block;
}


.redirect-unloged__input {
  position: relative;
  display: block;
  width: 100%;
  height: 53px;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
  background-color: #fff;
  padding: 0 16px;
  @include regular;
  color: #6e6e6e;
  font-size: 14px;
  text-align: center;
  margin-bottom: 15px;

  @include breakpoint($tablet--land) {
    font-size: 18px;
  }
}

.redirect-unloged__code {
    display: none;
    margin-top: 10px;
}

.redirect-unloged__code--show {
    display: block;
}

.redirect-unloged__checkbox-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding-top: 4px;
    padding-bottom: 9px;
}

.redirect-unloged__checkbox-wrapper--code {
    border-bottom: 1px solid #dedede;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.redirect-unloged__checkbox {
    position: absolute;
    display: none;
}

.redirect-unloged__label {
    @include regular;
    color: #5e5e5e;
    font-size: 12px;
    padding-top: 5px;
    padding-left: 33px;
    margin-bottom: 0;
    line-height: 21px;
    cursor: pointer;
    @include background("checkbox.png", 21px 21px, 0px 5px, $img-dir + "../../images/homepage/");

    @include breakpoint($tablet) {
        font-size: 14px;
    }
}

.redirect-unloged__checkbox:checked + label {
    @include background("checkbox-active.png", 30px 30px, -4px 0%, $img-dir + "../../images/homepage/");
}

.redirect-unloged__label a {
    color: #ef7b3b;
    text-decoration: underline;
}

.redirect-unloged__label a:hover {
    color: #ef7b3b;
    text-decoration: none;
}

.redirect-unloged__submit {
  position: relative;
  display: block;
  width: 100%;
  height: 52px;
  border-radius: 5px;
  background-color: #65b93f;
  border: 0;
  @include bold;
  color: #fff;
  font-size: 16px;
  text-align: center;
  margin-bottom: 5px;
}

.redirect-unloged__submit:hover {
  background-color: darken(#65b93f,10%);
}

.redirect-unloged__or {
  position: relative;
  display: block;
  @include light;
  color: #535353;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
}

.redirect-unloged__fb {
  position: relative;
  display: block;
  width: 100%;
  line-height: 55px;
  border-radius: 5px;
  background-color: #3b5997;
  padding-left: 60px;
  text-align: left;
  @include bold;
  color: #fff;
  font-size: 14px;
  text-decoration: none;
  margin-bottom: 10px;

  @include breakpoint($tablet) {
    font-size: 17px;
  }
}

.redirect-unloged__fb:hover {
  color: #fff;
  background-color: darken(#3b5997,10%);
}

.redirect-unloged__fb .fa{
  position: absolute;
  display: block;
  top: 18px;
  left: 22px;
  font-size: 22px;
}

.redirect-unloged__small-title {
  position: relative;
  display: block;
  @include bold;
  color: #212121;
  font-size: 25px;
  text-align: center;
  margin-top: 25px;
  margin-bottom: 0;
}

.redirect-unloged__text {
  position: relative;
  display: block;
  @include regular;
  color: #545454;
  font-size: 14px;
  text-align: center;
  margin-bottom: 0;
}

.redirect-unloged__login {
  position: relative;
  display: inline-block;
  @include bold;
  color: #ee7836;
  text-decoration: underline;
}

.redirect-unloged__login:hover {
  text-decoration: none;
  color: #ee7836;
}

.redirect-unloged__icon {
  position: relative;
  display: block;
  color: #ee7836;
  width: 190px;
  height: 222px;
  margin: auto;
  left: 13%;
  top: 50px;
}

.redirect-unloged__how-get {
  position: relative;
  display: inline-block;
  width: 100%;
  @include regular;
  color: #212121;
  font-size: 14px;
  margin-top: 7px;
  text-decoration: none;
  text-align: center;
  margin-bottom: 15px;

  @include breakpoint($tablet) {
    width: auto;
    text-align: left;
    float: left;
    margin-bottom: 0;
  }
}

.redirect-unloged__how-get:hover {
  text-decoration: underline;
  color: #ee7836;
}

.redirect-unloged__no-reward {
  position: relative;
  display: inline-block;
  width: 100%;
  @include regular;
  color: #212121;
  font-size: 14px;
  text-decoration: none;
  margin-top: 7px;
  text-align: center;

  @include breakpoint($tablet) {
    width: auto;
    text-align: right;
    float: right;
  }
}

.redirect-unloged__no-reward:hover {
  text-decoration: underline;
  color: #ee7836;
}

.redirect-unloged__no-reward strong {
  @include bold;
}

.redirect-unloged__error {
  position: relative;
  display: block;
  padding: 10px;
  background-color: #eb0132;
  border: 1px solid #eb0132;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(8, 8, 8, 0.1);
  margin-top: 0px;
  margin-bottom: 10px;

  @include regular;
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

.redirect-unloged__steps {
    margin: auto;
    margin-top: 15px;

    @include breakpoint($tablet) {
        margin-top: 40px;
    }

    @include breakpoint($tablet--land) {
        width: 96%;
    }
}

.redirect-unloged__steps .howitworks-header__image {
    @include breakpoint($tablet--land) {
        width: 165px;
    }
}

.redirect-unloged__steps .howitworks-header__content {
    margin-top: 15px;
}

.redirect-unloged__steps .howitworks-header__title {
    font-size: 14px;
}

.redirect-unloged__steps .howitworks-header__text {
    font-size: 12px;
}

.sign__condition {
    position: relative;
    display: block;
    @include regular;
    color: #9e9e9e;
    font-size: 12px;
    line-height: 1.5;
    text-align: left;
    margin: 15px 0;
}

.sign__condition a {
    color: #9e9e9e;
    text-decoration: underline;
}

.sign__condition a:hover {
    color: $color-orange;
    text-decoration: none;
}

.text-center {
    text-align: center;
}

.margin-bottom-05bs {
    margin-bottom: 0.5em;
}
