.filter-w {
  position: relative;
  display: block;
  margin-top: 18px;
  margin-bottom: 27px;
  z-index: 9;

  @include clearfix();
}

.filter {
  position: relative;
  display: block;

  @include clearfix();

  a {
    position: relative;
    display: inline-block;

    padding: 8px 20px;

    background-color: #fff; /* layer fill content */

    @include light();

    text-align: center;
    color: #1e1e1e; /* text color */
    font-size: 15px;

    text-decoration: none;

    width: 100%;
    margin-bottom: 5px;

    @media screen and (min-width: $min-sm) {
      width: auto;
      margin: 0 5px;

      float: left;

      &:first-child {
        margin-left: 0;
      }
    }

    &.active, &:hover {
      color: #fff;
      background-color: #686868; /* layer fill content */
    }

    &.orange {
      background-color: $color-orange;
      color: #fff;

      &:hover {
        background-color: darken($color-orange,10%);
      }
    }

  }
}
