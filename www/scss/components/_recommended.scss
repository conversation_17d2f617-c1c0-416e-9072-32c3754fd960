.recommended {
  position: relative;
  display: block;

  margin-top: 30px;
  padding: 18px 0;
  padding-bottom: 0;
  margin-bottom: 30px;

  background-color: #fff;

  margin-bottom: 30px;

  h3 {
    color: #000; /* text color */
    font-size: 18px;

    @include semibold();

    text-align: center;

  }

  .wrapper {
    position: relative;
    display: block;

    @include clearfix();

    .item {
      position: relative;
      display: block;

      float: left;

      width: 100%;
      height: 110px;

      margin-bottom: 0;

      background-color: #fff; /* layer fill content */
      box-shadow: 0 0 7px rgba(0,0,0,.09); /* drop shadow */

      border: 1px solid transparent;

      -webkit-transition: all 0.35s, -webkit-transform 0.35s;
      transition: all 0.35s, transform 0.35s;

      &:hover {
        //border: 1px solid $color-green;

        border-top: 1px solid $color-green;
        border-bottom: 1px solid $color-green;

        text-decoration: none;

        img {
          -webkit-transform: scale(0.9);
          transform: scale(0.9);
        }
      }

      .wrapper {
        position: relative;
        display: table;

        width: 100%;
        height: 110px;

        .table-cell {
          position: relative;
          display: table-cell;

          width: 50%;
          height: auto;

          vertical-align: middle;
        }
      }

      img {
        position: relative;
        display: block;

        margin: auto;

        max-width: 85%;
        //max-height: 85%;
        max-height: 80px;

        -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
        transition: opacity 0.35s, transform 0.35s;

        -webkit-transform: scale(0.8);
        transform: scale(0.8);

        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
      }

      .text {
        position: relative;
        display: block;

        //margin-top: $bs-padding;

        color: #717171; /* text color */
        @include regular();
        @include clearfix();

        font-size: 16px;
        line-height: 23px;
        text-align: left;

        margin-top: 0;
        //margin-bottom: 25px;

        text-decoration: none;

        &:hover {
          text-decoration: none;
        }

        ._upTo {
          //float: left;
          display: inline-block;
          vertical-align: bottom;
          line-height: 33px;
          top: 2px;
          position: relative;
          margin-right: 2px;
        }

        ._suffix {
          //color: #727272; /* text color */
          color: #727272;
          font-size: 14px;
          line-height: 16px;

          //float: left;

          display: inline-block;
          width: 100%;
        }

        ._value, ._symbol {
          color: $color-green;
          font-size: 21px;
          line-height: 33px;
          @include semibold();

          display: inline-block;
          margin-left: 2px;

          //float: left;
        }

        ._value {
          margin-left: 0;
        }
      }

    }

  }
}
