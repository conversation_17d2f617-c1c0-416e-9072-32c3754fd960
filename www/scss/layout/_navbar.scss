// Navbar
// -------------------------

$mobile-menu-height: 50px;

.header {
	position: fixed;
	width: 100%;
	top: 0px;
	z-index: 9999;
}

.header--addon {
	@include breakpoint($desktop) {
		position: relative;
	}
}

.header--addon .aa-dropdown-menu {
	@include breakpoint($desktop) {
		top: 220px;
	}
}

.header--addon .navbar-category,
.header--addon .navbar__inspiration {
	@include breakpoint($desktop) {
		position: absolute;
		top: 95px !important;
	}
}

.navbar {
	position: relative;
	display: block;
	width: 100%;
	height: auto;
	top: 0px;
	margin: 0;
	border: 0;
	border-bottom: 1px solid $box-light-border-color;
	background-color: #fafafa;
	z-index: 9;
}

.navbar__line {
	position: relative;
	display: flex;
	z-index: 5;
}

.navbar__line--tablet {
	display: none;

	@include breakpoint($tablet) {
		display: flex;
	}
}

.navbar__line--top {
	background-color: #fff;
	z-index: 99;

	@include breakpoint($tablet) {
		min-height: 50px;
		padding-top: 5px;
		padding-bottom: 4px;
		border-bottom: 1px solid #e5e5e5;
	}
}

.navbar__container {
	width: calc(100% - 30px);
	display: flex;
	align-items: center;
	margin: auto;

	@include breakpoint($tablet) {
		width: 95%;
		margin: auto;
	}

	@include breakpoint($desktop) {
		width: 1180px;
	}
}

.navbar__logo {
	position: relative;
	display: block;
	flex: 0 0 66px;
	height: 27px;
	//@include sprite-img($logo);
}

.navbar__logo svg {
	width: 66px;
	height: 27px;
}

.navbar__logo img {
	position: relative;
	display: block;
	max-width: 100%;
	height: auto;
}

.navbar__left {
	position: absolute;
	display: none;
	top: 50px;
	width: 100%;
	left: 0;
	padding: 10px 10px;
	background: #fff;
	border-bottom: 1px solid #c8c8c8;

	@include breakpoint($tablet) {
		position: relative;
		display: flex;
		align-items: center;
		width: auto;
		top: 0;
		flex: 1 1 auto;
		padding: 0;
		border-bottom: 0;
		margin-left: 15px;
	}

	@include breakpoint($tablet--land) {
		margin-left: 20px;
	}
}

.navbar__left.mobile {
	display: block;
}

.navbar__right {
	position: absolute;
	display: block;
	width: 100%;
	top: 70px;
	left: 0;
	padding-right: 20px;
	padding-left: 20px;

	@include breakpoint($tablet) {
		position: relative;
		display: flex;
		width: auto;
		flex: 0 0 auto;
		justify-content: flex-end;
		top: 0;
		padding-right: 0;
		margin-right: 0;
	}
}

.navbar__item-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	height: 45px;
}

.navbar__item {
	position: relative;
	display: inline-block;
	flex: 0 0 auto;
	align-self: center;
	@include regular;
	font-size: 16px;
	color: #444444;
	cursor: pointer;
	text-align: center;
	padding: 0 7px;
	border-bottom: 1px solid #f1f1f1;

	@include breakpoint($tablet) {
		font-size: 14px;
		border-bottom: 0;
		padding-right: 10px;
		padding-left: 10px;
	}

	@include breakpoint($tablet--land) {
		padding: 0px;
		padding-right: 20px;
		padding-left: 19px;
	}
}

.navbar__item:hover {
	color: $color-orange;
}

.navbar__item:before {
	position: absolute;
	display: block;
	content: '';
	width: 1px;
	height: 30px;
	background-color: #ededed;
	top: 50%;
	margin-top: -15px;
	right: 0;
}

.navbar__item:first-child {
	@include breakpoint($tablet) {
		padding-left: 0;
	}
}

.navbar__item:last-child {
	margin-right: 0;
}

.navbar__item:last-child:before {
	display: none;
}

.navbar__item--bold {
	//@include bold;
	color: #000;
}

.navbar__item--orange {
	color: #ee7836;
}

.navbar__item--orange:hover {
	color: #000;
}

.navbar__item--red {
	color: #e62f34;
}

.navbar__item--red:hover {
	color: #000;
}

.navbar__item--desktop {
	display: none;

	@include breakpoint($desktop) {
		display: inline-block;
	}
}

.navbar__item--category:hover {
	color: #fff;
}

.navbar--secondary-fixed .navbar__item--category,
.navbar__item--dropdown-active {
	color: #fff;
}

.navbar--secondary-fixed .navbar__item--category:after,
.navbar__item--dropdown-active:after {
	position: absolute;
	display: block;
	content: '';
	width: calc(100% + 5px);
	height: 40px;
	border-radius: 7px 7px 0 0;
	background-color: $color-orange;
	left: -15px;
	top: -8px;
	z-index: 9;

	@include breakpoint($desktop) {
		left: -20px;
	}
}

.navbar--secondary-fixed.header--addon .navbar-secondary {
	position: relative;
	top: -1px;
}

.navbar__item-inner {
	position: relative;
	z-index: 99;
}

.navbar__action-tag {
	position: absolute;
	top: -10px;
	right: 0;
	@include bold;
	font-size: 8px;
	//color: #fff;
	color: #e62f34;
	text-transform: uppercase;
	padding: 1px 5px;
	border-radius: 6px;
}

.navbar__action-tag--bg {
	color: #66b841;
}

.navbar__item-caret {
	position: relative;
	display: inline-block;
	width: 0;
	height: 0;
	margin-left: 2px;
	vertical-align: middle;
	border-top: 4px dashed;
	border-top: 4px solid\9;
	border-right: 4px solid transparent;
	border-left: 4px solid transparent;
	margin-left: 6px;
}

.navbar__mobile-icon {
	position: relative;
	display: flex;
	height: 50px;
	right: 0;
	align-items: center;
	justify-content: center;
	font-size: 21px;
	color: #fff;
	padding: 0 12px;
	background-color: #373737;

	@include breakpoint($tablet) {
		display: none;
	}
}

.navbar__mobile-icon--menu {
	width: 100px;
	margin-right: -15px;
}

.navbar__mobile-icon--search {
	background-color: $color-green;
	width: 61px;
	margin-left: auto;
}

.navbar__mobile-icon--lite-version {
	margin-right: 0;
}

.navbar__mobile-icon svg {
	width: 21px;
	height: 21px;
}

.navbar__mobile-text {
	position: relative;
	display: inline-block;
	top: 1px;
	@include regular;
	color: #fff;
	text-transform: uppercase;
	font-size: 14px;
	margin-left: 7px;
}

.navbar__logout {
	position: relative;
	display: none;
	margin-left: 10px;
	padding-left: 35px;
	@include clearfix();

	@include breakpoint($tablet) {
		display: inline-block;
		margin-left: 0;
		align-self: center;
		flex: 0 0 auto;
	}

	@include breakpoint($tablet--land) {
		margin-left: 10px;
	}
}

.navbar__logout-icon {
	position: absolute;
	display: block;
	left: 0px;
	top: 50%;
	margin-top: -18px;
	width: 25px;
	height: 36px;
	fill: #dddddd;
}

.navbar__logout-link {
	position: relative;
	display: block;
	@include bold;
	font-size: 14px;
	color: $color-orange;
	text-decoration: none;
}

.navbar__logout-link:hover {
	text-decoration: underline;
}

.navbar__logout-link.color--grey {
	color: grey;
}

.navbar__logout-link.color--grey:hover {
	color: $color-orange;
}

.navbar__logout-row {
	position: relative;
	display: block;
	@include regular;
	font-size: 12px;
	color: grey;
	@include clearfix;
}

.navbar__logout-login {
	position: relative;
	display: inline-block;
	@include regular;
	font-size: 12px;
	color: grey;
	text-decoration: underline;
}

.navbar__logout-login:hover {
	text-decoration: none;
}

.navbar__mobile-back-link {
	position: fixed;
	display: flex;
	align-items: center;
	width: 100%;
	top: $mobile-menu-height;
	padding: 12px 10px;
	background-color: #eaeaea;
	text-align: left;
	z-index: 99;

	@include breakpoint($tablet) {
		display: none;
	}
}

.navbar__mobile-back-link svg {
	margin-right: 7px;
	width: 12px;
	height: 12px;
}

.navbar--secondary-fixed.main {
	@include breakpoint($tablet) {
		padding-top: 140px;
	}
}

.navbar--secondary-fixed.main--addon.main {
	@include breakpoint($tablet) {
		padding-top: 0px;
	}
}

.navbar--secondary-fixed .navbar-secondary {
	@include breakpoint($tablet) {
		display: block;
	}
}

.navbar--kl-promo {
	padding-top: 125px;
}

.navbar--kl-promo.navbar--secondary-fixed {
	padding-top: 170px;
}

.navbar--lite-version {
}

.navbar--lite-version .navbar__item-wrapper {
	position: absolute;
	display: none;
	width: 100%;
	height: auto;
	flex-direction: column;
	top: 50px;
	background: #fff;
	padding: 1rem;

	@include breakpoint($tablet) {
		position: relative;
		display: flex;
		width: auto;
		flex-direction: row;
		top: 0;
		padding: 0;
	}
}

.navbar--lite-version .navbar__item-wrapper.show {
	display: flex !important;
}

.navbar--lite-version .navbar__item {
	padding: 1rem;

	@include breakpoint($tablet) {
		padding: 0;
		padding-left: 10px;
		padding-right: 10px;
	}

	@include breakpoint($tablet--land) {
		padding-left: 18px;
		padding-right: 18px;
	}
}

.navbar--lite-version .navbar__item:before {
	display: none;

	@include breakpoint($tablet) {
		display: block;
	}
}

.navbar--lite-version .navbar__item:nth-child(n + 10) {
	display: block;

	@include breakpoint($tablet) {
		display: none;
	}

	@include breakpoint($tablet--land) {
		display: block;
	}
}

.navbar__addon-promo {
	position: relative;
	display: flex;
	align-items: center;
	font-size: 13px;
	text-align: left;
	margin-left: auto;
	line-height: 1.25;
	padding: 0.25rem 0.75rem;
	padding-left: 38px;
	padding-right: 24px;
	border: 1px solid #e5e5e5;
	border-radius: 8px;
	opacity: 0;
	//box-shadow: 0px 8px 8px -8px rgba(0, 0, 0, 0.3);
	@include background('chrome.png', 22px 22px, 8px 50%);

	@include breakpoint($desktop) {
		opacity: 1;
	}
}

.navbar__addon-promo--show {
	opacity: 1;
}

.navbar__addon-promo:hover {
	color: #fff;
	background-color: $color-green;
}

.navbar__addon-promo:hover:after {
	@include background('svg/angle-right-solid-white.svg', contain, 50% 50%);
}

.navbar__addon-promo-icon--ff {
	background-image: url(../../images/firefox.png);
}

.navbar__addon-promo-icon--safari {
	background-image: url(../../images/safari.png);
}

.navbar__addon-promo:after {
	position: absolute;
	display: block;
	content: '';
	width: 8px;
	height: 16px;
	right: 8px;
	@include background('svg/angle-right-solid.svg', contain, 50% 50%);
}

.navbar__addon-promo small {
	position: relative;
	display: block;
	font-size: 11px;
}
