.sale-info-message {
  position: relative;
  display: block;

  margin-top: 10px;
  margin-bottom: 10px;

  //-webkit-box-shadow: inset 0 0 20px 0 rgb(250, 250, 250);
  //-moz-box-shadow: inset 0 0 20px 0 rgb(250, 250, 250);
  //box-shadow: inset 0 0 20px 0 rgb(250, 250, 250);

  //background-color: #757575;
  border-radius: 5px;
  margin-top: -10px;
  border-radius: 0 0 5px 5px;

  background-color: #d9edf7;
  border: 1px solid #bce8f1;
  border-top: 1px solid #f2f1f1;
}

.sale-info-message--show {
  display: block;
}

.sale-info-message__icon {
  position: absolute;
  display: none;

  width: 30px;
  height: 30px;

  top: 50%;
  left: 26px;

  transform: translate(0, -50%);

  color: #fff;
  color: #31708f;
  font-size: 30px;

  @media screen and (min-width: $min-sm) {
    display: block;
  }
}

.sale-info-message__content {
  padding: 15px;
  //padding-left: 60px;

  @media screen and (min-width: $min-sm) {
    padding-left: 80px;
  }
}

.sale-info-message__title {
  @include regular;
  color: #fff;
  color: #31708f;

  font-size: 16px;
  margin-bottom: 0;

  @media screen and (min-width: $min-sm) {
    font-size: 21px;
  }
}

.sale-info-message__text {
  @include regular;
  color: #fff;
  color: #31708f;
  font-size: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.sale-info-message__text a {
  color: #fff;

  text-decoration: underline;
  &:hover {
    text-decoration: none;
  }
}



